<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2018 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------
use think\facade\Route;

    Route::get('think', function () {
    return 'hello,ThinkPHP8!';
});

    Route::get('hello/:name', 'index/hello');

// 默认首页重定向到登录页
    Route::get('/', function() {
    return redirect('/login');
});

// 认证相关路由
    Route::get('login', 'Auth/login');              // 显示登录页面
    Route::post('auth/doLogin', 'Auth/doLogin');     // 处理登录请求
    Route::get('auth/logout', 'Auth/logout');        // 退出登录
    Route::get('auth/check', 'Auth/checkLogin');     // 检查登录状态
    Route::get('auth/setNickname', 'Auth/setNickname');     // 显示设置昵称页面
    Route::post('auth/doSetNickname', 'Auth/doSetNickname'); // 处理设置昵称请求

// 注册相关路由
    Route::get('register', 'Auth/register');         // 显示注册页面（ThinkPHP模板）
    Route::get('auth/register', 'Auth/register');     // 显示注册页面（备用路径）
    Route::post('auth/doRegister', 'Auth/doRegister'); // 处理注册请求
    Route::post('auth/checkUsername', 'Auth/checkUsername'); // 检查用户名是否可用

// 商城相关路由
// 注意：具体路由必须放在通用路由之前
    Route::get('shop/getItems', 'Shop/getItems')->middleware(['validation', 'auth'], ['response' => 'json']);   // 获取商品列表API
    Route::get('shop/getItemDetail/:id', 'Shop/getItemDetail')->middleware(['validation', 'auth'], ['response' => 'json']); // 获取商品详情API
    Route::post('shop/purchase', 'Shop/buyItem')->middleware(['validation', 'rate_limit', 'auth'], ['response' => 'json']);    // 购买商品API
    Route::post('shop/buyItem', 'Shop/buyItem')->middleware(['validation', 'rate_limit', 'auth'], ['response' => 'json']);    // 购买商品API（兼容）
    Route::get('user/getBalance', 'CurrencyAPI/getBalance')->middleware(['auth'], ['response' => 'json']); // 获取用户余额API
    Route::get('api/getShopConfig', 'admin/SettingsController/getShopConfig'); // 获取最新ShopConfig配置API（公开）
    Route::get('shop', 'Shop/index')->middleware('auth');               // 商城主页（ThinkPHP模板）
    Route::get('test-route', function() {
        return 'ThinkPHP路由测试成功！当前时间: ' . date('Y-m-d H:i:s');
    });  // 测试路由

    Route::get('test-session', function() {
        // 检查cookie中的session ID
        $cookieSessionId = $_COOKIE['PHPSESSID'] ?? null;

        // 如果有cookie中的session ID，尝试使用它
        if ($cookieSessionId && session_status() === PHP_SESSION_NONE) {
            session_id($cookieSessionId);
            session_start();
        } elseif (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        $sessionId = session_id();
        $userId = \think\facade\Session::get('user_id');
        $userInfo = \think\facade\Session::get('user_info');
        return json([
            'cookie_session_id' => $cookieSessionId,
            'current_session_id' => $sessionId,
            'user_id' => $userId,
            'user_info' => $userInfo,
            'has_user_id' => \think\facade\Session::has('user_id'),
            'session_status' => session_status(),
            'native_session' => $_SESSION ?? [],
            'native_user_id' => $_SESSION['user_id'] ?? null
        ]);
    });  // Session测试路由

// 购买历史插件路由
// 注意：具体API路由必须放在通用路由之前
    Route::group('purchase-history', function () {
    Route::get('getHistoryList', 'PurchaseHistory/getHistoryList'); // 获取购买历史列表API
    Route::get('getOrderDetail', 'PurchaseHistory/getOrderDetail'); // 获取订单详情API
    Route::get('getStatistics', 'PurchaseHistory/getStatistics');   // 获取统计信息API
    Route::get('exportHistory', 'PurchaseHistory/exportHistory');   // 导出购买历史
    Route::post('getItemIcons', 'PurchaseHistory/getItemIcons');    // 批量获取商品图标API
})->middleware('auth', ['response' => 'json']);
    Route::get('purchase-history', 'PurchaseHistory/index')->middleware('auth');  // 购买历史主页

// 管理面板路由
    Route::group('admin', function () {
    Route::get('checkAdmin', 'AdminDashboard/checkAdmin');      // 检查管理员权限API
    Route::get('getDashboardStats', 'AdminDashboard/getDashboardStats'); // 获取仪表盘基础统计API
    Route::get('getOrderStats', 'AdminDashboard/getOrderStats'); // 获取订单统计API
    Route::get('getCurrencyStats', 'AdminDashboard/getCurrencyStats'); // 获取货币统计API
    Route::get('getAdminList', 'admin/AdminController/getAdminList');           // 获取管理员列表API
    Route::post('addAdmin', 'admin/AdminController/addAdmin');                  // 添加管理员API
    Route::post('updateAdmin/:username', 'admin/AdminController/updateAdmin');  // 更新管理员API
    Route::delete('deleteAdmin/:username', 'admin/AdminController/deleteAdmin'); // 删除管理员API
    Route::get('getStats', 'Admin/getStats');                   // 获取统计信息API
    Route::get('getFastStats', 'Admin/getFastStats');           // 获取快速统计信息API
    Route::get('getStatsFlat', 'AdminDashboard/getStatsFlat');  // 获取扁平结构统计数据API
    Route::post('updateStatsAsync', 'Admin/updateStatsAsync');   // 异步更新统计数据API
    Route::get('getUserList', 'AdminUsers/getUserList');             // 获取用户列表API
    Route::get('getUserDetail', 'AdminUsers/getUserDetail');         // 获取用户详情API
    Route::post('updateUserNickname', 'AdminUsers/updateUserNickname'); // 更新用户昵称API
    Route::post('banUser', 'AdminUsers/banUser');                    // 封禁用户API
    Route::post('unbanUser', 'AdminUsers/unbanUser');                // 解封用户API
    
    // 保留现有的Admin控制器API路由
    Route::get('getUserStats', 'Admin/getUserStats');           // 获取用户统计API
    Route::post('batchUserOperation', 'Admin/batchUserOperation'); // 批量用户操作API
    Route::post('clearUserCache', 'Admin/clearUserCache');      // 清除用户缓存API
    Route::post('warmupUserCache', 'Admin/warmupUserCache');    // 预热用户缓存API
    Route::get('getItemList', 'Admin/getItemList');             // 获取商品列表API
    Route::get('getItemDetail', 'Admin/getItemDetail');         // 获取商品详情API
    Route::post('updateItem', 'Admin/updateItem');              // 更新商品信息API
    Route::get('getOrderList', 'Admin/getOrderList');           // 获取订单列表API
    Route::get('getOrderDetail', 'Admin/getOrderDetail');       // 获取订单详情API
    Route::get('getOrderStats', 'Admin/getOrderStats');         // 获取订单统计API
    Route::post('processOrder', 'Admin/processOrder');          // 处理订单API
    Route::post('getUserBalance', 'Admin/getUserBalance');       // 获取用户余额API
    Route::post('doRecharge', 'Admin/doRecharge');               // 执行充值操作API
    Route::get('getRechargeOptions', 'Admin/getRechargeOptions'); // 获取充值选项API
    Route::get('getRechargeLogList', 'Admin/getRechargeLogList'); // 获取充值记录列表API

    Route::get('getActivationRecords', 'Admin/getActivationRecords'); // 获取激活记录列表API
    Route::get('getActivationStats', 'Admin/getActivationStats'); // 获取激活记录统计API
    Route::post('api/savePaymentSettings', 'AdminSettings/savePaymentSettings'); // 保存支付设置API
    Route::get('api/getPaymentSettings', 'AdminSettings/getPaymentSettings'); // 获取支付设置API
    Route::post('api/saveGeneralSettings', 'AdminSettings/saveGeneralSettings'); // 保存基本设置API
    Route::get('api/getGeneralSettings', 'AdminSettings/getGeneralSettings'); // 获取基本设置API
    Route::get('api/getGlobalConfig', 'admin/SettingsController/getGlobalConfig'); // 获取全局配置API
    Route::post('api/updateCurrency', 'admin/SettingsController/updateCurrency'); // 更新货币配置API
    Route::post('api/addCategory', 'admin/SettingsController/addCategory'); // 添加商品分类API
    Route::post('api/updateCategory', 'admin/SettingsController/updateCategory'); // 更新商品分类API
    Route::post('api/removeCategory', 'admin/SettingsController/removeCategory'); // 删除商品分类API

    // 道具发送API路由（放在页面路由之前）
    Route::get('api/item-delivery/getStats', 'admin/ItemDeliveryController/getStats'); // 获取发送统计
    Route::get('api/item-delivery/getItemList', 'admin/ItemDeliveryController/getItemList'); // 获取道具列表
    Route::post('api/item-delivery/sendItem', 'admin/ItemDeliveryController/sendItem'); // 发送道具
    Route::get('api/item-delivery/getDeliveryHistory', 'admin/ItemDeliveryController/getDeliveryHistory'); // 获取发送记录

    // 道具发送管理页面路由
    Route::get('item-delivery', 'admin/ItemDeliveryController/index'); // 道具发送管理页面

    // 周卡月卡管理API路由
    Route::get('cards/configs', 'admin/CardController/getCardConfigs'); // 获取卡片配置列表
    Route::get('cards/config', 'admin/CardController/getCardConfig'); // 获取单个卡片配置
    Route::post('cards/config', 'admin/CardController/updateCardConfig'); // 更新卡片配置
    Route::get('cards/user-cards', 'admin/CardController/getUserCards'); // 获取用户卡片列表
    Route::get('cards/stats', 'admin/CardController/getCardStats'); // 获取卡片统计
    Route::post('cards/grant', 'admin/CardController/grantCard'); // 发放卡片
    Route::post('cards/extend', 'admin/CardController/extendCard'); // 延长卡片
    Route::post('cards/grant-daily-reward', 'admin/CardController/grantDailyReward'); // 发放每日奖励
    Route::post('cards/delete-user-card', 'admin/CardController/deleteUserCard'); // 删除用户卡片

    // 周卡月卡管理页面路由
    Route::get('cards', 'admin/CardController/index'); // 周卡月卡管理页面

    Route::post('api/saveShopSettings', 'AdminSettings/saveShopSettings'); // 保存商城设置API
    Route::get('api/getShopSettings', 'AdminSettings/getShopSettings'); // 获取商城设置API

    // ThinkPHP模板页面路由
    Route::get('users', 'admin/UserController/index');          // 用户管理页面
    Route::get('items', 'admin/ItemController/index');          // 商品管理页面
    Route::get('orders', 'admin/OrderController/index');        // 订单管理页面
    Route::get('recharge', 'admin/RechargeController/index');    // 充值管理页面
    // Route::get('user-status', 'AdminUsers/userStatus');             // 用户状态管理页面 - 已移至user-status组
    Route::get('settings', 'admin/SettingsController/index');   // 系统设置页面

    // Admin子控制器路由
    Route::group('user', function () {
        Route::get('/', 'admin/UserController/index');                    // 用户管理页面
        Route::get('getUserList', 'admin/UserController/getUserList');    // 获取用户列表API
        Route::get('getUserDetail', 'admin/UserController/getUserDetail'); // 获取用户详情API
        Route::get('getUserStats', 'admin/UserController/getUserStats');   // 获取用户统计API
        Route::post('updateUserStatus', 'admin/UserController/updateUserStatus'); // 更新用户状态API
        Route::post('batchOperation', 'admin/UserController/batchOperation'); // 批量操作API
        Route::get('exportUsers', 'admin/UserController/exportUsers');     // 导出用户数据API
    });

    Route::group('item', function () {
        Route::get('/', 'admin/ItemController/index');                    // 商品管理页面
        Route::get('getItemList', 'admin/ItemController/getItemList');    // 获取商品列表API
        Route::get('getItemDetail', 'admin/ItemController/getItemDetail'); // 获取商品详情API
        Route::get('getCategories', 'admin/ItemController/getCategories'); // 获取商品分类API
        Route::post('addItem', 'admin/ItemController/addItem');           // 添加商品API
        Route::post('updateItem', 'admin/ItemController/updateItem');     // 更新商品API
        Route::post('updateItemStatus', 'admin/ItemController/updateItemStatus'); // 更新商品状态API
        Route::post('deleteItem', 'admin/ItemController/deleteItem');     // 删除商品API
        Route::post('batchOperation', 'admin/ItemController/batchOperation'); // 批量操作API
        Route::get('exportItems', 'admin/ItemController/exportItems');    // 导出商品数据API
    });

    Route::group('order', function () {
        Route::get('/', 'admin/OrderController/index');                   // 订单管理页面
        Route::get('getOrderList', 'admin/OrderController/getOrderList'); // 获取订单列表API
        Route::get('getOrderDetail', 'admin/OrderController/getOrderDetail'); // 获取订单详情API
        Route::get('getOrderStats', 'admin/OrderController/getOrderStats'); // 获取订单统计API
        Route::post('updateOrderStatus', 'admin/OrderController/updateOrderStatus'); // 更新订单状态API
        Route::post('resendOrder', 'admin/OrderController/resendOrder'); // 补发订单API
        Route::post('forceCompleteOrder', 'admin/OrderController/forceCompleteOrder'); // 强制完成订单API
        Route::post('test', 'admin/TestController/test'); // 测试API
        Route::post('processRefund', 'admin/OrderController/processRefund'); // 处理退款API
        Route::post('batchOperation', 'admin/OrderController/batchOperation'); // 批量操作API
        Route::get('exportOrders', 'admin/OrderController/exportOrders'); // 导出订单数据API
    });

    Route::group('recharge', function () {
        Route::get('/', 'admin/RechargeController/index');                    // 充值管理页面
        Route::post('doRecharge', 'admin/RechargeController/doRecharge');     // 执行充值操作API
        Route::post('getHistory', 'admin/RechargeController/getRechargeHistory'); // 获取充值记录API
        Route::post('getUserBalance', 'admin/RechargeController/getUserBalance'); // 查询用户余额API
    });

    Route::group('user-status', function () {
        Route::get('/', 'admin/UserStatusController/index');                  // 用户状态管理页面
        Route::get('getUserStats', 'admin/UserStatusController/getUserStats'); // 获取用户状态统计API
        Route::get('getOnlineUsers', 'admin/UserStatusController/getOnlineUsers'); // 获取在线用户列表API
        Route::get('getBanRecords', 'admin/UserStatusController/getBanRecords'); // 获取封禁记录API
        Route::post('batchUserOperation', 'admin/UserStatusController/batchUserOperation'); // 批量用户操作API
        Route::post('clearUserCache', 'admin/UserStatusController/clearUserCache'); // 清除用户缓存API
        Route::post('warmupUserCache', 'admin/UserStatusController/warmupUserCache'); // 预热用户缓存API
    });





    // 编码诊断API
    Route::post('api/testDatabaseCharset', 'Admin/testDatabaseCharset'); // 测试数据库字符集API
    Route::post('api/testCharacterEncoding', 'Admin/testCharacterEncoding'); // 测试角色名编码API
    Route::get('/', 'AdminDashboard/index');                   // 管理面板首页
})->middleware('auth');

// 系统设置路由组（独立于admin组）
Route::group('admin/settings', function () {
    Route::get('/', 'admin/SettingsController/index');                // 系统设置页面
    Route::get('getSystemConfig', 'admin/SettingsController/getSystemConfig'); // 获取系统配置API
    Route::post('updateSystemConfig', 'admin/SettingsController/updateSystemConfig'); // 更新系统配置API
    Route::post('clearCache', 'admin/SettingsController/clearCache'); // 清理缓存API
})->middleware('auth', ['response' => 'json']);

// 系统监控API - 独立路由
Route::get('api/systemMonitor', 'admin/SettingsController/getSystemMonitor');

// 日志管理API - 独立路由
Route::get('api/getLogFiles', 'admin/SettingsController/getLogFiles');
Route::post('api/getLogContent', 'admin/SettingsController/getLogContent');
Route::post('api/clearLogs', 'admin/SettingsController/clearLogs');

// 测试路由（无需认证）
Route::group('test', function () {
    Route::get('api/getAdminList', 'Admin/testGetAdminList'); // 测试获取管理员列表API
    Route::post('api/addAdmin', 'Admin/testAddAdmin'); // 测试添加管理员API
    Route::get('api/debugSession', 'Admin/debugSession'); // 调试session状态API
    Route::get('getStatsFlat', 'AdminDashboard/getStatsFlat'); // 测试获取扁平统计数据API
    Route::get('dashboard', 'AdminDashboard/testIndex'); // 测试仪表盘页面（无认证）
    Route::get('dashboard-v2', 'AdminDashboard/dashboardV2'); // 新版仪表盘页面（无认证）
});

// 主要管理页面路由
Route::get('admin', 'AdminDashboard/index')->middleware('auth');    // 管理面板主页
Route::get('admin/dashboard', 'AdminDashboard/index');              // 仪表盘页面（快速加载，内置认证）



// API路由
    Route::group('api', function () {
    Route::rule('getItemIcon', 'Api/getItemIcon', 'GET|POST');   // 获取商品图标API
    Route::rule('getItemInfo', 'Api/getItemInfo', 'GET|POST');   // 获取商品信息API
    Route::post('test', 'admin/TestController/test'); // 测试API

    // 商城状态API
    Route::group('shop', function () {
        Route::get('status', 'Shop/getStatus'); // 获取商城状态
    });
});

// 缓存管理路由
    Route::group('cache', function () {
    Route::get('stats', 'CacheManager/getStats');               // 获取缓存统计信息
    Route::post('reset-stats', 'CacheManager/resetStats');      // 重置缓存统计
    Route::post('warmup', 'CacheManager/warmup');               // 缓存预热
    Route::post('clear-pattern', 'CacheManager/clearByPattern'); // 按模式清除缓存
    Route::post('clear-user', 'CacheManager/clearUserCache');   // 清除用户缓存
    Route::get('health', 'CacheManager/getHealthStatus');       // 获取缓存健康状态
    Route::get('/', 'CacheManager/index');                      // 缓存管理页面
});

// 在线充值路由
    Route::post('recharge/create', 'Recharge/create')->middleware('auth', ['response' => 'json']); // 创建充值订单
    Route::get('recharge/query', 'Recharge/query')->middleware('auth', ['response' => 'json']); // 查询订单状态
    Route::rule('recharge/notify', 'Recharge/notify', 'GET|POST'); // 支付回调通知（无需认证）

// 货币管理API路由
    Route::group('currency', function () {
    Route::get('balance', 'CurrencyAPI/getBalance');             // 获取用户货币余额
    Route::post('c-coin', 'CurrencyAPI/modifyCCoin');           // 修改C币余额
    Route::post('coin', 'CurrencyAPI/modifyCoin');              // 修改泡点余额
    Route::post('silver', 'CurrencyAPI/modifySilver');          // 修改积分余额
    Route::post('batch-deduct', 'CurrencyAPI/batchDeduct');     // 批量扣除货币
})->middleware(['performance', 'validation', 'rate_limit', 'auth']);

// 缓存管理路由组
    Route::group('cache_manager', function () {
    Route::post('clearItemsCache', 'CacheManager/clearItemsCache');         // 清除商品列表缓存
    Route::post('clearItemInfoCache', 'CacheManager/clearItemInfoCache');   // 清除商品信息缓存
    Route::post('clearAllItemsCache', 'CacheManager/clearAllItemsCache');   // 清除所有商品缓存
    Route::post('warmupItemsCache', 'CacheManager/warmupItemsCache');       // 预热商品缓存
    Route::get('getCacheStats', 'CacheManager/getCacheStats');              // 获取缓存统计
});
    Route::get('cache_manager', 'CacheManager/index');                          // 缓存管理页面



// 充值相关路由
    Route::group('recharge', function () {
    Route::get('rates', 'Recharge/getRates');                   // 获取充值比例配置
    Route::post('create', 'Recharge/create');                   // 创建充值订单
    Route::get('history', 'Recharge/getHistory');               // 获取充值记录
    Route::get('query', 'Recharge/queryStatus');                // 查询支付状态
    Route::post('cancel', 'Recharge/cancelOrder');              // 取消订单
    Route::post('notify', 'Recharge/notify');                   // 支付回调通知
    Route::get('return', 'Recharge/returnUrl');                 // 支付返回页面
})->middleware('auth');

// 卡片系统路由（周卡、月卡）
    Route::group('card', function () {
    Route::get('info', 'Card/getCardInfo');                     // 获取卡片配置和用户状态
    Route::post('purchase', 'Card/purchaseCard');               // 购买卡片
    Route::post('claim', 'Card/claimDailyReward');              // 领取每日奖励
    Route::get('status', 'Card/getUserStatus');                 // 获取用户卡片状态
})->middleware('auth');

// 性能监控相关路由
    Route::group('performance', function () {
    Route::get('dashboard', 'PerformanceDashboard/index');      // 性能仪表板
    Route::get('report', 'PerformanceDashboard/detailedReport'); // 详细性能报告
    Route::post('optimize', 'PerformanceDashboard/optimize');   // 执行性能优化
    Route::post('reset', 'PerformanceDashboard/resetStats');    // 重置性能统计
    Route::get('export', 'PerformanceDashboard/exportReport');  // 导出性能报告
})->middleware('auth');
