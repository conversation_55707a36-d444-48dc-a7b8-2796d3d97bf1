<?php
/**
 * 购买系统路由配置
 * 定义物品购买相关的API路由
 */

use think\facade\Route;

// 购买相关路由组
Route::group('purchase', function () {
    
    // 购买单个商品
    Route::post('buy', 'Purchase/buyItem')
        ->middleware(['auth'])
        ->name('purchase.buy');
    
    // 批量购买商品
    Route::post('batch-buy', 'Purchase/batchBuyItems')
        ->middleware(['auth'])
        ->name('purchase.batch_buy');
    
    // 获取购买历史
    Route::get('history', 'Purchase/getPurchaseHistory')
        ->middleware(['auth'])
        ->name('purchase.history');
    
    // 获取商品详情（购买前确认）
    Route::get('item/:item_id', 'Purchase/getItemDetail')
        ->middleware(['auth'])
        ->name('purchase.item_detail');
    
    // 检查物品发送状态
    Route::get('delivery-status/:unique_num', 'Purchase/checkDeliveryStatus')
        ->middleware(['auth'])
        ->name('purchase.delivery_status');
    
    // 获取订单状态
    Route::get('status', 'Purchase/getOrderStatus')
        ->middleware(['auth'])
        ->name('purchase.order_status');
    
})->prefix('api/');

// 管理员相关路由（如果需要）
Route::group('admin/purchase', function () {
    
    // 获取所有订单列表
    Route::get('orders', 'Admin/Purchase/getOrderList')
        ->middleware(['auth', 'admin'])
        ->name('admin.purchase.orders');
    
    // 重新发送失败的订单
    Route::post('retry/:order_id', 'Admin/Purchase/retryOrder')
        ->middleware(['auth', 'admin'])
        ->name('admin.purchase.retry');
    
    // 取消订单
    Route::post('cancel/:order_id', 'Admin/Purchase/cancelOrder')
        ->middleware(['auth', 'admin'])
        ->name('admin.purchase.cancel');
    
    // 退款订单
    Route::post('refund/:order_id', 'Admin/Purchase/refundOrder')
        ->middleware(['auth', 'admin'])
        ->name('admin.purchase.refund');
    
})->prefix('api/');

// WebHook路由（用于游戏服务器回调）
Route::group('webhook', function () {
    
    // 物品发送状态回调
    Route::post('delivery-callback', 'Webhook/Purchase/deliveryCallback')
        ->name('webhook.delivery_callback');
    
})->prefix('api/');

