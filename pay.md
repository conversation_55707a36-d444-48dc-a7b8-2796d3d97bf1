# 支付接口文档

## 基本信息

**API信息**（兼容易支付接口）
- **接口地址**：https://z-pay.cn/
- **商户ID（PID）**：20240114014914
- **商户密钥（PKEY）**：lgoiv0nWfXqXvHzxcRD9drz4UlYPefCk

## 目录

1. [页面跳转支付](#页面跳转支付) - 用户直接跳转到支付页面
2. [API接口支付](#api接口支付) - 后台API调用获取支付链接
3. [订单查询](#查询单个订单) - 查询订单支付状态
4. [订单退款](#提交订单退款) - 申请订单退款
5. [支付通知](#支付结果通知) - 处理支付结果回调
6. [签名算法](#md5签名算法) - MD5签名计算方法

---

## 页面跳转支付

### 接口说明
页面跳转支付是最常用的支付方式，用户在商户网站点击支付后，直接跳转到支付平台的收银台页面完成支付。

**请求URL**
```
https://z-pay.cn/submit.php
```

**请求方法**
- POST（推荐，安全性更高）
- GET（简单但容易被劫持）

**适用场景**
- 用户前台直接发起支付
- 使用form表单跳转
- 拼接URL直接跳转

### 请求参数

| 参数 | 名称 | 类型 | 必填 | 描述 | 示例 |
|------|------|------|------|------|------|
| name | 商品名称 | String | 是 | 商品名称不超过100字 | iPhone XS Max |
| money | 订单金额 | String | 是 | 最多保留两位小数 | 5.67 |
| type | 支付方式 | String | 是 | alipay(支付宝) / wxpay(微信) | alipay |
| out_trade_no | 订单编号 | String | 是 | 商户系统唯一订单号 | 201911914837526544601 |
| notify_url | 异步通知地址 | String | 是 | 服务器回调地址，不支持参数 | http://www.aaa.com/notify.php |
| return_url | 同步跳转地址 | String | 是 | 支付完成后跳转地址 | http://www.aaa.com/success.php |
| pid | 商户ID | String | 是 | 商户唯一标识 | 20240114014914 |
| cid | 支付渠道ID | String | 否 | 指定支付渠道，不填随机选择 | 1234 |
| param | 附加参数 | String | 否 | 自定义参数，原样返回 | 金色 256G |
| sign | 签名 | String | 是 | MD5签名，参考签名算法 | 28f9583617d9caf66834292b6ab1cc89 |
| sign_type | 签名类型 | String | 是 | 固定值：MD5 | MD5 |

### 使用示例

**GET请求示例**
```
https://z-pay.cn/submit.php?name=iPhone%20XS%20Max&money=5999.00&type=alipay&out_trade_no=201911914837526544601&notify_url=http://www.aaa.com/notify.php&pid=20240114014914&param=金色%20256G&return_url=http://www.aaa.com/success.php&sign=28f9583617d9caf66834292b6ab1cc89&sign_type=MD5
```

**Form表单示例**
```html
<form action="https://z-pay.cn/submit.php" method="POST">
    <input type="hidden" name="name" value="iPhone XS Max">
    <input type="hidden" name="money" value="5999.00">
    <input type="hidden" name="type" value="alipay">
    <input type="hidden" name="out_trade_no" value="202401140001">
    <input type="hidden" name="notify_url" value="http://yoursite.com/notify.php">
    <input type="hidden" name="return_url" value="http://yoursite.com/success.php">
    <input type="hidden" name="pid" value="20240114014914">
    <input type="hidden" name="param" value="金色 256G">
    <input type="hidden" name="sign" value="计算得出的签名">
    <input type="hidden" name="sign_type" value="MD5">
    <button type="submit">立即支付</button>
</form>
```

### 返回结果

**成功返回**
- 直接跳转到支付平台收银台页面
- 用户在收银台完成支付操作

**失败返回**
```json
{
    "code": "error",
    "msg": "具体的错误信息"
}
```

---
## API接口支付

### 接口说明
API接口支付适用于后台调用，获取支付链接或二维码，适合需要在自己页面展示支付信息的场景。

**请求URL**
```
https://z-pay.cn/mapi.php
```

**请求方法**
- POST（Content-Type: multipart/form-data）

### 请求参数

| 字段名 | 变量名 | 必填 | 类型 | 示例值 | 描述 |
|--------|--------|------|------|--------|------|
| 商户ID | pid | 是 | String | 20240114014914 | 商户唯一标识 |
| 支付渠道ID | cid | 否 | String | 1234 | 指定支付渠道，不填随机选择 |
| 支付方式 | type | 是 | String | alipay | alipay(支付宝) / wxpay(微信) |
| 商户订单号 | out_trade_no | 是 | String | 20160806151343349 | 商户系统唯一订单号 |
| 异步通知地址 | notify_url | 是 | String | http://www.pay.com/notify.php | 服务器异步通知地址 |
| 商品名称 | name | 是 | String | VIP会员 | 商品名称，超过127字节自动截取 |
| 商品金额 | money | 是 | String | 1.00 | 单位：元，最大2位小数 |
| 用户IP地址 | clientip | 是 | String | ************* | 用户发起支付的IP地址 |
| 设备类型 | device | 否 | String | pc | pc/mobile/app，默认pc |
| 业务扩展参数 | param | 否 | String | 金色256G | 支付后原样返回 |
| 签名字符串 | sign | 是 | String | 202cb962ac59075b964b07152d234b70 | MD5签名 |
| 签名类型 | sign_type | 是 | String | MD5 | 固定值：MD5 |

### 返回结果

**成功返回**

| 字段名 | 变量名 | 类型 | 示例值 | 描述 |
|--------|--------|------|--------|------|
| 返回状态码 | code | Int | 1 | 1为成功，其它值为失败 |
| 返回信息 | msg | String | 创建订单成功 | 失败时返回错误原因 |
| 订单号 | trade_no | String | 20160806151343349 | 支付平台订单号 |
| 内部订单号 | O_id | String | 123456 | 支付平台内部订单号 |
| 支付跳转URL | payurl | String | https://xxx.cn/pay/wxpay/202010903/ | 直接跳转支付的URL |
| 二维码链接 | qrcode | String | https://xxx.cn/pay/wxpay/202010903/ | 生成二维码的链接 |
| 二维码图片 | img | String | https://z-pay.cn/qrcode/123.jpg | 二维码图片地址 |

**失败返回**
```json
{
    "code": "error",
    "msg": "具体的错误信息"
}
```

### API调用示例

**PHP示例**
```php
<?php
function createPayment($orderData) {
    $params = [
        'pid' => '20240114014914',
        'type' => $orderData['type'],
        'out_trade_no' => $orderData['out_trade_no'],
        'notify_url' => $orderData['notify_url'],
        'name' => $orderData['name'],
        'money' => $orderData['money'],
        'clientip' => $_SERVER['REMOTE_ADDR'],
        'device' => 'pc',
        'param' => $orderData['param'] ?? '',
        'sign_type' => 'MD5'
    ];

    // 生成签名
    $params['sign'] = generateSign($params, 'lgoiv0nWfXqXvHzxcRD9drz4UlYPefCk');

    // 发送请求
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://z-pay.cn/mapi.php');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $params);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

    $response = curl_exec($ch);
    curl_close($ch);

    return json_decode($response, true);
}

// 使用示例
$result = createPayment([
    'type' => 'alipay',
    'out_trade_no' => '202401140001',
    'notify_url' => 'http://yoursite.com/notify.php',
    'name' => 'iPhone XS Max',
    'money' => '5999.00',
    'param' => '金色 256G'
]);

if ($result['code'] == 1) {
    // 支付创建成功
    if (isset($result['qrcode'])) {
        echo "二维码支付：" . $result['qrcode'];
    } elseif (isset($result['payurl'])) {
        echo "跳转支付：" . $result['payurl'];
    }
} else {
    echo "支付创建失败：" . $result['msg'];
}
?>
```

---
## 查询单个订单

### 接口说明
用于查询订单的支付状态和详细信息。

**请求URL**
```
https://z-pay.cn/api.php?act=order&pid={商户ID}&key={商户密钥}&out_trade_no={商户订单号}
```

**请求方法**
- GET

### 请求参数

| 参数 | 名称 | 类型 | 必填 | 描述 | 示例 |
|------|------|------|------|------|------|
| act | 操作类型 | String | 是 | 固定值：order | order |
| pid | 商户ID | String | 是 | 商户唯一标识 | 20240114014914 |
| key | 商户密钥 | String | 是 | 商户密钥 | lgoiv0nWfXqXvHzxcRD9drz4UlYPefCk |
| trade_no | 系统订单号 | String | 二选一 | 支付平台订单号 | 20160806151343312 |
| out_trade_no | 商户订单号 | String | 二选一 | 商户系统订单号 | 20160806151343349 |

**注意**：trade_no 和 out_trade_no 必须提供其中一个

### 返回结果

| 字段名 | 变量名 | 类型 | 示例值 | 描述 |
|--------|--------|------|--------|------|
| 返回状态码 | code | Int | 1 | 1为成功，其它值为失败 |
| 返回信息 | msg | String | 查询订单号成功！ | 查询结果说明 |
| 支付平台订单号 | trade_no | String | 2016080622555342651 | 支付平台生成的订单号 |
| 商户订单号 | out_trade_no | String | 20160806151343349 | 商户系统内部订单号 |
| 支付方式 | type | String | alipay | alipay(支付宝) / wxpay(微信) |
| 商户ID | pid | String | 20240114014914 | 发起支付的商户ID |
| 创建时间 | addtime | String | 2016-08-06 22:55:52 | 订单创建时间 |
| 完成时间 | endtime | String | 2016-08-06 22:55:52 | 交易完成时间 |
| 商品名称 | name | String | VIP会员 | 商品名称 |
| 商品金额 | money | String | 1.00 | 订单金额 |
| 支付状态 | status | Int | 1 | 1=已支付，0=未支付 |
| 扩展参数 | param | String | 金色256G | 业务扩展参数 |
| 支付者账号 | buyer | String | <EMAIL> | 支付者账号信息 |

### 查询示例

```php
<?php
// 查询订单状态
function queryOrder($outTradeNo) {
    $url = "https://z-pay.cn/api.php";
    $params = [
        'act' => 'order',
        'pid' => '20240114014914',
        'key' => 'lgoiv0nWfXqXvHzxcRD9drz4UlYPefCk',
        'out_trade_no' => $outTradeNo
    ];

    $queryString = http_build_query($params);
    $response = file_get_contents($url . '?' . $queryString);

    return json_decode($response, true);
}

// 使用示例
$result = queryOrder('202401140001');
if ($result['code'] == 1) {
    if ($result['status'] == 1) {
        echo "订单已支付";
    } else {
        echo "订单未支付";
    }
} else {
    echo "查询失败：" . $result['msg'];
}
?>
```

---
## 提交订单退款

### 接口说明
用于申请订单退款，需要订单已支付且符合退款条件。

**请求URL**
```
https://z-pay.cn/api.php?act=refund
```

**请求方法**
- POST

### 请求参数

| 字段名 | 变量名 | 必填 | 类型 | 示例值 | 描述 |
|--------|--------|------|------|--------|------|
| 商户ID | pid | 是 | String | 20240114014914 | 商户唯一标识 |
| 商户密钥 | key | 是 | String | lgoiv0nWfXqXvHzxcRD9drz4UlYPefCk | 商户密钥 |
| 支付平台订单号 | trade_no | 二选一 | String | 20160806151343349021 | 支付平台订单号 |
| 商户订单号 | out_trade_no | 二选一 | String | 20160806151343349 | 商户系统订单号 |
| 退款金额 | money | 是 | String | 1.50 | 退款金额，多数通道需与原订单金额一致 |

**注意**：trade_no 和 out_trade_no 必须提供其中一个

### 返回结果

| 字段名 | 变量名 | 类型 | 示例值 | 描述 |
|--------|--------|------|--------|------|
| 返回状态码 | code | Int | 1 | 1为成功，其它值为失败 |
| 返回信息 | msg | String | 退款成功 | 退款结果说明 |

### 退款示例

```php
<?php
function refundOrder($outTradeNo, $money) {
    $params = [
        'act' => 'refund',
        'pid' => '20240114014914',
        'key' => 'lgoiv0nWfXqXvHzxcRD9drz4UlYPefCk',
        'out_trade_no' => $outTradeNo,
        'money' => $money
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://z-pay.cn/api.php');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $params);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

    $response = curl_exec($ch);
    curl_close($ch);

    return json_decode($response, true);
}

// 使用示例
$result = refundOrder('202401140001', '5999.00');
if ($result['code'] == 1) {
    echo "退款成功";
} else {
    echo "退款失败：" . $result['msg'];
}
?>
```

## 支付结果通知

### 接口说明
支付完成后，支付平台会向商户系统发送支付结果通知，包括异步通知和同步跳转。

**通知类型**
- 服务器异步通知（notify_url）- 后台接收支付结果
- 页面跳转通知（return_url）- 用户支付完成后跳转

**请求方法**
- GET

### 通知参数

| 参数 | 名称 | 类型 | 描述 | 示例 |
|------|------|------|------|------|
| pid | 商户ID | String | 商户唯一标识 | 20240114014914 |
| name | 商品名称 | String | 商品名称，不超过100字 | iPhone XS Max |
| money | 订单金额 | String | 订单金额，最多2位小数 | 5999.00 |
| out_trade_no | 商户订单号 | String | 商户系统内部订单号 | 202401140001 |
| trade_no | 支付平台订单号 | String | 支付平台生成的订单号 | 2019011922001418111011411195 |
| param | 业务扩展参数 | String | 原样返回的自定义参数 | 金色 256G |
| trade_status | 支付状态 | String | TRADE_SUCCESS=成功 | TRADE_SUCCESS |
| type | 支付方式 | String | alipay(支付宝)/wxpay(微信) | alipay |
| sign | 签名 | String | MD5签名，用于验证 | ef6e3c5c6ff45018e8c82fd66fb056dc |
| sign_type | 签名类型 | String | 固定值：MD5 | MD5 |

### 签名验证

**验证方法**
1. 接收到通知参数后，按照签名算法重新计算签名
2. 将计算得出的签名与通知中的sign参数对比
3. 如果一致，说明是支付平台发送的真实通知

**验证示例**
```php
<?php
function verifyNotify($params, $key) {
    $sign = $params['sign'];
    unset($params['sign'], $params['sign_type']);

    // 移除空值参数
    $params = array_filter($params, function($value) {
        return $value !== '' && $value !== null;
    });

    // 按键名排序
    ksort($params);

    // 拼接字符串
    $signStr = '';
    foreach ($params as $k => $v) {
        $signStr .= $k . '=' . $v . '&';
    }
    $signStr = rtrim($signStr, '&') . $key;

    // 验证签名
    return md5($signStr) === $sign;
}

// 使用示例
$isValid = verifyNotify($_GET, 'lgoiv0nWfXqXvHzxcRD9drz4UlYPefCk');
if ($isValid && $_GET['trade_status'] === 'TRADE_SUCCESS') {
    // 支付成功，处理业务逻辑
    echo 'success'; // 必须返回success
} else {
    echo 'fail';
}
?>
```
### 重要注意事项

1. **返回响应**：收到回调信息后请返回“success”，否则程序将判定您的回调地址未正确通知到。

2. **重复通知**：同样的通知可能会多次发送给商户系统。商户系统必须能够正确处理重复的通知。

3. **幂等性处理**：当收到通知进行处理时，首先检查对应业务数据的状态，判断该通知是否已经处理过，如果没有处理过再进行处理，如果处理过直接返回结果成功。在对业务数据进行状态检查和处理之前，要采用数据锁进行并发控制，以避免函数重入造成的数据混乱。

4. **安全验证**：商户系统对于支付结果通知的内容一定要做签名验证，并校验返回的订单金额是否与商户侧的订单金额一致，防止数据泄漏导致出现“假通知”，造成资金损失。

5. **重试机制**：对后台通知交互时，如果平台收到商户的应答不是纯字符串success或超过5秒后返回时，平台认为通知失败，平台会通过一定的策略（通知频率为0/15/15/30/180/1800/1800/1800/1800/3600，单位：秒）间接性重新发起通知，尽可能提高通知的成功率，但不保证通知最终能成功。

---

## MD5签名算法

### 签名步骤

1. **参数排序**：将发送或接收到的所有参数按照参数名ASCII码从小到大排序（a-z），`sign`、`sign_type`、和空值不参与签名！

2. **参数拼接**：将排序后的参数拼接成URL键值对的格式，例如 `a=b&c=d&e=f`，参数值不要进行url编码。

3. **加密签名**：再将拼接好的字符串与商户密钥KEY进行MD5加密得出sign签名参数：
   ```
   sign = md5(a=b&c=d&e=f + KEY)
   ```
   **注意**：`+` 为各语言的拼接符，不是字符！md5结果为小写。

4. **代码示例**：具体签名与发起支付的示例代码可参考上述各接口的PHP示例。

### PHP签名函数

```php
<?php
function generateSign($params, $key) {
    // 移除sign和sign_type参数
    unset($params['sign']);
    unset($params['sign_type']);

    // 移除空值参数
    $params = array_filter($params, function($value) {
        return $value !== '' && $value !== null;
    });

    // 按键名排序
    ksort($params);

    // 拼接字符串
    $signStr = '';
    foreach ($params as $k => $v) {
        $signStr .= $k . '=' . $v . '&';
    }
    $signStr = rtrim($signStr, '&');

    // 加上密钥
    $signStr .= $key;

    // MD5加密，结果转小写
    return strtolower(md5($signStr));
}

// 使用示例
$params = [
    'name' => 'iPhone XS Max',
    'money' => '5999.00',
    'type' => 'alipay',
    'out_trade_no' => '202401140001',
    'notify_url' => 'http://yoursite.com/notify.php',
    'return_url' => 'http://yoursite.com/success.php',
    'pid' => '20240114014914',
    'param' => '金色 256G'
];

$key = 'lgoiv0nWfXqXvHzxcRD9drz4UlYPefCk';
$sign = generateSign($params, $key);
echo "生成的签名：" . $sign;
?>
```

---

## 总结

本文档提供了完整的支付接口使用说明，包括：

- ✅ **页面跳转支付** - 适用于用户直接支付场景
- ✅ **API接口支付** - 适用于后台调用获取支付信息
- ✅ **订单查询** - 查询支付状态和订单详情
- ✅ **订单退款** - 处理退款申请
- ✅ **支付通知** - 处理支付结果回调
- ✅ **签名算法** - 确保数据安全性

如有疑问，请联系技术支持。