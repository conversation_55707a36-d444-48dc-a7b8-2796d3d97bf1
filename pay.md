# 支付接口文档

## 基本信息

**API信息**（兼容 易支付 接口）
- 接口地址：https://zpayz.cn/
- 商户ID（PID）：20240114014914
- 商户密钥（PKEY）：lgoiv0nWfXqXvHzxcRD9drz4UlYPefCk

## 目录

1. [页面跳转支付](#页面跳转支付) - 用户直接跳转到支付页面
2. [API接口支付](#api接口支付) - 后台API调用获取支付链接
3. [订单查询](#查询单个订单) - 查询订单支付状态
4. [订单退款](#提交订单退款) - 申请订单退款
5. [支付通知](#支付结果通知) - 处理支付结果回调
6. [签名算法](#md5签名算法) - MD5签名计算方法

## 页面跳转支付

### 接口说明
页面跳转支付是最常用的支付方式，用户在商户网站点击支付后，直接跳转到支付平台的收银台页面完成支付。

**请求URL**
```
https://zpayz.cn/submit.php
```

**请求方法**
- POST（推荐，安全性更高）
- GET（简单但容易被劫持）

**适用场景**
- 用户前台直接发起支付
- 使用form表单跳转
- 拼接URL直接跳转

### 请求参数

| 参数 | 名称 | 类型 | 必填 | 描述 | 示例 |
|------|------|------|------|------|------|
| name | 商品名称 | String | 是 | 商品名称不超过100字 | iPhone XS Max |
| money | 订单金额 | String | 是 | 最多保留两位小数 | 5.67 |
| type | 支付方式 | String | 是 | alipay(支付宝) / wxpay(微信) | alipay |
| out_trade_no | 订单编号 | String | 是 | 商户系统唯一订单号 | 201911914837526544601 |
| notify_url | 异步通知地址 | String | 是 | 服务器回调地址，不支持参数 | http://www.aaa.com/notify.php |
| return_url | 同步跳转地址 | String | 是 | 支付完成后跳转地址 | http://www.aaa.com/success.php |
| pid | 商户ID | String | 是 | 商户唯一标识 | 20240114014914 |
| cid | 支付渠道ID | String | 否 | 指定支付渠道，不填随机选择 | 1234 |
| param | 附加参数 | String | 否 | 自定义参数，原样返回 | 金色 256G |
| sign | 签名 | String | 是 | MD5签名，参考签名算法 | 28f9583617d9caf66834292b6ab1cc89 |
| sign_type | 签名类型 | String | 是 | 固定值：MD5 | MD5 |

### 实现方式

#### 方式一：Form表单提交（推荐）

```html
<form action="https://zpayz.cn/submit.php" method="POST" id="payForm">
    <input type="hidden" name="name" value="iPhone XS Max">
    <input type="hidden" name="money" value="5999.00">
    <input type="hidden" name="type" value="alipay">
    <input type="hidden" name="out_trade_no" value="202401140001">
    <input type="hidden" name="notify_url" value="http://yoursite.com/notify.php">
    <input type="hidden" name="return_url" value="http://yoursite.com/success.php">
    <input type="hidden" name="pid" value="20240114014914">
    <input type="hidden" name="param" value="金色 256G">
    <input type="hidden" name="sign" value="计算得出的签名">
    <input type="hidden" name="sign_type" value="MD5">
    <button type="submit">立即支付</button>
</form>
```

#### 方式二：JavaScript自动提交

```javascript
function submitPayment(orderData) {
    // 创建表单
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = 'https://zpayz.cn/submit.php';

    // 添加参数
    const params = {
        name: orderData.name,
        money: orderData.money,
        type: orderData.type,
        out_trade_no: orderData.out_trade_no,
        notify_url: orderData.notify_url,
        return_url: orderData.return_url,
        pid: '20240114014914',
        param: orderData.param || '',
        sign: calculateSign(orderData), // 计算签名
        sign_type: 'MD5'
    };

    // 创建隐藏字段
    for (let key in params) {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = key;
        input.value = params[key];
        form.appendChild(input);
    }

    // 提交表单
    document.body.appendChild(form);
    form.submit();
}
```

#### 方式三：URL拼接跳转

```javascript
function redirectToPay(orderData) {
    const baseUrl = 'https://zpayz.cn/submit.php';
    const params = new URLSearchParams({
        name: orderData.name,
        money: orderData.money,
        type: orderData.type,
        out_trade_no: orderData.out_trade_no,
        notify_url: orderData.notify_url,
        return_url: orderData.return_url,
        pid: '20240114014914',
        param: orderData.param || '',
        sign: calculateSign(orderData),
        sign_type: 'MD5'
    });

    window.location.href = `${baseUrl}?${params.toString()}`;
}
```

### PHP签名示例

```php
<?php
function generateSign($params, $key) {
    // 移除sign和sign_type参数
    unset($params['sign']);
    unset($params['sign_type']);

    // 移除空值参数
    $params = array_filter($params, function($value) {
        return $value !== '' && $value !== null;
    });

    // 按键名排序
    ksort($params);

    // 拼接字符串
    $signStr = '';
    foreach ($params as $k => $v) {
        $signStr .= $k . '=' . $v . '&';
    }
    $signStr = rtrim($signStr, '&');

    // 加上密钥
    $signStr .= $key;

    // MD5加密
    return md5($signStr);
}

// 使用示例
$params = [
    'name' => 'iPhone XS Max',
    'money' => '5999.00',
    'type' => 'alipay',
    'out_trade_no' => '202401140001',
    'notify_url' => 'http://yoursite.com/notify.php',
    'return_url' => 'http://yoursite.com/success.php',
    'pid' => '20240114014914',
    'param' => '金色 256G'
];

$key = 'lgoiv0nWfXqXvHzxcRD9drz4UlYPefCk'; // 商户密钥
$sign = generateSign($params, $key);
$params['sign'] = $sign;
$params['sign_type'] = 'MD5';
?>
```

### 完整示例

```
GET请求示例：
https://zpayz.cn/submit.php?name=iPhone%20XS%20Max&money=5999.00&type=alipay&out_trade_no=202401140001&notify_url=http://yoursite.com/notify.php&return_url=http://yoursite.com/success.php&pid=20240114014914&param=金色%20256G&sign=28f9583617d9caf66834292b6ab1cc89&sign_type=MD5
```

### 返回结果

**成功返回**
- 直接跳转到支付平台收银台页面
- 用户在收银台完成支付操作

**失败返回**
```json
{
    "code": "error",
    "msg": "具体的错误信息"
}
```

### 注意事项

1. **安全性**：推荐使用POST方法，避免敏感信息在URL中暴露
2. **签名验证**：必须正确计算签名，否则支付会失败
3. **URL编码**：GET方式需要对中文参数进行URL编码
4. **回调地址**：notify_url和return_url必须是可访问的完整URL
5. **订单号唯一性**：out_trade_no在商户系统中必须唯一
6. **金额格式**：money参数最多保留两位小数

### 完整集成示例

以下是一个完整的页面跳转支付集成示例：

**前端支付页面 (pay.html)**
```html
<!DOCTYPE html>
<html>
<head>
    <title>支付页面</title>
    <meta charset="UTF-8">
    <style>
        .payment-container { max-width: 400px; margin: 50px auto; padding: 20px; }
        .pay-btn { width: 100%; padding: 15px; margin: 10px 0; border: none; border-radius: 5px; cursor: pointer; }
        .alipay { background: #1677ff; color: white; }
        .wxpay { background: #07c160; color: white; }
    </style>
</head>
<body>
    <div class="payment-container">
        <h2>订单支付</h2>
        <div class="order-info">
            <p>商品：iPhone XS Max</p>
            <p>金额：¥5999.00</p>
            <p>订单：202401140001</p>
        </div>

        <button onclick="pay('alipay')" class="pay-btn alipay">支付宝支付</button>
        <button onclick="pay('wxpay')" class="pay-btn wxpay">微信支付</button>
    </div>

    <script>
    function pay(payType) {
        const orderData = {
            name: 'iPhone XS Max',
            money: '5999.00',
            type: payType,
            out_trade_no: '202401140001',
            notify_url: 'http://yoursite.com/notify.php',
            return_url: 'http://yoursite.com/success.php',
            pid: '20240114014914',
            param: '金色 256G'
        };

        // 发送到后端生成签名
        fetch('/generate_payment.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(orderData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 创建表单并自动提交
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = 'https://zpayz.cn/submit.php';

                for (let key in data.params) {
                    const input = document.createElement('input');
                    input.type = 'hidden';
                    input.name = key;
                    input.value = data.params[key];
                    form.appendChild(input);
                }

                document.body.appendChild(form);
                form.submit();
            } else {
                alert('支付失败：' + data.message);
            }
        });
    }
    </script>
</body>
</html>
```

**后端签名生成 (generate_payment.php)**
```php
<?php
header('Content-Type: application/json');

define('PID', '20240114014914');
define('PKEY', 'lgoiv0nWfXqXvHzxcRD9drz4UlYPefCk');

function generateSign($params, $key) {
    unset($params['sign'], $params['sign_type']);
    $params = array_filter($params, fn($v) => $v !== '' && $v !== null);
    ksort($params);

    $signStr = '';
    foreach ($params as $k => $v) {
        $signStr .= $k . '=' . $v . '&';
    }
    return md5(rtrim($signStr, '&') . $key);
}

try {
    $input = json_decode(file_get_contents('php://input'), true);

    $params = [
        'name' => $input['name'],
        'money' => $input['money'],
        'type' => $input['type'],
        'out_trade_no' => $input['out_trade_no'],
        'notify_url' => $input['notify_url'],
        'return_url' => $input['return_url'],
        'pid' => PID,
        'param' => $input['param'] ?? '',
        'sign_type' => 'MD5'
    ];

    $params['sign'] = generateSign($params, PKEY);

    echo json_encode(['success' => true, 'params' => $params]);
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
?>
```

**异步通知处理 (notify.php)**
```php
<?php
function verifySign($params, $sign, $key) {
    unset($params['sign'], $params['sign_type']);
    ksort($params);
    $signStr = '';
    foreach ($params as $k => $v) {
        if ($v !== '') $signStr .= $k . '=' . $v . '&';
    }
    return md5(rtrim($signStr, '&') . $key) === $sign;
}

$isValid = verifySign($_POST, $_POST['sign'], 'lgoiv0nWfXqXvHzxcRD9drz4UlYPefCk');

if ($isValid && $_POST['trade_status'] === 'TRADE_SUCCESS') {
    // 处理支付成功逻辑
    // updateOrderStatus($_POST['out_trade_no'], 'paid');
    echo 'success';
} else {
    echo 'fail';
}
?>
```

---

## API接口支付

### 接口说明
API接口支付适用于后台调用，获取支付链接或二维码，适合需要在自己页面展示支付信息的场景。

**请求URL**
```
https://zpayz.cn/mapi.php
```

**请求方法**
- POST（Content-Type: multipart/form-data）

### 请求参数

| 字段名 | 变量名 | 必填 | 类型 | 示例值 | 描述 |
|--------|--------|------|------|--------|------|
| 商户ID | pid | 是 | String | 20240114014914 | 商户唯一标识 |
| 支付渠道ID | cid | 否 | String | 1234 | 指定支付渠道，不填随机选择 |
| 支付方式 | type | 是 | String | alipay | alipay(支付宝) / wxpay(微信) |
| 商户订单号 | out_trade_no | 是 | String | 20160806151343349 | 商户系统唯一订单号 |
| 异步通知地址 | notify_url | 是 | String | http://yoursite.com/notify.php | 服务器异步通知地址 |
| 商品名称 | name | 是 | String | VIP会员 | 商品名称，超过127字节自动截取 |
| 商品金额 | money | 是 | String | 1.00 | 单位：元，最大2位小数 |
| 用户IP地址 | clientip | 是 | String | ************* | 用户发起支付的IP地址 |
| 设备类型 | device | 否 | String | pc | pc/mobile/app，默认pc |
| 业务扩展参数 | param | 否 | String | 金色256G | 支付后原样返回 |
| 签名字符串 | sign | 是 | String | 202cb962ac59075b964b07152d234b70 | MD5签名 |
| 签名类型 | sign_type | 是 | String | MD5 | 固定值：MD5 |
### 返回结果

**成功返回**

| 字段名 | 变量名 | 类型 | 示例值 | 描述 |
|--------|--------|------|--------|------|
| 返回状态码 | code | Int | 1 | 1为成功，其它值为失败 |
| 返回信息 | msg | String | 创建订单成功 | 失败时返回错误原因 |
| 订单号 | trade_no | String | 20160806151343349 | 支付平台订单号 |
| 内部订单号 | O_id | String | 123456 | 支付平台内部订单号 |
| 支付跳转URL | payurl | String | https://xxx.cn/pay/wxpay/202010903/ | 直接跳转支付的URL |
| 二维码链接 | qrcode | String | https://xxx.cn/pay/wxpay/202010903/ | 生成二维码的链接 |
| 二维码图片 | img | String | https://z-pay.cn/qrcode/123.jpg | 二维码图片地址 |

**失败返回**
```json
{
    "code": "error",
    "msg": "具体的错误信息"
}
```

### API调用示例

**PHP示例**
```php
<?php
function createPayment($orderData) {
    $params = [
        'pid' => '20240114014914',
        'type' => $orderData['type'],
        'out_trade_no' => $orderData['out_trade_no'],
        'notify_url' => $orderData['notify_url'],
        'name' => $orderData['name'],
        'money' => $orderData['money'],
        'clientip' => $_SERVER['REMOTE_ADDR'],
        'device' => 'pc',
        'param' => $orderData['param'] ?? '',
        'sign_type' => 'MD5'
    ];

    // 生成签名
    $params['sign'] = generateSign($params, 'lgoiv0nWfXqXvHzxcRD9drz4UlYPefCk');

    // 发送请求
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://zpayz.cn/mapi.php');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $params);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

    $response = curl_exec($ch);
    curl_close($ch);

    return json_decode($response, true);
}

// 使用示例
$result = createPayment([
    'type' => 'alipay',
    'out_trade_no' => '202401140001',
    'notify_url' => 'http://yoursite.com/notify.php',
    'name' => 'iPhone XS Max',
    'money' => '5999.00',
    'param' => '金色 256G'
]);

if ($result['code'] == 1) {
    // 支付创建成功
    if (isset($result['qrcode'])) {
        echo "二维码支付：" . $result['qrcode'];
    } elseif (isset($result['payurl'])) {
        echo "跳转支付：" . $result['payurl'];
    }
} else {
    echo "支付创建失败：" . $result['msg'];
}
?>
```

## 查询单个订单

### 接口说明
用于查询订单的支付状态和详细信息。

**请求URL**
```
https://zpayz.cn/api.php?act=order&pid={商户ID}&key={商户密钥}&out_trade_no={商户订单号}
```

**请求方法**
- GET

### 请求参数

| 参数 | 名称 | 类型 | 必填 | 描述 | 示例 |
|------|------|------|------|------|------|
| act | 操作类型 | String | 是 | 固定值：order | order |
| pid | 商户ID | String | 是 | 商户唯一标识 | 20240114014914 |
| key | 商户密钥 | String | 是 | 商户密钥 | lgoiv0nWfXqXvHzxcRD9drz4UlYPefCk |
| trade_no | 系统订单号 | String | 二选一 | 支付平台订单号 | 20160806151343312 |
| out_trade_no | 商户订单号 | String | 二选一 | 商户系统订单号 | 20160806151343349 |

**注意**：trade_no 和 out_trade_no 必须提供其中一个
### 返回结果

| 字段名 | 变量名 | 类型 | 示例值 | 描述 |
|--------|--------|------|--------|------|
| 返回状态码 | code | Int | 1 | 1为成功，其它值为失败 |
| 返回信息 | msg | String | 查询订单号成功！ | 查询结果说明 |
| 支付平台订单号 | trade_no | String | 2016080622555342651 | 支付平台生成的订单号 |
| 商户订单号 | out_trade_no | String | 20160806151343349 | 商户系统内部订单号 |
| 支付方式 | type | String | alipay | alipay(支付宝) / wxpay(微信) |
| 商户ID | pid | String | 20240114014914 | 发起支付的商户ID |
| 创建时间 | addtime | String | 2016-08-06 22:55:52 | 订单创建时间 |
| 完成时间 | endtime | String | 2016-08-06 22:55:52 | 交易完成时间 |
| 商品名称 | name | String | VIP会员 | 商品名称 |
| 商品金额 | money | String | 1.00 | 订单金额 |
| 支付状态 | status | Int | 1 | 1=已支付，0=未支付 |
| 扩展参数 | param | String | 金色256G | 业务扩展参数 |
| 支付者账号 | buyer | String | <EMAIL> | 支付者账号信息 |

### 查询示例

```php
<?php
// 查询订单状态
function queryOrder($outTradeNo) {
    $url = "https://zpayz.cn/api.php";
    $params = [
        'act' => 'order',
        'pid' => '20240114014914',
        'key' => 'lgoiv0nWfXqXvHzxcRD9drz4UlYPefCk',
        'out_trade_no' => $outTradeNo
    ];

    $queryString = http_build_query($params);
    $response = file_get_contents($url . '?' . $queryString);

    return json_decode($response, true);
}

// 使用示例
$result = queryOrder('202401140001');
if ($result['code'] == 1) {
    if ($result['status'] == 1) {
        echo "订单已支付";
    } else {
        echo "订单未支付";
    }
} else {
    echo "查询失败：" . $result['msg'];
}
?>
```

---

## 提交订单退款

### 接口说明
用于申请订单退款，需要订单已支付且符合退款条件。

**请求URL**
```
https://zpayz.cn/api.php?act=refund
```

**请求方法**
- POST

### 请求参数

| 字段名 | 变量名 | 必填 | 类型 | 示例值 | 描述 |
|--------|--------|------|------|--------|------|
| 商户ID | pid | 是 | String | 20240114014914 | 商户唯一标识 |
| 商户密钥 | key | 是 | String | lgoiv0nWfXqXvHzxcRD9drz4UlYPefCk | 商户密钥 |
| 支付平台订单号 | trade_no | 二选一 | String | 20160806151343349021 | 支付平台订单号 |
| 商户订单号 | out_trade_no | 二选一 | String | 20160806151343349 | 商户系统订单号 |
| 退款金额 | money | 是 | String | 1.50 | 退款金额，多数通道需与原订单金额一致 |

**注意**：trade_no 和 out_trade_no 必须提供其中一个

### 返回结果

| 字段名 | 变量名 | 类型 | 示例值 | 描述 |
|--------|--------|------|--------|------|
| 返回状态码 | code | Int | 1 | 1为成功，其它值为失败 |
| 返回信息 | msg | String | 退款成功 | 退款结果说明 |

### 退款示例

```php
<?php
function refundOrder($outTradeNo, $money) {
    $params = [
        'act' => 'refund',
        'pid' => '20240114014914',
        'key' => 'lgoiv0nWfXqXvHzxcRD9drz4UlYPefCk',
        'out_trade_no' => $outTradeNo,
        'money' => $money
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://zpayz.cn/api.php');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $params);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

    $response = curl_exec($ch);
    curl_close($ch);

    return json_decode($response, true);
}

// 使用示例
$result = refundOrder('202401140001', '5999.00');
if ($result['code'] == 1) {
    echo "退款成功";
} else {
    echo "退款失败：" . $result['msg'];
}
?>
```

---
支付结果通知
请求URL
服务器异步通知（notify_url）、页面跳转通知（return_url）
请求方法
GET
请求参数
参数	名称	类型	描述	范例
pid	商户ID	Int		201901151314084206659771
name	商品名称	String	商品名称不超过100字	iphone
money	订单金额	String	最多保留两位小数	5.67
out_trade_no	商户订单号	Num	商户系统内部的订单号	201901191324552185692680
trade_no	易支付订单号	String	易支付订单号	2019011922001418111011411195
param	业务扩展参数	String	会通过notify_url原样返回	金色 256G
trade_status	支付状态	String	只有TRADE_SUCCESS是成功	TRADE_SUCCESS
type	支付方式	String	包括支付宝、微信	alipay
sign	签名（参考本页签名算法）	String	用于验证接受信息的正确性	ef6e3c5c6ff45018e8c82fd66fb056dc
sign_type	签名类型	String	默认为MD5	MD5
如何验证
请根据签名算法，验证自己生成的签名与参数中传入的签名是否一致，如果一致则说明是由官方向您发送的真实信息
注意事项
1.收到回调信息后请返回“success”，否则程序将判定您的回调地址未正确通知到。

2.同样的通知可能会多次发送给商户系统。商户系统必须能够正确处理重复的通知。

3.推荐的做法是，当收到通知进行处理时，首先检查对应业务数据的状态，判断该通知是否已经处理过，如果没有处理过再进行处理，如果处理过直接返回结果成功。在对业务数据进行状态检查和处理之前，要采用数据锁进行并发控制，以避免函数重入造成的数据混乱。

4.特别提醒：商户系统对于支付结果通知的内容一定要做签名验证,并校验返回的订单金额是否与商户侧的订单金额一致，防止数据泄漏导致出现“假通知”，造成资金损失。

5.对后台通知交互时，如果平台收到商户的应答不是纯字符串success或超过5秒后返回时，平台认为通知失败，平台会通过一定的策略（通知频率为0/15/15/30/180/1800/1800/1800/1800/3600，单位：秒）间接性重新发起通知，尽可能提高通知的成功率，但不保证通知最终能成功。

MD5签名算法
1、将发送或接收到的所有参数按照参数名ASCII码从小到大排序（a-z），sign、sign_type、和空值不参与签名！

2、将排序后的参数拼接成URL键值对的格式，例如 a=b&c=d&e=f，参数值不要进行url编码。

3、再将拼接好的字符串与商户密钥KEY进行MD5加密得出sign签名参数，sign = md5 ( a=b&c=d&e=f + KEY ) （注意：+ 为各语言的拼接符，不是字符！），md5结果为小写。

4、具体签名与发起支付的示例代码可下载SDK查看。