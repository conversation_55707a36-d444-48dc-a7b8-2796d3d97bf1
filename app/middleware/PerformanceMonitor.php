<?php

namespace app\middleware;

use think\Request;
use think\Response;
use app\service\PerformanceMonitorService;
use app\service\DatabasePoolService;

/**
 * 性能监控中间件
 * 自动监控所有API的响应时间和性能指标
 */
class PerformanceMonitor
{
    /**
     * 处理请求
     *
     * @param Request $request
     * @param \Closure $next
     * @return Response
     */
    public function handle(Request $request, \Closure $next): Response
    {
        // 开始性能监控
        $operation = $request->controller() . '::' . $request->action();
        $monitorId = PerformanceMonitorService::start($operation);
        
        // 记录请求信息
        $requestData = [
            'method' => $request->method(),
            'url' => $request->url(),
            'ip' => $request->ip(),
            'user_agent' => $request->header('User-Agent', ''),
            'request_size' => strlen($request->getContent()),
        ];

        try {
            // 执行请求
            $response = $next($request);
            
            // 记录响应信息
            $responseData = [
                'status_code' => $response->getCode(),
                'response_size' => strlen($response->getContent()),
                'content_type' => $response->getHeader('Content-Type'),
            ];
            
            // 获取数据库连接池统计
            $dbStats = DatabasePoolService::getStats();
            
            // 结束性能监控
            PerformanceMonitorService::end($monitorId, array_merge(
                $requestData,
                $responseData,
                ['db_stats' => $dbStats]
            ));
            
            // 添加性能头信息（仅在调试模式下）
            if (config('app.debug')) {
                $response->header([
                    'X-Response-Time' => microtime(true) - $_SERVER['REQUEST_TIME_FLOAT'],
                    'X-Memory-Usage' => memory_get_usage(true),
                    'X-Peak-Memory' => memory_get_peak_usage(true),
                    'X-DB-Connections' => $dbStats['total_connections'] ?? 0,
                ]);
            }
            
            return $response;
            
        } catch (\Exception $e) {
            // 记录异常信息
            PerformanceMonitorService::end($monitorId, array_merge(
                $requestData,
                [
                    'error' => $e->getMessage(),
                    'exception_class' => get_class($e),
                    'status_code' => 500
                ]
            ));
            
            throw $e;
        }
    }
}
