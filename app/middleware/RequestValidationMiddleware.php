<?php

namespace app\middleware;

use app\common\ApiResponse;
use app\exception\BusinessException;
use think\facade\Log;
use think\Request;
use think\Response;

/**
 * 请求验证中间件
 * 统一处理请求参数验证和安全检查
 * 
 * 设计原则：
 * 1. 单一职责原则：专门负责请求验证
 * 2. 开闭原则：易于扩展新的验证规则
 * 3. 依赖倒置原则：依赖抽象的验证接口
 */
class RequestValidationMiddleware
{
    /**
     * 处理请求
     * 
     * @param Request $request
     * @param \Closure $next
     * @return Response
     */
    public function handle(Request $request, \Closure $next): Response
    {
        try {
            // 1. 基础安全检查
            $this->performSecurityChecks($request);
            
            // 2. 请求参数清理
            $this->sanitizeRequest($request);
            
            // 3. 通用参数验证
            $this->validateCommonParams($request);
            
            // 4. 继续处理请求
            return $next($request);
            
        } catch (BusinessException $e) {
            // 业务异常直接抛出，由全局异常处理器处理
            throw $e;
        } catch (\Exception $e) {
            Log::error('请求验证中间件异常: ' . $e->getMessage());
            return ApiResponse::systemError('请求处理失败');
        }
    }

    /**
     * 执行安全检查
     * 
     * @param Request $request
     * @throws BusinessException
     */
    protected function performSecurityChecks(Request $request): void
    {
        // 1. 检查请求方法
        $this->validateRequestMethod($request);
        
        // 2. 检查Content-Type
        $this->validateContentType($request);
        
        // 3. 检查请求大小
        $this->validateRequestSize($request);
        
        // 4. 检查危险字符
        $this->checkDangerousChars($request);
        
        // 5. 检查SQL注入
        $this->checkSqlInjection($request);
        
        // 6. 检查XSS攻击
        $this->checkXssAttack($request);
    }

    /**
     * 验证请求方法
     * 
     * @param Request $request
     * @throws BusinessException
     */
    protected function validateRequestMethod(Request $request): void
    {
        $allowedMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'];
        $method = strtoupper($request->method());
        
        if (!in_array($method, $allowedMethods)) {
            throw BusinessException::validationError('不支持的请求方法: ' . $method);
        }
    }

    /**
     * 验证Content-Type
     * 
     * @param Request $request
     * @throws BusinessException
     */
    protected function validateContentType(Request $request): void
    {
        if ($request->isPost() || $request->isPut() || $request->isPatch()) {
            $contentType = $request->header('Content-Type', '');
            
            $allowedTypes = [
                'application/json',
                'application/x-www-form-urlencoded',
                'multipart/form-data',
                'text/plain'
            ];
            
            $isValid = false;
            foreach ($allowedTypes as $type) {
                if (strpos($contentType, $type) !== false) {
                    $isValid = true;
                    break;
                }
            }
            
            if (!$isValid && !empty($contentType)) {
                throw BusinessException::validationError('不支持的Content-Type: ' . $contentType);
            }
        }
    }

    /**
     * 验证请求大小
     * 
     * @param Request $request
     * @throws BusinessException
     */
    protected function validateRequestSize(Request $request): void
    {
        $maxSize = 10 * 1024 * 1024; // 10MB
        $contentLength = $request->header('Content-Length', 0);
        
        if ($contentLength > $maxSize) {
            throw BusinessException::validationError('请求数据过大');
        }
    }

    /**
     * 检查危险字符
     * 
     * @param Request $request
     * @throws BusinessException
     */
    protected function checkDangerousChars(Request $request): void
    {
        $dangerousPatterns = [
            '/\.\.\//i',  // 目录遍历
            '/\x00/i',    // NULL字节
            '/\x1a/i',    // 控制字符
        ];
        
        $allParams = array_merge($request->get(), $request->post());
        
        foreach ($allParams as $key => $value) {
            if (is_string($value)) {
                foreach ($dangerousPatterns as $pattern) {
                    if (preg_match($pattern, $value)) {
                        Log::warning('检测到危险字符', [
                            'param' => $key,
                            'value' => $value,
                            'ip' => $request->ip()
                        ]);
                        throw BusinessException::validationError('请求包含非法字符');
                    }
                }
            }
        }
    }

    /**
     * 检查SQL注入
     * 
     * @param Request $request
     * @throws BusinessException
     */
    protected function checkSqlInjection(Request $request): void
    {
        $sqlPatterns = [
            '/union\s+select/i',
            '/select\s+.*\s+from/i',
            '/insert\s+into/i',
            '/update\s+.*\s+set/i',
            '/delete\s+from/i',
            '/drop\s+table/i',
            '/exec\s*\(/i',
            '/script\s*:/i',
        ];
        
        $allParams = array_merge($request->get(), $request->post());
        
        foreach ($allParams as $key => $value) {
            if (is_string($value)) {
                foreach ($sqlPatterns as $pattern) {
                    if (preg_match($pattern, $value)) {
                        Log::warning('检测到SQL注入尝试', [
                            'param' => $key,
                            'value' => $value,
                            'ip' => $request->ip()
                        ]);
                        throw BusinessException::validationError('请求包含非法SQL语句');
                    }
                }
            }
        }
    }

    /**
     * 检查XSS攻击
     * 
     * @param Request $request
     * @throws BusinessException
     */
    protected function checkXssAttack(Request $request): void
    {
        $xssPatterns = [
            '/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/i',
            '/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/i',
            '/javascript\s*:/i',
            '/on\w+\s*=/i',
            '/<\s*img[^>]+src\s*=\s*["\']?\s*javascript\s*:/i',
        ];
        
        $allParams = array_merge($request->get(), $request->post());
        
        foreach ($allParams as $key => $value) {
            if (is_string($value)) {
                foreach ($xssPatterns as $pattern) {
                    if (preg_match($pattern, $value)) {
                        Log::warning('检测到XSS攻击尝试', [
                            'param' => $key,
                            'value' => $value,
                            'ip' => $request->ip()
                        ]);
                        throw BusinessException::validationError('请求包含非法脚本');
                    }
                }
            }
        }
    }

    /**
     * 清理请求参数
     * 
     * @param Request $request
     */
    protected function sanitizeRequest(Request $request): void
    {
        // 清理GET参数
        $getParams = $request->get();
        foreach ($getParams as $key => $value) {
            if (is_string($value)) {
                $getParams[$key] = $this->sanitizeString($value);
            }
        }
        
        // 清理POST参数
        $postParams = $request->post();
        foreach ($postParams as $key => $value) {
            if (is_string($value)) {
                $postParams[$key] = $this->sanitizeString($value);
            }
        }
    }

    /**
     * 清理字符串
     * 
     * @param string $value
     * @return string
     */
    protected function sanitizeString(string $value): string
    {
        // 移除控制字符
        $value = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $value);
        
        // 去除首尾空白
        $value = trim($value);
        
        return $value;
    }

    /**
     * 验证通用参数
     * 
     * @param Request $request
     * @throws BusinessException
     */
    protected function validateCommonParams(Request $request): void
    {
        // 验证分页参数
        $this->validatePaginationParams($request);
        
        // 验证排序参数
        $this->validateSortParams($request);
        
        // 验证日期参数
        $this->validateDateParams($request);
    }

    /**
     * 验证分页参数
     * 
     * @param Request $request
     * @throws BusinessException
     */
    protected function validatePaginationParams(Request $request): void
    {
        $page = $request->param('page');
        $limit = $request->param('limit');
        
        if ($page !== null) {
            if (!is_numeric($page) || $page < 1) {
                throw BusinessException::validationError('页码必须是大于0的整数');
            }
            
            if ($page > 10000) {
                throw BusinessException::validationError('页码不能超过10000');
            }
        }
        
        if ($limit !== null) {
            if (!is_numeric($limit) || $limit < 1) {
                throw BusinessException::validationError('每页数量必须是大于0的整数');
            }
            
            if ($limit > 1000) {
                throw BusinessException::validationError('每页数量不能超过1000');
            }
        }
    }

    /**
     * 验证排序参数
     * 
     * @param Request $request
     * @throws BusinessException
     */
    protected function validateSortParams(Request $request): void
    {
        $sort = $request->param('sort');
        $order = $request->param('order');
        
        if ($sort !== null) {
            // 只允许字母、数字、下划线
            if (!preg_match('/^[a-zA-Z0-9_]+$/', $sort)) {
                throw BusinessException::validationError('排序字段格式不正确');
            }
        }
        
        if ($order !== null) {
            $allowedOrders = ['asc', 'desc', 'ASC', 'DESC'];
            if (!in_array($order, $allowedOrders)) {
                throw BusinessException::validationError('排序方向只能是asc或desc');
            }
        }
    }

    /**
     * 验证日期参数
     * 
     * @param Request $request
     * @throws BusinessException
     */
    protected function validateDateParams(Request $request): void
    {
        $dateParams = ['start_date', 'end_date', 'date', 'created_at', 'updated_at'];
        
        foreach ($dateParams as $param) {
            $value = $request->param($param);
            if ($value !== null && !empty($value)) {
                if (!$this->isValidDate($value)) {
                    throw BusinessException::validationError("日期参数 {$param} 格式不正确");
                }
            }
        }
    }

    /**
     * 验证日期格式
     * 
     * @param string $date
     * @return bool
     */
    protected function isValidDate(string $date): bool
    {
        $formats = [
            'Y-m-d',
            'Y-m-d H:i:s',
            'Y/m/d',
            'Y/m/d H:i:s'
        ];
        
        foreach ($formats as $format) {
            $d = \DateTime::createFromFormat($format, $date);
            if ($d && $d->format($format) === $date) {
                return true;
            }
        }
        
        return false;
    }
}
