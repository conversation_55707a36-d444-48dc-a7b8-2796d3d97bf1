<?php

namespace app\middleware;

use think\facade\Session;
use think\facade\Db;
use think\Response;
use Closure;

/**
 * 认证中间件
 * 统一管理登录状态和昵称验证逻辑
 * 遵循单一职责原则：专门负责用户认证相关的验证
 */
class AuthMiddleware
{
    /**
     * 处理请求
     * 
     * @param \think\Request $request
     * @param Closure $next
     * @param array $options 中间件选项
     * @return Response
     */
    public function handle($request, Closure $next, array $options = [])
    {
        // 获取中间件配置选项
        $requireLogin = $options['login'] ?? true;  // 是否需要登录验证
        $requireNickname = $options['nickname'] ?? true;  // 是否需要昵称验证
        $responseType = $options['response'] ?? 'redirect';  // 响应类型：redirect 或 json
        
        // 1. 登录状态检查
        if ($requireLogin && !$this->isLoggedIn()) {
            return $this->handleAuthFailure('login', $responseType);
        }
        
        // 2. 昵称验证检查（管理员用户跳过昵称检查）
        if ($requireNickname && $requireLogin && !$this->hasNickname() && !$this->isAdmin()) {
            return $this->handleAuthFailure('nickname', $responseType);
        }
        
        // 验证通过，继续执行后续逻辑
        return $next($request);
    }
    
    /**
     * 检查用户是否已登录
     * 
     * @return bool
     */
    private function isLoggedIn(): bool
    {
        // 检查ThinkPHP Session
        if (Session::has('user_id')) {
            return true;
        }

        // 如果ThinkPHP Session为空，检查原生PHP session
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        return isset($_SESSION['user_id']);
    }
    
    /**
     * 检查用户是否已设置昵称
     *
     * @return bool
     */
    private function hasNickname(): bool
    {
        if (!$this->isLoggedIn()) {
            return false;
        }

        $userId = Session::get('user_id');
        $nicknameData = Db::table('user_nickname')
            ->where('user_id', $userId)
            ->find();

        return !empty($nicknameData);
    }

    /**
     * 检查用户是否为管理员
     *
     * @return bool
     */
    private function isAdmin(): bool
    {
        if (!$this->isLoggedIn()) {
            return false;
        }

        // 使用AuthHelper的管理员检查逻辑
        return \app\common\AuthHelper::isAdmin();
    }
    
    /**
     * 处理认证失败的情况
     * 
     * @param string $type 失败类型：login 或 nickname
     * @param string $responseType 响应类型：redirect 或 json
     * @return Response
     */
    private function handleAuthFailure(string $type, string $responseType): Response
    {
        if ($responseType === 'json') {
            // API接口返回JSON响应
            $code = ($type === 'login') ? 401 : 402;
            $message = ($type === 'login') ? '请先登录' : '请先设置昵称';
            
            return json([
                'code' => $code,
                'message' => $message,
                'data' => null
            ]);
        } else {
            // 页面请求返回重定向
            $redirectUrl = ($type === 'login') ? '/login' : '/auth/setNickname';
            return redirect($redirectUrl);
        }
    }
    
    /**
     * 获取当前登录用户ID
     * 
     * @return string|null
     */
    public static function getCurrentUserId(): ?string
    {
        return Session::get('user_id');
    }
    
    /**
     * 获取当前登录用户信息
     * 
     * @return array
     */
    public static function getCurrentUserInfo(): array
    {
        return Session::get('user_info', []);
    }
    
    /**
     * 获取当前用户昵称
     * 
     * @return string|null
     */
    public static function getCurrentUserNickname(): ?string
    {
        $userId = self::getCurrentUserId();
        if (!$userId) {
            return null;
        }
        
        $nicknameData = Db::table('user_nickname')
            ->where('user_id', $userId)
            ->find();
            
        return $nicknameData['nickname'] ?? null;
    }
}