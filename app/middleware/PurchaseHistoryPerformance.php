<?php

namespace app\middleware;

use think\facade\Log;
use think\facade\Cache;

/**
 * 购买历史性能监控中间件
 */
class PurchaseHistoryPerformance
{
    /**
     * 处理请求
     *
     * @param \think\Request $request
     * @param \Closure       $next
     */
    public function handle($request, \Closure $next)
    {
        $startTime = microtime(true);
        $startMemory = memory_get_usage();
        
        // 执行请求
        $response = $next($request);
        
        // 计算性能指标
        $endTime = microtime(true);
        $endMemory = memory_get_usage();
        
        $executionTime = round(($endTime - $startTime) * 1000, 2); // 毫秒
        $memoryUsage = round(($endMemory - $startMemory) / 1024 / 1024, 2); // MB
        
        // 记录性能日志
        $this->logPerformance($request, $executionTime, $memoryUsage);
        
        // 如果响应时间过长，记录警告
        if ($executionTime > 1000) { // 超过1秒
            Log::warning("购买历史查询响应时间过长", [
                'url' => $request->url(),
                'method' => $request->method(),
                'params' => $request->param(),
                'execution_time' => $executionTime . 'ms',
                'memory_usage' => $memoryUsage . 'MB'
            ]);
        }
        
        // 添加性能头信息（开发环境）
        if (app()->isDebug()) {
            $response->header([
                'X-Execution-Time' => $executionTime . 'ms',
                'X-Memory-Usage' => $memoryUsage . 'MB',
                'X-Timestamp' => date('Y-m-d H:i:s')
            ]);
        }
        
        return $response;
    }
    
    /**
     * 记录性能日志
     *
     * @param \think\Request $request
     * @param float $executionTime
     * @param float $memoryUsage
     */
    private function logPerformance($request, $executionTime, $memoryUsage)
    {
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'url' => $request->url(),
            'method' => $request->method(),
            'user_id' => session('user_id', 0),
            'execution_time' => $executionTime,
            'memory_usage' => $memoryUsage,
            'params' => $request->param()
        ];
        
        // 记录到日志文件
        Log::info("购买历史性能监控", $logData);
        
        // 记录到缓存（用于统计分析）
        $this->recordToCache($logData);
    }
    
    /**
     * 记录性能数据到缓存
     *
     * @param array $logData
     */
    private function recordToCache($logData)
    {
        try {
            $cacheKey = 'purchase_history_performance_' . date('Y-m-d-H');
            $cached = Cache::get($cacheKey, []);
            
            $cached[] = [
                'time' => $logData['timestamp'],
                'execution_time' => $logData['execution_time'],
                'memory_usage' => $logData['memory_usage'],
                'user_id' => $logData['user_id']
            ];
            
            // 只保留最近100条记录
            if (count($cached) > 100) {
                $cached = array_slice($cached, -100);
            }
            
            // 缓存1小时
            Cache::set($cacheKey, $cached, 3600);
            
        } catch (\Exception $e) {
            Log::error("记录性能数据到缓存失败: " . $e->getMessage());
        }
    }
    
    /**
     * 获取性能统计数据
     *
     * @return array
     */
    public static function getPerformanceStats()
    {
        try {
            $currentHour = date('Y-m-d-H');
            $cacheKey = 'purchase_history_performance_' . $currentHour;
            $data = Cache::get($cacheKey, []);
            
            if (empty($data)) {
                return [
                    'total_requests' => 0,
                    'avg_execution_time' => 0,
                    'max_execution_time' => 0,
                    'avg_memory_usage' => 0,
                    'max_memory_usage' => 0
                ];
            }
            
            $executionTimes = array_column($data, 'execution_time');
            $memoryUsages = array_column($data, 'memory_usage');
            
            return [
                'total_requests' => count($data),
                'avg_execution_time' => round(array_sum($executionTimes) / count($executionTimes), 2),
                'max_execution_time' => max($executionTimes),
                'min_execution_time' => min($executionTimes),
                'avg_memory_usage' => round(array_sum($memoryUsages) / count($memoryUsages), 2),
                'max_memory_usage' => max($memoryUsages),
                'min_memory_usage' => min($memoryUsages),
                'hour' => $currentHour
            ];
            
        } catch (\Exception $e) {
            Log::error("获取性能统计数据失败: " . $e->getMessage());
            return [];
        }
    }
}
