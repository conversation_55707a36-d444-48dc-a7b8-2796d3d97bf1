<?php

namespace app\middleware;

use app\common\ApiResponse;
use app\exception\BusinessException;
use app\service\CacheService;
use think\facade\Log;
use think\Request;
use think\Response;

/**
 * API限流中间件
 * 防止恶意请求和系统过载
 * 
 * 设计原则：
 * 1. 单一职责原则：专门负责请求限流
 * 2. 开闭原则：易于扩展不同的限流策略
 * 3. 依赖倒置原则：依赖抽象的缓存接口
 */
class RateLimitMiddleware
{
    /**
     * 默认限流配置
     */
    const DEFAULT_LIMITS = [
        'requests_per_minute' => 60,    // 每分钟请求数
        'requests_per_hour' => 1000,   // 每小时请求数
        'requests_per_day' => 10000,   // 每天请求数
    ];

    /**
     * API特定限流配置
     */
    const API_LIMITS = [
        '/api/purchase/buy' => [
            'requests_per_minute' => 10,
            'requests_per_hour' => 100,
            'requests_per_day' => 500,
        ],
        '/api/currency/modify' => [
            'requests_per_minute' => 5,
            'requests_per_hour' => 50,
            'requests_per_day' => 200,
        ],
        '/api/recharge' => [
            'requests_per_minute' => 20,
            'requests_per_hour' => 200,
            'requests_per_day' => 1000,
        ],
    ];

    /**
     * 处理请求
     * 
     * @param Request $request
     * @param \Closure $next
     * @return Response
     */
    public function handle(Request $request, \Closure $next): Response
    {
        try {
            // 1. 获取客户端标识
            $clientId = $this->getClientId($request);
            
            // 2. 获取限流配置
            $limits = $this->getLimits($request);
            
            // 3. 检查限流
            $this->checkRateLimit($clientId, $request->pathinfo(), $limits);
            
            // 4. 记录请求
            $this->recordRequest($clientId, $request->pathinfo());
            
            // 5. 继续处理请求
            $response = $next($request);
            
            // 6. 添加限流头信息
            $this->addRateLimitHeaders($response, $clientId, $request->pathinfo(), $limits);
            
            return $response;
            
        } catch (BusinessException $e) {
            // 限流异常，记录日志
            Log::warning('API限流触发', [
                'client_id' => $this->getClientId($request),
                'path' => $request->pathinfo(),
                'ip' => $request->ip(),
                'user_agent' => $request->header('User-Agent')
            ]);
            throw $e;
        } catch (\Exception $e) {
            Log::error('限流中间件异常: ' . $e->getMessage());
            // 限流失败时不阻止请求，但记录错误
            return $next($request);
        }
    }

    /**
     * 获取客户端标识
     * 
     * @param Request $request
     * @return string
     */
    protected function getClientId(Request $request): string
    {
        // 优先使用用户ID
        $userId = session('user_id');
        if ($userId) {
            return 'user:' . $userId;
        }
        
        // 其次使用IP地址
        $ip = $request->ip();
        
        // 考虑代理情况
        $realIp = $request->header('X-Real-IP') ?: 
                  $request->header('X-Forwarded-For') ?: 
                  $ip;
        
        return 'ip:' . $realIp;
    }

    /**
     * 获取限流配置
     * 
     * @param Request $request
     * @return array
     */
    protected function getLimits(Request $request): array
    {
        $path = $request->pathinfo();
        
        // 检查是否有特定API的限流配置
        foreach (self::API_LIMITS as $apiPath => $limits) {
            if (strpos($path, $apiPath) !== false) {
                return array_merge(self::DEFAULT_LIMITS, $limits);
            }
        }
        
        return self::DEFAULT_LIMITS;
    }

    /**
     * 检查限流
     * 
     * @param string $clientId
     * @param string $path
     * @param array $limits
     * @throws BusinessException
     */
    protected function checkRateLimit(string $clientId, string $path, array $limits): void
    {
        $now = time();
        
        // 检查每分钟限制
        if (isset($limits['requests_per_minute'])) {
            $this->checkPeriodLimit($clientId, $path, 'minute', 60, $limits['requests_per_minute'], $now);
        }
        
        // 检查每小时限制
        if (isset($limits['requests_per_hour'])) {
            $this->checkPeriodLimit($clientId, $path, 'hour', 3600, $limits['requests_per_hour'], $now);
        }
        
        // 检查每天限制
        if (isset($limits['requests_per_day'])) {
            $this->checkPeriodLimit($clientId, $path, 'day', 86400, $limits['requests_per_day'], $now);
        }
    }

    /**
     * 检查特定时间段的限制
     * 
     * @param string $clientId
     * @param string $path
     * @param string $period
     * @param int $seconds
     * @param int $limit
     * @param int $now
     * @throws BusinessException
     */
    protected function checkPeriodLimit(string $clientId, string $path, string $period, int $seconds, int $limit, int $now): void
    {
        $key = $this->getRateLimitKey($clientId, $path, $period, $now, $seconds);
        $current = CacheService::get($key, 0);
        
        if ($current >= $limit) {
            $resetTime = $this->getResetTime($now, $seconds);
            throw BusinessException::rateLimitExceeded(
                "请求过于频繁，请稍后重试",
                [
                    'period' => $period,
                    'limit' => $limit,
                    'current' => $current,
                    'reset_time' => $resetTime,
                    'reset_in' => $resetTime - $now
                ]
            );
        }
    }

    /**
     * 记录请求
     * 
     * @param string $clientId
     * @param string $path
     */
    protected function recordRequest(string $clientId, string $path): void
    {
        $now = time();
        
        // 记录每分钟请求数
        $this->incrementCounter($clientId, $path, 'minute', 60, $now);
        
        // 记录每小时请求数
        $this->incrementCounter($clientId, $path, 'hour', 3600, $now);
        
        // 记录每天请求数
        $this->incrementCounter($clientId, $path, 'day', 86400, $now);
    }

    /**
     * 增加计数器
     * 
     * @param string $clientId
     * @param string $path
     * @param string $period
     * @param int $seconds
     * @param int $now
     */
    protected function incrementCounter(string $clientId, string $path, string $period, int $seconds, int $now): void
    {
        $key = $this->getRateLimitKey($clientId, $path, $period, $now, $seconds);
        $ttl = $this->getResetTime($now, $seconds) - $now;
        
        $current = CacheService::get($key, 0);
        CacheService::set($key, $current + 1, $ttl);
    }

    /**
     * 获取限流缓存键
     * 
     * @param string $clientId
     * @param string $path
     * @param string $period
     * @param int $now
     * @param int $seconds
     * @return string
     */
    protected function getRateLimitKey(string $clientId, string $path, string $period, int $now, int $seconds): string
    {
        $window = floor($now / $seconds);
        $pathHash = md5($path);
        return "rate_limit:{$clientId}:{$pathHash}:{$period}:{$window}";
    }

    /**
     * 获取重置时间
     * 
     * @param int $now
     * @param int $seconds
     * @return int
     */
    protected function getResetTime(int $now, int $seconds): int
    {
        return (floor($now / $seconds) + 1) * $seconds;
    }

    /**
     * 添加限流头信息
     * 
     * @param Response $response
     * @param string $clientId
     * @param string $path
     * @param array $limits
     */
    protected function addRateLimitHeaders(Response $response, string $clientId, string $path, array $limits): void
    {
        $now = time();
        
        // 添加每分钟限制信息
        if (isset($limits['requests_per_minute'])) {
            $this->addPeriodHeaders($response, $clientId, $path, 'minute', 60, $limits['requests_per_minute'], $now);
        }
        
        // 添加每小时限制信息
        if (isset($limits['requests_per_hour'])) {
            $this->addPeriodHeaders($response, $clientId, $path, 'hour', 3600, $limits['requests_per_hour'], $now);
        }
    }

    /**
     * 添加特定时间段的头信息
     * 
     * @param Response $response
     * @param string $clientId
     * @param string $path
     * @param string $period
     * @param int $seconds
     * @param int $limit
     * @param int $now
     */
    protected function addPeriodHeaders(Response $response, string $clientId, string $path, string $period, int $seconds, int $limit, int $now): void
    {
        $key = $this->getRateLimitKey($clientId, $path, $period, $now, $seconds);
        $current = CacheService::get($key, 0);
        $remaining = max(0, $limit - $current);
        $resetTime = $this->getResetTime($now, $seconds);
        
        $response->header([
            "X-RateLimit-Limit-{$period}" => $limit,
            "X-RateLimit-Remaining-{$period}" => $remaining,
            "X-RateLimit-Reset-{$period}" => $resetTime,
        ]);
    }

    /**
     * 获取客户端当前限流状态
     * 
     * @param string $clientId
     * @param string $path
     * @return array
     */
    public function getRateLimitStatus(string $clientId, string $path): array
    {
        $now = time();
        $limits = self::DEFAULT_LIMITS;
        
        // 检查是否有特定API的限流配置
        foreach (self::API_LIMITS as $apiPath => $apiLimits) {
            if (strpos($path, $apiPath) !== false) {
                $limits = array_merge($limits, $apiLimits);
                break;
            }
        }
        
        $status = [];
        
        foreach ($limits as $period => $limit) {
            $seconds = $this->getPeriodSeconds($period);
            $key = $this->getRateLimitKey($clientId, $path, $period, $now, $seconds);
            $current = CacheService::get($key, 0);
            $remaining = max(0, $limit - $current);
            $resetTime = $this->getResetTime($now, $seconds);
            
            $status[$period] = [
                'limit' => $limit,
                'current' => $current,
                'remaining' => $remaining,
                'reset_time' => $resetTime,
                'reset_in' => $resetTime - $now
            ];
        }
        
        return $status;
    }

    /**
     * 获取时间段对应的秒数
     * 
     * @param string $period
     * @return int
     */
    protected function getPeriodSeconds(string $period): int
    {
        switch ($period) {
            case 'requests_per_minute':
                return 60;
            case 'requests_per_hour':
                return 3600;
            case 'requests_per_day':
                return 86400;
            default:
                return 3600;
        }
    }
}
