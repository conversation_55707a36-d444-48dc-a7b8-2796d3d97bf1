<?php
// 商品图标获取API
// 用途：根据商品ID获取对应的图标路径

// 加载环境变量
$envFile = __DIR__ . '/../../.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $key = trim($key);
            $value = trim($value);
            if (!array_key_exists($key, $_ENV)) {
                $_ENV[$key] = $value;
            }
        }
    }
}

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 数据库配置 - 从环境变量读取
$host = $_ENV['DB_HOST'] ?? '';
$dbname = $_ENV['DB_NAME'] ?? '';
$username = $_ENV['DB_USER'] ?? '';
$password = $_ENV['DB_PASS'] ?? '';

/**
 * 根据商品ID获取图标路径
 * @param int $itemId 商品ID
 * @param PDO $pdo 数据库连接
 * @return string 图标完整URL
 */
function getItemIcon($itemId, $pdo) {
    // 获取静态资源基础URL
    $protocol = $_ENV['STATIC_SERVER_PROTOCOL'] ?? $_ENV['API_SERVER_PROTOCOL'] ?? 'http';
    $host = $_ENV['STATIC_SERVER_HOST'] ?? $_ENV['API_SERVER_HOST'] ?? '127.0.0.1';
    $port = $_ENV['STATIC_SERVER_PORT'] ?? $_ENV['API_SERVER_PORT'] ?? '80';
    $basePath = $_ENV['STATIC_BASE_PATH'] ?? '/static';
    
    $portStr = ($port != '80') ? ':' . $port : '';
    $baseUrl = $protocol . '://' . $host . $portStr . $basePath;
    
    $defaultIcon = $baseUrl . '/img/default.png';
    
    if (empty($itemId)) {
        return $defaultIcon;
    }
    
    try {
        $stmt = $pdo->prepare("SELECT Icon FROM iteminfo WHERE ItemID = ?");
        $stmt->execute([$itemId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result && !empty($result['Icon'])) {
            $iconId = $result['Icon'];
            
            // 图标文件路径基础目录
            $imgDir = __DIR__ . '/../../public/static/img/';
            
            // 尝试多种文件名格式，按优先级查找
            $possibleFiles = [
                $iconId . '.png',        // 直接数字格式: 1.png
                'x' . $iconId . '.png',  // x前缀格式: x1.png
            ];
            
            foreach ($possibleFiles as $filename) {
                if (file_exists($imgDir . $filename)) {
                    return $baseUrl . '/img/' . $filename;
                }
            }
        }
        
        return $defaultIcon;
    } catch (PDOException $e) {
        return $defaultIcon;
    }
}

/**
 * 批量获取商品图标
 * @param array $itemIds 商品ID数组
 * @param PDO $pdo 数据库连接
 * @return array 商品ID和图标完整URL的映射数组
 */
function getItemIcons($itemIds, $pdo) {
    $icons = [];
    
    // 获取静态资源基础URL
    $protocol = $_ENV['STATIC_SERVER_PROTOCOL'] ?? $_ENV['API_SERVER_PROTOCOL'] ?? 'http';
    $host = $_ENV['STATIC_SERVER_HOST'] ?? $_ENV['API_SERVER_HOST'] ?? '127.0.0.1';
    $port = $_ENV['STATIC_SERVER_PORT'] ?? $_ENV['API_SERVER_PORT'] ?? '80';
    $basePath = $_ENV['STATIC_BASE_PATH'] ?? '/static';
    
    $portStr = ($port != '80') ? ':' . $port : '';
    $baseUrl = $protocol . '://' . $host . $portStr . $basePath;
    
    $defaultIcon = $baseUrl . '/img/default.png';
    
    if (empty($itemIds)) {
        return $icons;
    }
    
    try {
        // 构建IN查询
        $placeholders = str_repeat('?,', count($itemIds) - 1) . '?';
        $stmt = $pdo->prepare("SELECT ItemID, Icon FROM iteminfo WHERE ItemID IN ($placeholders)");
        $stmt->execute($itemIds);
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // 创建ID到图标的映射
        $iconMap = [];
        $imgDir = __DIR__ . '/../../public/static/img/';
        
        foreach ($results as $row) {
            if (!empty($row['Icon'])) {
                $iconId = $row['Icon'];
                $itemId = $row['ItemID'];
                
                // 尝试多种文件名格式，按优先级查找
                $possibleFiles = [
                    $iconId . '.png',        // 直接数字格式: 1.png
                    'x' . $iconId . '.png',  // x前缀格式: x1.png
                ];
                
                foreach ($possibleFiles as $filename) {
                    if (file_exists($imgDir . $filename)) {
                        $iconMap[$itemId] = $baseUrl . '/img/' . $filename;
                        break; // 找到文件就停止查找
                    }
                }
            }
        }
        
        // 为所有请求的ID分配图标（没找到的使用默认图标）
        foreach ($itemIds as $itemId) {
            $icons[$itemId] = $iconMap[$itemId] ?? $defaultIcon;
        }
        
        return $icons;
    } catch (PDOException $e) {
        // 出错时为所有ID返回默认图标
        foreach ($itemIds as $itemId) {
            $icons[$itemId] = $defaultIcon;
        }
        return $icons;
    }
}

try {
    // 连接数据库
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // 获取请求参数
    $method = $_SERVER['REQUEST_METHOD'];
    
    if ($method === 'GET') {
        // 单个商品图标获取
        $itemId = $_GET['item_id'] ?? null;
        
        if ($itemId === null) {
            echo json_encode([
                'code' => 400,
                'message' => '缺少item_id参数'
            ]);
            exit;
        }
        
        $icon = getItemIcon($itemId, $pdo);
        
        echo json_encode([
            'code' => 200,
            'data' => [
                'item_id' => $itemId,
                'icon' => $icon
            ]
        ]);
        
    } elseif ($method === 'POST') {
        // 批量商品图标获取
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!isset($input['item_ids']) || !is_array($input['item_ids'])) {
            echo json_encode([
                'code' => 400,
                'message' => '缺少item_ids参数或格式错误'
            ]);
            exit;
        }
        
        $itemIds = array_filter($input['item_ids'], function($id) {
            return !empty($id) && is_numeric($id);
        });
        
        if (empty($itemIds)) {
            echo json_encode([
                'code' => 400,
                'message' => '没有有效的商品ID'
            ]);
            exit;
        }
        
        $icons = getItemIcons($itemIds, $pdo);
        
        echo json_encode([
            'code' => 200,
            'data' => $icons
        ]);
        
    } else {
        echo json_encode([
            'code' => 405,
            'message' => '不支持的请求方法'
        ]);
    }
    
} catch (PDOException $e) {
    echo json_encode([
        'code' => 500,
        'message' => '数据库连接失败: ' . $e->getMessage()
    ]);
} catch (Exception $e) {
    echo json_encode([
        'code' => 500,
        'message' => '服务器错误: ' . $e->getMessage()
    ]);
}
?>