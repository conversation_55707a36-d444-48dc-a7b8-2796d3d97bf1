<?php
// 商品信息获取API
// 用途：根据商品ID获取对应的图标、名称、说明等信息

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 加载环境变量
$envFile = __DIR__ . '/../../.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $key = trim($key);
            $value = trim($value);
            if (!array_key_exists($key, $_ENV)) {
                $_ENV[$key] = $value;
            }
        }
    }
}

// 数据库配置 - 从环境变量读取
$host = $_ENV['DB_HOST'] ?? '';
$dbname = $_ENV['DB_NAME'] ?? '';
$username = $_ENV['DB_USER'] ?? '';
$password = $_ENV['DB_PASS'] ?? '';

/**
 * 根据商品ID获取完整信息
 * @param int $itemId 商品ID
 * @param PDO $pdo 数据库连接
 * @param array $fields 需要获取的字段，默认获取所有
 * @return array 商品信息
 */
function getItemInfo($itemId, $pdo, $fields = ['icon', 'name', 'explanation']) {
    $defaultInfo = [
        'icon' => '/static/img/default.png',
        'name' => '未知物品',
        'explanation' => '暂无说明'
    ];
    
    if (empty($itemId)) {
        return array_intersect_key($defaultInfo, array_flip($fields));
    }
    
    try {
        // 构建查询字段
        $selectFields = [];
        if (in_array('icon', $fields)) $selectFields[] = 'Icon';
        if (in_array('name', $fields)) $selectFields[] = 'ItemName';
        if (in_array('explanation', $fields)) $selectFields[] = 'Explanation';
        
        if (empty($selectFields)) {
            return [];
        }
        
        $sql = "SELECT " . implode(', ', $selectFields) . " FROM iteminfo WHERE ItemID = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$itemId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $info = [];
        
        if ($result) {
            if (in_array('icon', $fields)) {
                $info['icon'] = !empty($result['Icon']) ? '/static/img/' . $result['Icon'] . '.png' : $defaultInfo['icon'];
            }
            if (in_array('name', $fields)) {
                $info['name'] = !empty($result['ItemName']) ? $result['ItemName'] : $defaultInfo['name'];
            }
            if (in_array('explanation', $fields)) {
                $info['explanation'] = !empty($result['Explanation']) ? $result['Explanation'] : $defaultInfo['explanation'];
            }
        } else {
            // 没找到记录，返回默认值
            $info = array_intersect_key($defaultInfo, array_flip($fields));
        }
        
        return $info;
    } catch (PDOException $e) {
        return array_intersect_key($defaultInfo, array_flip($fields));
    }
}

/**
 * 批量获取商品信息
 * @param array $itemIds 商品ID数组
 * @param PDO $pdo 数据库连接
 * @param array $fields 需要获取的字段
 * @return array 商品ID和信息的映射数组
 */
function getItemInfos($itemIds, $pdo, $fields = ['icon', 'name', 'explanation']) {
    $infos = [];
    $defaultInfo = [
        'icon' => '/static/img/default.png',
        'name' => '未知物品',
        'explanation' => '暂无说明'
    ];
    
    if (empty($itemIds)) {
        return $infos;
    }
    
    try {
        // 构建查询字段
        $selectFields = ['ItemID'];
        if (in_array('icon', $fields)) $selectFields[] = 'Icon';
        if (in_array('name', $fields)) $selectFields[] = 'ItemName';
        if (in_array('explanation', $fields)) $selectFields[] = 'Explanation';
        
        // 构建IN查询
        $placeholders = str_repeat('?,', count($itemIds) - 1) . '?';
        $sql = "SELECT " . implode(', ', $selectFields) . " FROM iteminfo WHERE ItemID IN ($placeholders)";
        $stmt = $pdo->prepare($sql);
        $stmt->execute($itemIds);
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // 创建ID到信息的映射
        $infoMap = [];
        foreach ($results as $row) {
            $itemId = $row['ItemID'];
            $info = [];
            
            if (in_array('icon', $fields)) {
                $info['icon'] = !empty($row['Icon']) ? '/static/img/' . $row['Icon'] . '.png' : $defaultInfo['icon'];
            }
            if (in_array('name', $fields)) {
                $info['name'] = !empty($row['ItemName']) ? $row['ItemName'] : $defaultInfo['name'];
            }
            if (in_array('explanation', $fields)) {
                $info['explanation'] = !empty($row['Explanation']) ? $row['Explanation'] : $defaultInfo['explanation'];
            }
            
            $infoMap[$itemId] = $info;
        }
        
        // 为所有请求的ID分配信息（没找到的使用默认信息）
        foreach ($itemIds as $itemId) {
            $infos[$itemId] = $infoMap[$itemId] ?? array_intersect_key($defaultInfo, array_flip($fields));
        }
        
        return $infos;
    } catch (PDOException $e) {
        // 出错时为所有ID返回默认信息
        $defaultFields = array_intersect_key($defaultInfo, array_flip($fields));
        foreach ($itemIds as $itemId) {
            $infos[$itemId] = $defaultFields;
        }
        return $infos;
    }
}

/**
 * 解析请求的字段参数
 * @param string $fieldsParam 字段参数字符串
 * @return array 字段数组
 */
function parseFields($fieldsParam) {
    $availableFields = ['icon', 'name', 'explanation'];
    
    if (empty($fieldsParam)) {
        return $availableFields; // 默认返回所有字段
    }
    
    $requestedFields = array_map('trim', explode(',', $fieldsParam));
    $validFields = array_intersect($requestedFields, $availableFields);
    
    return empty($validFields) ? $availableFields : $validFields;
}

try {
    // 连接数据库
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // 获取请求参数
    $method = $_SERVER['REQUEST_METHOD'];
    
    if ($method === 'GET') {
        // 单个商品信息获取
        $itemId = $_GET['item_id'] ?? null;
        $fieldsParam = $_GET['fields'] ?? '';
        
        if ($itemId === null) {
            echo json_encode([
                'code' => 400,
                'message' => '缺少item_id参数'
            ]);
            exit;
        }
        
        $fields = parseFields($fieldsParam);
        $info = getItemInfo($itemId, $pdo, $fields);
        
        echo json_encode([
            'code' => 200,
            'data' => array_merge(['item_id' => $itemId], $info)
        ]);
        
    } elseif ($method === 'POST') {
        // 批量商品信息获取
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!isset($input['item_ids']) || !is_array($input['item_ids'])) {
            echo json_encode([
                'code' => 400,
                'message' => '缺少item_ids参数或格式错误'
            ]);
            exit;
        }
        
        $itemIds = array_filter($input['item_ids'], function($id) {
            return !empty($id) && is_numeric($id);
        });
        
        if (empty($itemIds)) {
            echo json_encode([
                'code' => 400,
                'message' => '没有有效的商品ID'
            ]);
            exit;
        }
        
        $fields = parseFields($input['fields'] ?? '');
        $infos = getItemInfos($itemIds, $pdo, $fields);
        
        echo json_encode([
            'code' => 200,
            'data' => $infos
        ]);
        
    } else {
        echo json_encode([
            'code' => 405,
            'message' => '不支持的请求方法'
        ]);
    }
    
} catch (PDOException $e) {
    echo json_encode([
        'code' => 500,
        'message' => '数据库连接失败: ' . $e->getMessage()
    ]);
} catch (Exception $e) {
    echo json_encode([
        'code' => 500,
        'message' => '服务器错误: ' . $e->getMessage()
    ]);
}
?>