<?php
declare (strict_types = 1);

namespace app;

use think\App;
use think\exception\ValidateException;
use think\Validate;
use think\Response;
use app\common\ApiResponse;

/**
 * 控制器基础类
 */
abstract class BaseController
{
    /**
     * Request实例
     * @var \think\Request
     */
    protected $request;

    /**
     * 应用实例
     * @var \think\App
     */
    protected $app;

    /**
     * 是否批量验证
     * @var bool
     */
    protected $batchValidate = false;

    /**
     * 控制器中间件
     * @var array
     */
    protected $middleware = [];

    /**
     * 构造方法
     * @access public
     * @param  App  $app  应用对象
     */
    public function __construct(App $app)
    {
        $this->app     = $app;
        $this->request = $this->app->request;

        // 控制器初始化
        $this->initialize();
    }

    // 初始化
    protected function initialize()
    {}

    /**
     * 验证数据
     * @access protected
     * @param  array        $data     数据
     * @param  string|array $validate 验证器名或者验证规则数组
     * @param  array        $message  提示信息
     * @param  bool         $batch    是否批量验证
     * @return array|string|true
     * @throws ValidateException
     */
    protected function validate(array $data, $validate, array $message = [], bool $batch = false)
    {
        if (is_array($validate)) {
            $v = new Validate();
            $v->rule($validate);
        } else {
            if (strpos($validate, '.')) {
                // 支持场景
                [$validate, $scene] = explode('.', $validate);
            }
            $class = false !== strpos($validate, '\\') ? $validate : $this->app->parseClass('validate', $validate);
            $v     = new $class();
            if (!empty($scene)) {
                $v->scene($scene);
            }
        }

        $v->message($message);

        // 是否批量验证
        if ($batch || $this->batchValidate) {
            $v->batch(true);
        }

        return $v->failException(true)->check($data);
    }

    /**
     * API响应便捷方法
     */

    /**
     * 成功响应
     *
     * @param mixed $data 响应数据
     * @param string $message 响应消息
     * @return Response
     */
    protected function success($data = [], string $message = '操作成功'): Response
    {
        return ApiResponse::success($data, $message);
    }

    /**
     * 失败响应
     *
     * @param string $message 错误消息
     * @param int $code 业务状态码
     * @param mixed $data 响应数据
     * @return Response
     */
    protected function error(string $message = '操作失败', int $code = 500, $data = null): Response
    {
        return ApiResponse::error($message, $code, $data);
    }

    /**
     * 参数错误响应
     *
     * @param string $message 错误消息
     * @param mixed $data 响应数据
     * @return Response
     */
    protected function paramError(string $message = '参数错误', $data = null): Response
    {
        return ApiResponse::paramError($message, $data);
    }

    /**
     * 未授权响应
     *
     * @param string $message 错误消息
     * @return Response
     */
    protected function unauthorized(string $message = '请先登录'): Response
    {
        return ApiResponse::unauthorized($message);
    }

    /**
     * 权限不足响应
     *
     * @param string $message 错误消息
     * @return Response
     */
    protected function forbidden(string $message = '权限不足'): Response
    {
        return ApiResponse::forbidden($message);
    }

    /**
     * 资源不存在响应
     *
     * @param string $message 错误消息
     * @return Response
     */
    protected function notFound(string $message = '资源不存在'): Response
    {
        return ApiResponse::notFound($message);
    }

    /**
     * 系统错误响应
     *
     * @param string $message 错误消息
     * @param mixed $data 响应数据
     * @return Response
     */
    protected function systemError(string $message = '系统异常，请稍后重试', $data = null): Response
    {
        return ApiResponse::systemError($message, $data);
    }

    /**
     * 分页数据响应
     *
     * @param array $list 数据列表
     * @param int $total 总数
     * @param int $page 当前页
     * @param int $limit 每页数量
     * @param string $message 响应消息
     * @return Response
     */
    protected function paginate(array $list, int $total, int $page, int $limit, string $message = '获取成功'): Response
    {
        return ApiResponse::paginate($list, $total, $page, $limit, $message);
    }

    /**
     * 验证错误响应
     *
     * @param array $errors 验证错误信息
     * @param string $message 主要错误消息
     * @return Response
     */
    protected function validationError(array $errors, string $message = '数据验证失败'): Response
    {
        return ApiResponse::validationError($errors, $message);
    }

}