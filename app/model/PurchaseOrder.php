<?php

namespace app\model;

use think\Model;
use think\model\concern\SoftDelete;

/**
 * 购买订单模型
 * 负责管理购买订单的数据操作
 * 
 * 设计原则：
 * 1. 单一职责原则：专门处理订单数据
 * 2. 开闭原则：易于扩展新的订单状态和类型
 * 3. 里氏替换原则：继承自ThinkPHP的Model基类
 */
class PurchaseOrder extends Model
{
    use SoftDelete;

    /**
     * 数据表名
     */
    protected $name = 'purchase_orders';

    /**
     * 主键
     */
    protected $pk = 'id';

    /**
     * 自动时间戳
     */
    protected $autoWriteTimestamp = true;

    /**
     * 软删除字段
     */
    protected $deleteTime = 'delete_time';

    /**
     * 字段类型转换
     */
    protected $type = [
        'id' => 'integer',
        'user_id' => 'string',
        'item_id' => 'integer', // 真正的物品ID
        'quantity' => 'integer',
        'currency_type' => 'string',
        'unit_price' => 'integer',
        'total_price' => 'integer',
        'status' => 'integer',
        'unique_num' => 'integer',
        'create_time' => 'timestamp',
        'update_time' => 'timestamp',
        'delete_time' => 'timestamp'
    ];

    /**
     * 字段自动完成
     */
    protected $auto = [];

    /**
     * 新增自动完成
     */
    protected $insert = [
        'status' => 1
    ];

    /**
     * 更新自动完成
     */
    protected $update = [];

    /**
     * 订单状态常量
     */
    const STATUS_PENDING = 1;      // 待处理
    const STATUS_PROCESSING = 2;   // 处理中
    const STATUS_COMPLETED = 3;    // 已完成
    const STATUS_FAILED = 4;       // 失败
    const STATUS_CANCELLED = 5;    // 已取消
    const STATUS_REFUNDED = 6;     // 已退款

    /**
     * 货币类型常量
     */
    const CURRENCY_COIN = 'COIN';   // C币
    const CURRENCY_POINT = 'POINT'; // 泡点
    const CURRENCY_SCORE = 'SCORE'; // 积分

    /**
     * 生成订单号
     * 
     * @return string 订单号
     */
    protected function generateOrderNo(): string
    {
        return 'PO' . date('YmdHis') . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
    }

    /**
     * 获取订单状态文本
     * 
     * @param int $status 状态码
     * @return string 状态文本
     */
    public static function getStatusText(int $status): string
    {
        $statusMap = [
            self::STATUS_PENDING => '待处理',
            self::STATUS_PROCESSING => '处理中',
            self::STATUS_COMPLETED => '已完成',
            self::STATUS_FAILED => '失败',
            self::STATUS_CANCELLED => '已取消',
            self::STATUS_REFUNDED => '已退款'
        ];

        return $statusMap[$status] ?? '未知状态';
    }

    /**
     * 获取货币类型文本
     * 
     * @param string|int $currency 货币类型
     * @return string 货币文本
     */
    public static function getCurrencyText($currency): string
    {
        // 数字类型映射（数据库实际存储的值）
        $currencyMap = [
            '1' => '泡点',
            '2' => '积分', 
            '3' => 'C币',
            1 => '泡点',
            2 => '积分',
            3 => 'C币',
            // 兼容旧的常量映射
            self::CURRENCY_COIN => 'C币',
            self::CURRENCY_POINT => '泡点',
            self::CURRENCY_SCORE => '积分'
        ];

        return $currencyMap[$currency] ?? '未知货币';
    }

    /**
     * 创建购买订单
     * 
     * @param array $orderData 订单数据
     * @return PurchaseOrder|false 订单实例或false
     */
    public static function createOrder(array $orderData)
    {
        try {
            $order = new self();
            $order->save($orderData);
            return $order;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 更新订单状态
     * 
     * @param int $orderId 订单ID
     * @param int $status 新状态
     * @param array $extraData 额外数据
     * @return bool 是否成功
     */
    public static function updateOrderStatus(int $orderId, int $status, array $extraData = []): bool
    {
        try {
            $updateData = array_merge(['status' => $status], $extraData);
            return self::where('id', $orderId)->update($updateData) > 0;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 根据用户ID获取订单列表
     * 
     * @param string $userId 用户ID
     * @param int $limit 限制数量
     * @param int $status 状态筛选（可选）
     * @return array 订单列表
     */
    public static function getUserOrders(string $userId, int $limit = 10, ?int $status = null): array
    {
        $query = self::where('user_id', $userId)
            ->order('create_time', 'desc')
            ->limit($limit);

        if ($status !== null) {
            $query->where('status', $status);
        }

        $orders = $query->select()->toArray();

        // 格式化订单数据
        foreach ($orders as &$order) {
            $order['status_text'] = self::getStatusText($order['status']);
            $order['currency_text'] = self::getCurrencyText($order['currency_type']);
            $order['create_time_formatted'] = date('Y-m-d H:i:s', $order['create_time']);
        }

        return $orders;
    }

    /**
     * 检查用户购买限制
     * 
     * @param string $userId 用户ID
     * @param int $itemId 商品ID
     * @param int $restriction 限制类型
     * @return array 检查结果
     */
    public static function checkPurchaseRestriction(string $userId, int $itemId, int $restriction): array
    {
        $query = self::where('user_id', $userId)
            ->where('item_id', $itemId)
            ->where('status', 'in', [self::STATUS_COMPLETED, self::STATUS_PROCESSING]);

        switch ($restriction) {
            case 1: // 账号限购一次
                $count = $query->count();
                break;

            case 2: // 每日一次
                $today = date('Y-m-d');
                $count = $query->whereTime('create_time', 'between', [$today . ' 00:00:00', $today . ' 23:59:59'])->count();
                break;

            case 3: // 每周一次
                $weekStart = date('Y-m-d', strtotime('this week Monday'));
                $weekEnd = date('Y-m-d', strtotime('this week Sunday'));
                $count = $query->whereTime('create_time', 'between', [$weekStart . ' 00:00:00', $weekEnd . ' 23:59:59'])->count();
                break;

            case 4: // 每月一次
                $monthStart = date('Y-m-01');
                $monthEnd = date('Y-m-t');
                $count = $query->whereTime('create_time', 'between', [$monthStart . ' 00:00:00', $monthEnd . ' 23:59:59'])->count();
                break;

            default:
                $count = 0;
                break;
        }

        return [
            'can_purchase' => $count == 0,
            'purchase_count' => $count,
            'restriction_type' => $restriction
        ];
    }

    /**
     * 获取订单统计信息
     * 
     * @param string $userId 用户ID
     * @param string|null $startDate 开始日期
     * @param string|null $endDate 结束日期
     * @return array 统计信息
     */
    public static function getOrderStatistics(string $userId, ?string $startDate = null, ?string $endDate = null): array
    {
        // 生成缓存键
        $cacheKey = 'order_statistics:' . $userId . ':' . md5(($startDate ?: '') . ($endDate ?: ''));
        
        // 尝试从缓存获取
        $statistics = \app\service\CacheService::get($cacheKey);
        if ($statistics !== null) {
            return $statistics;
        }

        $query = self::where('user_id', $userId);

        if ($startDate && $endDate) {
            $query->whereTime('create_time', 'between', [$startDate, $endDate]);
        }

        // 基础统计
        $totalOrders = (clone $query)->count();
        $completedOrders = (clone $query)->where('status', self::STATUS_COMPLETED)->count();
        $failedOrders = (clone $query)->where('status', self::STATUS_FAILED)->count();

        // 按货币类型统计已完成订单的消费
        $currencyStats = (clone $query)->where('status', self::STATUS_COMPLETED)
            ->field('currency_type, sum(total_price) as total_amount, count(*) as order_count')
            ->group('currency_type')
            ->select()
            ->toArray();

        $currencyBreakdown = [];
        $totalAmountByCurrency = [
            '1' => 0, // 余额
            '2' => 0, // 金币
            '3' => 0  // 银币
        ];

        foreach ($currencyStats as $stat) {
            $currencyType = $stat['currency_type'];
            $totalAmountByCurrency[$currencyType] = $stat['total_amount'];
            $currencyBreakdown[$currencyType] = [
                'total_amount' => $stat['total_amount'],
                'order_count' => $stat['order_count'],
                'currency_text' => self::getCurrencyText($currencyType)
            ];
        }

        $statistics = [
            'total_orders' => $totalOrders,
            'completed_orders' => $completedOrders,
            'failed_orders' => $failedOrders,
            'total_amount_by_currency' => $totalAmountByCurrency,
            'currency_breakdown' => $currencyBreakdown
        ];

        // 缓存结果（缓存5分钟）
        \app\service\CacheService::set($cacheKey, $statistics, 300);

        return $statistics;
    }

    /**
     * 根据订单号查找订单
     * 
     * @param string $orderNo 订单号
     * @return PurchaseOrder|null 订单实例
     */
    public static function findByOrderNo(string $orderNo): ?PurchaseOrder
    {
        return self::where('order_no', $orderNo)->find();
    }

    /**
     * 批量更新订单状态
     * 
     * @param array $orderIds 订单ID数组
     * @param int $status 新状态
     * @return int 更新数量
     */
    public static function batchUpdateStatus(array $orderIds, int $status): int
    {
        if (empty($orderIds)) {
            return 0;
        }

        return self::where('id', 'in', $orderIds)->update(['status' => $status]);
    }

    /**
     * 获取失败订单列表（用于重试）
     * 
     * @param int $limit 限制数量
     * @param int $maxRetries 最大重试次数
     * @return array 失败订单列表
     */
    public static function getFailedOrders(int $limit = 50, int $maxRetries = 3): array
    {
        return self::where('status', self::STATUS_FAILED)
            ->where('retry_count', '<', $maxRetries)
            ->order('create_time', 'asc')
            ->limit($limit)
            ->select()
            ->toArray();
    }

    /**
     * 增加重试次数
     * 
     * @param int $orderId 订单ID
     * @return bool 是否成功
     */
    public static function incrementRetryCount(int $orderId): bool
    {
        return self::where('id', $orderId)->inc('retry_count')->update() > 0;
    }
}