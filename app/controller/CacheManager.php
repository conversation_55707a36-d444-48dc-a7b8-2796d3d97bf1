<?php

namespace app\controller;

use app\service\ItemService;
use app\service\CacheService;
use app\common\ApiResponse;
use think\facade\Log;
use think\Response;

/**
 * 缓存管理控制器
 * 用于管理商城相关的缓存操作
 */
class CacheManager
{
    /**
     * 清除商品列表缓存
     * @return Response
     */
    public function clearItemsCache()
    {
        try {
            $category = input('category', '');
            $search = input('search', '');
            
            $result = ItemService::clearItemsCache($category, $search);
            
            if ($result) {
                return json([
                    'code' => 200,
                    'message' => '商品列表缓存清除成功',
                    'data' => null
                ]);
            } else {
                return json([
                    'code' => 500,
                    'message' => '商品列表缓存清除失败',
                    'data' => null
                ]);
            }
        } catch (\Exception $e) {
            Log::error('清除商品列表缓存异常: ' . $e->getMessage());
            return json([
                'code' => 500,
                'message' => '操作异常：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    
    /**
     * 清除指定商品信息缓存
     * @return Response
     */
    public function clearItemInfoCache()
    {
        try {
            $itemId = input('item_id/d', 0);
            
            if (empty($itemId)) {
                return json([
                    'code' => 400,
                    'message' => '请提供商品ID',
                    'data' => null
                ]);
            }
            
            $result = ItemService::clearItemInfoCache($itemId);
            
            if ($result) {
                return json([
                    'code' => 200,
                    'message' => '商品信息缓存清除成功',
                    'data' => null
                ]);
            } else {
                return json([
                    'code' => 500,
                    'message' => '商品信息缓存清除失败',
                    'data' => null
                ]);
            }
        } catch (\Exception $e) {
            Log::error('清除商品信息缓存异常: ' . $e->getMessage());
            return json([
                'code' => 500,
                'message' => '操作异常：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    
    /**
     * 清除所有商品相关缓存
     * @return Response
     */
    public function clearAllItemsCache()
    {
        try {
            $result = ItemService::clearAllItemsCache();
            
            if ($result) {
                return json([
                    'code' => 200,
                    'message' => '所有商品缓存清除成功',
                    'data' => null
                ]);
            } else {
                return json([
                    'code' => 500,
                    'message' => '清除缓存失败',
                    'data' => null
                ]);
            }
        } catch (\Exception $e) {
            Log::error('清除所有商品缓存异常: ' . $e->getMessage());
            return json([
                'code' => 500,
                'message' => '操作异常：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    
    /**
     * 预热商品缓存
     * @return Response
     */
    public function warmupItemsCache()
    {
        try {
            $maxPages = input('max_pages/d', 5);
            
            // 限制预热页数，避免过度消耗资源
            if ($maxPages > 10) {
                $maxPages = 10;
            }
            
            $result = ItemService::warmupItemsCache($maxPages);
            
            if ($result) {
                return json([
                    'code' => 200,
                    'message' => '商品缓存预热成功',
                    'data' => ['pages' => $maxPages]
                ]);
            } else {
                return json([
                    'code' => 500,
                    'message' => '缓存预热失败',
                    'data' => null
                ]);
            }
        } catch (\Exception $e) {
            Log::error('预热商品缓存异常: ' . $e->getMessage());
            return json([
                'code' => 500,
                'message' => '操作异常：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    
    /**
     * 获取缓存统计信息
     * @return Response
     */
    public function getCacheStats()
    {
        try {
            $stats = CacheService::getStats();
            
            return json([
                'code' => 200,
                'message' => '获取缓存统计成功',
                'data' => $stats
            ]);
        } catch (\Exception $e) {
            Log::error('获取缓存统计异常: ' . $e->getMessage());
            return json([
                'code' => 500,
                'message' => '操作异常：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    
    /**
     * 缓存管理页面
     * @return string
     */
    public function index()
    {
        return view('cache/index');
    }

    /**
     * 获取缓存统计信息
     *
     * @return Response
     */
    public function getStats(): Response
    {
        try {
            $stats = CacheService::getStats();

            return ApiResponse::success($stats, '获取缓存统计成功');
        } catch (\Exception $e) {
            Log::error('获取缓存统计失败: ' . $e->getMessage());
            return ApiResponse::error('获取缓存统计失败');
        }
    }

    /**
     * 重置缓存统计信息
     *
     * @return Response
     */
    public function resetStats(): Response
    {
        try {
            CacheService::resetStats();

            return ApiResponse::success([], '重置缓存统计成功');
        } catch (\Exception $e) {
            Log::error('重置缓存统计失败: ' . $e->getMessage());
            return ApiResponse::error('重置缓存统计失败');
        }
    }

    /**
     * 缓存预热
     *
     * @return Response
     */
    public function warmup(): Response
    {
        try {
            $results = CacheService::warmup();

            return ApiResponse::success($results, '缓存预热完成');
        } catch (\Exception $e) {
            Log::error('缓存预热失败: ' . $e->getMessage());
            return ApiResponse::error('缓存预热失败');
        }
    }

    /**
     * 清除指定模式的缓存
     *
     * @return Response
     */
    public function clearByPattern(): Response
    {
        try {
            $pattern = $this->request->param('pattern', '');

            if (empty($pattern)) {
                return ApiResponse::paramError('缓存模式不能为空');
            }

            $count = CacheService::deleteByPattern($pattern);

            return ApiResponse::success([
                'pattern' => $pattern,
                'deleted_count' => $count
            ], "成功清除 {$count} 个缓存");
        } catch (\Exception $e) {
            Log::error('清除缓存失败: ' . $e->getMessage());
            return ApiResponse::error('清除缓存失败');
        }
    }

    /**
     * 清除用户相关缓存
     *
     * @return Response
     */
    public function clearUserCache(): Response
    {
        try {
            // 清除用户列表缓存
            $userListCount = CacheService::deleteByPattern('user_list_*');

            // 清除管理员权限缓存
            $adminAuthCount = CacheService::deleteByPattern('admin_auth:*');

            return ApiResponse::success([
                'user_list_cleared' => $userListCount,
                'admin_auth_cleared' => $adminAuthCount,
                'total_cleared' => $userListCount + $adminAuthCount
            ], '用户相关缓存清除完成');
        } catch (\Exception $e) {
            Log::error('清除用户缓存失败: ' . $e->getMessage());
            return ApiResponse::error('清除用户缓存失败');
        }
    }

    /**
     * 获取缓存健康状态
     *
     * @return Response
     */
    public function getHealthStatus(): Response
    {
        try {
            $stats = CacheService::getStats();

            // 计算健康状态
            $health = [
                'status' => 'healthy',
                'issues' => []
            ];

            // 检查连接状态
            if ($stats['connection_status'] !== 'connected') {
                $health['status'] = 'unhealthy';
                $health['issues'][] = 'Redis连接失败';
            }

            // 检查错误率
            $totalRequests = $stats['total_requests'];
            if ($totalRequests > 0) {
                $errorRate = ($stats['errors'] / $totalRequests) * 100;
                if ($errorRate > 5) { // 错误率超过5%
                    $health['status'] = 'warning';
                    $health['issues'][] = "错误率过高: {$errorRate}%";
                }
            }

            // 检查命中率
            if ($totalRequests > 100) { // 有足够的请求样本
                $hitRate = floatval(str_replace('%', '', $stats['hit_rate']));
                if ($hitRate < 70) { // 命中率低于70%
                    $health['status'] = 'warning';
                    $health['issues'][] = "缓存命中率偏低: {$stats['hit_rate']}";
                }
            }

            $health['stats'] = $stats;

            return ApiResponse::success($health, '获取缓存健康状态成功');
        } catch (\Exception $e) {
            Log::error('获取缓存健康状态失败: ' . $e->getMessage());
            return ApiResponse::error('获取缓存健康状态失败');
        }
    }
}