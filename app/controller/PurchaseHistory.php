<?php

namespace app\controller;

use app\BaseController;
use app\model\PurchaseOrder;
use app\service\ItemService;
use app\service\DatabaseService;
use think\facade\Session;
use think\facade\View;
use think\facade\Log;
use think\Request;
use Exception;

/**
 * 购买历史插件控制器
 * 专门负责处理购买历史相关的功能
 * 
 * 设计原则：
 * 1. 单一职责原则：专门处理购买历史查看功能
 * 2. 开闭原则：易于扩展新的历史查看功能
 * 3. 依赖倒置原则：依赖抽象的模型和服务接口
 * 4. 接口隔离原则：提供清晰的API接口
 */
class PurchaseHistory extends BaseController
{
    /**
     * 购买历史主页
     * 显示购买历史的Web界面（优化版 - 快速加载）
     * 
     * @return \think\Response
     */
    public function index()
    {
        try {
            // 检查用户登录状态
            $userId = Session::get('user_id');
            if (!$userId) {
                return redirect('/login');
            }

            // 获取用户基本信息（轻量级）
            $nickname = \app\middleware\AuthMiddleware::getCurrentUserNickname();

            // 传递基本数据到视图，统计信息通过AJAX异步加载
            View::assign([
                'user_nickname' => $nickname
            ]);

            return View::fetch('purchase_history/index');

        } catch (Exception $e) {
            Log::error('购买历史页面加载异常: ' . $e->getMessage());
            return redirect('/shop')->with('error', '加载购买历史失败');
        }
    }

    /**
     * 获取购买历史列表API
     * 
     * @param Request $request
     * @return \think\Response
     */
    public function getHistoryList(Request $request)
    {
        try {
            // 验证用户登录状态
            $userId = Session::get('user_id');
            if (!$userId) {
                return json([
                    'code' => 401,
                    'message' => '请先登录',
                    'data' => null
                ]);
            }

            // 获取请求参数
            $page = $request->param('page', 1, 'intval');
            $limit = $request->param('limit', 10, 'intval');
            $status = $request->param('status', null, 'intval');
            $startDate = $request->param('start_date', '', 'trim');
            $endDate = $request->param('end_date', '', 'trim');
            $currencyType = $request->param('currency_type', '', 'trim');
            $itemName = $request->param('item_name', '', 'trim');

            // 参数验证（支持分页大小选择：10、20、50、100）
            $allowedLimits = [10, 20, 50, 100];
            if (!in_array($limit, $allowedLimits)) {
                $limit = 10; // 默认10条
            }
            $page = max($page, 1);

            // 获取购买历史
            $result = $this->getPurchaseHistoryWithFilters($userId, $page, $limit, [
                'status' => $status,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'currency_type' => $currencyType,
                'item_name' => $itemName
            ]);

            // 使用统一的ApiResponse格式
            $response = $this->success($result, '获取成功');

            // 设置HTTP缓存头（缓存5分钟）
            $response->header([
                'Cache-Control' => 'public, max-age=300',
                'Expires' => gmdate('D, d M Y H:i:s', time() + 300) . ' GMT',
                'Last-Modified' => gmdate('D, d M Y H:i:s', time()) . ' GMT'
            ]);

            return $response;

        } catch (Exception $e) {
            Log::error('获取购买历史列表异常: ' . $e->getMessage());
            return $this->systemError('获取购买历史失败');
        }
    }

    /**
     * 获取订单详情
     * 
     * @param Request $request
     * @return \think\Response
     */
    public function getOrderDetail(Request $request)
    {
        try {
            // 验证用户登录状态
            $userId = Session::get('user_id');
            if (!$userId) {
                return json([
                    'code' => 401,
                    'message' => '请先登录',
                    'data' => null
                ]);
            }

            // 获取订单ID
            $orderId = $request->param('order_id', 0, 'intval');
            if (!$orderId) {
                return json([
                    'code' => 400,
                    'message' => '订单ID不能为空',
                    'data' => null
                ]);
            }

            // 获取订单详情
            $order = PurchaseOrder::where('id', $orderId)
                ->where('user_id', $userId)
                ->find();

            if (!$order) {
                return json([
                    'code' => 404,
                    'message' => '订单不存在',
                    'data' => null
                ]);
            }

            // 格式化订单数据
            $orderData = $this->formatOrderDetail($order->toArray());

            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $orderData
            ]);

        } catch (Exception $e) {
            Log::error('获取订单详情异常: ' . $e->getMessage());
            return json([
                'code' => 500,
                'message' => '获取订单详情失败',
                'data' => null
            ]);
        }
    }

    /**
     * 获取购买统计信息
     * 
     * @param Request $request
     * @return \think\Response
     */
    public function getStatistics(Request $request)
    {
        try {
            // 验证用户登录状态
            $userId = Session::get('user_id');
            if (!$userId) {
                return json([
                    'code' => 401,
                    'message' => '请先登录',
                    'data' => null
                ]);
            }

            // 获取时间范围参数
            $startDate = $request->param('start_date', '', 'trim');
            $endDate = $request->param('end_date', '', 'trim');

            // 获取统计信息
            $statistics = PurchaseOrder::getOrderStatistics($userId, $startDate, $endDate);

            // 设置HTTP缓存头（缓存10分钟）
            $response = json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $statistics
            ]);

            $response->header([
                'Cache-Control' => 'public, max-age=600',
                'Expires' => gmdate('D, d M Y H:i:s', time() + 600) . ' GMT',
                'Last-Modified' => gmdate('D, d M Y H:i:s', time()) . ' GMT'
            ]);

            return $response;

        } catch (Exception $e) {
            Log::error('获取购买统计异常: ' . $e->getMessage());
            return json([
                'code' => 500,
                'message' => '获取统计信息失败',
                'data' => null
            ]);
        }
    }

    /**
     * 批量获取商品图标API（用于前端延迟加载）
     * 
     * @param Request $request
     * @return \think\Response
     */
    public function getItemIcons(Request $request)
    {
        try {
            // 验证用户登录状态
            $userId = Session::get('user_id');
            if (!$userId) {
                return json([
                    'code' => 401,
                    'message' => '请先登录',
                    'data' => null
                ]);
            }

            // 获取商品ID列表
            $itemIds = $request->param('item_ids', []);
            if (!is_array($itemIds)) {
                $itemIds = [];
            }
            if (empty($itemIds)) {
                return json([
                    'code' => 400,
                    'message' => '商品ID列表不能为空',
                    'data' => null
                ]);
            }

            // 限制单次请求的数量
            $itemIds = array_slice($itemIds, 0, 50);
            
            // 获取图标信息
            $icons = $this->getItemIconsOptimized($itemIds);

            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $icons
            ]);

        } catch (Exception $e) {
            Log::error('批量获取商品图标异常: ' . $e->getMessage());
            return json([
                'code' => 500,
                'message' => '获取商品图标失败',
                'data' => null
            ]);
        }
    }

    /**
     * 导出购买历史
     * 
     * @param Request $request
     * @return \think\Response
     */
    public function exportHistory(Request $request)
    {
        try {
            // 验证用户登录状态
            $userId = Session::get('user_id');
            if (!$userId) {
                return json([
                    'code' => 401,
                    'message' => '请先登录',
                    'data' => null
                ]);
            }

            // 获取导出参数
            $startDate = $request->param('start_date', '', 'trim');
            $endDate = $request->param('end_date', '', 'trim');
            $status = $request->param('status', '', 'trim');
            $currencyType = $request->param('currency_type', '', 'trim');
            $format = $request->param('format', 'csv', 'trim');

            // 只支持CSV导出
            if ($format !== 'csv') {
                return json([
                    'code' => 400,
                    'message' => '只支持CSV格式导出',
                    'data' => null
                ]);
            }

            // 获取所有符合条件的订单
            $orders = $this->getOrdersForExport($userId, $startDate, $endDate, $status, $currencyType);

            return $this->exportToCsv($orders);

        } catch (Exception $e) {
            Log::error('导出购买历史异常: ' . $e->getMessage());
            return json([
                'code' => 500,
                'message' => '导出失败',
                'data' => null
            ]);
        }
    }

    /**
     * 获取用户统计信息
     * 
     * @param string $userId
     * @return array
     */
    private function getUserStatistics(string $userId): array
    {
        try {
            // 生成缓存键
            $cacheKey = 'user_statistics:' . $userId;
            
            // 尝试从缓存获取
            $statistics = \app\service\CacheService::get($cacheKey);
            if ($statistics !== null) {
                return $statistics;
            }

            // 使用PurchaseOrder模型的统计方法（已包含缓存）
            $statistics = PurchaseOrder::getOrderStatistics($userId);

            // 添加最近30天统计
            $startDate30 = date('Y-m-d H:i:s', strtotime('-30 days'));
            $endDate30 = date('Y-m-d H:i:s');
            $statistics['recent_30_days'] = PurchaseOrder::getOrderStatistics($userId, $startDate30, $endDate30);

            // 添加本月统计
            $startDateMonth = date('Y-m-01 00:00:00');
            $endDateMonth = date('Y-m-t 23:59:59');
            $statistics['this_month'] = PurchaseOrder::getOrderStatistics($userId, $startDateMonth, $endDateMonth);

            // 缓存结果（缓存10分钟）
            \app\service\CacheService::set($cacheKey, $statistics, 600);

            return $statistics;

        } catch (Exception $e) {
            Log::error('获取用户统计信息异常: ' . $e->getMessage());
            return [
                'total_orders' => 0,
                'completed_orders' => 0,
                'failed_orders' => 0,
                'total_amount' => 0,
                'recent_30_days' => [
                    'total_orders' => 0,
                    'completed_orders' => 0,
                    'failed_orders' => 0,
                    'total_amount' => 0
                ],
                'this_month' => [
                    'total_orders' => 0,
                    'completed_orders' => 0,
                    'failed_orders' => 0,
                    'total_amount' => 0
                ]
            ];
        }
    }

    /**
     * 获取带筛选条件的购买历史（性能优化版）
     * 
     * @param string $userId
     * @param int $page
     * @param int $limit
     * @param array $filters
     * @return array
     */
    private function getPurchaseHistoryWithFilters(string $userId, int $page, int $limit, array $filters): array
    {
        try {
            // 生成缓存键
            $cacheKey = 'purchase_history:' . $userId . ':' . md5(serialize($filters) . $page . $limit);
            
            // 尝试从缓存获取
            $result = \app\service\CacheService::get($cacheKey);
            if ($result !== null) {
                return $result;
            }

            // 使用优化的查询，只选择必要字段，强制使用索引
            $query = PurchaseOrder::field('id,order_no,item_id,item_name,quantity,currency_type,unit_price,total_price,status,create_time')
                ->where('user_id', $userId)
                ->force('idx_user_create_time'); // 强制使用新创建的优化索引

            // 应用筛选条件
            if (!empty($filters['status'])) {
                $query->where('status', $filters['status']);
            }

            if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
                $query->whereTime('create_time', 'between', [
                    $filters['start_date'] . ' 00:00:00',
                    $filters['end_date'] . ' 23:59:59'
                ]);
            }

            if (!empty($filters['currency_type'])) {
                // 将前端传递的字符串货币类型转换为数据库中的数字类型
                $currencyTypeMap = [
                    'COIN' => '1',   // C币
                    'POINT' => '2',  // 泡点
                    'SCORE' => '3'   // 积分
                ];
                $dbCurrencyType = $currencyTypeMap[$filters['currency_type']] ?? $filters['currency_type'];
                $query->where('currency_type', $dbCurrencyType);
            }

            if (!empty($filters['item_name'])) {
                $query->where('item_name', 'like', '%' . $filters['item_name'] . '%');
            }

            // 克隆查询对象用于计数，避免影响主查询
            $countQuery = clone $query;
            $total = $countQuery->count();

            // 获取分页数据 - 使用索引优化的查询，只查询必要字段
            $orders = $query->field('id,order_no,item_id,item_name,quantity,currency_type,unit_price,total_price,status,create_time')
                ->order('create_time', 'desc')
                ->limit(($page - 1) * $limit, $limit)
                ->select()
                ->toArray();

            // 获取所有商品ID用于批量查询图标
            $itemIds = array_unique(array_filter(array_column($orders, 'item_id')));
            $itemIcons = [];

            if (!empty($itemIds)) {
                $itemIcons = $this->getItemIconsBatch($itemIds);
            }

            // 快速格式化订单数据 - 只处理必要字段
            $formattedOrders = [];

            // 预定义映射数组，避免重复函数调用
            $currencyMap = ['1' => '泡点', '2' => '积分', '3' => 'C币'];
            $statusMap = [1 => '待处理', 2 => '处理中', 3 => '已完成', 4 => '失败', 5 => '已取消', 6 => '已退款'];

            foreach ($orders as $order) {
                $itemId = $order['item_id'];
                $formattedOrders[] = [
                    'id' => $order['id'],
                    'order_no' => $order['order_no'],
                    'item_id' => $itemId,
                    'item_name' => $order['item_name'] ?? '未知商品',
                    'item_icon' => $itemIcons[$itemId] ?? '/static/img/default.png',
                    'quantity' => $order['quantity'],
                    'currency_type' => $order['currency_type'],
                    'currency_text' => $currencyMap[$order['currency_type']] ?? '未知货币',
                    'unit_price' => $order['unit_price'],
                    'total_price' => $order['total_price'],
                    'status' => $order['status'],
                    'status_text' => $statusMap[$order['status']] ?? '未知状态',
                    'create_time' => $order['create_time'],
                    'create_time_formatted' => date('Y-m-d H:i:s', strtotime($order['create_time']))
                ];
            }

            $result = [
                'list' => $formattedOrders,
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'pages' => ceil($total / $limit)
            ];

            // 延长缓存时间到15分钟，减少数据库查询
            \app\service\CacheService::set($cacheKey, $result, 900);

            return $result;

        } catch (Exception $e) {
            Log::error('获取购买历史异常: ' . $e->getMessage());
            return [
                'list' => [],
                'total' => 0,
                'page' => $page,
                'limit' => $limit,
                'pages' => 0
            ];
        }
    }

    /**
     * 获取状态文本
     * 
     * @param int $status
     * @return string
     */
    private function getStatusText($status): string
    {
        switch ($status) {
            case 1:
                return '待处理';
            case 2:
                return '处理中';
            case 3:
                return '已完成';
            case 4:
                return '失败';
            case 5:
                return '已取消';
            case 6:
                return '已退款';
            default:
                return '未知状态';
        }
    }



    /**
     * 批量获取商品图标（优化版）
     * 
     * @param array $itemIds 商品ID数组
     * @return array 商品ID和图标路径的映射数组
     */
    private function getItemIconsOptimized(array $itemIds): array
    {
        $icons = [];
        $defaultIcon = '/static/img/default.png';
        
        if (empty($itemIds)) {
            return $icons;
        }
        
        // 过滤掉null值
        $itemIds = array_filter($itemIds, function($id) {
            return $id !== null && $id !== '';
        });
        
        if (empty($itemIds)) {
            return $icons;
        }
        
        try {
            // 批量从缓存中获取图标信息
            $cacheKeys = [];
            foreach ($itemIds as $itemId) {
                $cacheKeys[] = 'item_icon:' . $itemId;
            }
            
            // 使用批量获取缓存，提高性能
            $cachedIcons = \app\service\CacheService::getMultiple($cacheKeys);
            $uncachedIds = [];
            
            foreach ($itemIds as $itemId) {
                $cacheKey = 'item_icon:' . $itemId;
                if (isset($cachedIcons[$cacheKey]) && $cachedIcons[$cacheKey] !== null) {
                    $icons[$itemId] = $cachedIcons[$cacheKey];
                } else {
                    $uncachedIds[] = $itemId;
                }
            }
            
            // 如果所有图标都已缓存，直接返回
            if (empty($uncachedIds)) {
                return $icons;
            }
            
            // 限制单次API调用的数量，避免超时
            $batchSize = 50;
            $batches = array_chunk($uncachedIds, $batchSize);
            
            foreach ($batches as $batch) {
                // 直接调用本地方法获取图标，提高性能
                $batchIcons = $this->getItemIconsFromDatabase($batch);
                
                // 批量缓存新获取的图标信息（缓存2小时）
                $cacheData = [];
                foreach ($batchIcons as $itemId => $iconUrl) {
                    $cacheKey = 'item_icon:' . $itemId;
                    $cacheData[$cacheKey] = $iconUrl;
                    $icons[$itemId] = $iconUrl;
                }
                \app\service\CacheService::setMultiple($cacheData, 7200);
                
                // 为未找到图标的商品ID设置默认图标并缓存
                $defaultCacheData = [];
                foreach ($batch as $itemId) {
                    if (!isset($icons[$itemId])) {
                        $cacheKey = 'item_icon:' . $itemId;
                        $defaultCacheData[$cacheKey] = $defaultIcon;
                        $icons[$itemId] = $defaultIcon;
                    }
                }
                if (!empty($defaultCacheData)) {
                    \app\service\CacheService::setMultiple($defaultCacheData, 7200);
                }
            }
            
            return $icons;
            
        } catch (\Exception $e) {
            Log::error('获取商品图标异常: ' . $e->getMessage());
            // 出错时为所有未缓存的ID返回默认图标并缓存
            $defaultCacheData = [];
            foreach ($itemIds as $itemId) {
                if (!isset($icons[$itemId])) {
                    $cacheKey = 'item_icon:' . $itemId;
                    $defaultCacheData[$cacheKey] = $defaultIcon;
                    $icons[$itemId] = $defaultIcon;
                }
            }
            if (!empty($defaultCacheData)) {
                \app\service\CacheService::setMultiple($defaultCacheData, 600);
            }
            return $icons;
        }
    }
    
    /**
     * 从数据库直接获取商品图标
     * 
     * @param array $itemIds 商品ID数组
     * @return array 商品图标数组，键为商品ID，值为图标URL
     */
    private function getItemIconsFromDatabase(array $itemIds): array
    {
        $icons = [];
        
        try {
            if (empty($itemIds)) {
                return $icons;
            }
            
            // 获取静态资源基础URL
            $staticBaseUrl = \app\service\ApiService::getStaticBaseUrl();
            $staticBasePath = config('api.static.base_path', '/static');
            $baseUrl = $staticBaseUrl . $staticBasePath . '/img/';
            
            // 从数据库查询商品图标信息
            $itemIdsStr = implode(',', array_map('intval', $itemIds));
            $sql = "SELECT ItemID, Icon FROM iteminfo WHERE ItemID IN ({$itemIdsStr})";
            
            $db = \think\facade\Db::connect('mysql');
            $results = $db->query($sql);
            
            foreach ($results as $row) {
                $itemId = $row['ItemID'];
                $iconId = $row['Icon'];
                
                // 尝试多种图标文件格式
                $iconUrl = null;
                $possiblePaths = [
                    "/www/wwwroot/shop-new/public/static/img/{$iconId}.png",
                    "/www/wwwroot/shop-new/public/static/img/x{$iconId}.png"
                ];
                
                foreach ($possiblePaths as $path) {
                    if (file_exists($path)) {
                        $filename = basename($path);
                        $iconUrl = $baseUrl . $filename;
                        break;
                    }
                }
                
                if ($iconUrl) {
                    $icons[$itemId] = $iconUrl;
                }
            }
            
        } catch (\Exception $e) {
            \think\facade\Log::error('从数据库获取商品图标异常: ' . $e->getMessage());
        }
        
        return $icons;
    }

    /**
     * 格式化订单数据
     * 
     * @param array $order
     * @return array
     */
    private function formatOrderData(array $order): array
    {
        try {
            // 获取商品信息（使用item_id作为商品ID）
            $itemInfo = $this->getItemInfo($order['item_id']);

            return [
                'id' => $order['id'],
                'order_no' => $order['order_no'],
                'item_id' => $order['item_id'], // 真正的物品ID（可能为NULL）
                'item_name' => $itemInfo['name'],
                'item_icon' => $itemInfo['icon'],
                'quantity' => $order['quantity'],
                'unit_price' => $order['unit_price'],
                'total_price' => $order['total_price'],
                'currency_type' => $order['currency_type'],
                'currency_text' => PurchaseOrder::getCurrencyText($order['currency_type']),
                'status' => $order['status'],
                'status_text' => PurchaseOrder::getStatusText($order['status']),
                'unique_num' => $order['unique_num'],
                'create_time' => $order['create_time'],
                'create_time_formatted' => is_numeric($order['create_time']) ? date('Y-m-d H:i:s', $order['create_time']) : $order['create_time'],
                'update_time' => $order['update_time'],
                'update_time_formatted' => is_numeric($order['update_time']) ? date('Y-m-d H:i:s', $order['update_time']) : $order['update_time']
            ];

        } catch (Exception $e) {
            Log::error('格式化订单数据异常: ' . $e->getMessage());
            return $order;
        }
    }

    /**
     * 格式化订单详情
     * 
     * @param array $order
     * @return array
     */
    private function formatOrderDetail(array $order): array
    {
        try {
            // 使用getItemIconsOptimized方法获取图标（与订单列表保持一致）
            $itemIds = [$order['item_id']];
            $itemIcons = $this->getItemIconsOptimized($itemIds);
            
            // 获取商品信息（用于获取商品名称）
            $itemInfo = $this->getItemInfo($order['item_id']);

            $formattedOrder = [
                'id' => $order['id'],
                'order_no' => $order['order_no'],
                'item_id' => $order['item_id'],
                'item_name' => $itemInfo['name'],
                'item_icon' => $itemIcons[$order['item_id']] ?? '/static/img/default.png', // 使用API获取的图标
                'quantity' => $order['quantity'],
                'unit_price' => $order['unit_price'],
                'total_price' => $order['total_price'],
                'currency_type' => $order['currency_type'],
                'currency_text' => PurchaseOrder::getCurrencyText($order['currency_type']),
                'status' => $order['status'],
                'status_text' => PurchaseOrder::getStatusText($order['status']),
                'unique_num' => $order['unique_num'],
                'create_time' => $order['create_time'],
                'create_time_formatted' => is_numeric($order['create_time']) ? date('Y-m-d H:i:s', $order['create_time']) : $order['create_time'],
                'update_time' => $order['update_time'],
                'update_time_formatted' => is_numeric($order['update_time']) ? date('Y-m-d H:i:s', $order['update_time']) : $order['update_time'],
                // 添加详细信息
                'retry_count' => $order['retry_count'] ?? 0,
                'error_message' => $order['error_message'] ?? '',
                'delivery_status' => $this->getDeliveryStatus($order)
            ];

            return $formattedOrder;
            
        } catch (Exception $e) {
            Log::error('格式化订单详情异常: ' . $e->getMessage());
            // 如果出错，回退到原来的方法
            $formattedOrder = $this->formatOrderData($order);
            $formattedOrder['retry_count'] = $order['retry_count'] ?? 0;
            $formattedOrder['error_message'] = $order['error_message'] ?? '';
            $formattedOrder['delivery_status'] = $this->getDeliveryStatus($order);
            return $formattedOrder;
        }
    }

    /**
     * 转换货币类型为文本
     * 
     * @param string $currencyType 货币类型数字
     * @return string 货币文本
     */
    private function convertCurrencyText(string $currencyType): string
    {
        // 直接使用数字类型，因为PurchaseOrder::getCurrencyText已经支持数字映射
        return PurchaseOrder::getCurrencyText($currencyType);
    }

    /**
     * 批量获取商品图标
     *
     * @param array $itemIds 商品ID数组
     * @return array 商品ID => 图标URL的映射
     */
    private function getItemIconsBatch(array $itemIds): array
    {
        $icons = [];
        $defaultIcon = '/static/img/default.png';

        if (empty($itemIds)) {
            return $icons;
        }

        try {
            // 先尝试从缓存获取 - 使用更长的缓存时间
            sort($itemIds); // 确保缓存键一致
            $cacheKey = 'purchase_history_icons_' . md5(implode(',', $itemIds));
            $cached = \app\service\CacheService::get($cacheKey, false);
            if ($cached !== false) {
                return $cached;
            }

            // 使用优化的直接数据库查询，避免额外的服务层开销
            $itemIdsStr = implode(',', array_map('intval', $itemIds));
            $sql = "SELECT ItemID, Icon FROM iteminfo WHERE ItemID IN ({$itemIdsStr})";

            $db = \think\facade\Db::connect('mysql');
            $results = $db->query($sql);

            // 快速构建图标映射
            $iconMap = [];
            foreach ($results as $row) {
                $iconId = $row['Icon'];
                if (!empty($iconId)) {
                    // 简化图标路径生成，优先使用最常见的格式
                    $iconPath = "/static/img/{$iconId}.png";
                    if (file_exists(root_path() . "public{$iconPath}")) {
                        $iconMap[$row['ItemID']] = $iconPath;
                    } else {
                        $iconMap[$row['ItemID']] = "/static/img/x{$iconId}.png";
                    }
                } else {
                    $iconMap[$row['ItemID']] = $defaultIcon;
                }
            }

            // 为所有请求的ID分配图标
            foreach ($itemIds as $itemId) {
                $icons[$itemId] = $iconMap[$itemId] ?? $defaultIcon;
            }

            // 缓存结果，缓存30分钟
            \app\service\CacheService::set($cacheKey, $icons, 1800);

            return $icons;

        } catch (\Exception $e) {
            \think\facade\Log::error('批量获取商品图标失败: ' . $e->getMessage());

            // 出错时为所有ID返回默认图标
            foreach ($itemIds as $itemId) {
                $icons[$itemId] = $defaultIcon;
            }
            return $icons;
        }
    }

    /**
     * 获取商品信息
     *
     * @param int $itemId 商品ID
     * @return array
     */
    private function getItemInfo(int $itemId): array
    {
        try {
            // 使用DatabaseService查询本地数据库中的商品信息
            $result = DatabaseService::getFromLocal('iteminfo', ['ItemID' => $itemId], 'ItemName,Icon', 3600);

            if (!empty($result) && is_array($result) && count($result) > 0) {
                $item = $result[0]; // getFromRemote返回数组，取第一个元素
                return [
                    'name' => $item['ItemName'] ?? '未知物品',
                    'icon' => !empty($item['Icon']) ? '/static/img/' . $item['Icon'] . '.png' : '/static/img/default.png'
                ];
            }

            // 没找到记录，返回默认值
            return [
                'name' => '未知物品',
                'icon' => '/static/img/default.png'
            ];

        } catch (Exception $e) {
            return [
                'name' => '未知物品',
                'icon' => '/static/img/default.png'
            ];
        }
    }

    /**
     * 获取发送状态
     * 
     * @param int $uniqueNum
     * @return string
     */
    private function checkDeliveryStatusByUniqueNum(int $uniqueNum): string
    {
        try {
            $itemDeliveryService = new \app\service\ItemDeliveryService();
            $isDelivered = $itemDeliveryService->checkItemDeliveryStatus($uniqueNum);
            return $isDelivered ? '已发送' : '未发送';
        } catch (Exception $e) {
            return '未知';
        }
    }

    /**
     * 获取用于导出的订单数据
     * 
     * @param string $userId
     * @param string $startDate
     * @param string $endDate
     * @param string $status
     * @param string $currencyType
     * @return array
     */
    private function getOrdersForExport(string $userId, string $startDate = '', string $endDate = '', string $status = '', string $currencyType = ''): array
    {
        $query = PurchaseOrder::where('user_id', $userId);

        // 日期筛选
        if (!empty($startDate) && !empty($endDate)) {
            $query->whereTime('create_time', 'between', [
                $startDate . ' 00:00:00',
                $endDate . ' 23:59:59'
            ]);
        }

        // 状态筛选
        if (!empty($status)) {
            $query->where('status', $status);
        }

        // 货币类型筛选
        if (!empty($currencyType)) {
            // 将前端传递的字符串货币类型转换为数据库中的数字类型
            $currencyMap = [
                'COIN' => 1,
                'POINT' => 2,
                'SCORE' => 3
            ];
            
            if (isset($currencyMap[$currencyType])) {
                $query->where('currency_type', $currencyMap[$currencyType]);
            }
        }

        $orders = $query->order('create_time', 'desc')
            ->limit(1000) // 限制导出数量
            ->select()
            ->toArray();

        $formattedOrders = [];
        foreach ($orders as $order) {
            $formattedOrders[] = $this->formatOrderData($order);
        }

        return $formattedOrders;
    }

    /**
     * 导出为CSV格式
     * 
     * @param array $orders
     * @return \think\Response
     */
    private function exportToCsv(array $orders)
    {
        $filename = 'purchase_history_' . date('Y-m-d_H-i-s') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv; charset=utf-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"'
        ];

        $csvData = "\xEF\xBB\xBF"; // UTF-8 BOM
        $csvData .= "订单号,商品名称,数量,单价,总价,货币类型,状态,创建时间\n";

        foreach ($orders as $order) {
            $csvData .= sprintf(
                "\"%s\",\"%s\",\"%d\",\"%d\",\"%d\",\"%s\",\"%s\",\"%s\"\n",
                $order['order_no'],
                $order['item_name'],
                $order['quantity'],
                $order['unit_price'],
                $order['total_price'],
                $order['currency_text'],
                $order['status_text'],
                $order['create_time_formatted']
            );
        }

        return response($csvData, 200, $headers);
    }

    /**
     * 获取物品发送状态
     * 
     * @param array $order 订单数据
     * @return string
     */
    private function getDeliveryStatus(array $order): string
    {
        try {
            // 如果没有unique_num，说明物品还没有发送
            if (empty($order['unique_num'])) {
                return '未发送';
            }

            // 检查物品是否存在于游戏数据库中
            $remoteConfig = [
                'type' => 'mysql',
                'hostname' => env('REMOTE_DB_HOST'),
                'hostport' => env('REMOTE_DB_PORT'),
                'database' => env('ITEM_DB_NAME'),
                'username' => env('REMOTE_DB_USER'),
                'password' => env('REMOTE_DB_PASS'),
                'charset' => env('REMOTE_DB_CHARSET')
            ];
            
            $remoteDb = \think\facade\Db::connect($remoteConfig);
            $item = $remoteDb->table('seal_item')
                ->where('UniqueNum', $order['unique_num'])
                ->find();
                
            if ($item) {
                return '已发送';
            } else {
                return '发送失败';
            }
            
        } catch (Exception $e) {
            \think\facade\Log::error('检查发送状态失败: ' . $e->getMessage());
            return '状态未知';
        }
    }

}