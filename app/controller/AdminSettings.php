<?php

namespace app\controller;

use app\controller\admin\BaseController;
use think\facade\View;
use think\facade\Log;
use app\controller\admin\SettingsController;

class AdminSettings extends BaseController
{
    /**
     * 设置管理主页（新版ThinkPHP模板）
     */
    public function index()
    {
        try {
            // 获取设置数据
            $settingsController = new SettingsController($this->app);
            $settingsController->request = $this->request;
            
            // 获取基本设置
            $generalResponse = $settingsController->getGeneralSettings();
            $generalData = json_decode($generalResponse->getContent(), true);
            
            // 获取支付设置
            $paymentResponse = $settingsController->getPaymentSettings();
            $paymentData = json_decode($paymentResponse->getContent(), true);
            
            // 获取商城设置
            $shopResponse = $settingsController->getShopSettings();
            $shopData = json_decode($shopResponse->getContent(), true);
            
            // 传递数据到视图
            View::assign('general_settings', $generalData['data'] ?? []);
            View::assign('payment_settings', $paymentData['data'] ?? []);
            View::assign('shop_settings', $shopData['data'] ?? []);
            
            return View::fetch('admin/settings_new');
            
        } catch (\Exception $e) {
            Log::error('设置管理页面加载失败: ' . $e->getMessage());
            
            // 传递默认数据
            View::assign('general_settings', []);
            View::assign('payment_settings', []);
            View::assign('shop_settings', []);
            
            return View::fetch('admin/settings_new');
        }
    }
    
    /**
     * 获取基本设置API
     */
    public function getGeneralSettings()
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            $settingsController = new SettingsController($this->app);
            $settingsController->request = $this->request;

            return $settingsController->getGeneralSettings();

        } catch (\Exception $e) {
            Log::error('获取基本设置失败: ' . $e->getMessage());
            return $this->error('获取基本设置失败');
        }
    }
    
    /**
     * 保存基本设置API
     */
    public function saveGeneralSettings()
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            $settingsController = new SettingsController($this->app);
            $settingsController->request = $this->request;

            return $settingsController->saveGeneralSettings();

        } catch (\Exception $e) {
            Log::error('保存基本设置失败: ' . $e->getMessage());
            return $this->error('保存基本设置失败');
        }
    }
    
    /**
     * 获取支付设置API
     */
    public function getPaymentSettings()
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            $settingsController = new SettingsController($this->app);
            $settingsController->request = $this->request;

            return $settingsController->getPaymentSettings();

        } catch (\Exception $e) {
            Log::error('获取支付设置失败: ' . $e->getMessage());
            return $this->error('获取支付设置失败');
        }
    }
    
    /**
     * 保存支付设置API
     */
    public function savePaymentSettings()
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            $settingsController = new SettingsController($this->app);
            $settingsController->request = $this->request;

            return $settingsController->savePaymentSettings();

        } catch (\Exception $e) {
            Log::error('保存支付设置失败: ' . $e->getMessage());
            return $this->error('保存支付设置失败');
        }
    }
    
    /**
     * 获取商城设置API
     */
    public function getShopSettings()
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            $settingsController = new SettingsController($this->app);
            $settingsController->request = $this->request;

            return $settingsController->getShopSettings();

        } catch (\Exception $e) {
            Log::error('获取商城设置失败: ' . $e->getMessage());
            return $this->error('获取商城设置失败');
        }
    }
    
    /**
     * 保存商城设置API
     */
    public function saveShopSettings()
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            $settingsController = new SettingsController($this->app);
            $settingsController->request = $this->request;

            return $settingsController->saveShopSettings();

        } catch (\Exception $e) {
            Log::error('保存商城设置失败: ' . $e->getMessage());
            return $this->error('保存商城设置失败');
        }
    }
    
    /**
     * 上传文件API
     */
    public function uploadFile()
    {
        try {
            $file = $this->request->file('file');
            $type = $this->request->post('type', 'image');
            
            if (!$file) {
                return $this->paramError('请选择要上传的文件');
            }
            
            // 验证文件类型
            $allowedTypes = [
                'image' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
                'document' => ['pdf', 'doc', 'docx', 'xls', 'xlsx'],
                'archive' => ['zip', 'rar', '7z']
            ];
            
            $extension = strtolower($file->getOriginalExtension());
            if (!isset($allowedTypes[$type]) || !in_array($extension, $allowedTypes[$type])) {
                return $this->paramError('不支持的文件类型');
            }
            
            // 生成文件名
            $filename = date('Y/m/d/') . uniqid() . '.' . $extension;
            $uploadPath = 'uploads/' . $type . '/' . $filename;
            
            // 保存文件
            $savePath = public_path() . $uploadPath;
            $saveDir = dirname($savePath);
            
            if (!is_dir($saveDir)) {
                mkdir($saveDir, 0755, true);
            }
            
            $file->move($saveDir, basename($savePath));
            
            return $this->success([
                'url' => '/' . $uploadPath,
                'filename' => $file->getOriginalName(),
                'size' => $file->getSize()
            ], '文件上传成功');
            
        } catch (\Exception $e) {
            Log::error('文件上传失败: ' . $e->getMessage());
            return $this->systemError('文件上传失败');
        }
    }
    
    /**
     * 清理缓存API
     */
    public function clearCache()
    {
        try {
            $cacheType = $this->request->post('type', 'all');
            
            switch ($cacheType) {
                case 'template':
                    // 清理模板缓存
                    $this->clearTemplateCache();
                    break;
                case 'data':
                    // 清理数据缓存
                    \think\facade\Cache::clear();
                    break;
                case 'log':
                    // 清理日志文件
                    $this->clearLogFiles();
                    break;
                case 'all':
                default:
                    // 清理所有缓存
                    $this->clearTemplateCache();
                    \think\facade\Cache::clear();
                    $this->clearLogFiles();
                    break;
            }
            
            return $this->success([], '缓存清理成功');
            
        } catch (\Exception $e) {
            Log::error('清理缓存失败: ' . $e->getMessage());
            return $this->systemError('清理缓存失败');
        }
    }
    
    /**
     * 清理模板缓存
     */
    private function clearTemplateCache()
    {
        $cachePath = runtime_path() . 'temp/';
        if (is_dir($cachePath)) {
            $this->deleteDirectory($cachePath);
        }
    }
    
    /**
     * 清理日志文件
     */
    private function clearLogFiles()
    {
        $logPath = runtime_path() . 'log/';
        if (is_dir($logPath)) {
            $files = glob($logPath . '*.log');
            foreach ($files as $file) {
                if (is_file($file)) {
                    unlink($file);
                }
            }
        }
    }
    
    /**
     * 递归删除目录
     */
    private function deleteDirectory($dir)
    {
        if (!is_dir($dir)) {
            return false;
        }
        
        $files = array_diff(scandir($dir), ['.', '..']);
        foreach ($files as $file) {
            $path = $dir . DIRECTORY_SEPARATOR . $file;
            if (is_dir($path)) {
                $this->deleteDirectory($path);
            } else {
                unlink($path);
            }
        }
        
        return rmdir($dir);
    }
    
    /**
     * 系统信息API
     */
    public function getSystemInfo()
    {
        try {
            $info = [
                'php_version' => PHP_VERSION,
                'os' => PHP_OS,
                'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
                'mysql_version' => $this->getMysqlVersion(),
                'disk_usage' => $this->getDiskUsage(),
                'memory_usage' => $this->getMemoryUsage(),
                'upload_max_filesize' => ini_get('upload_max_filesize'),
                'post_max_size' => ini_get('post_max_size'),
                'max_execution_time' => ini_get('max_execution_time'),
                'timezone' => date_default_timezone_get()
            ];
            
            return $this->success($info, '获取系统信息成功');
            
        } catch (\Exception $e) {
            Log::error('获取系统信息失败: ' . $e->getMessage());
            return $this->systemError('获取系统信息失败');
        }
    }
    
    /**
     * 获取MySQL版本
     */
    private function getMysqlVersion()
    {
        try {
            $result = \think\facade\Db::query("SELECT VERSION() as version");
            return $result[0]['version'] ?? 'Unknown';
        } catch (\Exception $e) {
            return 'Unknown';
        }
    }
    
    /**
     * 获取磁盘使用情况
     */
    private function getDiskUsage()
    {
        try {
            $total = disk_total_space('/');
            $free = disk_free_space('/');
            $used = $total - $free;
            
            return [
                'total' => $this->formatBytes($total),
                'used' => $this->formatBytes($used),
                'free' => $this->formatBytes($free),
                'percentage' => round(($used / $total) * 100, 2)
            ];
        } catch (\Exception $e) {
            return ['total' => 'Unknown', 'used' => 'Unknown', 'free' => 'Unknown', 'percentage' => 0];
        }
    }
    
    /**
     * 获取内存使用情况
     */
    private function getMemoryUsage()
    {
        try {
            $current = memory_get_usage(true);
            $peak = memory_get_peak_usage(true);
            $limit = ini_get('memory_limit');
            
            return [
                'current' => $this->formatBytes($current),
                'peak' => $this->formatBytes($peak),
                'limit' => $limit
            ];
        } catch (\Exception $e) {
            return ['current' => 'Unknown', 'peak' => 'Unknown', 'limit' => 'Unknown'];
        }
    }
    
    /**
     * 格式化字节数
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
