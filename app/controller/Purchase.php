<?php

namespace app\controller;

use app\BaseController;
use app\service\ItemDeliveryService;
use app\exception\BusinessException;
use think\facade\Session;
use think\facade\Log;
use think\Request;
use Exception;

/**
 * 购买控制器
 * 负责处理商品购买相关的HTTP请求
 * 
 * 设计原则：
 * 1. 单一职责原则：专门处理购买相关的请求
 * 2. 依赖注入原则：注入ItemDeliveryService服务
 * 3. 接口隔离原则：提供清晰的API接口
 */
class Purchase extends BaseController
{
    /**
     * 物品发送服务
     */
    private $itemDeliveryService;

    /**
     * 构造函数
     */
    public function __construct()
    {
        parent::__construct();
        $this->itemDeliveryService = new ItemDeliveryService();
    }

    /**
     * 购买商品
     * 
     * @param Request $request
     * @return \think\response\Json
     * @throws BusinessException 业务异常
     */
    public function buyItem(Request $request)
    {
        // 1. 验证用户登录状态
        $userId = Session::get('user_id');
        if (!$userId) {
            throw BusinessException::userNotLogin();
        }

        // 2. 获取请求参数
        $itemId = $request->param('item_id', 0, 'intval');
        $quantity = $request->param('quantity', 1, 'intval');
        $source = $request->param('source', 'web_shop', 'trim');

        // 3. 验证参数
        $this->validatePurchaseParams($itemId, $quantity);

        // 4. 执行购买逻辑
        $result = $this->itemDeliveryService->purchaseAndDeliverItem(
            $userId,
            $itemId,
            $quantity,
            $source
        );

        // 5. 记录操作日志
        $this->logPurchaseAttempt($userId, $itemId, $quantity, $result['success']);

        // 6. 返回结果
        if ($result['success']) {
            return $this->success($result['data'] ?? [], $result['message']);
        } else {
            throw BusinessException::paymentFailed($result['message'], $result['data'] ?? null);
        }
    }

    /**
     * 获取购买历史
     * 
     * @param Request $request
     * @return \think\Response
     */
    public function getPurchaseHistory(Request $request)
    {
        try {
            // 1. 验证用户登录状态
            $userId = Session::get('user_id');
            if (!$userId) {
                return json([
                    'success' => false,
                    'message' => '请先登录',
                    'code' => 401
                ]);
            }

            // 2. 获取分页参数
            $limit = $request->param('limit', 10, 'intval');
            $limit = min(max($limit, 1), 50); // 限制在1-50之间

            // 3. 获取购买历史
            $result = $this->itemDeliveryService->getUserPurchaseHistory($userId, $limit);

            return json([
                'success' => $result['success'],
                'message' => $result['message'],
                'data' => $result['data'],
                'code' => $result['success'] ? 200 : 400
            ]);

        } catch (Exception $e) {
            Log::error('获取购买历史异常: ' . $e->getMessage());
            return json([
                'success' => false,
                'message' => '获取购买历史失败',
                'code' => 500
            ]);
        }
    }

    /**
     * 检查物品发送状态
     * 
     * @param Request $request
     * @return \think\Response
     */
    public function checkDeliveryStatus(Request $request)
    {
        try {
            // 1. 验证用户登录状态
            $userId = Session::get('user_id');
            if (!$userId) {
                return json([
                    'success' => false,
                    'message' => '请先登录',
                    'code' => 401
                ]);
            }

            // 2. 获取参数
            $uniqueNum = $request->param('unique_num', 0, 'intval');
            if (!$uniqueNum) {
                return json([
                    'success' => false,
                    'message' => '参数错误',
                    'code' => 400
                ]);
            }

            // 3. 检查发送状态
            $isDelivered = $this->itemDeliveryService->checkItemDeliveryStatus($uniqueNum);

            return json([
                'success' => true,
                'message' => '查询成功',
                'data' => [
                    'unique_num' => $uniqueNum,
                    'is_delivered' => $isDelivered,
                    'status' => $isDelivered ? '已发送' : '未发送'
                ],
                'code' => 200
            ]);

        } catch (Exception $e) {
            Log::error('检查发送状态异常: ' . $e->getMessage());
            return json([
                'success' => false,
                'message' => '查询失败',
                'code' => 500
            ]);
        }
    }

    /**
     * 获取商品详情（用于购买前确认）
     * 
     * @param Request $request
     * @return \think\Response
     */
    public function getItemDetail(Request $request)
    {
        try {
            $itemId = $request->param('item_id', 0, 'intval');
            if (!$itemId) {
                return json([
                    'success' => false,
                    'message' => '商品ID不能为空',
                    'code' => 400
                ]);
            }

            // 获取商品信息
            $item = db('pointshop')
                ->where('id', $itemId)
                ->where('status', 1)
                ->find();

            if (!$item) {
                return json([
                    'success' => false,
                    'message' => '商品不存在或已下架',
                    'code' => 404
                ]);
            }

            // 格式化商品信息
            $itemDetail = [
                'id' => $item['id'],
                'name' => $item['name'],
                'price' => $item['price'],
                'price_type' => $item['price_type'],
                'class' => $item['class'],
                'restriction' => $item['restriction'],
                'restriction_text' => $this->getRestrictionText($item['restriction']),
                'limit_quantity' => $item['limit_quantity'],
                'item_info' => $item['item_info'] ?? '',
                'status' => $item['status']
            ];

            return json([
                'success' => true,
                'message' => '获取成功',
                'data' => $itemDetail,
                'code' => 200
            ]);

        } catch (Exception $e) {
            Log::error('获取商品详情异常: ' . $e->getMessage());
            return json([
                'success' => false,
                'message' => '获取商品详情失败',
                'code' => 500
            ]);
        }
    }

    /**
     * 验证购买参数
     *
     * @param int $itemId 商品ID
     * @param int $quantity 数量
     * @throws BusinessException 参数验证失败时抛出异常
     */
    private function validatePurchaseParams(int $itemId, int $quantity): void
    {
        if ($itemId <= 0) {
            throw BusinessException::validationError('商品ID无效', ['item_id' => $itemId]);
        }

        if ($quantity <= 0 || $quantity > 999) {
            throw BusinessException::validationError('购买数量必须在1-999之间', ['quantity' => $quantity]);
        }
    }

    /**
     * 记录购买尝试日志
     * 
     * @param string $userId 用户ID
     * @param int $itemId 商品ID
     * @param int $quantity 数量
     * @param bool $success 是否成功
     */
    private function logPurchaseAttempt(string $userId, int $itemId, int $quantity, bool $success): void
    {
        $logData = [
            'user_id' => $userId,
            'item_id' => $itemId,
            'quantity' => $quantity,
            'success' => $success,
            'ip' => request()->ip(),
            'user_agent' => request()->header('User-Agent'),
            'timestamp' => date('Y-m-d H:i:s')
        ];

        Log::info('购买尝试: ' . json_encode($logData, JSON_UNESCAPED_UNICODE));
    }

    /**
     * 获取限制类型文本
     * 
     * @param int $restriction 限制类型
     * @return string 限制文本
     */
    private function getRestrictionText(int $restriction): string
    {
        $restrictionMap = [
            0 => '不限购',
            1 => '账号限购一次',
            2 => '每日限购一次',
            3 => '每周限购一次',
            4 => '每月限购一次'
        ];

        return $restrictionMap[$restriction] ?? '无限制';
    }

    /**
     * 获取订单状态
     * 
     * @param Request $request
     * @return \think\Response
     */
    public function getOrderStatus(Request $request)
    {
        try {
            // 1. 验证用户登录状态
            $userId = Session::get('user_id');
            if (!$userId) {
                return json([
                    'success' => false,
                    'message' => '请先登录',
                    'code' => 401
                ]);
            }

            // 2. 获取订单号
            $orderNo = $request->param('order_no', '', 'trim');
            if (empty($orderNo)) {
                return json([
                    'success' => false,
                    'message' => '订单号不能为空',
                    'code' => 400
                ]);
            }

            // 3. 查询订单状态
            $result = $this->itemDeliveryService->getOrderStatus($orderNo, $userId);

            return json([
                'success' => $result['success'],
                'message' => $result['message'],
                'data' => $result['data'] ?? [],
                'code' => $result['success'] ? 200 : 400
            ]);

        } catch (Exception $e) {
            Log::error('查询订单状态异常: ' . $e->getMessage());
            return json([
                'success' => false,
                'message' => '查询订单状态失败',
                'code' => 500
            ]);
        }
    }

    /**
     * 批量购买商品
     * 
     * @param Request $request
     * @return \think\Response
     */
    public function batchBuyItems(Request $request)
    {
        try {
            // 1. 验证用户登录状态
            $userId = Session::get('user_id');
            if (!$userId) {
                return json([
                    'success' => false,
                    'message' => '请先登录',
                    'code' => 401
                ]);
            }

            // 2. 获取批量购买参数
            $items = $request->param('items', []);
            $source = $request->param('source', 'web_shop', 'trim');

            if (empty($items) || !is_array($items)) {
                return json([
                    'success' => false,
                    'message' => '购买商品列表不能为空',
                    'code' => 400
                ]);
            }

            if (count($items) > 10) {
                return json([
                    'success' => false,
                    'message' => '单次最多购买10种商品',
                    'code' => 400
                ]);
            }

            // 3. 批量处理购买
            $results = [];
            $successCount = 0;
            $failCount = 0;

            foreach ($items as $item) {
                $itemId = intval($item['item_id'] ?? 0);
                $quantity = intval($item['quantity'] ?? 1);

                if ($itemId <= 0 || $quantity <= 0) {
                    $results[] = [
                        'item_id' => $itemId,
                        'success' => false,
                        'message' => '参数错误'
                    ];
                    $failCount++;
                    continue;
                }

                $result = $this->itemDeliveryService->purchaseAndDeliverItem(
                    $userId,
                    $itemId,
                    $quantity,
                    $source
                );

                $results[] = [
                    'item_id' => $itemId,
                    'success' => $result['success'],
                    'message' => $result['message'],
                    'data' => $result['data'] ?? []
                ];

                if ($result['success']) {
                    $successCount++;
                } else {
                    $failCount++;
                }
            }

            return json([
                'success' => $successCount > 0,
                'message' => "批量购买完成：成功{$successCount}个，失败{$failCount}个",
                'data' => [
                    'results' => $results,
                    'summary' => [
                        'total' => count($items),
                        'success' => $successCount,
                        'fail' => $failCount
                    ]
                ],
                'code' => 200
            ]);

        } catch (Exception $e) {
            Log::error('批量购买异常: ' . $e->getMessage());
            return json([
                'success' => false,
                'message' => '批量购买失败',
                'code' => 500
            ]);
        }
    }
}