<?php

namespace app\controller;

use app\BaseController;
use think\facade\View;
use think\facade\Log;
use app\controller\admin\ItemController;

class AdminItems extends BaseController
{
    /**
     * 商品管理主页（新版ThinkPHP模板）
     */
    public function index()
    {
        try {
            // 获取商品统计数据
            $itemController = new ItemController($this->app);
            $itemController->request = $this->request;
            
            // 获取商品列表（第一页数据用于初始显示）
            $this->request = $this->request->withGet([
                'page' => 1,
                'limit' => 20
            ]);
            $itemController->request = $this->request;
            
            $itemsResponse = $itemController->getItemList();
            $itemsData = json_decode($itemsResponse->getContent(), true);
            
            // 传递数据到视图
            View::assign('items', $itemsData['data'] ?? []);
            View::assign('total_items', $itemsData['total'] ?? 0);
            
            return View::fetch('admin/items_new');
            
        } catch (\Exception $e) {
            Log::error('商品管理页面加载失败: ' . $e->getMessage());
            
            // 传递默认数据
            View::assign('items', []);
            View::assign('total_items', 0);
            
            return View::fetch('admin/items_new');
        }
    }
    
    /**
     * 获取商品列表API
     */
    public function getItemList()
    {
        try {
            $itemController = new ItemController($this->app);
            $itemController->request = $this->request;
            
            return $itemController->getItemList();
            
        } catch (\Exception $e) {
            Log::error('获取商品列表失败: ' . $e->getMessage());
            return $this->systemError('获取商品列表失败');
        }
    }
    
    /**
     * 获取商品详情API
     */
    public function getItemDetail()
    {
        try {
            $itemController = new ItemController($this->app);
            $itemController->request = $this->request;
            
            return $itemController->getItemDetail();
            
        } catch (\Exception $e) {
            Log::error('获取商品详情失败: ' . $e->getMessage());
            return $this->systemError('获取商品详情失败');
        }
    }
    
    /**
     * 添加商品API
     */
    public function addItem()
    {
        try {
            $itemController = new ItemController($this->app);
            $itemController->request = $this->request;
            
            return $itemController->addItem();
            
        } catch (\Exception $e) {
            Log::error('添加商品失败: ' . $e->getMessage());
            return $this->systemError('添加商品失败');
        }
    }
    
    /**
     * 更新商品API
     */
    public function updateItem()
    {
        try {
            $itemController = new ItemController($this->app);
            $itemController->request = $this->request;
            
            return $itemController->updateItem();
            
        } catch (\Exception $e) {
            Log::error('更新商品失败: ' . $e->getMessage());
            return $this->systemError('更新商品失败');
        }
    }
    
    /**
     * 删除商品API
     */
    public function deleteItem()
    {
        try {
            $itemController = new ItemController($this->app);
            $itemController->request = $this->request;
            
            return $itemController->deleteItem();
            
        } catch (\Exception $e) {
            Log::error('删除商品失败: ' . $e->getMessage());
            return $this->systemError('删除商品失败');
        }
    }
    
    /**
     * 批量操作商品API
     */
    public function batchOperation()
    {
        try {
            $operation = $this->request->post('operation');
            $itemIds = $this->request->post('item_ids', []);
            
            if (empty($operation) || empty($itemIds)) {
                return $this->paramError('操作类型和商品ID不能为空');
            }
            
            $itemController = new ItemController($this->app);
            $itemController->request = $this->request;
            
            $results = [];
            $successCount = 0;
            $failCount = 0;
            
            foreach ($itemIds as $itemId) {
                try {
                    // 设置当前商品ID
                    $this->request = $this->request->withPost(['item_id' => $itemId]);
                    $itemController->request = $this->request;
                    
                    switch ($operation) {
                        case 'delete':
                            $response = $itemController->deleteItem();
                            break;
                        case 'enable':
                            $this->request = $this->request->withPost(['status' => 1]);
                            $itemController->request = $this->request;
                            $response = $itemController->updateItem();
                            break;
                        case 'disable':
                            $this->request = $this->request->withPost(['status' => 0]);
                            $itemController->request = $this->request;
                            $response = $itemController->updateItem();
                            break;
                        default:
                            throw new \Exception('不支持的操作类型');
                    }
                    
                    $responseData = json_decode($response->getContent(), true);
                    if ($responseData['code'] === 200) {
                        $successCount++;
                        $results[] = ['item_id' => $itemId, 'status' => 'success'];
                    } else {
                        $failCount++;
                        $results[] = ['item_id' => $itemId, 'status' => 'failed', 'message' => $responseData['message']];
                    }
                    
                } catch (\Exception $e) {
                    $failCount++;
                    $results[] = ['item_id' => $itemId, 'status' => 'failed', 'message' => $e->getMessage()];
                }
            }
            
            return $this->success([
                'success_count' => $successCount,
                'fail_count' => $failCount,
                'results' => $results
            ], "批量操作完成，成功：{$successCount}，失败：{$failCount}");
            
        } catch (\Exception $e) {
            Log::error('批量操作商品失败: ' . $e->getMessage());
            return $this->systemError('批量操作失败');
        }
    }
    
    /**
     * 导出商品数据API
     */
    public function exportItems()
    {
        try {
            $format = $this->request->get('format', 'csv');
            $filters = $this->request->get();
            
            // 获取所有商品数据
            $this->request = $this->request->withGet([
                'page' => 1,
                'limit' => 10000, // 大数量导出
                'search' => $filters['search'] ?? '',
                'category' => $filters['category'] ?? '',
                'status' => $filters['status'] ?? ''
            ]);
            
            $itemController = new ItemController($this->app);
            $itemController->request = $this->request;
            
            $itemsResponse = $itemController->getItemList();
            $itemsData = json_decode($itemsResponse->getContent(), true);
            
            if ($itemsData['code'] !== 200) {
                return $this->systemError('获取商品数据失败');
            }
            
            $items = $itemsData['data'] ?? [];
            
            if ($format === 'csv') {
                return $this->exportToCsv($items);
            } elseif ($format === 'excel') {
                return $this->exportToExcel($items);
            } else {
                return $this->paramError('不支持的导出格式');
            }
            
        } catch (\Exception $e) {
            Log::error('导出商品数据失败: ' . $e->getMessage());
            return $this->systemError('导出商品数据失败');
        }
    }
    
    /**
     * 导出为CSV格式
     */
    private function exportToCsv($items)
    {
        $filename = 'items_' . date('Y-m-d_H-i-s') . '.csv';
        
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename=' . $filename);
        
        $output = fopen('php://output', 'w');
        
        // 写入BOM以支持中文
        fwrite($output, "\xEF\xBB\xBF");
        
        // 写入表头
        fputcsv($output, ['商品ID', '商品名称', '分类', '价格', '货币类型', '状态', '创建时间']);
        
        // 写入数据
        foreach ($items as $item) {
            fputcsv($output, [
                $item['id'] ?? '',
                $item['name'] ?? '',
                $item['category_name'] ?? '',
                $item['price'] ?? '',
                $item['price_type_name'] ?? '',
                $item['status'] == 1 ? '启用' : '禁用',
                $item['created_at'] ?? ''
            ]);
        }
        
        fclose($output);
        exit;
    }
    
    /**
     * 导出为Excel格式（简化版）
     */
    private function exportToExcel($items)
    {
        // 这里简化为CSV格式，实际项目中可以使用PhpSpreadsheet库生成真正的Excel文件
        return $this->exportToCsv($items);
    }
    
    /**
     * 获取商品统计信息API
     */
    public function getItemStats()
    {
        try {
            $stats = [
                'total_items' => 0,
                'active_items' => 0,
                'disabled_items' => 0,
                'categories' => []
            ];
            
            // 获取总商品数
            $totalResult = \think\facade\Db::table('shop_items')->count();
            $stats['total_items'] = $totalResult;
            
            // 获取启用商品数
            $activeResult = \think\facade\Db::table('shop_items')->where('status', 1)->count();
            $stats['active_items'] = $activeResult;
            
            // 获取禁用商品数
            $disabledResult = \think\facade\Db::table('shop_items')->where('status', 0)->count();
            $stats['disabled_items'] = $disabledResult;
            
            // 获取分类统计
            $categoryResult = \think\facade\Db::table('shop_items')
                ->field('class, COUNT(*) as count')
                ->group('class')
                ->select();
            
            $categoryNames = [
                '1' => '高级时装',
                '2' => '玛可时装',
                '3' => '辅助道具',
                '4' => '宠物',
                '5' => '坐骑',
                '6' => '料理',
                '7' => '积分',
                '8' => '其他'
            ];
            
            foreach ($categoryResult as $category) {
                $stats['categories'][] = [
                    'name' => $categoryNames[$category['class']] ?? '未知分类',
                    'count' => $category['count']
                ];
            }
            
            return $this->success($stats, '获取商品统计信息成功');
            
        } catch (\Exception $e) {
            Log::error('获取商品统计信息失败: ' . $e->getMessage());
            return $this->systemError('获取商品统计信息失败');
        }
    }
}
