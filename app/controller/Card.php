<?php
declare(strict_types=1);

namespace app\controller;

use app\BaseController;
use think\App;
use think\Response;
use think\facade\Session;
use think\facade\Log;
use app\service\CardService;

/**
 * 卡片系统控制器
 * 处理周卡、月卡相关的API请求
 */
class Card extends BaseController
{
    private $cardService;

    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->cardService = new CardService();
    }

    /**
     * 获取卡片配置和用户状态
     * 
     * @return Response
     */
    public function getCardInfo(): Response
    {
        try {
            $userId = Session::get('user_id');
            if (empty($userId)) {
                return json(['code' => 401, 'message' => '请先登录']);
            }

            // 获取所有可用卡片
            $cards = $this->cardService->getAvailableCards();
            
            // 获取用户卡片状态
            $userStatus = $this->cardService->getUserCardStatus($userId);

            return json([
                'code' => 0,
                'message' => 'success',
                'data' => [
                    'cards' => $cards,
                    'user_status' => $userStatus
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('获取卡片信息失败: ' . $e->getMessage());
            return json(['code' => 500, 'message' => '获取卡片信息失败']);
        }
    }

    /**
     * 购买卡片
     * 
     * @return Response
     */
    public function purchaseCard(): Response
    {
        try {
            $userId = Session::get('user_id');
            if (empty($userId)) {
                return json(['code' => 401, 'message' => '请先登录']);
            }

            $cardId = $this->request->param('card_id');
            if (empty($cardId)) {
                return json(['code' => 400, 'message' => '卡片ID不能为空']);
            }

            $result = $this->cardService->purchaseCard($userId, (int)$cardId);

            if ($result['success']) {
                return json([
                    'code' => 0,
                    'message' => $result['message'],
                    'data' => [
                        'instant_rewards' => $result['instant_rewards'] ?? [],
                        'user_card_id' => $result['user_card_id'] ?? null
                    ]
                ]);
            } else {
                return json(['code' => 400, 'message' => $result['message']]);
            }

        } catch (\Exception $e) {
            Log::error('购买卡片失败: ' . $e->getMessage());
            return json(['code' => 500, 'message' => '购买失败，请稍后重试']);
        }
    }

    /**
     * 领取每日奖励
     * 
     * @return Response
     */
    public function claimDailyReward(): Response
    {
        try {
            $userId = Session::get('user_id');
            if (empty($userId)) {
                return json(['code' => 401, 'message' => '请先登录']);
            }

            $cardType = $this->request->param('card_type');
            if (empty($cardType)) {
                return json(['code' => 400, 'message' => '卡片类型不能为空']);
            }

            $result = $this->cardService->claimDailyReward($userId, $cardType);

            if ($result['success']) {
                return json([
                    'code' => 0,
                    'message' => $result['message'],
                    'data' => [
                        'rewards' => $result['rewards'] ?? [],
                        'remaining_days' => $result['remaining_days'] ?? 0
                    ]
                ]);
            } else {
                return json(['code' => 400, 'message' => $result['message']]);
            }

        } catch (\Exception $e) {
            Log::error('领取每日奖励失败: ' . $e->getMessage());
            return json(['code' => 500, 'message' => '领取失败，请稍后重试']);
        }
    }

    /**
     * 获取用户卡片状态
     * 
     * @return Response
     */
    public function getUserStatus(): Response
    {
        try {
            $userId = Session::get('user_id');
            if (empty($userId)) {
                return json(['code' => 401, 'message' => '请先登录']);
            }

            $userStatus = $this->cardService->getUserCardStatus($userId);

            return json([
                'code' => 0,
                'message' => 'success',
                'data' => $userStatus
            ]);

        } catch (\Exception $e) {
            Log::error('获取用户卡片状态失败: ' . $e->getMessage());
            return json(['code' => 500, 'message' => '获取状态失败']);
        }
    }
}
