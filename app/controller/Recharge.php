<?php

namespace app\controller;

use app\BaseController;
use app\service\CacheService;
use think\facade\Session;
use think\facade\View;
use think\facade\Log;
use think\facade\Request;
use think\Response;
use Exception;

/**
 * 在线充值控制器
 * 负责处理用户在线充值相关的HTTP请求
 * 
 * 设计原则：
 * 1. 单一职责原则：专门处理在线充值相关的请求
 * 2. 安全性原则：严格验证支付回调和签名
 * 3. 接口隔离原则：提供清晰的充值API接口
 */
class Recharge extends BaseController
{
    /**
     * 支付配置（从环境变量读取）
     */
    private $payPid;
    private $payKey;
    private $payUrl;
    private $payNotifyUrl;
    private $payReturnUrl;
    
    /**
     * 动态生成的支付回调地址（已在上面定义）
     */
    
    /**
     * 获取充值汇率配置（从支付设置中读取）
     */
    private function getExchangeRates(): array
    {
        $paymentSettings = $this->getPaymentSettings();

        return [
            'c_coin' => $paymentSettings['ccoin_recharge_ratio'] ?? 1,    // C币充值比例
            'point' => $paymentSettings['coin_recharge_ratio'] ?? 100,    // 泡点充值比例
        ];
    }
    
    /**
     * 构造函数 - 初始化动态配置
     */
    public function __construct(\think\App $app)
    {
        parent::__construct($app);
        
        // 从环境变量获取支付配置
        $this->payPid = env('PAY_PID', '20240114014914');
        $this->payKey = env('PAY_KEY', 'lgoiv0nWfXqXvHzxcRD9drz4UlYPefCk');
        $this->payUrl = env('PAY_URL', 'https://zpayz.cn/mapi.php');
        
        // 从环境变量获取域名配置
        $host = env('API_SERVER_HOST', 'test.cccseal.com');
        $protocol = env('API_SERVER_PROTOCOL', 'http');
        
        // 动态生成回调地址
        $this->payNotifyUrl = $protocol . '://' . $host . '/recharge/notify';
        $this->payReturnUrl = $protocol . '://' . $host . '/shop';
    }
    
    /**
     * 检查登录状态
     */
    protected function checkAuth()
    {
        return \app\common\AuthHelper::checkLoginForApi();
    }
    
    /**
     * 发起充值
     * POST /recharge/create
     */
    public function create()
    {
        // 检查登录状态
        $authCheck = $this->checkAuth();
        if ($authCheck) {
            return $authCheck;
        }
        
        try {
            $userId = Session::get('user_id');
            $currencyType = $this->request->post('currency_type', '');
            $amount = (float)$this->request->post('amount', 0);
            $payType = $this->request->post('pay_type', 'alipay');
            $cardId = $this->request->post('card_id', 0);
            $cardType = $this->request->post('card_type', '');

            // 参数验证
            if (!in_array($currencyType, ['c_coin', 'point', 'card_purchase'])) {
                return json([
                    'code' => 400,
                    'message' => '不支持的充值类型',
                    'data' => null
                ]);
            }
            
            if ($amount <= 0 || $amount > 10000) {
                return json([
                    'code' => 400,
                    'message' => '充值金额必须在0.01-10000之间',
                    'data' => null
                ]);
            }
            
            if (!in_array($payType, ['alipay', 'wxpay'])) {
                return json([
                    'code' => 400,
                    'message' => '不支持的支付方式',
                    'data' => null
                ]);
            }
            
            // 生成订单号
            $outTradeNo = ($currencyType === 'card_purchase' ? 'CD' : 'RC') . date('YmdHis') . rand(1000, 9999);

            // 处理不同类型的订单
            if ($currencyType === 'card_purchase') {
                // 卡片购买
                if (empty($cardId) || empty($cardType)) {
                    return json([
                        'code' => 400,
                        'message' => '卡片信息不完整',
                        'data' => null
                    ]);
                }

                // 获取卡片信息
                $cardInfo = \think\facade\Db::table('card_config')->where('id', $cardId)->where('status', 1)->find();
                if (!$cardInfo) {
                    return json([
                        'code' => 400,
                        'message' => '卡片不存在或已下架',
                        'data' => null
                    ]);
                }

                $productName = $cardInfo['name'] . " - ¥{$amount}";
                $rechargeAmount = 0; // 卡片购买不需要计算充值数量
            } else {
                // 普通充值
                $exchangeRates = $this->getExchangeRates();
                $rechargeAmount = $amount * $exchangeRates[$currencyType];
                $productName = $currencyType === 'c_coin' ? "C币充值 {$rechargeAmount}个" : "泡点充值 {$rechargeAmount}个";
            }
            
            // 构建支付参数
            $paramData = [
                'user_id' => $userId,
                'currency_type' => $currencyType,
                'recharge_amount' => $rechargeAmount
            ];

            // 如果是卡片购买，添加卡片信息
            if ($currencyType === 'card_purchase') {
                $paramData['card_id'] = $cardId;
                $paramData['card_type'] = $cardType;
            }

            $payParams = [
                'pid' => $this->payPid,
                'type' => $payType,
                'out_trade_no' => $outTradeNo,
                'notify_url' => $this->payNotifyUrl,
                'return_url' => $this->payReturnUrl,
                'name' => $productName,
                'money' => number_format($amount, 2, '.', ''),
                'clientip' => $this->request->ip(),
                'device' => 'pc',
                'param' => json_encode($paramData),
                'sign_type' => 'MD5'
            ];
            
            // 生成签名
            $payParams['sign'] = $this->generateSign($payParams);
            
            // 记录充值订单
            $this->saveRechargeOrder($outTradeNo, $userId, $currencyType, $amount, $rechargeAmount, $payType);
            
            // 调用支付API获取二维码
            $qrResult = $this->getPaymentQrCode($payParams);
            
            if ($qrResult['success']) {
                return json([
                    'code' => 200,
                    'message' => '充值订单创建成功',
                    'data' => [
                        'out_trade_no' => $outTradeNo,
                        'qrcode_url' => $qrResult['qrcode'],
                        'qrcode_img' => $qrResult['img'] ?? '',
                        'pay_url' => $qrResult['payurl'] ?? '',
                        'amount' => $amount,
                        'currency_type' => $currencyType,
                        'recharge_amount' => $rechargeAmount,
                        'pay_type' => $payType
                    ]
                ]);
            } else {
                return json([
                    'code' => 500,
                    'message' => $qrResult['message'],
                    'data' => null
                ]);
            }
            
        } catch (Exception $e) {
            Log::error('创建充值订单失败: ' . $e->getMessage());
            return json([
                'code' => 500,
                'message' => '创建充值订单失败',
                'data' => null
            ]);
        }
    }
    
    /**
     * 支付回调处理
     * GET/POST /recharge/notify
     */
    public function notify()
    {
        try {
            $params = $this->request->param();
            
            // 验证签名
            if (!$this->verifySign($params)) {
                Log::error('支付回调签名验证失败: ' . json_encode($params));
                return 'fail';
            }
            
            // 检查支付状态
            if ($params['trade_status'] !== 'TRADE_SUCCESS') {
                Log::info('支付状态非成功: ' . $params['trade_status']);
                return 'fail';
            }
            
            $outTradeNo = $params['out_trade_no'];
            $tradeNo = $params['trade_no'];
            $money = (float)$params['money'];
            
            // 解析附加参数
            $paramData = json_decode($params['param'], true);
            if (!$paramData) {
                Log::error('解析附加参数失败: ' . $params['param']);
                return 'fail';
            }
            
            $userId = $paramData['user_id'];
            $currencyType = $paramData['currency_type'];
            $rechargeAmount = $paramData['recharge_amount'];
            
            // 检查订单是否已处理
            if ($this->isOrderProcessed($outTradeNo)) {
                return 'success';
            }
            
            // 执行充值
            $result = $this->processRecharge($userId, $currencyType, $rechargeAmount, $outTradeNo, $tradeNo, $money);
            
            if ($result) {
                return 'success';
            } else {
                return 'fail';
            }
            
        } catch (Exception $e) {
            Log::error('处理支付回调失败: ' . $e->getMessage());
            return 'fail';
        }
    }
    
    /**
     * 生成支付签名
     */
    private function generateSign(array $params): string
    {
        // 移除sign和sign_type参数
        unset($params['sign'], $params['sign_type']);
        
        // 移除空值参数
        $params = array_filter($params, function($value) {
            return $value !== '' && $value !== null;
        });
        
        // 按键名排序
        ksort($params);
        
        // 构建签名字符串
        $signStr = '';
        foreach ($params as $key => $value) {
            $signStr .= $key . '=' . $value . '&';
        }
        
        // 去掉最后一个&符号，然后添加密钥
        $signStr = rtrim($signStr, '&') . $this->payKey;
        
        return md5($signStr);
    }
    
    /**
     * 验证支付签名
     */
    private function verifySign(array $params): bool
    {
        $sign = $params['sign'] ?? '';
        if (empty($sign)) {
            return false;
        }
        
        // 创建参数副本用于签名计算
        $signParams = $params;
        unset($signParams['sign'], $signParams['sign_type']);
        
        // 移除空值参数
        $signParams = array_filter($signParams, function($value) {
            return $value !== '' && $value !== null;
        });
        
        // 按键名排序
        ksort($signParams);
        
        // 构建签名字符串
        $signStr = '';
        foreach ($signParams as $key => $value) {
            $signStr .= $key . '=' . $value . '&';
        }
        
        // 去掉最后一个&符号，然后添加密钥
        $signStr = rtrim($signStr, '&') . $this->payKey;
        
        $calculatedSign = md5($signStr);
        
        // 记录签名验证信息用于调试
        Log::info('签名验证详情', [
            'received_sign' => $sign,
            'calculated_sign' => $calculatedSign,
            'sign_string' => $signStr,
            'params' => $signParams
        ]);
        
        return strtolower($sign) === strtolower($calculatedSign);
    }
    
    /**
     * 保存充值订单
     */
    private function saveRechargeOrder(string $outTradeNo, string $userId, string $currencyType, float $money, int $rechargeAmount, string $payType): void
    {
        \think\facade\Db::table('recharge_orders')->insert([
            'out_trade_no' => $outTradeNo,
            'user_id' => $userId,
            'currency_type' => $currencyType,
            'money' => $money,
            'recharge_amount' => $rechargeAmount,
            'pay_type' => $payType,
            'status' => 0, // 0-待支付
            'notify_status' => 0, // 0-未回调
            'notify_count' => 0,
            'ip_address' => $this->request->ip(),
            'user_agent' => $this->request->header('User-Agent'),
            'return_url' => $this->payReturnUrl,
            'notify_url' => $this->payNotifyUrl,
            'remark' => "用户在线充值{$currencyType}，金额{$money}元",
            'create_time' => date('Y-m-d H:i:s'),
            'update_time' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * 检查订单是否已处理
     */
    private function isOrderProcessed(string $outTradeNo): bool
    {
        $order = \think\facade\Db::table('recharge_orders')
            ->where('out_trade_no', $outTradeNo)
            ->where('status', 1)
            ->find();
            
        return !empty($order);
    }
    
    /**
     * 获取支付二维码
     * @param array $payParams 支付参数
     * @return array
     */
    private function getPaymentQrCode(array $payParams): array
    {
        try {
            // 调用支付API
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, 'https://zpayz.cn/mapi.php');
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($payParams));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/x-www-form-urlencoded'
            ]);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            if ($error) {
                Log::error('支付API请求失败: ' . $error);
                return ['success' => false, 'message' => '支付接口请求失败'];
            }
            
            if ($httpCode !== 200) {
                Log::error('支付API返回错误状态码: ' . $httpCode);
                return ['success' => false, 'message' => '支付接口返回错误'];
            }
            
            $result = json_decode($response, true);
            
            if (!$result) {
                Log::error('支付API返回数据解析失败: ' . $response);
                return ['success' => false, 'message' => '支付接口返回数据格式错误'];
            }
            
            // 记录支付API返回的完整数据用于调试
            Log::info('支付API返回数据: ' . json_encode($result));

            if ($result['code'] == 1) {
                // 成功获取支付信息
                return [
                    'success' => true,
                    'qrcode' => $result['qrcode'] ?? $result['payurl'] ?? '',
                    'img' => $result['img'] ?? '',
                    'payurl' => $result['payurl'] ?? '',
                    'trade_no' => $result['trade_no'] ?? ''
                ];
            } else {
                Log::error('支付API返回错误: ' . ($result['msg'] ?? '未知错误') . ', 完整响应: ' . json_encode($result));
                return ['success' => false, 'message' => $result['msg'] ?? '获取支付二维码失败'];
            }
            
        } catch (Exception $e) {
            Log::error('获取支付二维码异常: ' . $e->getMessage());
            return ['success' => false, 'message' => '获取支付二维码失败'];
        }
    }
    
    /**
     * 查询订单状态
     * GET /recharge/query
     */
    public function query()
    {
        // 检查登录状态
        $authCheck = $this->checkAuth();
        if ($authCheck) {
            return $authCheck;
        }
        
        try {
            $outTradeNo = $this->request->get('out_trade_no', '');
            
            if (empty($outTradeNo)) {
                return json([
                    'code' => 400,
                    'message' => '订单号不能为空',
                    'data' => null
                ]);
            }
            
            // 查询本地订单状态
            $order = \think\facade\Db::table('recharge_orders')
                ->where('out_trade_no', $outTradeNo)
                ->find();
                
            if (!$order) {
                return json([
                    'code' => 404,
                    'message' => '订单不存在',
                    'data' => null
                ]);
            }
            
            return json([
                'code' => 200,
                'message' => '查询成功',
                'data' => [
                    'status' => $order['status'],
                    'pay_time' => $order['pay_time'] ?? null
                ]
            ]);
            
        } catch (Exception $e) {
            Log::error('查询订单状态失败: ' . $e->getMessage());
            return json([
                'code' => 500,
                'message' => '查询失败',
                'data' => null
            ]);
        }
    }
    
    /**
     * 处理充值
     */
    private function processRecharge(string $userId, string $currencyType, int $rechargeAmount, string $outTradeNo, string $tradeNo, float $money): bool
    {
        // 开启事务
        \think\facade\Db::startTrans();
        
        try {
            // 更新订单状态
            \think\facade\Db::table('recharge_orders')
                ->where('out_trade_no', $outTradeNo)
                ->update([
                    'trade_no' => $tradeNo,
                    'status' => 1, // 1-已支付
                    'notify_status' => 1, // 1-回调成功
                    'notify_count' => \think\facade\Db::raw('notify_count + 1'),
                    'pay_time' => date('Y-m-d H:i:s'),
                    'notify_time' => date('Y-m-d H:i:s'),
                    'update_time' => date('Y-m-d H:i:s')
                ]);
            
            // 执行充值或卡片购买
            if ($currencyType === 'card_purchase') {
                // 处理卡片购买
                $this->processCardPurchase($userId, $paramData);
            } elseif ($currencyType === 'c_coin') {
                // 充值C币 - 使用独立的c_coin表
                $numericUserId = is_numeric($userId) ? (int)$userId : (int)preg_replace('/[^0-9]/', '', $userId);
                
                // 检查记录是否存在
                $exists = \think\facade\Db::table('c_coin')
                    ->where('user_id', $numericUserId)
                    ->find();
                
                if ($exists) {
                    // 更新现有记录 - 使用原生SQL确保更新成功
                    \think\facade\Db::execute(
                        'UPDATE c_coin SET balance = balance + ?, updated_at = NOW() WHERE user_id = ?',
                        [$rechargeAmount, $numericUserId]
                    );
                } else {
                    // 创建新记录
                    \think\facade\Db::table('c_coin')
                        ->insert([
                            'user_id' => $numericUserId,
                            'balance' => $rechargeAmount,
                            'game_account' => $userId,
                            'nickname' => $userId,
                            'created_at' => date('Y-m-d H:i:s'),
                            'updated_at' => date('Y-m-d H:i:s')
                        ]);
                }
                
                // C币充值赠送积分
                $this->processScoreBonus($userId, 'c_coin', $rechargeAmount);
                
            } elseif ($currencyType === 'point') {
                // 充值泡点 - 使用seal_member数据库的idtable1-5表
                // 泡点表的id字段是用户名（字符串），不是数字ID
                
                // 泡点分布在5个表中，需要找到用户所在的表
                $updated = false;
                for ($i = 1; $i <= 5; $i++) {
                    $exists = \think\facade\Db::connect('seal_member')
                        ->table('idtable' . $i)
                        ->where('id', $userId)
                        ->find();
                    
                    if ($exists) {
                        \think\facade\Db::connect('seal_member')
                            ->execute(
                                'UPDATE idtable' . $i . ' SET point = point + ? WHERE id = ?',
                                [$rechargeAmount, $userId]
                            );
                        $updated = true;
                        break;
                    }
                }
                
                if (!$updated) {
                    // 如果用户在idtable中不存在，创建在idtable1中
                    \think\facade\Db::connect('seal_member')
                        ->table('idtable1')
                        ->insert([
                            'id' => $userId,
                            'point' => $rechargeAmount
                        ]);
                }
                
                // 泡点充值赠送积分
                $this->processScoreBonus($userId, 'point', $rechargeAmount);
                
            } else {
                // 充值积分 - 使用seal_web数据库的sealmember表的hahapoint字段
                \think\facade\Db::connect('seal_web')
                    ->execute(
                        'UPDATE sealmember SET hahapoint = hahapoint + ? WHERE id = UNHEX(HEX(?))',
                        [$rechargeAmount, $userId]
                    );
            }
            
            // 清除用户余额缓存
            $this->clearUserBalanceCache($userId);
            
            // 获取充值前后余额
            $oldBalance = $this->getBalanceBeforeRecharge($userId, $currencyType, $rechargeAmount);
            $newBalance = $this->getCurrentBalance($userId, $currencyType);
            
            // 计算积分赠送金额
            $bonusScoreAmount = $this->calculateBonusScore($currencyType, $rechargeAmount);
            
            // 记录充值日志
            \think\facade\Db::table('recharge_logs')->insert([
                'username' => $userId,
                'currency_type' => $currencyType,
                'operation' => 'recharge',
                'amount' => $rechargeAmount,
                'old_balance' => $oldBalance,
                'new_balance' => $newBalance,
                'bonus_score_amount' => $bonusScoreAmount,
                'pay_type' => 'online',
                'order_no' => $outTradeNo,
                'pay_amount' => $money,
                'trade_no' => $tradeNo,
                'reason' => '在线充值',
                'remark' => '系统自动充值',
                'admin_user' => 'system',
                'ip_address' => $this->request->ip(),
                'create_time' => date('Y-m-d H:i:s')
            ]);
            
            \think\facade\Db::commit();
            
            Log::info("用户 {$userId} 充值成功，类型：{$currencyType}，数量：{$rechargeAmount}，订单号：{$outTradeNo}");
            
            return true;
            
        } catch (Exception $e) {
            \think\facade\Db::rollback();
            Log::error("充值处理失败：" . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 处理积分赠送
     * 
     * @param string $userId 用户ID
     * @param string $currencyType 货币类型
     * @param int $amount 充值金额
     */
    private function processScoreBonus(string $userId, string $currencyType, int $amount): void
    {
        try {
            // 获取支付设置
            $paymentSettings = $this->getPaymentSettings();
            
            // 计算赠送积分数量
            $bonusAmount = 0;
            if ($currencyType === 'c_coin') {
                $bonusAmount = intval($amount * $paymentSettings['ccoin_bonus_rate'] / 100);
            } elseif ($currencyType === 'point') {
                // 泡点赠送积分：确保至少赠送1积分（当充值金额大于0时）
                $exactBonus = $amount * $paymentSettings['coin_bonus_rate'] / 100;
                $bonusAmount = $exactBonus >= 1 ? intval($exactBonus) : ($amount > 0 ? 1 : 0);
            }
            
            if ($bonusAmount > 0) {
                // 获取当前积分余额
                $currentScore = $this->getCurrentScoreBalance($userId);
                if ($currentScore === false) {
                    $currentScore = 0;
                }
                
                // 计算新的积分余额
                $newScore = $currentScore + $bonusAmount;
                
                // 更新积分余额
                \think\facade\Db::connect('seal_web')
                    ->execute(
                        'UPDATE sealmember SET hahapoint = ? WHERE id = ?',
                        [$newScore, $userId]
                    );
                
                Log::info("积分赠送成功 - 用户: {$userId}, 充值类型: {$currencyType}, 充值数量: {$amount}, 赠送积分: {$bonusAmount}, 原积分: {$currentScore}, 新积分: {$newScore}");
            }
        } catch (Exception $e) {
            Log::error("积分赠送失败 - 用户: {$userId}, 充值类型: {$currencyType}, 错误: " . $e->getMessage());
        }
    }
    
    /**
     * 获取充值比例配置（API接口）
     * GET /recharge/rates
     */
    public function getRates()
    {
        try {
            $exchangeRates = $this->getExchangeRates();
            $paymentSettings = $this->getPaymentSettings();

            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => [
                    'exchange_rates' => $exchangeRates,
                    'min_amount' => $paymentSettings['min_recharge_amount'] ?? 1,
                    'max_amount' => $paymentSettings['max_recharge_amount'] ?? 10000,
                    'show_recharge_button' => $paymentSettings['show_recharge_button'] ?? true
                ]
            ]);
        } catch (Exception $e) {
            Log::error('获取充值比例失败: ' . $e->getMessage());
            return json([
                'code' => 500,
                'message' => '获取充值比例失败',
                'data' => null
            ]);
        }
    }

    /**
     * 获取支付设置
     *
     * @return array
     */
    private function getPaymentSettings(): array
    {
        $configFile = app()->getConfigPath() . 'payment_settings.php';
        if (file_exists($configFile)) {
            return include $configFile;
        }

        // 默认设置
        return [
            'ccoin_recharge_ratio' => 1,    // C币充值比例
            'coin_recharge_ratio' => 100,   // 泡点充值比例
            'ccoin_bonus_rate' => 100,      // C币赠送积分比例 100%
            'coin_bonus_rate' => 1,         // 泡点赠送积分比例 1%
            'min_recharge_amount' => 1,
            'max_recharge_amount' => 10000,
            'show_recharge_button' => true
        ];
    }
    
    /**
     * 获取用户当前积分余额
     * 
     * @param string $username 用户名
     * @return int|false
     */
    private function getCurrentScoreBalance(string $username)
    {
        try {
            $result = \think\facade\Db::connect('seal_web')
                ->table('sealmember')
                ->where('id', $username)
                ->value('hahapoint');
            
            return $result !== null ? (int)$result : 0;
        } catch (Exception $e) {
            Log::error("获取用户积分余额失败 - 用户: {$username}, 错误: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 计算积分赠送金额
     * 
     * @param string $currencyType 货币类型
     * @param int $amount 充值金额
     * @return int
     */
    private function calculateBonusScore(string $currencyType, int $amount): int
    {
        try {
            // 获取支付设置
            $paymentSettings = $this->getPaymentSettings();
            
            // 计算赠送积分数量
            $bonusAmount = 0;
            if ($currencyType === 'c_coin') {
                $bonusAmount = intval($amount * $paymentSettings['ccoin_bonus_rate'] / 100);
            } elseif ($currencyType === 'point') {
                // 泡点赠送积分：确保至少赠送1积分（当充值金额大于0时）
                $exactBonus = $amount * $paymentSettings['coin_bonus_rate'] / 100;
                $bonusAmount = $exactBonus >= 1 ? intval($exactBonus) : ($amount > 0 ? 1 : 0);
            }
            
            return $bonusAmount;
        } catch (Exception $e) {
            Log::error("计算积分赠送失败 - 货币类型: {$currencyType}, 金额: {$amount}, 错误: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * 获取当前余额（根据货币类型）
     */
    private function getCurrentBalance(string $userId, string $currencyType): int
    {
        try {
            if ($currencyType === 'c_coin') {
                $numericUserId = is_numeric($userId) ? (int)$userId : (int)preg_replace('/[^0-9]/', '', $userId);
                $balance = \think\facade\Db::table('c_coin')
                    ->where('user_id', $numericUserId)
                    ->value('balance');
                return (int)($balance ?? 0);
                
            } elseif ($currencyType === 'point') {
                // 查找用户在哪个idtable中
                for ($i = 1; $i <= 5; $i++) {
                    $balance = \think\facade\Db::connect('seal_member')
                        ->table('idtable' . $i)
                        ->where('id', $userId)
                        ->value('point');
                    if ($balance !== null) {
                        return (int)$balance;
                    }
                }
                return 0;
                
            } elseif ($currencyType === 'score') {
                return $this->getCurrentScoreBalance($userId);
            }
            
            return 0;
        } catch (Exception $e) {
            Log::error('获取余额失败: ' . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * 获取充值前余额（当前余额减去充值金额）
     */
    private function getBalanceBeforeRecharge(string $userId, string $currencyType, int $rechargeAmount): int
    {
        $currentBalance = $this->getCurrentBalance($userId, $currencyType);
        return max(0, $currentBalance - $rechargeAmount);
    }
    
    /**
     * 查询支付状态
     * GET /recharge/query
     */
    public function queryStatus()
    {
        // 检查登录状态
        $authCheck = $this->checkAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            $outTradeNo = $this->request->param('out_trade_no');

            if (empty($outTradeNo)) {
                return json([
                    'code' => 400,
                    'message' => '订单号不能为空',
                    'data' => null
                ]);
            }

            // 查询订单状态
            $order = \think\facade\Db::table('recharge_orders')
                ->where('out_trade_no', $outTradeNo)
                ->where('user_id', Session::get('user_id'))
                ->find();

            if (!$order) {
                return json([
                    'code' => 404,
                    'message' => '订单不存在',
                    'data' => null
                ]);
            }

            return json([
                'code' => 200,
                'message' => 'success',
                'data' => [
                    'out_trade_no' => $order['out_trade_no'],
                    'status' => $order['status'],
                    'amount' => $order['amount'],
                    'currency_type' => $order['currency_type'],
                    'recharge_amount' => $order['recharge_amount'],
                    'pay_time' => $order['pay_time'],
                    'create_time' => $order['create_time']
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('查询支付状态失败: ' . $e->getMessage());
            return json([
                'code' => 500,
                'message' => '查询失败',
                'data' => null
            ]);
        }
    }

    /**
     * 取消订单
     * POST /recharge/cancel
     */
    public function cancelOrder()
    {
        // 检查登录状态
        $authCheck = $this->checkAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            $outTradeNo = $this->request->param('out_trade_no');

            if (empty($outTradeNo)) {
                return json([
                    'code' => 400,
                    'message' => '订单号不能为空',
                    'data' => null
                ]);
            }

            // 查询订单
            $order = \think\facade\Db::table('recharge_orders')
                ->where('out_trade_no', $outTradeNo)
                ->where('user_id', Session::get('user_id'))
                ->find();

            if (!$order) {
                return json([
                    'code' => 404,
                    'message' => '订单不存在',
                    'data' => null
                ]);
            }

            // 只能取消待支付的订单
            if ($order['status'] != 0) {
                return json([
                    'code' => 400,
                    'message' => '只能取消待支付的订单',
                    'data' => null
                ]);
            }

            // 更新订单状态为已取消
            \think\facade\Db::table('recharge_orders')
                ->where('out_trade_no', $outTradeNo)
                ->update([
                    'status' => 3,
                    'update_time' => date('Y-m-d H:i:s')
                ]);

            return json([
                'code' => 200,
                'message' => '订单已取消',
                'data' => null
            ]);

        } catch (\Exception $e) {
            Log::error('取消订单失败: ' . $e->getMessage());
            return json([
                'code' => 500,
                'message' => '取消失败',
                'data' => null
            ]);
        }
    }

    /**
     * 获取充值记录
     * GET /recharge/history
     */
    public function getHistory()
    {
        // 检查登录状态
        $authCheck = $this->checkAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            $userId = Session::get('user_id');
            $page = (int)$this->request->param('page', 1);
            $limit = (int)$this->request->param('limit', 10);
            $status = $this->request->param('status', '');
            $currencyType = $this->request->param('currency_type', '');
            $payType = $this->request->param('pay_type', '');

            // 参数验证
            if ($page < 1) $page = 1;
            if ($limit < 1 || $limit > 100) $limit = 10;

            $offset = ($page - 1) * $limit;

            // 构建查询条件
            $where = [['user_id', '=', $userId]];

            if ($status !== '') {
                $where[] = ['status', '=', $status];
            }
            if ($currencyType !== '') {
                $where[] = ['currency_type', '=', $currencyType];
            }
            if ($payType !== '') {
                $where[] = ['pay_type', '=', $payType];
            }

            // 查询充值记录
            $records = \think\facade\Db::table('recharge_orders')
                ->where($where)
                ->order('create_time', 'desc')
                ->limit($offset, $limit)
                ->select();

            // 查询总数
            $total = \think\facade\Db::table('recharge_orders')
                ->where($where)
                ->count();

            $pages = ceil($total / $limit);

            return json([
                'code' => 200,
                'message' => 'success',
                'data' => [
                    'list' => $records ? $records->toArray() : [],
                    'total' => $total,
                    'page' => $page,
                    'limit' => $limit,
                    'pages' => $pages
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('获取充值记录失败: ' . $e->getMessage());
            return json([
                'code' => 500,
                'message' => '获取充值记录失败',
                'data' => null
            ]);
        }
    }

    /**
     * 取消充值订单
     *
     * @return Response
     */
    public function cancel(): Response
    {
        try {
            $username = Session::get('username');
            if (!$username) {
                return json([
                    'code' => 401,
                    'message' => '请先登录',
                    'data' => null
                ]);
            }

            $postData = Request::post();
            $outTradeNo = $postData['out_trade_no'] ?? '';

            if (empty($outTradeNo)) {
                return json([
                    'code' => 400,
                    'message' => '订单号不能为空',
                    'data' => null
                ]);
            }

            // 查找订单
            $order = \think\facade\Db::table('recharge_orders')
                ->where('out_trade_no', $outTradeNo)
                ->where('user_id', $username)
                ->find();

            if (!$order) {
                return json([
                    'code' => 404,
                    'message' => '订单不存在',
                    'data' => null
                ]);
            }

            // 检查订单状态
            if ($order['status'] != 0) {
                $statusText = [
                    1 => '已支付',
                    2 => '已失败',
                    3 => '已取消'
                ];
                return json([
                    'code' => 400,
                    'message' => '订单状态为' . ($statusText[$order['status']] ?? '未知') . '，无法取消',
                    'data' => null
                ]);
            }

            // 开启事务
            \think\facade\Db::startTrans();

            try {
                // 更新订单状态为已取消
                $updateResult = \think\facade\Db::table('recharge_orders')
                    ->where('id', $order['id'])
                    ->where('status', 0) // 再次确认状态，避免并发问题
                    ->update([
                        'status' => 3,
                        'remark' => '用户主动取消',
                        'update_time' => date('Y-m-d H:i:s')
                    ]);

                if ($updateResult === 0) {
                    throw new Exception('订单状态已变更，取消失败');
                }

                // 记录取消日志
                \think\facade\Db::table('recharge_logs')->insert([
                    'username' => $username,
                    'currency_type' => $order['currency_type'],
                    'operation' => 'cancel',
                    'amount' => $order['recharge_amount'],
                    'old_balance' => 0,
                    'new_balance' => 0,
                    'order_no' => $order['out_trade_no'],
                    'pay_amount' => $order['amount'],
                    'reason' => '用户主动取消订单',
                    'remark' => '订单取消',
                    'ip_address' => Request::ip(),
                    'create_time' => date('Y-m-d H:i:s')
                ]);

                // 提交事务
                \think\facade\Db::commit();

                Log::info("用户 {$username} 取消充值订单: {$outTradeNo}");

                return json([
                    'code' => 200,
                    'message' => '订单已取消',
                    'data' => null
                ]);

            } catch (Exception $e) {
                // 回滚事务
                \think\facade\Db::rollback();
                throw $e;
            }

        } catch (Exception $e) {
            Log::error('取消充值订单失败: ' . $e->getMessage());
            return json([
                'code' => 500,
                'message' => '取消订单失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 清除用户余额缓存
     *
     * @param string $userId 用户ID
     */
    private function clearUserBalanceCache(string $userId): void
    {
        try {
            $cacheKey = 'user_balance:' . $userId;
            CacheService::delete($cacheKey);
            Log::info("已清除用户 {$userId} 的余额缓存");
        } catch (Exception $e) {
            Log::warning('清除用户余额缓存失败: ' . $e->getMessage());
        }
    }

    /**
     * 处理卡片购买
     *
     * @param string $userId 用户ID
     * @param array $paramData 参数数据
     * @throws Exception
     */
    private function processCardPurchase(string $userId, array $paramData): void
    {
        try {
            $cardId = $paramData['card_id'] ?? 0;
            $cardType = $paramData['card_type'] ?? '';

            if (empty($cardId) || empty($cardType)) {
                throw new Exception('卡片信息不完整');
            }

            // 引入卡片服务
            $cardService = new \app\service\CardService();

            // 执行卡片购买（跳过重复购买检查，因为支付已经完成）
            $result = $cardService->purchaseCard($userId, (int)$cardId, true);

            if (!$result['success']) {
                throw new Exception('卡片购买失败: ' . $result['message']);
            }

            Log::info("用户 {$userId} 成功购买卡片 {$cardId}");

        } catch (Exception $e) {
            Log::error('处理卡片购买失败: ' . $e->getMessage());
            throw $e;
        }
    }
}