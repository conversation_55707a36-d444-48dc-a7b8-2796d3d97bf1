<?php
declare(strict_types=1);

namespace app\controller\admin;

use think\App;
use think\Response;
use think\facade\View;
use think\facade\Log;
use think\facade\Db;
use app\service\ItemService;
use app\service\AdminManagementService;

/**
 * 管理后台商品管理控制器
 * 负责商品的增删改查和状态管理
 */
class ItemController extends BaseController
{
    /**
     * 商品服务
     * @var ItemService
     */
    protected ItemService $itemService;

    /**
     * 管理服务
     * @var AdminManagementService
     */
    protected AdminManagementService $adminManagementService;

    /**
     * 构造函数
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->itemService = new ItemService();
        $this->adminManagementService = new AdminManagementService();
    }

    /**
     * 商品管理页面
     * 
     * @return Response
     */
    public function index(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        // 设置页面数据
        View::assign([
            'pageTitle' => '商品管理',
            'pageIcon' => 'bi bi-box',
            'pageDescription' => '管理系统商品，查看商品信息和状态'
        ]);
        
        return response(View::fetch('admin/items'));
    }

    /**
     * 获取商品列表
     * 
     * @return Response
     */
    public function getItemList(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }
        
        try {
            // 检查是否请求统计数据
            $statsOnly = $this->request->param('stats', false);
            if ($statsOnly) {
                return $this->getItemStats();
            }

            $params = $this->getPaginationParams();
            $category = $this->request->param('category', '');
            $status = $this->request->param('status', '');
            $currency = $this->request->param('currency', '');

            // 调用ItemService获取商品列表
            $result = ItemService::getItems(
                $params['page'],
                $params['limit'],
                $category,
                $params['search'],
                $status,
                'default',
                $currency
            );
            
            // 使用管理服务处理商品数据格式
            $items = [];
            if (isset($result['items']) && is_array($result['items'])) {
                $items = $this->adminManagementService->formatItemsForAdmin($result['items']);
            }
            
            $this->logAdminAction('view_items', '查看商品列表', [
                'page' => $params['page'],
                'search' => $params['search'],
                'category' => $category
            ]);
            
            return $this->success([
                'list' => $items,
                'total' => $result['total'] ?? 0,
                'page' => $params['page'],
                'limit' => $params['limit']
            ]);
        } catch (\Exception $e) {
            Log::error('获取商品列表失败: ' . $e->getMessage());
            return $this->error('获取商品列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取商品详情
     * 
     * @return Response
     */
    public function getItemDetail(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }
        
        try {
            $itemId = $this->request->param('item_id');
            
            if (empty($itemId)) {
                return $this->error('商品ID不能为空', 400);
            }
            
            // 获取商品详细信息
            $itemDetail = $this->itemService->getItemDetail($itemId);
            
            if (!$itemDetail) {
                return $this->error('商品不存在', 404);
            }
            
            $this->logAdminAction('view_item_detail', '查看商品详情', ['item_id' => $itemId]);
            
            return $this->success($itemDetail);
        } catch (\Exception $e) {
            Log::error('获取商品详情失败: ' . $e->getMessage());
            return $this->error('获取商品详情失败');
        }
    }

    /**
     * 添加商品
     * 
     * @return Response
     */
    public function addItem(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }
        
        try {
            $data = $this->request->post();
            
            // 验证必填字段
            $required = ['name', 'category', 'price', 'description'];
            foreach ($required as $field) {
                if (empty($data[$field])) {
                    return $this->error("字段 {$field} 不能为空", 400);
                }
            }
            
            // 添加商品
            $result = ItemService::addItem($data);
            
            if ($result) {
                $this->logAdminAction('add_item', '添加商品', $data);
                return $this->success(['item_id' => $result], '商品添加成功');
            } else {
                return $this->error('商品添加失败');
            }
        } catch (\Exception $e) {
            Log::error('添加商品失败: ' . $e->getMessage());
            return $this->error('添加商品失败');
        }
    }

    /**
     * 更新商品
     * 
     * @return Response
     */
    public function updateItem(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }
        
        try {
            // 获取JSON数据
            $jsonData = json_decode($this->request->getContent(), true);
            $itemId = $jsonData['item_id'] ?? null;

            if (empty($itemId)) {
                return $this->error('商品ID不能为空', 400);
            }

            // 移除item_id，避免更新主键
            unset($jsonData['item_id']);
            $data = $jsonData;
            
            // 更新商品
            $result = ItemService::updateItem($itemId, $data);
            
            if ($result) {
                $this->logAdminAction('update_item', '更新商品', [
                    'item_id' => $itemId,
                    'data' => $data
                ]);
                return $this->success([], '商品更新成功');
            } else {
                return $this->error('商品更新失败');
            }
        } catch (\Exception $e) {
            Log::error('更新商品失败: ' . $e->getMessage());
            return $this->error('更新商品失败');
        }
    }

    /**
     * 删除商品
     * 
     * @return Response
     */
    public function deleteItem(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }
        
        try {
            $itemId = $this->request->param('item_id');
            
            if (empty($itemId)) {
                return $this->error('商品ID不能为空', 400);
            }
            
            // 删除商品
            $result = ItemService::deleteItem($itemId);
            
            if ($result) {
                $this->logAdminAction('delete_item', '删除商品', ['item_id' => $itemId]);
                return $this->success([], '商品删除成功');
            } else {
                return $this->error('商品删除失败');
            }
        } catch (\Exception $e) {
            Log::error('删除商品失败: ' . $e->getMessage());
            return $this->error('删除商品失败');
        }
    }

    /**
     * 更新商品状态
     *
     * @return Response
     */
    public function updateItemStatus(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            $itemId = $this->request->param('id');
            $status = $this->request->param('status');

            if (empty($itemId)) {
                return $this->error('商品ID不能为空', 400);
            }

            if (!in_array($status, [0, 1, 2])) {
                return $this->error('状态值无效', 400);
            }

            // 更新商品状态
            $result = ItemService::updateItemStatus($itemId, $status);

            if ($result) {
                $statusText = $status == 1 ? '上架' : '下架';
                $this->logAdminAction('update_item_status', "商品{$statusText}", ['item_id' => $itemId, 'status' => $status]);
                return $this->success([], "商品{$statusText}成功");
            } else {
                return $this->error('更新商品状态失败');
            }
        } catch (\Exception $e) {
            Log::error('更新商品状态失败: ' . $e->getMessage());
            return $this->error('更新商品状态失败');
        }
    }

    /**
     * 批量操作商品
     * 
     * @return Response
     */
    public function batchOperation(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }
        
        try {
            $itemIds = $this->request->param('item_ids', []);
            $operation = $this->request->param('operation');
            
            if (empty($itemIds) || !is_array($itemIds)) {
                return $this->error('请选择要操作的商品', 400);
            }
            
            if (!in_array($operation, ['enable', 'disable', 'delete', 'update_category'])) {
                return $this->error('操作类型无效', 400);
            }
            
            $result = false;
            
            switch ($operation) {
                case 'enable':
                    $result = $this->itemService->batchUpdateStatus($itemIds, 1);
                    break;
                case 'disable':
                    $result = $this->itemService->batchUpdateStatus($itemIds, 0);
                    break;
                case 'delete':
                    $result = $this->itemService->batchDeleteItems($itemIds);
                    break;
                case 'update_category':
                    $category = $this->request->param('category');
                    $result = $this->itemService->batchUpdateCategory($itemIds, $category);
                    break;
            }
            
            if ($result) {
                $this->logAdminAction('batch_item_operation', "批量{$operation}商品", [
                    'item_ids' => $itemIds,
                    'operation' => $operation
                ]);
                
                return $this->success([], '批量操作成功');
            } else {
                return $this->error('批量操作失败');
            }
        } catch (\Exception $e) {
            Log::error('批量操作商品失败: ' . $e->getMessage());
            return $this->error('批量操作失败');
        }
    }

    /**
     * 导出商品数据
     *
     * @return Response
     */
    public function exportItems(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            $format = $this->request->param('format', 'csv');
            $filters = [
                'search' => $this->request->param('search', ''),
                'category' => $this->request->param('category', ''),
                'status' => $this->request->param('status', '')
            ];

            // 调用商品服务导出数据
            $result = $this->itemService->exportItems($format, $filters);

            if ($result) {
                $this->logAdminAction('export_items', '导出商品数据', [
                    'format' => $format,
                    'filters' => $filters
                ]);

                return $this->success(['download_url' => $result], '导出成功');
            } else {
                return $this->error('导出失败');
            }
        } catch (\Exception $e) {
            Log::error('导出商品数据失败: ' . $e->getMessage());
            return $this->error('导出失败');
        }
    }

    /**
     * 获取商品分类列表
     *
     * @return Response
     */
    public function getCategories(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            // 使用全局配置获取分类
            $allCategories = \app\service\ShopConfigService::getAllCategories();

            // 构建API返回格式
            $categories = [
                ['value' => '', 'label' => '全部分类']
            ];

            foreach ($allCategories as $id => $name) {
                $categories[] = [
                    'value' => (string)$id,
                    'label' => $name
                ];
            }

            return $this->success($categories);
        } catch (\Exception $e) {
            Log::error('获取商品分类失败: ' . $e->getMessage());
            return $this->error('获取商品分类失败');
        }
    }

    /**
     * 获取商品统计数据
     *
     * @return Response
     */
    private function getItemStats(): Response
    {
        try {
            // 先测试基本查询
            $totalItems = 0;
            $activeItems = 0;
            $inactiveItems = 0;

            try {
                // 获取商品总数
                $totalItems = Db::table('pointshop')->count();

                // 获取上架商品数 (status = 1)
                $activeItems = Db::table('pointshop')->where('status', 1)->count();

                // 获取下架商品数 (status = 2)
                $inactiveItems = Db::table('pointshop')->where('status', 2)->count();

            } catch (\Exception $e) {
                Log::error('查询pointshop表失败: ' . $e->getMessage());
                // 返回默认值而不是抛出错误
            }

            $stats = [
                'total' => $totalItems,
                'active' => $activeItems,
                'inactive' => $inactiveItems,
                'total_sales' => 0 // 暂时设为0
            ];

            Log::info('商品统计数据: ' . json_encode($stats));
            return $this->success($stats);

        } catch (\Exception $e) {
            Log::error('获取商品统计失败: ' . $e->getMessage());
            // 返回默认统计数据而不是错误
            return $this->success([
                'total' => 0,
                'active' => 0,
                'inactive' => 0,
                'total_sales' => 0
            ]);
        }
    }
}
