<?php
declare(strict_types=1);

namespace app\controller\admin;

use think\App;
use think\Response;
use think\facade\View;
use think\facade\Log;
use think\facade\Db;
use app\service\UserService;
use app\service\AdminManagementService;
use app\common\ApiResponse;

/**
 * 管理后台用户管理控制器
 * 负责用户的增删改查和状态管理
 */
class UserController extends BaseController
{
    /**
     * 用户服务
     * @var UserService
     */
    protected UserService $userService;

    /**
     * 管理服务
     * @var AdminManagementService
     */
    protected AdminManagementService $adminManagementService;

    /**
     * 构造函数
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->userService = new UserService();
        $this->adminManagementService = new AdminManagementService();
    }

    /**
     * 用户管理页面
     *
     * @return Response
     */
    public function index(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        // 设置页面数据
        View::assign([
            'pageTitle' => '用户管理',
            'pageIcon' => 'bi bi-people',
            'pageDescription' => '管理系统用户，查看用户信息和状态'
        ]);

        return response(View::fetch('admin/users'));
    }

    /**
     * 获取用户列表
     * 
     * @return Response
     */
    public function getUserList(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }
        
        try {
            $params = $this->getPaginationParams();
            $status = $this->request->param('status', '');
            $role = $this->request->param('role', '');

            // 调用用户服务获取用户列表
            $result = $this->userService->getUserListOptimized(
                $params['page'],
                $params['limit'],
                $params['search'],
                $status,
                $role
            );
            
            $this->logAdminAction('view_users', '查看用户列表', [
                'page' => $params['page'],
                'search' => $params['search']
            ]);
            
            return ApiResponse::success($result, 'success');
        } catch (\Exception $e) {
            Log::error('获取用户列表失败: ' . $e->getMessage());
            return ApiResponse::error('获取用户列表失败');
        }
    }

    /**
     * 获取用户详情
     *
     * @return Response
     */
    public function getUserDetail(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            $userId = $this->request->param('user_id');
            $username = $this->request->param('username');

            if (empty($userId) && empty($username)) {
                return $this->error('用户ID或用户名不能为空', 400);
            }

            // 获取用户详细信息
            if (!empty($username)) {
                $userDetail = $this->userService->getUserDetailByUsername($username);
                $identifier = $username;
            } else {
                $userDetail = $this->userService->getUserDetail($userId);
                $identifier = $userId;
            }

            if (!$userDetail) {
                return $this->error('用户不存在', 404);
            }

            $this->logAdminAction('view_user_detail', '查看用户详情', [
                'user_id' => $userDetail['id'] ?? null,
                'username' => $userDetail['username'] ?? $identifier
            ]);

            return $this->success($userDetail);
        } catch (\Exception $e) {
            Log::error('获取用户详情失败: ' . $e->getMessage());
            return $this->error('获取用户详情失败');
        }
    }

    /**
     * 获取用户统计信息
     * 
     * @return Response
     */
    public function getUserStats(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            // 获取总用户数
            $totalUsers = $this->userService->getTotalUserCount();

            // 获取激活用户数
            $activeUsers = $this->userService->getActiveUserCount();

            // 获取封禁用户数
            $bannedUsers = $this->userService->getBannedUserCount();

            // 获取在线用户数
            $onlineUsers = $this->getOnlineUserCount();

            // 获取今日新增用户数
            $newUsers = $this->getTodayNewUserCount();

            return $this->success([
                'total' => $totalUsers,
                'active' => $activeUsers,
                'banned' => $bannedUsers,
                'online_users' => $onlineUsers,
                'new_users' => $newUsers
            ]);

        } catch (\Exception $e) {
            Log::error('获取用户统计信息失败: ' . $e->getMessage());
            return $this->error('获取用户统计信息失败');
        }
    }

    /**
     * 更新用户状态
     *
     * @return Response
     */
    public function updateUserStatus(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            $userId = $this->request->param('user_id');
            $username = $this->request->param('username');
            $status = $this->request->param('status');
            $reason = $this->request->param('reason', '');

            if ((empty($userId) && empty($username)) || !in_array($status, ['0', '1'])) {
                return $this->error('参数错误', 400);
            }

            // 如果传递的是username，先获取用户ID
            if (!empty($username) && empty($userId)) {
                $userInfo = $this->userService->getUserByUsername($username);
                if (!$userInfo) {
                    return $this->error('用户不存在', 404);
                }
                $userId = $userInfo['id'];
            }

            // 更新用户状态
            $result = $this->userService->updateUserStatus($userId, (int)$status, $reason);

            if ($result) {
                $statusText = $status == '1' ? '启用' : '禁用';
                $this->logAdminAction('update_user_status', "用户状态{$statusText}", [
                    'user_id' => $userId,
                    'username' => $username,
                    'status' => $status,
                    'reason' => $reason
                ]);

                return $this->success([], '用户状态更新成功');
            } else {
                return $this->error('用户状态更新失败');
            }
        } catch (\Exception $e) {
            Log::error('更新用户状态失败: ' . $e->getMessage());
            return $this->error('更新用户状态失败');
        }
    }

    /**
     * 封禁用户
     *
     * @return Response
     */
    public function banUser(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            $username = $this->request->param('username');
            $reason = $this->request->param('reason', '管理员封禁');

            if (empty($username)) {
                return $this->error('用户名不能为空', 400);
            }

            // 封禁用户
            $result = $this->userService->banUser($username);

            if ($result) {
                $this->logAdminAction('ban_user', '封禁用户', [
                    'username' => $username,
                    'reason' => $reason
                ]);

                return $this->success([], '用户封禁成功');
            } else {
                return $this->error('用户封禁失败');
            }
        } catch (\Exception $e) {
            Log::error('封禁用户失败: ' . $e->getMessage());
            return $this->error('封禁用户失败');
        }
    }

    /**
     * 解封用户
     *
     * @return Response
     */
    public function unbanUser(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            $username = $this->request->param('username');
            $reason = $this->request->param('reason', '管理员解封');

            if (empty($username)) {
                return $this->error('用户名不能为空', 400);
            }

            // 解封用户
            $result = $this->userService->unbanUser($username);

            if ($result) {
                $this->logAdminAction('unban_user', '解封用户', [
                    'username' => $username,
                    'reason' => $reason
                ]);

                return $this->success([], '用户解封成功');
            } else {
                return $this->error('用户解封失败');
            }
        } catch (\Exception $e) {
            Log::error('解封用户失败: ' . $e->getMessage());
            return $this->error('解封用户失败');
        }
    }

    /**
     * 批量操作用户
     * 
     * @return Response
     */
    public function batchOperation(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }
        
        try {
            $userIds = $this->request->param('user_ids', []);
            $operation = $this->request->param('operation');
            $reason = $this->request->param('reason', '');
            
            if (empty($userIds) || !is_array($userIds)) {
                return $this->error('请选择要操作的用户', 400);
            }
            
            if (!in_array($operation, ['enable', 'disable', 'delete'])) {
                return $this->error('操作类型无效', 400);
            }
            
            $result = false;
            
            switch ($operation) {
                case 'enable':
                    $result = $this->userService->batchUpdateStatus($userIds, 1, $reason);
                    break;
                case 'disable':
                    $result = $this->userService->batchUpdateStatus($userIds, 0, $reason);
                    break;
                case 'delete':
                    $result = $this->userService->batchDeleteUsers($userIds, $reason);
                    break;
            }
            
            if ($result) {
                $this->logAdminAction('batch_user_operation', "批量{$operation}用户", [
                    'user_ids' => $userIds,
                    'operation' => $operation,
                    'reason' => $reason
                ]);
                
                return $this->success([], '批量操作成功');
            } else {
                return $this->error('批量操作失败');
            }
        } catch (\Exception $e) {
            Log::error('批量操作用户失败: ' . $e->getMessage());
            return $this->error('批量操作失败');
        }
    }

    /**
     * 导出用户数据
     * 
     * @return Response
     */
    public function exportUsers(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }
        
        try {
            $format = $this->request->param('format', 'csv');
            $filters = [
                'search' => $this->request->param('search', ''),
                'status' => $this->request->param('status', ''),
                'date_range' => $this->request->param('date_range', '')
            ];
            
            // 调用用户服务导出数据
            $result = $this->userService->exportUsers($format, $filters);
            
            if ($result) {
                $this->logAdminAction('export_users', '导出用户数据', [
                    'format' => $format,
                    'filters' => $filters
                ]);
                
                return $this->success(['download_url' => $result], '导出成功');
            } else {
                return $this->error('导出失败');
            }
        } catch (\Exception $e) {
            Log::error('导出用户数据失败: ' . $e->getMessage());
            return $this->error('导出失败');
        }
    }

    /**
     * 获取今日操作数
     * 
     * @return int
     */
    protected function getTodayActionCount(): int
    {
        try {
            // 这里可以从日志表或操作记录表获取今日操作数
            // 暂时返回0，后续可以实现具体逻辑
            return 0;
        } catch (\Exception $e) {
            Log::error('获取今日操作数失败: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * 获取在线用户数
     *
     * @return int
     */
    private function getOnlineUserCount(): int
    {
        try {
            // 从gdb0101数据库的pc表中获取在线用户数
            // play_flag = 0 表示离线，非0表示在线
            $onlineCount = Db::connect('gdb0101')
                ->table('pc')
                ->where('play_flag', '<>', 0)
                ->count();

            return (int)$onlineCount;
        } catch (\Exception $e) {
            Log::error('获取在线用户数失败: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * 获取今日新增用户数
     *
     * @return int
     */
    private function getTodayNewUserCount(): int
    {
        try {
            $todayCount = 0;

            // 从idtable1-idtable5表中统计今日新增用户
            for ($i = 1; $i <= 5; $i++) {
                $count = Db::connect('seal_member')
                    ->table('idtable' . $i)
                    ->whereTime('regist_date', 'today')
                    ->count();
                $todayCount += $count;
            }

            return $todayCount;
        } catch (\Exception $e) {
            Log::error('获取今日新增用户数失败: ' . $e->getMessage());
            return 0;
        }
    }
}
