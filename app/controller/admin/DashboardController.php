<?php
declare(strict_types=1);

namespace app\controller\admin;

use think\App;
use think\Response;
use think\facade\View;
use think\facade\Log;
use app\service\AdminManagementService;
use app\service\UserService;
use app\service\OrderService;
use app\middleware\AuthMiddleware;

/**
 * 管理后台仪表盘控制器
 * 负责管理面板首页和统计数据
 */
class DashboardController extends BaseController
{
    /**
     * 管理服务
     * @var AdminManagementService
     */
    protected AdminManagementService $adminManagementService;

    /**
     * 用户服务
     * @var UserService
     */
    protected UserService $userService;

    /**
     * 订单服务
     * @var OrderService
     */
    protected OrderService $orderService;

    /**
     * 构造函数
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->adminManagementService = new AdminManagementService();
        $this->userService = new UserService();
        $this->orderService = new OrderService();
    }

    /**
     * 管理面板主页
     * 
     * @return Response
     */
    public function index(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }
        
        // 获取管理员信息
        $adminInfo = $this->adminService->getAdminInfo();
        $userInfo = AuthMiddleware::getCurrentUserInfo();
        $nickname = AuthMiddleware::getCurrentUserNickname();

        // 获取当前管理员的最后登录时间
        $lastLoginTime = $this->getCurrentAdminLastLogin();

        // 获取系统信息
        $systemInfo = $this->getSystemInfo();

        // 传递数据到视图
        View::assign([
            'pageTitle' => '管理面板',
            'admin_info' => $adminInfo,
            'user_info' => $userInfo,
            'user_nickname' => $nickname,
            'last_login_time' => $lastLoginTime,
            'system_info' => $systemInfo
        ]);
        
        return response(View::fetch('admin/index_new'));
    }

    /**
     * 获取统计数据
     * 
     * @return Response
     */
    public function getStats(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            // 获取各种统计数据
            $stats = [
                'users' => $this->getUserStats(),
                'items' => $this->getItemStats(),
                'orders' => $this->getOrderStats(),
                'revenue' => $this->getRevenueStats(),
                'system' => $this->getSystemStats()
            ];

            return $this->success($stats);
        } catch (\Exception $e) {
            Log::error('获取统计数据失败: ' . $e->getMessage());
            return $this->error('获取统计数据失败');
        }
    }

    /**
     * 获取用户统计
     * 
     * @return array
     */
    protected function getUserStats(): array
    {
        try {
            $totalUsers = $this->userService->getTotalUserCount();
            $activeUsers = $this->userService->getActiveUserCount();
            $newUsers = $this->userService->getTodayNewUserCount();
            $onlineUsers = $this->userService->getOnlineUserCount();

            return [
                'total' => $totalUsers,
                'active' => $activeUsers,
                'new_today' => $newUsers,
                'online' => $onlineUsers,
                'growth_rate' => $this->calculateUserGrowthRate()
            ];
        } catch (\Exception $e) {
            Log::error('获取用户统计失败: ' . $e->getMessage());
            return [
                'total' => 0,
                'active' => 0,
                'new_today' => 0,
                'online' => 0,
                'growth_rate' => 0
            ];
        }
    }

    /**
     * 获取商品统计
     * 
     * @return array
     */
    protected function getItemStats(): array
    {
        try {
            // 这里需要实现商品统计逻辑
            return [
                'total' => 0,
                'active' => 0,
                'inactive' => 0,
                'categories' => 0
            ];
        } catch (\Exception $e) {
            Log::error('获取商品统计失败: ' . $e->getMessage());
            return [
                'total' => 0,
                'active' => 0,
                'inactive' => 0,
                'categories' => 0
            ];
        }
    }

    /**
     * 获取订单统计
     * 
     * @return array
     */
    protected function getOrderStats(): array
    {
        try {
            $totalOrders = $this->orderService->getTotalOrderCount();
            $todayOrders = $this->orderService->getTodayOrderCount();
            $pendingOrders = $this->orderService->getPendingOrderCount();

            return [
                'total' => $totalOrders,
                'today' => $todayOrders,
                'pending' => $pendingOrders,
                'completed' => $totalOrders - $pendingOrders
            ];
        } catch (\Exception $e) {
            Log::error('获取订单统计失败: ' . $e->getMessage());
            return [
                'total' => 0,
                'today' => 0,
                'pending' => 0,
                'completed' => 0
            ];
        }
    }

    /**
     * 获取收入统计
     * 
     * @return array
     */
    protected function getRevenueStats(): array
    {
        try {
            $totalRevenue = $this->orderService->getTotalRevenue();
            $todayRevenue = $this->orderService->getTodayRevenue();
            $monthRevenue = $this->orderService->getMonthRevenue();

            return [
                'total' => $totalRevenue,
                'today' => $todayRevenue,
                'month' => $monthRevenue,
                'growth_rate' => $this->calculateRevenueGrowthRate()
            ];
        } catch (\Exception $e) {
            Log::error('获取收入统计失败: ' . $e->getMessage());
            return [
                'total' => 0,
                'today' => 0,
                'month' => 0,
                'growth_rate' => 0
            ];
        }
    }

    /**
     * 获取系统统计
     *
     * @return array
     */
    protected function getSystemStats(): array
    {
        try {
            return [
                'cpu_usage' => $this->getCpuUsage(),
                'memory_usage' => $this->getMemoryUsage(),
                'disk_usage' => $this->getDiskUsage(),
                'database_status' => $this->getDatabaseStatus()
            ];
        } catch (\Exception $e) {
            Log::error('获取系统统计失败: ' . $e->getMessage());
            return [
                'cpu_usage' => 0,
                'memory_usage' => 0,
                'disk_usage' => 0,
                'database_status' => 'unknown'
            ];
        }
    }

    /**
     * 异步更新统计数据
     *
     * @return Response
     */
    public function updateStatsAsync(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            // 异步更新统计数据
            $result = $this->adminManagementService->updateStatsAsync();

            if ($result) {
                return $this->success([], '统计数据更新成功');
            } else {
                return $this->error('统计数据更新失败');
            }
        } catch (\Exception $e) {
            Log::error('异步更新统计数据失败: ' . $e->getMessage());
            return $this->error('异步更新统计数据失败');
        }
    }

    /**
     * 获取最近活动
     * 
     * @return Response
     */
    public function getRecentActivity(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            $recentOrders = $this->orderService->getRecentOrders(10);
            $recentUsers = $this->userService->getRecentUsers(10);

            return $this->success([
                'orders' => $recentOrders,
                'users' => $recentUsers
            ]);
        } catch (\Exception $e) {
            Log::error('获取最近活动失败: ' . $e->getMessage());
            return $this->error('获取最近活动失败');
        }
    }

    /**
     * 获取当前管理员最后登录时间
     * 
     * @return string|null
     */
    protected function getCurrentAdminLastLogin(): ?string
    {
        $admin = $this->getCurrentAdmin();
        return $admin['last_login'] ?? null;
    }

    /**
     * 获取系统信息
     * 
     * @return array
     */
    protected function getSystemInfo(): array
    {
        return [
            'php_version' => PHP_VERSION,
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'system_load' => $this->getSystemLoad(),
            'uptime' => $this->getSystemUptime()
        ];
    }

    /**
     * 计算用户增长率
     * 
     * @return float
     */
    protected function calculateUserGrowthRate(): float
    {
        // 实现用户增长率计算逻辑
        return 0.0;
    }

    /**
     * 计算收入增长率
     * 
     * @return float
     */
    protected function calculateRevenueGrowthRate(): float
    {
        // 实现收入增长率计算逻辑
        return 0.0;
    }

    /**
     * 获取CPU使用率
     * 
     * @return float
     */
    protected function getCpuUsage(): float
    {
        // 实现CPU使用率获取逻辑
        return 0.0;
    }

    /**
     * 获取内存使用率
     * 
     * @return float
     */
    protected function getMemoryUsage(): float
    {
        $memInfo = memory_get_usage(true);
        $memLimit = ini_get('memory_limit');
        
        if ($memLimit == -1) {
            return 0.0;
        }
        
        $memLimitBytes = $this->convertToBytes($memLimit);
        return ($memInfo / $memLimitBytes) * 100;
    }

    /**
     * 获取磁盘使用率
     * 
     * @return float
     */
    protected function getDiskUsage(): float
    {
        $totalBytes = disk_total_space('.');
        $freeBytes = disk_free_space('.');
        
        if ($totalBytes === false || $freeBytes === false) {
            return 0.0;
        }
        
        return (($totalBytes - $freeBytes) / $totalBytes) * 100;
    }

    /**
     * 获取数据库状态
     * 
     * @return string
     */
    protected function getDatabaseStatus(): string
    {
        try {
            // 简单的数据库连接测试
            \think\facade\Db::query('SELECT 1');
            return 'connected';
        } catch (\Exception $e) {
            return 'disconnected';
        }
    }

    /**
     * 获取系统负载
     * 
     * @return array|null
     */
    protected function getSystemLoad(): ?array
    {
        if (function_exists('sys_getloadavg')) {
            $load = sys_getloadavg();
            return [
                '1min' => $load[0],
                '5min' => $load[1],
                '15min' => $load[2]
            ];
        }
        
        return null;
    }

    /**
     * 获取系统运行时间
     * 
     * @return string|null
     */
    protected function getSystemUptime(): ?string
    {
        if (file_exists('/proc/uptime')) {
            $uptime = file_get_contents('/proc/uptime');
            $uptimeSeconds = (float)explode(' ', $uptime)[0];
            
            $days = floor($uptimeSeconds / 86400);
            $hours = floor(($uptimeSeconds % 86400) / 3600);
            $minutes = floor(($uptimeSeconds % 3600) / 60);
            
            return "{$days}天 {$hours}小时 {$minutes}分钟";
        }
        
        return null;
    }

    /**
     * 转换内存单位为字节
     * 
     * @param string $val
     * @return int
     */
    protected function convertToBytes(string $val): int
    {
        $val = trim($val);
        $last = strtolower($val[strlen($val) - 1]);
        $val = (int)$val;
        
        switch ($last) {
            case 'g':
                $val *= 1024;
            case 'm':
                $val *= 1024;
            case 'k':
                $val *= 1024;
        }
        
        return $val;
    }
}
