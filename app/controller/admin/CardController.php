<?php

namespace app\controller\admin;

use app\service\CardService;
use think\facade\Log;
use think\facade\Db;
use think\Response;
use think\App;

/**
 * 周卡月卡管理控制器
 */
class CardController extends BaseController
{
    /**
     * 卡片服务
     */
    protected CardService $cardService;

    /**
     * 构造函数
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->cardService = new CardService();
    }

    /**
     * 卡片管理主页面
     */
    public function index(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        return view('admin/cards', [
            'pageTitle' => '周卡月卡管理',
            'pageIcon' => 'bi bi-credit-card-2-front',
            'pageDescription' => '管理周卡月卡配置和用户卡片'
        ]);
    }

    /**
     * 获取卡片配置列表
     */
    public function getCardConfigs(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            $cards = $this->cardService->getAvailableCards();

            return $this->success(['cards' => $cards], '获取卡片配置成功');
        } catch (\Exception $e) {
            Log::error('获取卡片配置失败: ' . $e->getMessage());
            return $this->error('获取卡片配置失败');
        }
    }

    /**
     * 获取用户卡片列表
     */
    public function getUserCards(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            $page = $this->request->param('page', 1);
            $limit = $this->request->param('limit', 20);
            $search = $this->request->param('search', '');
            $cardType = $this->request->param('card_type', '');
            $status = $this->request->param('status', '');

            $query = Db::table('user_cards uc')
                ->leftJoin('card_config cc', 'uc.card_id = cc.id')
                ->field('uc.*, cc.name as card_name, cc.card_type, cc.duration_days')
                ->order('uc.id desc');

            // 搜索条件
            if (!empty($search)) {
                $query->where('uc.user_id', 'like', '%' . $search . '%');
            }

            if (!empty($cardType)) {
                $query->where('cc.card_type', $cardType);
            }

            if (!empty($status)) {
                if ($status === 'active') {
                    $query->where('uc.expire_time', '>', date('Y-m-d H:i:s'));
                } elseif ($status === 'expired') {
                    $query->where('uc.expire_time', '<=', date('Y-m-d H:i:s'));
                }
            }

            $total = $query->count();
            $userCards = $query->page($page, $limit)->select()->toArray();

            // 处理数据
            foreach ($userCards as &$userCard) {
                $userCard['remaining_days'] = max(0, ceil((strtotime($userCard['expire_time']) - time()) / 86400));
                $userCard['is_expired'] = strtotime($userCard['expire_time']) <= time();
                $userCard['progress_percent'] = $userCard['duration_days'] > 0 ? 
                    round(($userCard['claimed_days'] / $userCard['duration_days']) * 100, 1) : 0;
            }

            return $this->success([
                'list' => $userCards,
                'total' => $total,
                'page' => $page,
                'limit' => $limit
            ], '获取用户卡片列表成功');

        } catch (\Exception $e) {
            Log::error('获取用户卡片列表失败: ' . $e->getMessage());
            return $this->error('获取用户卡片列表失败');
        }
    }

    /**
     * 手动发放卡片
     */
    public function grantCard(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            $userId = $this->request->param('user_id');
            $cardId = $this->request->param('card_id');
            $duration = $this->request->param('duration');
            $reason = $this->request->param('reason', '管理员发放');

            // 验证参数
            if (empty($userId) || empty($cardId)) {
                return $this->error('用户ID和卡片ID不能为空', 400);
            }

            if (empty($duration) || $duration <= 0) {
                return $this->error('有效期必须大于0', 400);
            }

            // 获取卡片配置
            $cardConfig = Db::table('card_config')->where('id', $cardId)->find();
            if (!$cardConfig) {
                return $this->error('卡片配置不存在', 400);
            }

            // 检查用户是否已有该类型的卡片
            $existingCard = Db::table('user_cards')
                ->where('user_id', $userId)
                ->where('card_id', $cardId)
                ->where('expire_time', '>', date('Y-m-d H:i:s'))
                ->find();

            if ($existingCard) {
                return $this->error('用户已拥有该类型的有效卡片', 400);
            }

            Db::startTrans();
            try {
                // 创建用户卡片记录
                $expireTime = date('Y-m-d H:i:s', time() + $duration * 24 * 3600);
                $userCardId = Db::table('user_cards')->insertGetId([
                    'user_id' => $userId,
                    'card_id' => $cardId,
                    'card_type' => $cardConfig['card_type'],
                    'purchase_time' => date('Y-m-d H:i:s'),
                    'expire_time' => $expireTime,
                    'total_days' => $duration,
                    'claimed_days' => 0,
                    'last_claim_date' => null,
                    'instant_reward_claimed' => 1, // 标记即时奖励已发放
                    'status' => 1,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);

                // 发放即时奖励
                $instantRewards = json_decode($cardConfig['instant_reward'], true) ?: [];

                if (!empty($instantRewards)) {
                    $result = $this->cardService->grantRewards($userId, $instantRewards, $reason);

                    if (!$result['success']) {
                        throw new \Exception('发放即时奖励失败: ' . $result['message']);
                    }

                    Log::info("管理员发放卡片即时奖励成功 - 用户: {$userId}, 卡片: {$cardConfig['name']}");
                }

                Db::commit();

                // 清除用户余额缓存，确保前端显示最新余额
                $this->clearUserBalanceCache($userId);

                // 记录管理员操作日志
                $this->logAdminAction('grant_card', '发放卡片', [
                    'user_id' => $userId,
                    'card_id' => $cardId,
                    'card_name' => $cardConfig['name'],
                    'duration' => $duration,
                    'reason' => $reason
                ]);

                return $this->success([
                    'user_card_id' => $userCardId,
                    'card_name' => $cardConfig['name'],
                    'duration' => $duration,
                    'expire_time' => $expireTime
                ], '卡片发放成功');

            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            Log::error('发放卡片失败: ' . $e->getMessage());
            return $this->error('发放卡片失败: ' . $e->getMessage());
        }
    }

    /**
     * 延长卡片有效期
     */
    public function extendCard(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            $userCardId = $this->request->param('user_card_id');
            $extendDays = $this->request->param('extend_days');
            $reason = $this->request->param('reason', '管理员延期');

            // 验证参数
            if (empty($userCardId) || empty($extendDays)) {
                return $this->error('用户卡片ID和延长天数不能为空', 400);
            }

            if ($extendDays <= 0) {
                return $this->error('延长天数必须大于0', 400);
            }

            // 获取用户卡片信息
            $userCard = Db::table('user_cards uc')
                ->leftJoin('card_config cc', 'uc.card_id = cc.id')
                ->field('uc.*, cc.name as card_name')
                ->where('uc.id', $userCardId)
                ->find();

            if (!$userCard) {
                return $this->error('用户卡片不存在', 400);
            }

            // 计算新的过期时间
            $currentExpireTime = strtotime($userCard['expire_time']);
            $newExpireTime = date('Y-m-d H:i:s', $currentExpireTime + $extendDays * 24 * 3600);

            // 更新卡片有效期
            $result = Db::table('user_cards')
                ->where('id', $userCardId)
                ->update([
                    'expire_time' => $newExpireTime,
                    'update_time' => date('Y-m-d H:i:s')
                ]);

            if ($result) {
                // 记录管理员操作日志
                $this->logAdminAction('extend_card', '延长卡片', [
                    'user_card_id' => $userCardId,
                    'user_id' => $userCard['user_id'],
                    'card_name' => $userCard['card_name'],
                    'extend_days' => $extendDays,
                    'old_expire_time' => $userCard['expire_time'],
                    'new_expire_time' => $newExpireTime,
                    'reason' => $reason
                ]);

                return $this->success([
                    'new_expire_time' => $newExpireTime,
                    'extend_days' => $extendDays
                ], '卡片延期成功');
            } else {
                return $this->error('卡片延期失败');
            }

        } catch (\Exception $e) {
            Log::error('延长卡片失败: ' . $e->getMessage());
            return $this->error('延长卡片失败: ' . $e->getMessage());
        }
    }

    /**
     * 手动为用户发放每日奖励
     */
    public function grantDailyReward(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            $userCardId = $this->request->param('user_card_id');
            $reason = $this->request->param('reason', '管理员发放每日奖励');

            // 验证参数
            if (empty($userCardId)) {
                return $this->error('用户卡片ID不能为空', 400);
            }

            // 获取用户卡片信息
            $userCard = Db::table('user_cards uc')
                ->leftJoin('card_config cc', 'uc.card_id = cc.id')
                ->field('uc.*, cc.name as card_name, cc.daily_reward')
                ->where('uc.id', $userCardId)
                ->find();

            if (!$userCard) {
                return $this->error('用户卡片不存在', 400);
            }

            // 检查卡片是否过期
            if (strtotime($userCard['expire_time']) <= time()) {
                return $this->error('卡片已过期', 400);
            }

            // 发放每日奖励
            $dailyRewards = json_decode($userCard['daily_reward'], true) ?: [];
            if (empty($dailyRewards)) {
                return $this->error('该卡片没有配置每日奖励', 400);
            }

            Db::startTrans();
            try {
                // 发放奖励
                $result = $this->cardService->grantRewards($userCard['user_id'], $dailyRewards, $reason);
                if (!$result['success']) {
                    throw new \Exception('发放每日奖励失败: ' . $result['message']);
                }

                // 更新领取记录
                Db::table('user_cards')
                    ->where('id', $userCardId)
                    ->update([
                        'claimed_days' => $userCard['claimed_days'] + 1,
                        'last_claim_date' => date('Y-m-d'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);

                Db::commit();

                // 清除用户余额缓存，确保前端显示最新余额
                $this->clearUserBalanceCache($userCard['user_id']);

                // 记录管理员操作日志
                $this->logAdminAction('grant_daily_reward', '发放每日奖励', [
                    'user_card_id' => $userCardId,
                    'user_id' => $userCard['user_id'],
                    'card_name' => $userCard['card_name'],
                    'claimed_days' => $userCard['claimed_days'] + 1,
                    'reason' => $reason
                ]);

                return $this->success([
                    'claimed_days' => $userCard['claimed_days'] + 1,
                    'last_claim_date' => date('Y-m-d')
                ], '每日奖励发放成功');

            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            Log::error('发放每日奖励失败: ' . $e->getMessage());
            return $this->error('发放每日奖励失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除用户卡片
     */
    public function deleteUserCard(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            $userCardId = $this->request->param('user_card_id');
            $reason = $this->request->param('reason', '管理员删除');

            // 验证参数
            if (empty($userCardId)) {
                return $this->error('用户卡片ID不能为空', 400);
            }

            // 获取用户卡片信息
            $userCard = Db::table('user_cards uc')
                ->leftJoin('card_config cc', 'uc.card_id = cc.id')
                ->field('uc.*, cc.name as card_name')
                ->where('uc.id', $userCardId)
                ->find();

            if (!$userCard) {
                return $this->error('用户卡片不存在', 400);
            }

            // 删除用户卡片
            $result = Db::table('user_cards')->where('id', $userCardId)->delete();

            if ($result) {
                // 记录管理员操作日志
                $this->logAdminAction('delete_user_card', '删除用户卡片', [
                    'user_card_id' => $userCardId,
                    'user_id' => $userCard['user_id'],
                    'card_name' => $userCard['card_name'],
                    'expire_time' => $userCard['expire_time'],
                    'claimed_days' => $userCard['claimed_days'],
                    'reason' => $reason
                ]);

                return $this->success(null, '用户卡片删除成功');
            } else {
                return $this->error('用户卡片删除失败');
            }

        } catch (\Exception $e) {
            Log::error('删除用户卡片失败: ' . $e->getMessage());
            return $this->error('删除用户卡片失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取卡片统计信息
     */
    public function getCardStats(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            // 总卡片数量
            $totalCards = Db::table('user_cards')->count();

            // 活跃卡片数量
            $activeCards = Db::table('user_cards')
                ->where('expire_time', '>', date('Y-m-d H:i:s'))
                ->count();

            // 过期卡片数量
            $expiredCards = $totalCards - $activeCards;

            // 今日新增卡片
            $todayCards = Db::table('user_cards')
                ->whereTime('purchase_time', 'today')
                ->count();

            // 按类型统计
            $cardTypeStats = Db::table('user_cards uc')
                ->leftJoin('card_config cc', 'uc.card_id = cc.id')
                ->field('cc.card_type, cc.name, COUNT(*) as count')
                ->where('uc.expire_time', '>', date('Y-m-d H:i:s'))
                ->group('cc.card_type, cc.name')
                ->select()
                ->toArray();

            return $this->success([
                'total_cards' => $totalCards,
                'active_cards' => $activeCards,
                'expired_cards' => $expiredCards,
                'today_cards' => $todayCards,
                'card_type_stats' => $cardTypeStats
            ], '获取卡片统计成功');

        } catch (\Exception $e) {
            Log::error('获取卡片统计失败: ' . $e->getMessage());
            return $this->error('获取卡片统计失败');
        }
    }

    /**
     * 获取单个卡片配置
     */
    public function getCardConfig(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            $cardId = $this->request->param('card_id');

            if (empty($cardId)) {
                return $this->error('卡片ID不能为空', 400);
            }

            $card = Db::table('card_config')->where('id', $cardId)->find();
            if (!$card) {
                return $this->error('卡片配置不存在', 400);
            }

            // 解析奖励配置
            $card['instant_reward_parsed'] = json_decode($card['instant_reward'], true) ?: [];
            $card['daily_reward_parsed'] = json_decode($card['daily_reward'], true) ?: [];

            return $this->success($card, '获取卡片配置成功');

        } catch (\Exception $e) {
            Log::error('获取卡片配置失败: ' . $e->getMessage());
            return $this->error('获取卡片配置失败');
        }
    }

    /**
     * 更新卡片配置
     */
    public function updateCardConfig(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            $cardId = $this->request->param('card_id');
            $name = $this->request->param('name');
            $description = $this->request->param('description');
            $price = $this->request->param('price');
            $durationDays = $this->request->param('duration_days');
            $icon = $this->request->param('icon');
            $instantReward = $this->request->param('instant_reward');
            $dailyReward = $this->request->param('daily_reward');
            $status = $this->request->param('status', 1);
            $sortOrder = $this->request->param('sort_order', 0);

            // 验证参数
            if (empty($cardId) || empty($name) || empty($price) || empty($durationDays)) {
                return $this->error('必填参数不能为空', 400);
            }

            if ($price <= 0) {
                return $this->error('价格必须大于0', 400);
            }

            if ($durationDays <= 0) {
                return $this->error('有效期必须大于0天', 400);
            }

            // 验证奖励配置格式
            if (!empty($instantReward)) {
                $instantRewardArray = json_decode($instantReward, true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    return $this->error('即时奖励配置格式错误', 400);
                }
            }

            if (!empty($dailyReward)) {
                $dailyRewardArray = json_decode($dailyReward, true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    return $this->error('每日奖励配置格式错误', 400);
                }
            }

            // 检查卡片是否存在
            $existingCard = Db::table('card_config')->where('id', $cardId)->find();
            if (!$existingCard) {
                return $this->error('卡片配置不存在', 400);
            }

            // 更新卡片配置
            $updateData = [
                'name' => $name,
                'description' => $description,
                'price' => $price,
                'duration_days' => $durationDays,
                'icon' => $icon,
                'instant_reward' => $instantReward ?: '[]',
                'daily_reward' => $dailyReward ?: '[]',
                'status' => $status,
                'sort_order' => $sortOrder,
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $result = Db::table('card_config')
                ->where('id', $cardId)
                ->update($updateData);

            if ($result) {
                // 记录管理员操作日志
                $this->logAdminAction('update_card_config', '更新卡片配置', [
                    'card_id' => $cardId,
                    'card_name' => $name,
                    'old_data' => $existingCard,
                    'new_data' => $updateData
                ]);

                return $this->success([
                    'card_id' => $cardId,
                    'card_name' => $name
                ], '卡片配置更新成功');
            } else {
                return $this->error('卡片配置更新失败');
            }

        } catch (\Exception $e) {
            Log::error('更新卡片配置失败: ' . $e->getMessage());
            return $this->error('更新卡片配置失败: ' . $e->getMessage());
        }
    }

    /**
     * 清除用户余额缓存
     *
     * @param string $userId 用户ID
     * @return void
     */
    private function clearUserBalanceCache(string $userId): void
    {
        try {
            // 清除用户余额缓存
            $cacheKey = 'user_balance:' . $userId;
            \app\service\CacheService::delete($cacheKey);

            // 清除泡点缓存
            $coinCacheKey = 'user_balance:coin:' . $userId;
            \app\service\CacheService::delete($coinCacheKey);

            // 清除用户列表缓存（如果用户在管理界面中）
            $userCacheService = new \app\service\UserCacheService();
            $userCacheService->clearUserListCache();

            Log::info("已清除用户 {$userId} 的余额缓存");
        } catch (\Exception $e) {
            Log::warning("清除用户余额缓存失败: " . $e->getMessage());
        }
    }
}
