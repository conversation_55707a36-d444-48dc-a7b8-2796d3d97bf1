<?php
declare(strict_types=1);

namespace app\controller\admin;

use think\Response;
use think\facade\View;
use think\facade\Log;

/**
 * 管理后台管理员管理控制器
 * 负责管理员账户的增删改查
 */
class AdminController extends BaseController
{
    /**
     * 管理员管理页面
     * 
     * @return Response
     */
    public function index(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        // 设置页面数据
        View::assign([
            'pageTitle' => '管理员管理',
            'pageIcon' => 'bi bi-person-gear',
            'pageDescription' => '管理系统管理员账户'
        ]);
        
        return response(View::fetch('admin/admin_management'));
    }

    /**
     * 获取管理员列表
     * 
     * @return Response
     */
    public function getAdminList(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            $configPath = app()->getConfigPath() . 'admin_users.php';
            $admins = [];

            if (file_exists($configPath)) {
                $admins = include $configPath;
            }

            // 转换为前端需要的格式
            $adminList = [];
            foreach ($admins as $username => $admin) {
                $adminList[] = [
                    'username' => $username,
                    'email' => $admin['email'] ?? '',
                    'level' => $admin['level'] ?? 2,
                    'status' => $admin['status'] ?? 1,
                    'create_time' => $admin['created_at'] ?? '',
                    'last_login' => $admin['last_login'] ?? ''
                ];
            }

            $this->logAdminAction('view_admin_list', '查看管理员列表');

            return $this->success($adminList);
        } catch (\Exception $e) {
            Log::error('获取管理员列表失败: ' . $e->getMessage());
            return $this->error('获取管理员列表失败');
        }
    }

    /**
     * 获取管理员详情
     * 
     * @return Response
     */
    public function getAdminDetail(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            $username = $this->request->param('username');

            if (empty($username)) {
                return $this->error('用户名不能为空', 400);
            }

            $configPath = app()->getConfigPath() . 'admin_users.php';
            $admins = [];

            if (file_exists($configPath)) {
                $admins = include $configPath;
            }

            if (!isset($admins[$username])) {
                return $this->error('管理员不存在', 404);
            }

            $admin = $admins[$username];

            // 返回管理员信息（不包含密码）
            $adminInfo = [
                'username' => $username,
                'email' => $admin['email'] ?? '',
                'level' => $admin['level'] ?? 2,
                'status' => $admin['status'] ?? 1,
                'create_time' => $admin['created_at'] ?? '',
                'last_login' => $admin['last_login'] ?? ''
            ];

            $this->logAdminAction('view_admin_detail', '查看管理员详情', ['username' => $username]);

            return $this->success($adminInfo);
        } catch (\Exception $e) {
            Log::error('获取管理员详情失败: ' . $e->getMessage());
            return $this->error('获取管理员详情失败');
        }
    }

    /**
     * 添加管理员
     * 
     * @return Response
     */
    public function addAdmin(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            $data = $this->request->post();

            // 验证必填字段
            if (empty($data['username']) || empty($data['password'])) {
                return $this->error('用户名和密码不能为空', 400);
            }

            // 验证用户名格式
            if (!preg_match('/^[a-zA-Z0-9_]{3,20}$/', $data['username'])) {
                return $this->error('用户名只能包含字母、数字和下划线，长度3-20位', 400);
            }

            // 验证密码强度
            if (strlen($data['password']) < 6) {
                return $this->error('密码长度不能少于6位', 400);
            }

            // 验证邮箱格式
            if (!empty($data['email']) && !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
                return $this->error('邮箱格式不正确', 400);
            }

            $configPath = app()->getConfigPath() . 'admin_users.php';
            $admins = [];

            if (file_exists($configPath)) {
                $admins = include $configPath;
            }

            // 检查用户名是否已存在
            if (isset($admins[$data['username']])) {
                return $this->error('用户名已存在', 400);
            }

            // 添加新管理员
            $admins[$data['username']] = [
                'password' => password_hash($data['password'], PASSWORD_DEFAULT),
                'level' => (int)($data['level'] ?? 2),
                'email' => $data['email'] ?? '',
                'created_at' => date('Y-m-d H:i:s'),
                'status' => 1,
                'last_login' => ''
            ];

            // 保存配置
            $configContent = "<?php\nreturn " . var_export($admins, true) . ";\n";
            $result = file_put_contents($configPath, $configContent);

            if ($result === false) {
                return $this->error('保存配置文件失败，请检查文件权限');
            }

            $this->logAdminAction('add_admin', '添加管理员', [
                'new_admin' => $data['username'],
                'level' => $data['level'] ?? 2
            ]);

            return $this->success([], '管理员添加成功');
        } catch (\Exception $e) {
            Log::error('添加管理员失败: ' . $e->getMessage());
            return $this->error('添加管理员失败');
        }
    }

    /**
     * 更新管理员
     * 
     * @return Response
     */
    public function updateAdmin(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            $username = $this->request->param('username');
            $data = $this->request->post();

            if (empty($username)) {
                return $this->error('用户名不能为空', 400);
            }

            $configPath = app()->getConfigPath() . 'admin_users.php';
            $admins = [];

            if (file_exists($configPath)) {
                $admins = include $configPath;
            }

            if (!isset($admins[$username])) {
                return $this->error('管理员不存在', 404);
            }

            // 更新管理员信息
            if (isset($data['email'])) {
                if (!empty($data['email']) && !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
                    return $this->error('邮箱格式不正确', 400);
                }
                $admins[$username]['email'] = $data['email'];
            }

            if (isset($data['level'])) {
                $admins[$username]['level'] = (int)$data['level'];
            }

            if (isset($data['status'])) {
                $admins[$username]['status'] = (int)$data['status'];
            }

            // 如果提供了新密码，则更新密码
            if (!empty($data['password'])) {
                if (strlen($data['password']) < 6) {
                    return $this->error('密码长度不能少于6位', 400);
                }
                $admins[$username]['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
            }

            // 保存配置
            $configContent = "<?php\nreturn " . var_export($admins, true) . ";\n";
            $result = file_put_contents($configPath, $configContent);

            if ($result === false) {
                return $this->error('保存配置文件失败，请检查文件权限');
            }

            $this->logAdminAction('update_admin', '更新管理员', [
                'target_admin' => $username,
                'updated_fields' => array_keys($data)
            ]);

            return $this->success([], '管理员信息更新成功');
        } catch (\Exception $e) {
            Log::error('更新管理员失败: ' . $e->getMessage());
            return $this->error('更新管理员失败');
        }
    }

    /**
     * 删除管理员
     * 
     * @return Response
     */
    public function deleteAdmin(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            $username = $this->request->param('username');

            if (empty($username)) {
                return $this->error('用户名不能为空', 400);
            }

            // 不能删除自己
            $currentAdmin = $this->getCurrentAdmin();
            if ($currentAdmin && $currentAdmin['username'] === $username) {
                return $this->error('不能删除自己的账户', 400);
            }

            $configPath = app()->getConfigPath() . 'admin_users.php';
            $admins = [];

            if (file_exists($configPath)) {
                $admins = include $configPath;
            }

            if (!isset($admins[$username])) {
                return $this->error('管理员不存在', 404);
            }

            // 删除管理员
            unset($admins[$username]);

            // 保存配置
            $configContent = "<?php\nreturn " . var_export($admins, true) . ";\n";
            $result = file_put_contents($configPath, $configContent);

            if ($result === false) {
                return $this->error('保存配置文件失败，请检查文件权限');
            }

            $this->logAdminAction('delete_admin', '删除管理员', ['deleted_admin' => $username]);

            return $this->success([], '管理员删除成功');
        } catch (\Exception $e) {
            Log::error('删除管理员失败: ' . $e->getMessage());
            return $this->error('删除管理员失败');
        }
    }

    /**
     * 重置管理员密码
     * 
     * @return Response
     */
    public function resetPassword(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            $username = $this->request->param('username');
            $newPassword = $this->request->param('new_password');

            if (empty($username) || empty($newPassword)) {
                return $this->error('用户名和新密码不能为空', 400);
            }

            if (strlen($newPassword) < 6) {
                return $this->error('密码长度不能少于6位', 400);
            }

            $configPath = app()->getConfigPath() . 'admin_users.php';
            $admins = [];

            if (file_exists($configPath)) {
                $admins = include $configPath;
            }

            if (!isset($admins[$username])) {
                return $this->error('管理员不存在', 404);
            }

            // 重置密码
            $admins[$username]['password'] = password_hash($newPassword, PASSWORD_DEFAULT);

            // 保存配置
            $configContent = "<?php\nreturn " . var_export($admins, true) . ";\n";
            $result = file_put_contents($configPath, $configContent);

            if ($result === false) {
                return $this->error('保存配置文件失败，请检查文件权限');
            }

            $this->logAdminAction('reset_admin_password', '重置管理员密码', ['target_admin' => $username]);

            return $this->success([], '密码重置成功');
        } catch (\Exception $e) {
            Log::error('重置管理员密码失败: ' . $e->getMessage());
            return $this->error('重置管理员密码失败');
        }
    }
}
