<?php
declare(strict_types=1);

namespace app\controller\admin;

use app\BaseController as AppBaseController;
use think\App;
use think\Response;
use think\facade\Log;
use think\facade\Session;
use app\service\AdminService;

/**
 * 管理后台基础控制器
 * 提供通用的认证、权限检查等功能
 */
abstract class BaseController extends AppBaseController
{
    /**
     * 管理员服务
     * @var AdminService
     */
    protected AdminService $adminService;

    /**
     * 构造函数
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->adminService = new AdminService();
    }

    /**
     * 检查管理员权限
     * 
     * @return Response|null 如果认证失败返回Response，成功返回null
     */
    protected function checkAdminAuth(): ?Response
    {
        try {
            // 添加调试信息
            Log::info('checkAdminAuth 开始检查', [
                'url' => $this->request->url(),
                'path' => $this->request->pathinfo(),
                'is_ajax' => $this->request->isAjax(),
                'accept_header' => $this->request->header('Accept'),
                'is_api_request' => $this->isApiRequest()
            ]);

            // 检查是否已登录
            if (!$this->isAdminLoggedIn()) {
                Log::info('用户未登录');
                return $this->redirectToLogin();
            }

            Log::info('用户已登录，检查管理员权限');

            // 检查管理员权限
            if (!$this->hasAdminPermission()) {
                Log::info('用户没有管理员权限');
                return $this->accessDenied();
            }

            Log::info('管理员权限检查通过');
            Log::info('checkAdminAuth 返回 null，认证成功');
            return null;
        } catch (\Exception $e) {
            Log::error('管理员权限检查失败: ' . $e->getMessage());
            return $this->redirectToLogin();
        }
    }

    /**
     * 检查是否已登录
     * 
     * @return bool
     */
    protected function isAdminLoggedIn(): bool
    {
        $userId = Session::get('user_id');
        $userInfo = Session::get('user_info');
        
        return !empty($userId) && !empty($userInfo);
    }

    /**
     * 检查管理员权限
     *
     * @return bool
     */
    protected function hasAdminPermission(): bool
    {
        $userId = Session::get('user_id');
        $userInfo = Session::get('user_info');

        Log::info('hasAdminPermission - userId: ' . $userId);
        Log::info('hasAdminPermission - userInfo: ' . json_encode($userInfo));

        if (empty($userId)) {
            Log::info('hasAdminPermission - userId为空');
            return false;
        }

        // 使用AuthHelper的管理员检查逻辑，它更完善
        $isAdmin = \app\common\AuthHelper::isAdmin($userId);
        Log::info('hasAdminPermission - AuthHelper检查结果: ' . ($isAdmin ? 'true' : 'false'));

        return $isAdmin;
    }

    /**
     * 重定向到登录页面
     *
     * @return Response
     */
    protected function redirectToLogin(): Response
    {
        // 检查是否为Ajax请求或API请求
        if ($this->request->isAjax() ||
            $this->isApiRequest() ||
            $this->request->header('Accept') === 'application/json' ||
            strpos($this->request->header('Content-Type'), 'application/json') !== false) {
            return json([
                'success' => false,
                'code' => 401,
                'message' => '请先登录',
                'data' => null
            ]);
        }

        return redirect('/login');
    }

    /**
     * 检查是否为API请求
     *
     * @return bool
     */
    protected function isApiRequest(): bool
    {
        $path = $this->request->pathinfo();
        $url = $this->request->url();

        // 检查路径中是否包含API相关标识
        return strpos($path, '/api/') !== false ||
               strpos($path, '/admin/api/') !== false ||
               strpos($url, '/admin/settings/get') !== false ||
               strpos($url, '/admin/settings/save') !== false ||
               strpos($url, '/admin/settings/clear') !== false ||
               strpos($url, 'getSystemMonitor') !== false ||
               strpos($url, 'getShopSettings') !== false ||
               strpos($url, 'saveShopSettings') !== false ||
               strpos($url, 'getPaymentSettings') !== false ||
               strpos($url, 'savePaymentSettings') !== false ||
               strpos($url, 'getGeneralSettings') !== false ||
               strpos($url, 'saveGeneralSettings') !== false ||
               strpos($url, '/admin/item-delivery/get') !== false ||
               strpos($url, '/admin/item-delivery/sendItem') !== false ||
               strpos($url, '/admin/cards/configs') !== false ||
               strpos($url, '/admin/cards/config') !== false ||
               strpos($url, '/admin/cards/user-cards') !== false ||
               strpos($url, '/admin/cards/stats') !== false ||
               strpos($url, '/admin/cards/grant') !== false ||
               strpos($url, '/admin/cards/extend') !== false ||
               strpos($url, '/admin/cards/grant-daily-reward') !== false ||
               strpos($url, '/admin/cards/delete-user-card') !== false;
    }

    /**
     * 访问被拒绝
     *
     * @return Response
     */
    protected function accessDenied(): Response
    {
        // 检查是否为Ajax请求或API请求
        if ($this->request->isAjax() ||
            $this->isApiRequest() ||
            $this->request->header('Accept') === 'application/json' ||
            strpos($this->request->header('Content-Type'), 'application/json') !== false) {
            return json([
                'success' => false,
                'code' => 403,
                'message' => '权限不足',
                'data' => null
            ]);
        }

        return response('权限不足', 403);
    }

    /**
     * 获取当前管理员信息
     * 
     * @return array|null
     */
    protected function getCurrentAdmin(): ?array
    {
        $userInfo = Session::get('user_info');
        
        if (empty($userInfo) || !isset($userInfo['account'])) {
            return null;
        }

        $account = $userInfo['account'];
        $configPath = app()->getConfigPath() . 'admin_users.php';
        
        if (!file_exists($configPath)) {
            return null;
        }

        $admins = include $configPath;
        
        if (!isset($admins[$account])) {
            return null;
        }

        return array_merge(['username' => $account], $admins[$account]);
    }

    /**
     * 记录管理员操作日志
     * 
     * @param string $action 操作类型
     * @param string $description 操作描述
     * @param array $data 操作数据
     * @return void
     */
    protected function logAdminAction(string $action, string $description, array $data = []): void
    {
        $admin = $this->getCurrentAdmin();
        $adminUsername = $admin['username'] ?? 'unknown';

        Log::info("管理员操作: {$action}", [
            'admin' => $adminUsername,
            'description' => $description,
            'data' => $data,
            'ip' => $this->request->ip(),
            'user_agent' => $this->request->header('User-Agent'),
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * 统一的API响应格式
     * 
     * @param bool $success 是否成功
     * @param mixed $data 响应数据
     * @param string $message 响应消息
     * @param int $code 响应代码
     * @return Response
     */
    protected function apiResponse(bool $success, $data = null, string $message = '', int $code = 0): Response
    {
        $responseData = [
            'success' => $success,
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];

        // 使用配置的JSON选项，确保中文字符正确显示
        $jsonOptions = config('app.json_options', JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

        return json($responseData)->options(['json_encode_param' => $jsonOptions]);
    }

    /**
     * 成功响应
     *
     * @param mixed $data 响应数据
     * @param string $message 响应消息
     * @return Response
     */
    protected function success($data = null, string $message = 'success'): Response
    {
        return $this->apiResponse(true, $data, $message, 200);
    }

    /**
     * 错误响应
     * 
     * @param string $message 错误消息
     * @param int $code 错误代码
     * @param mixed $data 响应数据
     * @return Response
     */
    protected function error(string $message = '操作失败', int $code = 1, $data = null): Response
    {
        return $this->apiResponse(false, $data, $message, $code);
    }

    /**
     * 获取分页参数
     * 
     * @return array
     */
    protected function getPaginationParams(): array
    {
        return [
            'page' => (int)$this->request->param('page', 1),
            'limit' => (int)$this->request->param('limit', 20),
            'search' => trim($this->request->param('search', '')),
        ];
    }
}
