<?php

namespace app\controller\admin;

use app\service\ItemDeliveryService;
use app\service\ItemService;
use think\facade\Log;
use think\facade\Db;
use think\Response;
use think\App;

/**
 * 管理员道具发送控制器
 * 负责管理员直接向用户发送道具的功能
 */
class ItemDeliveryController extends BaseController
{
    /**
     * 道具发送服务
     */
    protected ItemDeliveryService $itemDeliveryService;

    /**
     * 构造函数
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->itemDeliveryService = new ItemDeliveryService();
    }

    /**
     * 道具发送管理页面
     */
    public function index(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        return view('admin/item_delivery', [
            'pageTitle' => '道具发送',
            'pageIcon' => 'bi bi-gift',
            'pageDescription' => '向用户发送游戏道具'
        ]);
    }

    /**
     * 获取可发送的道具列表（从iteminfo表获取所有道具）
     */
    public function getItemList(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            $page = $this->request->param('page', 1);
            $limit = $this->request->param('limit', 20);
            $search = $this->request->param('search', '');

            // 构建查询条件
            $query = Db::table('iteminfo');

            // 搜索条件
            if (!empty($search)) {
                $query->where('ItemName', 'like', '%' . $search . '%');
            }

            // 获取总数
            $total = $query->count();

            // 分页查询
            $items = $query->page($page, $limit)
                ->field('ItemID, ItemName, Explanation, Icon')
                ->order('ItemID', 'asc')
                ->select()
                ->toArray();

            // 格式化数据
            $formattedItems = [];
            foreach ($items as $item) {
                $formattedItems[] = [
                    'id' => $item['ItemID'],
                    'name' => $item['ItemName'] ?? '未知道具',
                    'item' => $item['ItemID'], // 使用ItemID作为item字段
                    'item_info' => $item['Explanation'] ?? '',
                    'icon' => $item['Icon'] ? '/static/img/' . $item['Icon'] . '.png' : '/static/img/default.png',
                ];
            }

            $result = [
                'items' => $formattedItems,
                'total' => $total,
                'page' => $page,
                'limit' => $limit
            ];

            return $this->success($result, '获取道具列表成功');
        } catch (\Exception $e) {
            Log::error('获取道具列表失败: ' . $e->getMessage());
            return $this->error('获取道具列表失败');
        }
    }

    /**
     * 获取商城商品列表（保留原有方法）
     */
    public function getShopItems(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            $page = $this->request->param('page', 1);
            $limit = $this->request->param('limit', 20);
            $search = $this->request->param('search', '');
            $category = $this->request->param('category', '');

            // 获取商品列表（只获取上架的商品）
            $result = ItemService::getItems($page, $limit, $category, $search, '1');

            return $this->success($result, '获取商品列表成功');
        } catch (\Exception $e) {
            Log::error('获取商品列表失败: ' . $e->getMessage());
            return $this->error('获取商品列表失败');
        }
    }

    /**
     * 发送道具给用户
     */
    public function sendItem(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            $username = $this->request->param('username');
            $itemId = $this->request->param('item_id');
            $quantity = $this->request->param('quantity', 1);
            $ioo = $this->request->param('ioo', 0); // ItemOp2字段
            $timeLimit = $this->request->param('time_limit', 0); // ItemLimit字段
            $sendMode = $this->request->param('send_mode', 'batch'); // 发送模式
            $reason = $this->request->param('reason', '管理员发送');

            // 验证参数
            if (empty($username) || empty($itemId)) {
                return $this->error('用户名和道具ID不能为空', 400);
            }

            if ($quantity <= 0 || $quantity > 999) {
                return $this->error('数量必须在1-999之间', 400);
            }

            if ($ioo < 0) {
                return $this->error('道具属性(IOO)不能为负数', 400);
            }

            if ($timeLimit < 0) {
                return $this->error('时间限制不能为负数', 400);
            }

            // 验证用户是否存在
            if (!$this->checkUserExists($username)) {
                return $this->error('用户不存在', 404);
            }

            // 获取道具信息（从iteminfo表）
            $itemInfo = $this->getItemInfoFromDatabase($itemId);
            if (!$itemInfo) {
                return $this->error('道具ID不存在', 404);
            }

            // 根据发送模式发送道具
            if ($sendMode === 'individual' && $quantity > 1) {
                // 逐条发送：发送多个单独的道具
                $successCount = 0;
                $failedCount = 0;
                $errors = [];

                for ($i = 0; $i < $quantity; $i++) {
                    $deliveryResult = $this->sendSingleItemToGame(
                        $username,
                        $itemInfo,
                        1, // 每次发送1个
                        $ioo,
                        $timeLimit
                    );

                    if ($deliveryResult['success']) {
                        $successCount++;
                        // 记录发送日志
                        $this->recordDeliveryLog($username, $itemInfo, 1, $reason, $deliveryResult, $ioo, $timeLimit);
                    } else {
                        $failedCount++;
                        $errors[] = $deliveryResult['message'];
                    }
                }

                if ($failedCount > 0) {
                    return $this->error("发送完成，成功: {$successCount}个，失败: {$failedCount}个。错误: " . implode(', ', array_unique($errors)));
                }
            } else {
                // 批量发送：发送1个包含指定数量的道具
                $deliveryResult = $this->sendSingleItemToGame(
                    $username,
                    $itemInfo,
                    $quantity,
                    $ioo,
                    $timeLimit
                );

                if (!$deliveryResult['success']) {
                    return $this->error('发送道具失败: ' . $deliveryResult['message']);
                }

                // 记录发送日志
                $this->recordDeliveryLog($username, $itemInfo, $quantity, $reason, $deliveryResult, $ioo, $timeLimit);
            }

            // 记录管理员操作日志
            $this->logAdminAction('send_item', '发送道具', [
                'username' => $username,
                'item_id' => $itemId,
                'item_name' => $itemInfo['ItemName'],
                'quantity' => $quantity,
                'ioo' => $ioo,
                'time_limit' => $timeLimit,
                'send_mode' => $sendMode,
                'reason' => $reason
            ]);

            return $this->success([
                'unique_num' => $deliveryResult['unique_num'] ?? null,
                'delivery_mode' => $deliveryResult['delivery_mode'] ?? 'batch'
            ], '道具发送成功');

        } catch (\Exception $e) {
            Log::error('发送道具失败: ' . $e->getMessage());
            return $this->error('发送道具失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取发送统计数据
     */
    public function getStats(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            $today = date('Y-m-d');
            $weekStart = date('Y-m-d', strtotime('monday this week'));
            $monthStart = date('Y-m-01');

            $stats = [
                'today' => Db::table('admin_item_delivery_log')
                    ->where('create_time', '>=', $today . ' 00:00:00')
                    ->where('create_time', '<=', $today . ' 23:59:59')
                    ->where('status', 1)
                    ->sum('quantity') ?: 0,
                'week' => Db::table('admin_item_delivery_log')
                    ->where('create_time', '>=', $weekStart . ' 00:00:00')
                    ->where('status', 1)
                    ->sum('quantity') ?: 0,
                'month' => Db::table('admin_item_delivery_log')
                    ->where('create_time', '>=', $monthStart . ' 00:00:00')
                    ->where('status', 1)
                    ->sum('quantity') ?: 0,
                'total' => Db::table('admin_item_delivery_log')
                    ->where('status', 1)
                    ->sum('quantity') ?: 0
            ];

            return $this->success($stats, '获取统计数据成功');
        } catch (\Exception $e) {
            Log::error('获取统计数据失败: ' . $e->getMessage());
            return $this->error('获取统计数据失败');
        }
    }

    /**
     * 获取发送记录
     */
    public function getDeliveryHistory(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            $page = $this->request->param('page', 1);
            $limit = $this->request->param('limit', 20);
            $username = $this->request->param('username', '');
            $dateRange = $this->request->param('date_range', '');
            $type = $this->request->param('type', 'item'); // 只支持道具类型

            $records = [];
            $total = 0;

            // 获取道具发送记录
            if ($type === 'item') {
                $itemQuery = Db::table('admin_item_delivery_log');

                // 用户名筛选
                if (!empty($username)) {
                    $itemQuery->where('username', 'like', '%' . $username . '%');
                }

                // 日期范围筛选
                if (!empty($dateRange)) {
                    $dates = explode(' - ', $dateRange);
                    if (count($dates) === 2) {
                        $itemQuery->whereBetween('create_time', [$dates[0] . ' 00:00:00', $dates[1] . ' 23:59:59']);
                    }
                }

                $itemRecords = $itemQuery->order('create_time', 'desc')
                    ->select()
                    ->toArray();

                // 为道具记录添加类型标识
                foreach ($itemRecords as &$record) {
                    $record['record_type'] = 'item';
                    $record['type_name'] = '道具发送';
                    $record['type_icon'] = 'bi-gift';
                    $record['type_color'] = 'primary';
                }

                $records = array_merge($records, $itemRecords);
            }



            // 按时间排序
            usort($records, function($a, $b) {
                return strtotime($b['create_time']) - strtotime($a['create_time']);
            });

            $total = count($records);

            // 分页
            $offset = ($page - 1) * $limit;
            $list = array_slice($records, $offset, $limit);

            return $this->success([
                'list' => $list,
                'total' => $total,
                'page' => $page,
                'limit' => $limit
            ], '获取发送记录成功');

        } catch (\Exception $e) {
            Log::error('获取发送记录失败: ' . $e->getMessage());
            return $this->error('获取发送记录失败');
        }
    }

    /**
     * 检查用户是否存在
     */
    private function checkUserExists(string $username): bool
    {
        try {
            // 使用UserService检查用户是否存在于idtable1-idtable5中
            $userService = new \app\service\UserService();
            $user = $userService->getUserByUsername($username);

            return !empty($user);
        } catch (\Exception $e) {
            Log::error('检查用户存在性失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 记录发送日志
     */
    private function recordDeliveryLog(string $username, array $itemInfo, int $quantity, string $reason, array $deliveryResult, int $ioo = 0, int $timeLimit = 0): void
    {
        try {
            // 获取管理员用户名
            $adminUser = session('admin_username') ?: session('username') ?: 'system';

            $logData = [
                'admin_user' => $adminUser,
                'username' => $username,
                'item_id' => $itemInfo['ItemID'] ?? $itemInfo['item'] ?? 0,
                'item_name' => $itemInfo['ItemName'] ?? $itemInfo['name'] ?? '未知道具',
                'quantity' => $quantity,
                'reason' => $reason,
                'unique_num' => $deliveryResult['unique_num'] ?? null,
                'delivery_mode' => $deliveryResult['delivery_mode'] ?? 'batch',
                'ioo' => $ioo,
                'time_limit' => $timeLimit,
                'status' => 1, // 1=成功
                'create_time' => date('Y-m-d H:i:s')
            ];

            Log::info('准备插入发送日志', $logData);

            // 使用原生SQL插入，避免字段缓存问题
            $sql = "INSERT INTO admin_item_delivery_log (admin_user, username, item_id, item_name, quantity, reason, unique_num, delivery_mode, ioo, time_limit, status, create_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            $params = [
                $logData['admin_user'],
                $logData['username'],
                $logData['item_id'],
                $logData['item_name'],
                $logData['quantity'],
                $logData['reason'],
                $logData['unique_num'],
                $logData['delivery_mode'],
                $logData['ioo'],
                $logData['time_limit'],
                $logData['status'],
                $logData['create_time']
            ];

            $result = Db::execute($sql, $params);
            Log::info('发送日志插入结果', ['result' => $result]);
        } catch (\Exception $e) {
            Log::error('记录发送日志失败: ' . $e->getMessage());
            Log::error('日志数据: ' . json_encode($logData ?? []));
        }
    }

    /**
     * 从iteminfo表获取道具信息
     */
    private function getItemInfoFromDatabase($itemId): ?array
    {
        try {
            $item = Db::table('iteminfo')
                ->where('ItemID', $itemId)
                ->field('ItemID, ItemName, Explanation')
                ->find();

            return $item ? $item : null;
        } catch (\Exception $e) {
            Log::error('获取道具信息失败: ' . $e->getMessage());
            return null;
        }
    }





    /**
     * 发送单个道具到游戏
     */
    private function sendSingleItemToGame($username, $itemInfo, $quantity, $ioo = 0, $timeLimit = 0): array
    {
        try {
            // 获取远程数据库配置
            $remoteConfig = [
                'type' => 'mysql',
                'hostname' => env('REMOTE_DB_HOST'),
                'hostport' => env('REMOTE_DB_PORT'),
                'database' => env('ITEM_DB_NAME', 'item'),
                'username' => env('REMOTE_DB_USER'),
                'password' => env('REMOTE_DB_PASS'),
                'charset'  => env('REMOTE_DB_CHARSET', 'utf8'),
            ];

            // 连接远程数据库
            $remoteDb = Db::connect($remoteConfig);

            // 插入道具到seal_item表
            $insertData = [
                'ItemType' => $itemInfo['ItemID'],
                'ItemOp1' => $quantity, // IO字段，道具数量
                'ItemOp2' => $ioo, // IOO字段，道具属性
                'ItemLimit' => $timeLimit, // 时间限制（秒）
                'OwnerID' => $username,
                'OwnerDate' => date('Y-m-d H:i:s'),
                'bxaid' => null
            ];

            $uniqueNum = $remoteDb->table('seal_item')->insertGetId($insertData);

            if ($uniqueNum) {
                return [
                    'success' => true,
                    'unique_num' => $uniqueNum,
                    'message' => '道具发送成功'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => '插入道具记录失败'
                ];
            }
        } catch (\Exception $e) {
            Log::error('发送道具到游戏失败: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => '发送道具失败: ' . $e->getMessage()
            ];
        }
    }
}
