<?php

namespace app\controller\admin;

use app\controller\admin\BaseController;
use think\facade\View;
use think\facade\Log;
use think\Response;
use app\service\UserService;
use app\service\AdminManagementService;
use app\common\ApiResponse;

/**
 * 管理后台用户状态管理控制器
 * 负责用户状态查看、封禁解封等功能
 */
class UserStatusController extends BaseController
{
    /**
     * 用户服务
     * @var UserService
     */
    protected UserService $userService;

    /**
     * 管理服务
     * @var AdminManagementService
     */
    protected AdminManagementService $adminManagementService;

    /**
     * 构造函数
     */
    public function __construct(\think\App $app)
    {
        parent::__construct($app);
        $this->userService = new UserService();
        $this->adminManagementService = new AdminManagementService();
    }

    /**
     * 用户状态管理页面
     * 
     * @return Response
     */
    public function index(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        // 设置页面数据
        View::assign([
            'pageTitle' => '用户状态管理',
            'pageIcon' => 'bi bi-person-check',
            'pageDescription' => '管理用户状态，查看封禁记录和在线状态'
        ]);

        return response(View::fetch('admin/user_status_simple'));
    }

    /**
     * 获取用户状态统计（API接口）
     * 
     * @return Response
     */
    public function getUserStats(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }
        
        try {
            $stats = $this->userService->getUserStats();

            $this->logAdminAction('view_user_stats', '查看用户状态统计');

            return ApiResponse::success($stats, 'success');
        } catch (\Exception $e) {
            Log::error('获取用户状态统计失败: ' . $e->getMessage());
            return ApiResponse::error('获取用户状态统计失败');
        }
    }

    /**
     * 获取在线用户列表（API接口）
     * 
     * @return Response
     */
    public function getOnlineUsers(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }
        
        try {
            $params = $this->getPaginationParams();
            
            $result = $this->userService->getOnlineUsers(
                $params['page'], 
                $params['limit'], 
                $params['search']
            );
            
            $this->logAdminAction('view_online_users', '查看在线用户列表', [
                'page' => $params['page'],
                'search' => $params['search']
            ]);
            
            return ApiResponse::success($result, 'success');
        } catch (\Exception $e) {
            Log::error('获取在线用户列表失败: ' . $e->getMessage());
            return ApiResponse::error('获取在线用户列表失败');
        }
    }

    /**
     * 获取封禁记录（API接口）
     * 
     * @return Response
     */
    public function getBanRecords(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }
        
        try {
            $params = $this->getPaginationParams();
            $status = $this->request->param('status', '');
            $dateRange = $this->request->param('date_range', '');
            
            $result = $this->userService->getBanRecords(
                $params['page'], 
                $params['limit'], 
                $params['search'], 
                $status, 
                $dateRange
            );
            
            $this->logAdminAction('view_ban_records', '查看封禁记录', [
                'page' => $params['page'],
                'search' => $params['search'],
                'status' => $status
            ]);
            
            return ApiResponse::success($result, 'success');
        } catch (\Exception $e) {
            Log::error('获取封禁记录失败: ' . $e->getMessage());
            return ApiResponse::error('获取封禁记录失败');
        }
    }

    /**
     * 批量用户操作（API接口）
     * 
     * @return Response
     */
    public function batchUserOperation(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }
        
        try {
            $userIds = $this->request->param('user_ids', []);
            $operation = $this->request->param('operation');
            $reason = $this->request->param('reason', '');
            
            if (empty($userIds) || !is_array($userIds)) {
                return ApiResponse::error('请选择要操作的用户');
            }
            
            if (!in_array($operation, ['ban', 'unban', 'kick'])) {
                return ApiResponse::error('操作类型无效');
            }
            
            $result = false;
            
            switch ($operation) {
                case 'ban':
                    $result = $this->userService->batchBanUsers($userIds, $reason);
                    break;
                case 'unban':
                    $result = $this->userService->batchUnbanUsers($userIds, $reason);
                    break;
                case 'kick':
                    $result = $this->userService->batchKickUsers($userIds, $reason);
                    break;
            }
            
            if ($result) {
                $this->logAdminAction('batch_user_operation', "批量{$operation}用户", [
                    'user_ids' => $userIds,
                    'operation' => $operation,
                    'reason' => $reason
                ]);
                
                return ApiResponse::success([], '操作成功');
            } else {
                return ApiResponse::error('操作失败');
            }
        } catch (\Exception $e) {
            Log::error('批量用户操作失败: ' . $e->getMessage());
            return ApiResponse::error('批量用户操作失败');
        }
    }

    /**
     * 清除用户缓存（API接口）
     * 
     * @return Response
     */
    public function clearUserCache(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }
        
        try {
            $username = $this->request->param('username', '');
            
            if (empty($username)) {
                return ApiResponse::error('用户名不能为空');
            }
            
            $result = $this->userService->clearUserCache($username);
            
            if ($result) {
                $this->logAdminAction('clear_user_cache', '清除用户缓存', ['username' => $username]);
                return ApiResponse::success([], '缓存清除成功');
            } else {
                return ApiResponse::error('缓存清除失败');
            }
        } catch (\Exception $e) {
            Log::error('清除用户缓存失败: ' . $e->getMessage());
            return ApiResponse::error('清除用户缓存失败');
        }
    }

    /**
     * 预热用户缓存（API接口）
     * 
     * @return Response
     */
    public function warmupUserCache(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }
        
        try {
            $result = $this->userService->warmupUserCache();
            
            if ($result) {
                $this->logAdminAction('warmup_user_cache', '预热用户缓存');
                return ApiResponse::success([], '缓存预热成功');
            } else {
                return ApiResponse::error('缓存预热失败');
            }
        } catch (\Exception $e) {
            Log::error('预热用户缓存失败: ' . $e->getMessage());
            return ApiResponse::error('预热用户缓存失败');
        }
    }
}
