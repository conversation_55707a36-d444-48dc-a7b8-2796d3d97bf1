<?php
declare(strict_types=1);

namespace app\controller\admin;

use think\App;
use think\Response;
use think\facade\View;
use think\facade\Log;
use think\facade\Db;
use app\service\OrderService;

/**
 * 管理后台订单管理控制器
 * 负责订单的查看、处理和统计
 */
class OrderController extends BaseController
{
    /**
     * 订单服务
     * @var OrderService
     */
    protected OrderService $orderService;

    /**
     * 构造函数
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->orderService = new OrderService();
    }

    /**
     * 订单管理页面
     * 
     * @return Response
     */
    public function index(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        // 设置页面数据
        View::assign([
            'pageTitle' => '订单管理',
            'pageIcon' => 'bi bi-receipt',
            'pageDescription' => '管理系统订单，查看订单信息和状态'
        ]);
        
        return response(View::fetch('admin/orders'));
    }

    /**
     * 获取订单列表
     * 
     * @return Response
     */
    public function getOrderList(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }
        
        try {
            $params = $this->getPaginationParams();
            $status = $this->request->param('status', '');
            $dateRange = $this->request->param('date_range', '');
            
            // 调用订单服务获取订单列表
            $result = $this->orderService->getOrderList(
                $params['page'], 
                $params['limit'], 
                $params['search'], 
                $status, 
                $dateRange
            );
            
            $this->logAdminAction('view_orders', '查看订单列表', [
                'page' => $params['page'],
                'search' => $params['search'],
                'status' => $status
            ]);
            
            return json([
                'code' => 200,
                'message' => 'success',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            Log::error('获取订单列表失败: ' . $e->getMessage());
            
            return json([
                'code' => 500,
                'message' => '获取订单列表失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 获取订单详情
     * 
     * @return Response
     */
    public function getOrderDetail(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }
        
        try {
            $orderId = $this->request->param('order_id');
            
            if (empty($orderId)) {
                return $this->error('订单ID不能为空', 400);
            }
            
            // 获取订单详细信息
            $orderDetail = $this->orderService->getOrderDetail($orderId);
            
            if (!$orderDetail) {
                return $this->error('订单不存在', 404);
            }
            
            $this->logAdminAction('view_order_detail', '查看订单详情', ['order_id' => $orderId]);
            
            return $this->success($orderDetail);
        } catch (\Exception $e) {
            Log::error('获取订单详情失败: ' . $e->getMessage());
            return $this->error('获取订单详情失败');
        }
    }

    /**
     * 更新订单状态
     * 
     * @return Response
     */
    public function updateOrderStatus(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }
        
        try {
            // 获取JSON请求体数据
            $input = $this->request->getContent();
            $data = json_decode($input, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::error('JSON解析失败: ' . json_last_error_msg() . ', 原始数据: ' . $input);
                return $this->error('请求数据格式错误', 400);
            }

            $orderId = $data['order_id'] ?? $this->request->param('order_id');
            $status = $data['status'] ?? $this->request->param('status');
            $reason = $data['reason'] ?? $this->request->param('reason', '');

            Log::info('更新订单状态请求参数', [
                'order_id' => $orderId,
                'status' => $status,
                'reason' => $reason,
                'raw_input' => $input
            ]);

            if (empty($orderId) || empty($status)) {
                return $this->error('订单ID和状态不能为空', 400);
            }
            
            // 验证状态值
            $validStatuses = ['pending', 'processing', 'completed', 'cancelled', 'refunded'];
            if (!in_array($status, $validStatuses)) {
                return $this->error('无效的订单状态', 400);
            }
            
            // 更新订单状态
            $result = $this->orderService->updateOrderStatus($orderId, $status, $reason);
            
            if ($result) {
                $this->logAdminAction('update_order_status', '更新订单状态', [
                    'order_id' => $orderId,
                    'status' => $status,
                    'reason' => $reason
                ]);

                // 清除缓存确保数据立即更新
                try {
                    $this->clearCache();
                } catch (\Exception $e) {
                    Log::warning('清除缓存失败: ' . $e->getMessage());
                }

                return $this->success([], '订单状态更新成功');
            } else {
                return $this->error('订单状态更新失败');
            }
        } catch (\Exception $e) {
            Log::error('更新订单状态失败: ' . $e->getMessage());
            return $this->error('更新订单状态失败');
        }
    }

    /**
     * 处理退款
     * 
     * @return Response
     */
    public function processRefund(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }
        
        try {
            $orderId = $this->request->param('order_id');
            $refundAmount = $this->request->param('refund_amount');
            $reason = $this->request->param('reason', '');
            
            if (empty($orderId) || empty($refundAmount)) {
                return $this->error('订单ID和退款金额不能为空', 400);
            }
            
            // 验证退款金额
            if (!is_numeric($refundAmount) || $refundAmount <= 0) {
                return $this->error('退款金额必须大于0', 400);
            }
            
            // 处理退款
            $result = $this->orderService->processRefund($orderId, (float)$refundAmount, $reason);
            
            if ($result) {
                $this->logAdminAction('process_refund', '处理退款', [
                    'order_id' => $orderId,
                    'refund_amount' => $refundAmount,
                    'reason' => $reason
                ]);
                
                return $this->success([], '退款处理成功');
            } else {
                return $this->error('退款处理失败');
            }
        } catch (\Exception $e) {
            Log::error('处理退款失败: ' . $e->getMessage());
            return $this->error('处理退款失败');
        }
    }

    /**
     * 批量操作订单
     * 
     * @return Response
     */
    public function batchOperation(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }
        
        try {
            $orderIds = $this->request->param('order_ids', []);
            $operation = $this->request->param('operation');
            $reason = $this->request->param('reason', '');
            
            if (empty($orderIds) || !is_array($orderIds)) {
                return $this->error('请选择要操作的订单', 400);
            }
            
            if (!in_array($operation, ['complete', 'cancel', 'export'])) {
                return $this->error('操作类型无效', 400);
            }
            
            $result = false;
            
            switch ($operation) {
                case 'complete':
                    $result = $this->orderService->batchCompleteOrders($orderIds, $reason);
                    break;
                case 'cancel':
                    $result = $this->orderService->batchCancelOrders($orderIds, $reason);
                    break;
                case 'export':
                    $result = $this->orderService->batchExportOrders($orderIds);
                    break;
            }
            
            if ($result) {
                $this->logAdminAction('batch_order_operation', "批量{$operation}订单", [
                    'order_ids' => $orderIds,
                    'operation' => $operation,
                    'reason' => $reason
                ]);
                
                if ($operation === 'export') {
                    return $this->success(['download_url' => $result], '导出成功');
                } else {
                    return $this->success([], '批量操作成功');
                }
            } else {
                return $this->error('批量操作失败');
            }
        } catch (\Exception $e) {
            Log::error('批量操作订单失败: ' . $e->getMessage());
            return $this->error('批量操作失败');
        }
    }

    /**
     * 获取订单统计
     * 
     * @return Response
     */
    public function getOrderStats(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }
        
        try {
            $dateRange = $this->request->param('date_range', '');
            
            // 获取订单统计数据
            $stats = $this->orderService->getOrderStats($dateRange);
            
            return $this->success($stats);
        } catch (\Exception $e) {
            Log::error('获取订单统计失败: ' . $e->getMessage());
            return $this->error('获取订单统计失败');
        }
    }

    /**
     * 导出订单数据
     * 
     * @return Response
     */
    public function exportOrders(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }
        
        try {
            $format = $this->request->param('format', 'csv');
            $filters = [
                'search' => $this->request->param('search', ''),
                'status' => $this->request->param('status', ''),
                'date_range' => $this->request->param('date_range', '')
            ];
            
            // 调用订单服务导出数据
            $result = $this->orderService->exportOrders($format, $filters);
            
            if ($result) {
                $this->logAdminAction('export_orders', '导出订单数据', [
                    'format' => $format,
                    'filters' => $filters
                ]);
                
                return $this->success(['download_url' => $result], '导出成功');
            } else {
                return $this->error('导出失败');
            }
        } catch (\Exception $e) {
            Log::error('导出订单数据失败: ' . $e->getMessage());
            return $this->error('导出失败');
        }
    }

    /**
     * 补发订单
     *
     * @return Response
     */
    public function resendOrder(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            // 获取JSON请求体数据
            $input = $this->request->getContent();
            $data = json_decode($input, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::error('JSON解析失败: ' . json_last_error_msg() . ', 原始数据: ' . $input);
                return $this->error('请求数据格式错误', 400);
            }

            $orderId = $data['order_id'] ?? $this->request->param('order_id');
            $reason = $data['reason'] ?? $this->request->param('reason', '管理员手动补发');

            Log::info('补发订单请求参数', [
                'order_id' => $orderId,
                'reason' => $reason,
                'raw_input' => $input
            ]);

            if (empty($orderId)) {
                return $this->error('订单ID不能为空', 400);
            }

            // 获取订单详情
            $orderDetail = $this->orderService->getOrderDetail($orderId);
            if (!$orderDetail) {
                return $this->error('订单不存在', 404);
            }

            // 检查订单状态，只有失败的订单才能补发
            if ($orderDetail['status_code'] != 4) {
                return $this->error('只有失败的订单才能补发', 400);
            }

            // 检查物品是否存在（从iteminfo表检查）
            $itemExists = Db::table('iteminfo')->where('ItemID', $orderDetail['item_id'])->find();
            if (!$itemExists) {
                return $this->error('物品信息不存在，无法补发', 400);
            }

            // 调用补发服务
            $result = $this->orderService->resendOrder($orderId, $reason);

            if ($result['success']) {
                $this->logAdminAction('resend_order', '补发订单', [
                    'order_id' => $orderId,
                    'reason' => $reason
                ]);

                // 清除缓存确保数据立即更新
                try {
                    $this->clearCache();
                } catch (\Exception $e) {
                    Log::warning('清除缓存失败: ' . $e->getMessage());
                }

                return $this->success([], '订单补发成功');
            } else {
                return $this->error($result['message'] ?? '订单补发失败');
            }

        } catch (\Exception $e) {
            Log::error('补发订单失败: ' . $e->getMessage());
            Log::error('补发订单异常详情: ' . $e->getTraceAsString());
            return $this->error('补发订单失败: ' . $e->getMessage());
        }
    }

    /**
     * 强制完成订单（用于无法自动发送的情况）
     *
     * @return Response
     */
    public function forceCompleteOrder(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            // 获取JSON请求体数据
            $input = $this->request->getContent();
            $data = json_decode($input, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::error('JSON解析失败: ' . json_last_error_msg() . ', 原始数据: ' . $input);
                return $this->error('请求数据格式错误', 400);
            }

            $orderId = $data['order_id'] ?? $this->request->param('order_id');
            $reason = $data['reason'] ?? $this->request->param('reason', '管理员手动完成');

            Log::info('强制完成订单请求参数', [
                'order_id' => $orderId,
                'reason' => $reason,
                'raw_input' => $input
            ]);

            if (empty($orderId)) {
                return $this->error('订单ID不能为空', 400);
            }

            // 获取订单详情
            $orderDetail = $this->orderService->getOrderDetail($orderId);
            if (!$orderDetail) {
                return $this->error('订单不存在', 404);
            }

            // 检查订单状态，只有待处理或失败的订单才能强制完成
            if (!in_array($orderDetail['status_code'], [1, 4])) {
                return $this->error('只有待处理或失败的订单才能强制完成', 400);
            }

            // 强制更新为已完成状态
            $result = $this->orderService->updateOrderStatus($orderId, 'completed', $reason);

            if ($result) {
                $this->logAdminAction('force_complete_order', '强制完成订单', [
                    'order_id' => $orderId,
                    'reason' => $reason
                ]);

                // 清除缓存确保数据立即更新
                try {
                    $this->clearCache();
                } catch (\Exception $e) {
                    Log::warning('清除缓存失败: ' . $e->getMessage());
                }

                return $this->success([], '订单已强制完成');
            } else {
                return $this->error('强制完成订单失败');
            }

        } catch (\Exception $e) {
            Log::error('强制完成订单失败: ' . $e->getMessage());
            Log::error('强制完成订单异常详情: ' . $e->getTraceAsString());
            return $this->error('强制完成订单失败: ' . $e->getMessage());
        }
    }

    /**
     * 清除订单缓存
     */
    public function clearOrderCache(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            // 清除订单相关的缓存
            $this->clearCache();

            $this->logAdminAction('clear_order_cache', '清除订单缓存');
            Log::info('订单缓存清除成功');

            return $this->success([], '缓存清除成功');

        } catch (\Exception $e) {
            Log::error('清除订单缓存失败: ' . $e->getMessage());
            return $this->error('清除缓存失败');
        }
    }

    /**
     * 清除缓存的私有方法
     */
    private function clearCache(): void
    {
        try {
            // 清除ThinkPHP缓存
            \think\facade\Cache::clear();

            // 清除文件缓存
            $cacheDir = app()->getRuntimePath() . 'cache';
            if (is_dir($cacheDir)) {
                $this->clearDirectory($cacheDir);
            }

            // 清除临时文件
            $tempDir = app()->getRuntimePath() . 'temp';
            if (is_dir($tempDir)) {
                $this->clearDirectory($tempDir);
            }

            Log::info('缓存清除完成');

        } catch (\Exception $e) {
            Log::error('清除缓存时出错: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 递归清除目录
     */
    private function clearDirectory(string $dir): void
    {
        if (!is_dir($dir)) {
            return;
        }

        $files = array_diff(scandir($dir), ['.', '..']);

        foreach ($files as $file) {
            $filePath = $dir . DIRECTORY_SEPARATOR . $file;

            if (is_dir($filePath)) {
                $this->clearDirectory($filePath);
                @rmdir($filePath);
            } else {
                @unlink($filePath);
            }
        }
    }
}
