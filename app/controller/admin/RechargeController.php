<?php

namespace app\controller\admin;

use app\controller\admin\BaseController;
use think\facade\View;
use think\facade\Log;
use think\Response;
use app\service\RechargeService;
use app\common\ApiResponse;

/**
 * 管理后台充值管理控制器
 * 负责用户充值、余额管理和充值记录查看
 */
class RechargeController extends BaseController
{
    /**
     * 充值服务
     * @var RechargeService
     */
    protected RechargeService $rechargeService;

    /**
     * 构造函数
     */
    public function __construct(\think\App $app)
    {
        parent::__construct($app);
        $this->rechargeService = new RechargeService();
    }

    /**
     * 充值管理页面
     * 
     * @return Response
     */
    public function index(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }
        
        // 获取支持的货币类型和操作类型
        $currencies = RechargeService::getSupportedCurrencies();
        $operations = RechargeService::getSupportedOperations();
        
        View::assign([
            'pageTitle' => '充值管理',
            'pageIcon' => 'bi bi-credit-card',
            'pageDescription' => '管理用户充值和余额操作',
            'currencies' => $currencies,
            'operations' => $operations
        ]);
        
        return response(View::fetch('admin/recharge'));
    }

    /**
     * 执行充值操作（API接口）
     *
     * @return Response
     */
    public function doRecharge(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }
        
        try {
            // 支持多种参数名格式
            $username = $this->request->param('account', '') ?: $this->request->param('username', '');
            $currencyType = $this->request->param('currency', '') ?: $this->request->param('currency_type', '');
            $operation = $this->request->param('operation', '');
            $amount = (int)$this->request->param('amount', 0);
            $reason = $this->request->param('reason', '');
            
            // 获取当前管理员信息，提供多种备用方案
            $adminUser = session('user_info.username');

            if (empty($adminUser)) {
                $userInfo = session('user_info');
                $userId = session('user_id');

                // 备用方案1：从user_info数组中获取username
                if (is_array($userInfo) && !empty($userInfo['username'])) {
                    $adminUser = $userInfo['username'];
                } elseif (is_array($userInfo) && !empty($userInfo['id'])) {
                    // 备用方案2：从user_info数组中获取id
                    $adminUser = $userInfo['id'];
                } elseif (!empty($userId)) {
                    // 备用方案3：使用user_id
                    $adminUser = $userId;
                } else {
                    // 最终备用方案
                    $adminUser = 'system';
                }
            }

            // 执行充值操作
            $result = $this->rechargeService->recharge($username, $currencyType, $operation, $amount, $reason, $adminUser);
            
            if ($result['success']) {
                $this->logAdminAction('recharge_operation', '执行充值操作', [
                    'username' => $username,
                    'currency_type' => $currencyType,
                    'operation' => $operation,
                    'amount' => $amount,
                    'reason' => $reason
                ]);
                
                return ApiResponse::success($result['data'], $result['message']);
            } else {
                return ApiResponse::error($result['message']);
            }
        } catch (\Exception $e) {
            Log::error('充值操作失败: ' . $e->getMessage());
            return ApiResponse::error('充值操作失败');
        }
    }

    /**
     * 获取充值记录（API接口）
     *
     * @return Response
     */
    public function getRechargeHistory(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }
        
        try {
            $params = $this->getPaginationParams();
            // 支持多种参数名格式
            $username = $this->request->param('account', '') ?: $this->request->param('username', '');
            $currencyType = $this->request->param('currency', '') ?: $this->request->param('currency_type', '');
            $dateRange = $this->request->param('date_range', '');
            
            $result = $this->rechargeService->getRechargeHistory(
                $params['page'], 
                $params['limit'], 
                $username, 
                $currencyType, 
                $dateRange
            );
            
            return ApiResponse::success($result, 'success');
        } catch (\Exception $e) {
            Log::error('获取充值记录失败: ' . $e->getMessage());
            return ApiResponse::error('获取充值记录失败');
        }
    }

    /**
     * 查询用户余额（API接口）
     * 
     * @return Response
     */
    public function getUserBalance(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }
        
        try {
            // 支持多种参数名格式
            $username = $this->request->param('account', '') ?: $this->request->param('username', '');

            if (empty($username)) {
                return ApiResponse::error('用户名不能为空');
            }
            
            $balance = $this->rechargeService->getUserBalance($username);
            
            if ($balance !== false) {
                return ApiResponse::success($balance, '查询成功');
            } else {
                return ApiResponse::error('用户不存在或查询失败');
            }
        } catch (\Exception $e) {
            Log::error('查询用户余额失败: ' . $e->getMessage());
            return ApiResponse::error('查询用户余额失败');
        }
    }
}
