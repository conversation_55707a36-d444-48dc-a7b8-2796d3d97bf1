<?php
declare(strict_types=1);

namespace app\controller\admin;

use think\App;
use think\Response;
use think\facade\View;
use think\facade\Log;


/**
 * 管理后台系统设置控制器
 * 负责系统配置管理等功能
 */
class SettingsController extends BaseController
{
    /**
     * 构造函数
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
    }

    /**
     * 系统设置页面
     * 
     * @return Response
     */
    public function index(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        // 设置页面数据
        View::assign([
            'pageTitle' => '系统设置',
            'pageIcon' => 'bi bi-gear',
            'pageDescription' => '管理系统配置和参数'
        ]);
        
        return response(View::fetch('admin/settings'));
    }



    /**
     * 获取系统配置
     * 
     * @return Response
     */
    public function getSystemConfig(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            // 获取系统配置
            $config = [
                'site_name' => config('app.site_name', '游戏商城'),
                'site_description' => config('app.site_description', ''),
                'maintenance_mode' => config('app.maintenance_mode', false),
                'registration_enabled' => config('app.registration_enabled', true),
                'email_verification' => config('app.email_verification', false),
                'max_login_attempts' => config('app.max_login_attempts', 5),
                'session_timeout' => config('app.session_timeout', 3600),
            ];

            return $this->success($config);
        } catch (\Exception $e) {
            Log::error('获取系统配置失败: ' . $e->getMessage());
            return $this->error('获取系统配置失败');
        }
    }

    /**
     * 更新系统配置
     * 
     * @return Response
     */
    public function updateSystemConfig(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            $data = $this->request->post();
            
            // 验证配置项
            $allowedKeys = [
                'site_name', 'site_description', 'maintenance_mode',
                'registration_enabled', 'email_verification',
                'max_login_attempts', 'session_timeout'
            ];

            $config = [];
            foreach ($allowedKeys as $key) {
                if (isset($data[$key])) {
                    $config[$key] = $data[$key];
                }
            }

            if (empty($config)) {
                return $this->error('没有有效的配置项', 400);
            }

            // 更新配置（这里需要实现具体的配置更新逻辑）
            $result = $this->updateConfig($config);

            if ($result) {
                $this->logAdminAction('update_system_config', '更新系统配置', $config);
                return $this->success([], '系统配置更新成功');
            } else {
                return $this->error('系统配置更新失败');
            }
        } catch (\Exception $e) {
            Log::error('更新系统配置失败: ' . $e->getMessage());
            return $this->error('更新系统配置失败');
        }
    }

    /**
     * 获取系统监控信息
     * 
     * @return Response
     */
    public function getSystemMonitor(): Response
    {
        try {
            $monitor = [
                'server_info' => $this->getServerInfo(),
                'database_info' => $this->getDatabaseInfo(),
                'performance_info' => $this->getPerformanceInfo(),
                'security_info' => $this->getSecurityInfo(),
                'system_stats' => $this->getSystemStats()
            ];

            return json([
                'success' => true,
                'code' => 0,
                'message' => 'success',
                'data' => $monitor
            ]);
        } catch (\Exception $e) {
            Log::error('获取系统监控信息失败: ' . $e->getMessage());
            return json([
                'success' => false,
                'code' => 500,
                'message' => '获取系统监控信息失败',
                'data' => null
            ]);
        }
    }

    /**
     * 清理系统缓存
     * 
     * @return Response
     */
    public function clearCache(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            $cacheType = $this->request->param('type', 'all');
            
            $result = false;
            
            switch ($cacheType) {
                case 'template':
                    $result = $this->clearTemplateCache();
                    break;
                case 'data':
                    $result = $this->clearDataCache();
                    break;
                case 'log':
                    $result = $this->clearLogFiles();
                    break;
                case 'all':
                default:
                    $result = $this->clearAllCache();
                    break;
            }

            if ($result) {
                $this->logAdminAction('clear_cache', '清理系统缓存', ['type' => $cacheType]);
                return $this->success([], '缓存清理成功');
            } else {
                return $this->error('缓存清理失败');
            }
        } catch (\Exception $e) {
            Log::error('清理缓存失败: ' . $e->getMessage());
            return $this->error('清理缓存失败');
        }
    }

    /**
     * 更新配置
     * 
     * @param array $config
     * @return bool
     */
    protected function updateConfig(array $config): bool
    {
        // 这里需要实现具体的配置更新逻辑
        // 可以写入配置文件或数据库
        return true;
    }

    /**
     * 获取日志文件列表
     *
     * @return Response
     */
    public function getLogFiles(): Response
    {
        try {
            $logPath = app()->getRuntimePath() . 'log/';
            $logFiles = [];

            if (is_dir($logPath)) {
                $iterator = new \RecursiveIteratorIterator(
                    new \RecursiveDirectoryIterator($logPath, \RecursiveDirectoryIterator::SKIP_DOTS)
                );

                foreach ($iterator as $file) {
                    if ($file->isFile() && $file->getExtension() === 'log') {
                        $relativePath = str_replace($logPath, '', $file->getPathname());
                        $logFiles[] = [
                            'name' => $file->getFilename(),
                            'path' => $relativePath,
                            'size' => $this->formatFileSize($file->getSize()),
                            'size_bytes' => $file->getSize(),
                            'modified' => date('Y-m-d H:i:s', $file->getMTime()),
                            'modified_timestamp' => $file->getMTime()
                        ];
                    }
                }

                // 按修改时间倒序排列
                usort($logFiles, function($a, $b) {
                    return $b['modified_timestamp'] - $a['modified_timestamp'];
                });
            }

            return json([
                'success' => true,
                'code' => 0,
                'message' => 'success',
                'data' => $logFiles
            ]);
        } catch (\Exception $e) {
            Log::error('获取日志文件列表失败: ' . $e->getMessage());
            return json([
                'success' => false,
                'code' => 500,
                'message' => '获取日志文件列表失败',
                'data' => null
            ]);
        }
    }

    /**
     * 获取日志内容
     *
     * @return Response
     */
    public function getLogContent(): Response
    {
        try {
            $logFile = $this->request->param('file', '');
            $lines = $this->request->param('lines', 100);
            $search = $this->request->param('search', '');

            if (empty($logFile)) {
                return json([
                    'success' => false,
                    'code' => 400,
                    'message' => '请指定日志文件',
                    'data' => null
                ]);
            }

            $logPath = app()->getRuntimePath() . 'log/' . $logFile;

            // 安全检查：确保文件在日志目录内
            $realLogPath = realpath($logPath);
            $realLogDir = realpath(app()->getRuntimePath() . 'log/');

            if (!$realLogPath || strpos($realLogPath, $realLogDir) !== 0) {
                return json([
                    'success' => false,
                    'code' => 403,
                    'message' => '无效的日志文件路径',
                    'data' => null
                ]);
            }

            if (!file_exists($logPath)) {
                return json([
                    'success' => false,
                    'code' => 404,
                    'message' => '日志文件不存在',
                    'data' => null
                ]);
            }

            $content = $this->readLogFile($logPath, $lines, $search);

            return json([
                'success' => true,
                'code' => 0,
                'message' => 'success',
                'data' => [
                    'file' => $logFile,
                    'content' => $content['lines'],
                    'total_lines' => $content['total'],
                    'filtered_lines' => $content['filtered'],
                    'file_size' => $this->formatFileSize(filesize($logPath))
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取日志内容失败: ' . $e->getMessage());
            return json([
                'success' => false,
                'code' => 500,
                'message' => '获取日志内容失败',
                'data' => null
            ]);
        }
    }

    /**
     * 清理日志
     *
     * @return Response
     */
    public function clearLogs(): Response
    {
        try {
            $type = $this->request->param('type', 'old'); // old: 清理旧日志, all: 清理所有日志
            $days = $this->request->param('days', 7); // 保留天数

            $logPath = app()->getRuntimePath() . 'log/';
            $deletedFiles = 0;
            $deletedSize = 0;

            if ($type === 'all') {
                // 清理所有日志文件
                $iterator = new \RecursiveIteratorIterator(
                    new \RecursiveDirectoryIterator($logPath, \RecursiveDirectoryIterator::SKIP_DOTS)
                );

                foreach ($iterator as $file) {
                    if ($file->isFile() && $file->getExtension() === 'log') {
                        $deletedSize += $file->getSize();
                        unlink($file->getPathname());
                        $deletedFiles++;
                    }
                }
            } else {
                // 只清理旧日志文件
                $cutoffTime = time() - ($days * 24 * 3600);
                $iterator = new \RecursiveIteratorIterator(
                    new \RecursiveDirectoryIterator($logPath, \RecursiveDirectoryIterator::SKIP_DOTS)
                );

                foreach ($iterator as $file) {
                    if ($file->isFile() && $file->getExtension() === 'log' && $file->getMTime() < $cutoffTime) {
                        $deletedSize += $file->getSize();
                        unlink($file->getPathname());
                        $deletedFiles++;
                    }
                }
            }

            return json([
                'success' => true,
                'code' => 0,
                'message' => '日志清理成功',
                'data' => [
                    'deleted_files' => $deletedFiles,
                    'deleted_size' => $this->formatFileSize($deletedSize),
                    'type' => $type,
                    'days' => $days
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('清理日志失败: ' . $e->getMessage());
            return json([
                'success' => false,
                'code' => 500,
                'message' => '清理日志失败',
                'data' => null
            ]);
        }
    }

    /**
     * 获取服务器信息
     * 
     * @return array
     */
    protected function getServerInfo(): array
    {
        return [
            'php_version' => PHP_VERSION,
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'operating_system' => PHP_OS,
            'server_time' => date('Y-m-d H:i:s'),
            'timezone' => date_default_timezone_get()
        ];
    }

    /**
     * 获取数据库信息
     * 
     * @return array
     */
    protected function getDatabaseInfo(): array
    {
        try {
            $version = \think\facade\Db::query('SELECT VERSION() as version')[0]['version'] ?? 'Unknown';
            return [
                'version' => $version,
                'status' => 'connected'
            ];
        } catch (\Exception $e) {
            return [
                'version' => 'Unknown',
                'status' => 'disconnected'
            ];
        }
    }

    /**
     * 获取性能信息
     * 
     * @return array
     */
    protected function getPerformanceInfo(): array
    {
        return [
            'memory_usage' => memory_get_usage(true),
            'memory_peak' => memory_get_peak_usage(true),
            'memory_limit' => ini_get('memory_limit'),
            'execution_time' => round((microtime(true) - $_SERVER['REQUEST_TIME_FLOAT']) * 1000, 2)
        ];
    }

    /**
     * 获取安全信息
     *
     * @return array
     */
    protected function getSecurityInfo(): array
    {
        return [
            'https_enabled' => isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on',
            'session_secure' => ini_get('session.cookie_secure'),
            'session_httponly' => ini_get('session.cookie_httponly'),
            'display_errors' => ini_get('display_errors')
        ];
    }

    /**
     * 获取系统统计信息（CPU和内存）
     *
     * @return array
     */
    protected function getSystemStats(): array
    {
        return [
            'cpu_usage' => $this->getCpuUsage(),
            'memory_info' => $this->getMemoryInfo(),
            'disk_usage' => $this->getDiskUsage(),
            'load_average' => $this->getLoadAverage(),
            'network_info' => $this->getNetworkInfo()
        ];
    }

    /**
     * 获取CPU使用率
     *
     * @return float
     */
    protected function getCpuUsage(): float
    {
        if (function_exists('sys_getloadavg')) {
            $load = sys_getloadavg();
            return round($load[0] * 100 / $this->getCpuCores(), 2);
        }

        // Linux系统的CPU使用率计算
        if (is_readable('/proc/stat')) {
            $stat1 = file_get_contents('/proc/stat');
            usleep(100000); // 等待0.1秒
            $stat2 = file_get_contents('/proc/stat');

            $info1 = explode(' ', preg_replace('!cpu +!', '', $stat1));
            $info2 = explode(' ', preg_replace('!cpu +!', '', $stat2));

            $dif = [];
            $dif['user'] = $info2[0] - $info1[0];
            $dif['nice'] = $info2[1] - $info1[1];
            $dif['sys'] = $info2[2] - $info1[2];
            $dif['idle'] = $info2[3] - $info1[3];

            $total = array_sum($dif);
            $cpu = 100 - ($dif['idle'] * 100 / $total);

            return round($cpu, 2);
        }

        return 0.0;
    }

    /**
     * 获取CPU核心数
     *
     * @return int
     */
    protected function getCpuCores(): int
    {
        if (is_readable('/proc/cpuinfo')) {
            $cpuinfo = file_get_contents('/proc/cpuinfo');
            preg_match_all('/^processor/m', $cpuinfo, $matches);
            return count($matches[0]);
        }

        return 1;
    }

    /**
     * 获取内存信息
     *
     * @return array
     */
    protected function getMemoryInfo(): array
    {
        $memInfo = [
            'total' => 0,
            'free' => 0,
            'used' => 0,
            'usage_percent' => 0
        ];

        if (is_readable('/proc/meminfo')) {
            $meminfo = file_get_contents('/proc/meminfo');
            preg_match('/MemTotal:\s+(\d+)\s+kB/', $meminfo, $matches);
            $memInfo['total'] = isset($matches[1]) ? intval($matches[1]) * 1024 : 0;

            preg_match('/MemAvailable:\s+(\d+)\s+kB/', $meminfo, $matches);
            if (isset($matches[1])) {
                $memInfo['free'] = intval($matches[1]) * 1024;
            } else {
                preg_match('/MemFree:\s+(\d+)\s+kB/', $meminfo, $matches);
                $memInfo['free'] = isset($matches[1]) ? intval($matches[1]) * 1024 : 0;
            }

            $memInfo['used'] = $memInfo['total'] - $memInfo['free'];
            $memInfo['usage_percent'] = $memInfo['total'] > 0 ? round(($memInfo['used'] / $memInfo['total']) * 100, 2) : 0;
        }

        return $memInfo;
    }

    /**
     * 获取磁盘使用情况
     *
     * @return array
     */
    protected function getDiskUsage(): array
    {
        $diskInfo = [
            'total' => 0,
            'free' => 0,
            'used' => 0,
            'usage_percent' => 0
        ];

        $path = '/';
        if (function_exists('disk_total_space') && function_exists('disk_free_space')) {
            $diskInfo['total'] = disk_total_space($path);
            $diskInfo['free'] = disk_free_space($path);
            $diskInfo['used'] = $diskInfo['total'] - $diskInfo['free'];
            $diskInfo['usage_percent'] = $diskInfo['total'] > 0 ? round(($diskInfo['used'] / $diskInfo['total']) * 100, 2) : 0;
        }

        return $diskInfo;
    }

    /**
     * 获取系统负载
     *
     * @return array
     */
    protected function getLoadAverage(): array
    {
        $load = [
            '1min' => 0,
            '5min' => 0,
            '15min' => 0
        ];

        if (function_exists('sys_getloadavg')) {
            $loadavg = sys_getloadavg();
            $load['1min'] = round($loadavg[0], 2);
            $load['5min'] = round($loadavg[1], 2);
            $load['15min'] = round($loadavg[2], 2);
        }

        return $load;
    }

    /**
     * 获取网络信息
     *
     * @return array
     */
    protected function getNetworkInfo(): array
    {
        $networkInfo = [
            'interfaces' => [],
            'connections' => 0,
            'total_rx_bytes' => 0,
            'total_tx_bytes' => 0,
            'total_rx_packets' => 0,
            'total_tx_packets' => 0
        ];

        // 读取网络接口信息
        if (is_readable('/proc/net/dev')) {
            $netDev = file_get_contents('/proc/net/dev');
            $lines = explode("\n", $netDev);

            foreach ($lines as $line) {
                if (strpos($line, ':') !== false) {
                    $parts = explode(':', $line);
                    $interface = trim($parts[0]);

                    // 跳过回环接口
                    if ($interface === 'lo') continue;

                    $stats = preg_split('/\s+/', trim($parts[1]));
                    if (count($stats) >= 16) {
                        $rxBytes = intval($stats[0]);
                        $rxPackets = intval($stats[1]);
                        $txBytes = intval($stats[8]);
                        $txPackets = intval($stats[9]);

                        $networkInfo['interfaces'][$interface] = [
                            'rx_bytes' => $rxBytes,
                            'tx_bytes' => $txBytes,
                            'rx_packets' => $rxPackets,
                            'tx_packets' => $txPackets
                        ];

                        $networkInfo['total_rx_bytes'] += $rxBytes;
                        $networkInfo['total_tx_bytes'] += $txBytes;
                        $networkInfo['total_rx_packets'] += $rxPackets;
                        $networkInfo['total_tx_packets'] += $txPackets;
                    }
                }
            }
        }

        // 获取网络连接数
        if (is_readable('/proc/net/sockstat')) {
            $sockstat = file_get_contents('/proc/net/sockstat');
            if (preg_match('/TCP: inuse (\d+)/', $sockstat, $matches)) {
                $networkInfo['connections'] = intval($matches[1]);
            }
        }

        return $networkInfo;
    }

    /**
     * 清理模板缓存
     *
     * @return bool
     */
    protected function clearTemplateCache(): bool
    {
        try {
            // 清理ThinkPHP模板缓存
            $templateCachePath = app()->getRuntimePath() . 'temp/';
            if (is_dir($templateCachePath)) {
                $this->deleteDirectory($templateCachePath);
            }

            // 清理视图缓存
            $viewCachePath = app()->getRuntimePath() . 'cache/';
            if (is_dir($viewCachePath)) {
                $this->deleteDirectory($viewCachePath);
            }

            return true;
        } catch (\Exception $e) {
            Log::error('清理模板缓存失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 清理数据缓存
     *
     * @return bool
     */
    protected function clearDataCache(): bool
    {
        try {
            // 清理文件缓存
            $dataCachePath = app()->getRuntimePath() . 'cache/';
            if (is_dir($dataCachePath)) {
                $files = glob($dataCachePath . '*.php');
                foreach ($files as $file) {
                    if (is_file($file)) {
                        unlink($file);
                    }
                }
            }

            // 清理Redis缓存（如果使用）
            try {
                $redis = \think\facade\Cache::store('redis');
                if ($redis) {
                    $redis->clear();
                }
            } catch (\Exception $e) {
                // Redis不可用时忽略错误
            }

            return true;
        } catch (\Exception $e) {
            Log::error('清理数据缓存失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 清理日志文件
     *
     * @return bool
     */
    protected function clearLogFiles(): bool
    {
        try {
            $logPath = app()->getRuntimePath() . 'log/';
            if (is_dir($logPath)) {
                // 只清理7天前的日志文件
                $cutoffTime = time() - (7 * 24 * 3600);
                $this->deleteOldFiles($logPath, $cutoffTime);
            }

            return true;
        } catch (\Exception $e) {
            Log::error('清理日志文件失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 递归删除目录
     *
     * @param string $dir
     * @return bool
     */
    protected function deleteDirectory(string $dir): bool
    {
        if (!is_dir($dir)) {
            return false;
        }

        $files = array_diff(scandir($dir), ['.', '..']);
        foreach ($files as $file) {
            $path = $dir . DIRECTORY_SEPARATOR . $file;
            if (is_dir($path)) {
                $this->deleteDirectory($path);
            } else {
                unlink($path);
            }
        }

        return rmdir($dir);
    }

    /**
     * 删除旧文件
     *
     * @param string $dir
     * @param int $cutoffTime
     * @return void
     */
    protected function deleteOldFiles(string $dir, int $cutoffTime): void
    {
        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($dir, \RecursiveDirectoryIterator::SKIP_DOTS),
            \RecursiveIteratorIterator::CHILD_FIRST
        );

        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getMTime() < $cutoffTime) {
                unlink($file->getPathname());
            }
        }
    }

    /**
     * 清理所有缓存
     *
     * @return bool
     */
    protected function clearAllCache(): bool
    {
        return $this->clearTemplateCache() &&
               $this->clearDataCache() &&
               $this->clearLogFiles();
    }

    /**
     * 获取基本设置
     *
     * @return Response
     */
    public function getGeneralSettings(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            $configFile = app()->getConfigPath() . 'general_settings.php';
            $settings = [];

            if (file_exists($configFile)) {
                $settings = include $configFile;
            }

            // 默认设置
            $defaultSettings = [
                'site_name' => '游戏商城',
                'site_url' => 'http://localhost',
                'site_description' => '',
                'timezone' => 'Asia/Shanghai',
                'language' => 'zh-CN',
                'maintenance_mode' => false,
                'registration_enabled' => true,
                'email_verification' => false,
                'max_login_attempts' => 5,
                'session_timeout' => 3600,
                'admin_email' => '',
                'smtp_host' => '',
                'smtp_port' => 587,
                'smtp_username' => '',
                'smtp_password' => '',
                'smtp_encryption' => 'tls',

            ];

            $settings = array_merge($defaultSettings, $settings);

            $this->logAdminAction('view_general_settings', '查看基本设置');

            return $this->success($settings);
        } catch (\Exception $e) {
            Log::error('获取基本设置失败: ' . $e->getMessage());
            return $this->error('获取基本设置失败');
        }
    }

    /**
     * 保存基本设置
     *
     * @return Response
     */
    public function saveGeneralSettings(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            // 获取请求数据，支持JSON和表单数据
            $contentType = $this->request->header('content-type');
            if (strpos($contentType, 'application/json') !== false) {
                $data = json_decode($this->request->getContent(), true);
            } else {
                $data = $this->request->post();
            }

            if (empty($data)) {
                return $this->error('请求数据为空', 400);
            }

            // 验证必填字段
            if (empty($data['site_name'])) {
                return $this->error('网站名称不能为空', 400);
            }

            // 验证邮箱格式
            if (!empty($data['admin_email']) && !filter_var($data['admin_email'], FILTER_VALIDATE_EMAIL)) {
                return $this->error('管理员邮箱格式不正确', 400);
            }

            // 验证URL格式
            if (!empty($data['site_url']) && !filter_var($data['site_url'], FILTER_VALIDATE_URL)) {
                return $this->error('网站URL格式不正确', 400);
            }

            // 过滤和验证数据
            $allowedKeys = [
                'site_name', 'site_url', 'site_description', 'timezone', 'language',
                'maintenance_mode', 'registration_enabled', 'email_verification',
                'max_login_attempts', 'session_timeout', 'admin_email',
                'smtp_host', 'smtp_port', 'smtp_username', 'smtp_password', 'smtp_encryption'
            ];

            $settings = [];
            foreach ($allowedKeys as $key) {
                if (isset($data[$key])) {
                    $settings[$key] = $data[$key];
                }
            }

            // 类型转换
            if (isset($settings['maintenance_mode'])) {
                $settings['maintenance_mode'] = (bool)$settings['maintenance_mode'];
            }
            if (isset($settings['registration_enabled'])) {
                $settings['registration_enabled'] = (bool)$settings['registration_enabled'];
            }
            if (isset($settings['email_verification'])) {
                $settings['email_verification'] = (bool)$settings['email_verification'];
            }
            if (isset($settings['max_login_attempts'])) {
                $settings['max_login_attempts'] = (int)$settings['max_login_attempts'];
            }
            if (isset($settings['session_timeout'])) {
                $settings['session_timeout'] = (int)$settings['session_timeout'];
            }
            if (isset($settings['smtp_port'])) {
                $settings['smtp_port'] = (int)$settings['smtp_port'];
            }

            // 保存配置文件
            $configFile = app()->getConfigPath() . 'general_settings.php';
            $configContent = "<?php\nreturn " . var_export($settings, true) . ";\n";

            if (file_put_contents($configFile, $configContent) === false) {
                return $this->error('保存配置文件失败，请检查文件权限');
            }

            $this->logAdminAction('save_general_settings', '保存基本设置', $settings);

            return $this->success([], '基本设置保存成功');
        } catch (\Exception $e) {
            Log::error('保存基本设置失败: ' . $e->getMessage());
            return $this->error('保存基本设置失败');
        }
    }

    /**
     * 获取全局配置信息（货币类型和商品分类）
     *
     * @return Response
     */
    public function getGlobalConfig(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            // 使用ShopConfigService获取配置
            $currencies = \app\service\ShopConfigService::getAllCurrencyTypes();
            $categories = \app\service\ShopConfigService::getAllCategories();

            return $this->success([
                'currencies' => $currencies,
                'categories' => $categories
            ], '获取全局配置成功');
        } catch (\Exception $e) {
            Log::error('获取全局配置失败: ' . $e->getMessage());
            return $this->error('获取全局配置失败');
        }
    }

    /**
     * 获取前端ShopConfig配置（公开接口）
     *
     * @return Response
     */
    public function getShopConfig(): Response
    {
        try {
            // 清除缓存确保获取最新配置
            \app\service\ShopConfigService::clearCache();

            // 使用公开方法获取配置
            $currencies = \app\service\ShopConfigService::getAllCurrencyTypes();
            $categories = \app\service\ShopConfigService::getAllCategories();

            $frontendConfig = [
                'categories' => $categories,
                'currencies' => [],
                'currency_types' => $currencies,
            ];

            // 处理货币类型
            foreach ($currencies as $id => $info) {
                $frontendConfig['currencies'][$id] = [
                    'name' => $info['name'],
                    'shortName' => $info['short_name'],
                    'symbol' => $info['symbol'] ?? '',
                    'colorClass' => $info['color_class'] ?? 'bg-secondary'
                ];
            }

            return json([
                'success' => true,
                'code' => 200,
                'data' => $frontendConfig,
                'message' => '获取配置成功'
            ]);
        } catch (\Exception $e) {
            Log::error('获取ShopConfig失败: ' . $e->getMessage());
            return json([
                'success' => false,
                'code' => 500,
                'data' => null,
                'message' => '获取配置失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 更新货币配置
     *
     * @return Response
     */
    public function updateCurrency(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            $data = $this->request->post();
            $currencyId = $data['currency_id'] ?? '';
            $name = trim($data['name'] ?? '');
            $shortName = trim($data['short_name'] ?? '');
            $colorClass = trim($data['color_class'] ?? 'bg-secondary');

            if (empty($currencyId) || empty($name) || empty($shortName)) {
                return $this->error('货币ID、名称和简称不能为空');
            }

            // 读取当前配置
            $configFile = app()->getConfigPath() . 'shop_config.php';
            $config = include $configFile;

            // 更新货币配置
            $config['currency_types'][$currencyId] = [
                'name' => $name,
                'short_name' => $shortName,
                'symbol' => $data['symbol'] ?? '',
                'color_class' => $colorClass
            ];

            // 保存配置文件
            $this->saveShopConfig($config);

            $this->logAdminAction('update_currency', '更新货币配置', [
                'currency_id' => $currencyId,
                'name' => $name,
                'short_name' => $shortName
            ]);

            // 清除ShopConfigService缓存
            \app\service\ShopConfigService::clearCache();

            return $this->success($config['currency_types'], '货币配置更新成功');
        } catch (\Exception $e) {
            Log::error('更新货币配置失败: ' . $e->getMessage());
            return $this->error('更新货币配置失败');
        }
    }

    /**
     * 添加商品分类
     *
     * @return Response
     */
    public function addCategory(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            $data = $this->request->post();
            $categoryName = trim($data['category_name'] ?? '');

            if (empty($categoryName)) {
                return $this->error('分类名称不能为空');
            }

            // 读取当前配置
            $configFile = app()->getConfigPath() . 'shop_config.php';
            $config = include $configFile;

            // 检查分类是否已存在
            if (in_array($categoryName, $config['item_categories'])) {
                return $this->error('分类已存在');
            }

            // 获取新的分类ID（最大ID + 1）
            $maxId = empty($config['item_categories']) ? 0 : max(array_keys($config['item_categories']));
            $newId = $maxId + 1;

            // 添加新分类
            $config['item_categories'][$newId] = $categoryName;

            // 保存配置文件
            $this->saveShopConfig($config);

            $this->logAdminAction('add_category', '添加商品分类', [
                'category_id' => $newId,
                'category_name' => $categoryName
            ]);

            // 清除ShopConfigService缓存
            \app\service\ShopConfigService::clearCache();

            return $this->success($config['item_categories'], '分类添加成功');
        } catch (\Exception $e) {
            Log::error('添加分类失败: ' . $e->getMessage());
            return $this->error('添加分类失败');
        }
    }

    /**
     * 更新商品分类
     *
     * @return Response
     */
    public function updateCategory(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            $data = $this->request->post();
            $categoryId = $data['category_id'] ?? '';
            $categoryName = trim($data['category_name'] ?? '');

            // 添加调试日志
            Log::info('更新分类请求数据', [
                'data' => $data,
                'category_id' => $categoryId,
                'category_name' => $categoryName
            ]);

            if (empty($categoryId) || empty($categoryName)) {
                return $this->error('分类ID和名称不能为空');
            }

            // 确保categoryId是整数类型
            $categoryId = (int)$categoryId;

            // 读取当前配置
            $configFile = app()->getConfigPath() . 'shop_config.php';
            $config = include $configFile;

            Log::info('当前配置中的分类', [
                'item_categories' => $config['item_categories'],
                'looking_for_id' => $categoryId,
                'id_exists' => isset($config['item_categories'][$categoryId])
            ]);

            // 检查分类ID是否存在
            if (!isset($config['item_categories'][$categoryId])) {
                return $this->error('分类不存在，ID: ' . $categoryId);
            }

            // 检查新名称是否与其他分类重复
            foreach ($config['item_categories'] as $id => $name) {
                if ($id != $categoryId && $name === $categoryName) {
                    return $this->error('分类名称已存在');
                }
            }

            // 更新分类
            $oldName = $config['item_categories'][$categoryId];
            $config['item_categories'][$categoryId] = $categoryName;

            // 保存配置文件
            $this->saveShopConfig($config);

            $this->logAdminAction('update_category', '更新商品分类', [
                'category_id' => $categoryId,
                'old_name' => $oldName,
                'new_name' => $categoryName
            ]);

            // 清除ShopConfigService缓存
            \app\service\ShopConfigService::clearCache();

            return $this->success($config['item_categories'], '分类更新成功');
        } catch (\Exception $e) {
            Log::error('更新分类失败: ' . $e->getMessage());
            return $this->error('更新分类失败');
        }
    }

    /**
     * 删除商品分类
     *
     * @return Response
     */
    public function removeCategory(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            $data = $this->request->post();
            $categoryId = $data['category_id'] ?? '';

            if (empty($categoryId)) {
                return $this->error('分类ID不能为空');
            }

            // 确保categoryId是整数类型
            $categoryId = (int)$categoryId;

            // 读取当前配置
            $configFile = app()->getConfigPath() . 'shop_config.php';
            $config = include $configFile;

            // 检查分类是否存在
            if (!isset($config['item_categories'][$categoryId])) {
                return $this->error('分类不存在，ID: ' . $categoryId);
            }

            // 删除分类
            $categoryName = $config['item_categories'][$categoryId];
            unset($config['item_categories'][$categoryId]);

            // 保存配置文件
            $this->saveShopConfig($config);

            $this->logAdminAction('remove_category', '删除商品分类', [
                'category_id' => $categoryId,
                'category_name' => $categoryName
            ]);

            // 清除ShopConfigService缓存
            \app\service\ShopConfigService::clearCache();

            return $this->success($config['item_categories'], '分类删除成功');
        } catch (\Exception $e) {
            Log::error('删除分类失败: ' . $e->getMessage());
            return $this->error('删除分类失败');
        }
    }

    /**
     * 保存商城配置文件
     *
     * @param array $config
     * @throws \Exception
     */
    private function saveShopConfig(array $config): void
    {
        $configFile = app()->getConfigPath() . 'shop_config.php';

        $configContent = "<?php\n/**\n * 商城全局配置文件\n * 用于统一管理商品分类、货币类型等配置信息\n */\n\nreturn " . var_export($config, true) . ";\n";

        if (file_put_contents($configFile, $configContent) === false) {
            throw new \Exception('保存配置文件失败，请检查文件权限');
        }

        // 清除ShopConfigService的缓存
        \app\service\ShopConfigService::clearCache();
    }



    /**
     * 获取支付设置
     *
     * @return Response
     */
    public function getPaymentSettings(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            $configFile = app()->getConfigPath() . 'payment_settings.php';
            $settings = [];

            if (file_exists($configFile)) {
                $settings = include $configFile;
            }

            // 默认设置
            $defaultSettings = [
                'enable_ccoin' => true,
                'enable_coin' => true,
                'enable_silver' => true,
                'enable_recharge' => true,
                'show_recharge_button' => true,  // 是否显示充值按钮
                'ccoin_rate' => 1,
                'coin_rate' => 100,
                'silver_rate' => 1,
                'ccoin_bonus_rate' => 100,
                'coin_bonus_rate' => 1,
                // 充值比例设置
                'ccoin_recharge_ratio' => 100,  // 充值1元获得多少C币
                'coin_recharge_ratio' => 1000,  // 充值1元获得多少泡点
                // 充值赠送设置
                'ccoin_bonus_enabled' => false,  // 是否启用C币充值赠送
                'ccoin_bonus_ratio' => 10,       // C币充值赠送比例（每100C币赠送多少）
                'coin_bonus_enabled' => false,   // 是否启用泡点充值赠送
                'coin_bonus_ratio' => 100,       // 泡点充值赠送比例（每1000泡点赠送多少）
                // 充值赠送积分设置
                'ccoin_silver_bonus_enabled' => false,  // 是否启用C币充值赠送积分
                'ccoin_silver_bonus_ratio' => 100,      // C币充值赠送积分比例（每充值1元赠送多少积分）
                'coin_silver_bonus_enabled' => false,   // 是否启用泡点充值赠送积分
                'coin_silver_bonus_ratio' => 10,        // 泡点充值赠送积分比例（每充值1元赠送多少积分）
                'min_recharge_amount' => 1,
                'max_recharge_amount' => 10000,
                'recharge_fee_rate' => 0,
                'auto_delivery' => true,
                'payment_timeout' => 1800
            ];

            $settings = array_merge($defaultSettings, $settings);

            $this->logAdminAction('view_payment_settings', '查看支付设置');

            return $this->success($settings);
        } catch (\Exception $e) {
            Log::error('获取支付设置失败: ' . $e->getMessage());
            return $this->error('获取支付设置失败');
        }
    }

    /**
     * 保存支付设置
     *
     * @return Response
     */
    public function savePaymentSettings(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            // 获取请求数据，支持JSON和表单数据
            $contentType = $this->request->header('content-type');
            if (strpos($contentType, 'application/json') !== false) {
                $data = json_decode($this->request->getContent(), true);
            } else {
                $data = $this->request->post();
            }

            if (empty($data)) {
                return $this->error('请求数据为空', 400);
            }

            // 验证数值字段
            $numericFields = ['ccoin_rate', 'coin_rate', 'silver_rate', 'ccoin_bonus_rate', 'coin_bonus_rate',
                             'ccoin_recharge_ratio', 'coin_recharge_ratio', 'ccoin_bonus_ratio', 'coin_bonus_ratio',
                             'ccoin_silver_bonus_ratio', 'coin_silver_bonus_ratio',
                             'min_recharge_amount', 'max_recharge_amount', 'recharge_fee_rate', 'payment_timeout'];

            foreach ($numericFields as $field) {
                if (isset($data[$field]) && !is_numeric($data[$field])) {
                    return $this->error("{$field} 必须是数字", 400);
                }
            }

            // 过滤和验证数据
            $allowedKeys = [
                'enable_ccoin', 'enable_coin', 'enable_silver', 'enable_recharge', 'show_recharge_button',
                'ccoin_rate', 'coin_rate', 'silver_rate', 'ccoin_bonus_rate', 'coin_bonus_rate',
                'ccoin_recharge_ratio', 'coin_recharge_ratio',
                'ccoin_bonus_enabled', 'ccoin_bonus_ratio', 'coin_bonus_enabled', 'coin_bonus_ratio',
                'ccoin_silver_bonus_enabled', 'ccoin_silver_bonus_ratio', 'coin_silver_bonus_enabled', 'coin_silver_bonus_ratio',
                'min_recharge_amount', 'max_recharge_amount', 'recharge_fee_rate',
                'auto_delivery', 'payment_timeout'
            ];

            $settings = [];
            foreach ($allowedKeys as $key) {
                if (isset($data[$key])) {
                    $settings[$key] = $data[$key];
                }
            }

            // 类型转换
            $booleanFields = ['enable_ccoin', 'enable_coin', 'enable_silver', 'enable_recharge', 'show_recharge_button', 'auto_delivery',
                             'ccoin_bonus_enabled', 'coin_bonus_enabled', 'ccoin_silver_bonus_enabled', 'coin_silver_bonus_enabled'];
            foreach ($booleanFields as $field) {
                if (isset($settings[$field])) {
                    $settings[$field] = (bool)$settings[$field];
                }
            }

            foreach ($numericFields as $field) {
                if (isset($settings[$field])) {
                    if (in_array($field, ['ccoin_rate', 'coin_rate', 'silver_rate', 'recharge_fee_rate'])) {
                        $settings[$field] = (float)$settings[$field];
                    } else {
                        // 充值比例和赠送比例都使用整数
                        $settings[$field] = (int)$settings[$field];
                    }
                }
            }

            // 保存配置文件
            $configFile = app()->getConfigPath() . 'payment_settings.php';
            $configContent = "<?php\nreturn " . var_export($settings, true) . ";\n";

            if (file_put_contents($configFile, $configContent) === false) {
                return $this->error('保存配置文件失败，请检查文件权限');
            }

            $this->logAdminAction('save_payment_settings', '保存支付设置', $settings);

            return $this->success([], '支付设置保存成功');
        } catch (\Exception $e) {
            Log::error('保存支付设置失败: ' . $e->getMessage());
            return $this->error('保存支付设置失败');
        }
    }

    /**
     * 获取商城设置
     *
     * @return Response
     */
    public function getShopSettings(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            $configFile = app()->getConfigPath() . 'shop_settings.php';
            $settings = [];

            if (file_exists($configFile)) {
                $settings = include $configFile;
            }

            // 默认设置
            $defaultSettings = [
                'shop_enabled' => true,
                'items_per_page' => 12,
                'max_purchase_quantity' => 99,
                'show_out_of_stock' => true,
                'show_price' => true,
                'show_description' => true,
                'allow_guest_purchase' => false,
                'require_confirmation' => true,
                'auto_refresh_balance' => true,
                'cache_enabled' => true,
                'cache_duration' => 300,
                'maintenance_message' => '商城正在维护中，请稍后再试。'
            ];

            $settings = array_merge($defaultSettings, $settings);

            $this->logAdminAction('view_shop_settings', '查看商城设置');

            return $this->success($settings);
        } catch (\Exception $e) {
            Log::error('获取商城设置失败: ' . $e->getMessage());
            return $this->error('获取商城设置失败');
        }
    }

    /**
     * 保存商城设置
     *
     * @return Response
     */
    public function saveShopSettings(): Response
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) {
            return $authCheck;
        }

        try {
            // 获取请求数据，支持JSON和表单数据
            $contentType = $this->request->header('content-type');

            if (strpos($contentType, 'application/json') !== false) {
                $data = json_decode($this->request->getContent(), true);
            } else {
                $data = $this->request->post();
            }

            if (empty($data)) {
                return $this->error('请求数据为空', 400);
            }

            // 验证和过滤数据
            $allowedKeys = [
                'shop_enabled', 'items_per_page', 'max_purchase_quantity',
                'show_out_of_stock', 'show_price', 'show_description',
                'allow_guest_purchase', 'require_confirmation', 'auto_refresh_balance',
                'cache_enabled', 'cache_duration', 'maintenance_message'
            ];

            $settings = [];
            foreach ($allowedKeys as $key) {
                if (isset($data[$key])) {
                    $settings[$key] = $data[$key];
                }
            }

            // 类型转换
            if (isset($settings['shop_enabled'])) {
                $settings['shop_enabled'] = (bool)$settings['shop_enabled'];
            }
            if (isset($settings['items_per_page'])) {
                $settings['items_per_page'] = max(1, min(100, (int)$settings['items_per_page']));
            }
            if (isset($settings['max_purchase_quantity'])) {
                $settings['max_purchase_quantity'] = max(1, min(999, (int)$settings['max_purchase_quantity']));
            }
            if (isset($settings['cache_duration'])) {
                $settings['cache_duration'] = max(60, (int)$settings['cache_duration']);
            }

            // 添加更新时间
            $settings['updated_at'] = date('Y-m-d H:i:s');

            // 读取现有配置
            $configFile = app()->getConfigPath() . 'shop_settings.php';
            $existingSettings = [];
            if (file_exists($configFile)) {
                $existingSettings = include $configFile;
            }

            // 合并设置
            $finalSettings = array_merge($existingSettings, $settings);

            // 保存配置文件
            $configContent = "<?php\n/**\n * 商城设置配置文件\n * 用于管理商城的基本设置参数\n */\n\nreturn " . var_export($finalSettings, true) . ";\n";

            if (file_put_contents($configFile, $configContent) === false) {
                return $this->error('保存配置文件失败，请检查文件权限');
            }

            $this->logAdminAction('save_shop_settings', '保存商城设置', $settings);

            return $this->success([], '商城设置保存成功');
        } catch (\Exception $e) {
            Log::error('保存商城设置失败: ' . $e->getMessage());
            return $this->error('保存商城设置失败');
        }
    }

    /**
     * 格式化文件大小
     *
     * @param int $bytes
     * @return string
     */
    protected function formatFileSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);

        return round($bytes, 2) . ' ' . $units[$pow];
    }

    /**
     * 读取日志文件
     *
     * @param string $filePath
     * @param int $lines
     * @param string $search
     * @return array
     */
    protected function readLogFile(string $filePath, int $lines = 100, string $search = ''): array
    {
        $result = [
            'lines' => [],
            'total' => 0,
            'filtered' => 0
        ];

        if (!file_exists($filePath)) {
            return $result;
        }

        // 使用SplFileObject逆序读取文件
        $file = new \SplFileObject($filePath);
        $file->seek(PHP_INT_MAX);
        $totalLines = $file->key() + 1;
        $result['total'] = $totalLines;

        $readLines = [];
        $currentLine = $totalLines - 1;
        $foundLines = 0;

        // 从文件末尾开始读取
        while ($currentLine >= 0 && $foundLines < $lines) {
            $file->seek($currentLine);
            $line = trim($file->current());

            if (!empty($line)) {
                // 如果有搜索条件，检查是否匹配
                if (empty($search) || stripos($line, $search) !== false) {
                    $readLines[] = [
                        'line_number' => $currentLine + 1,
                        'content' => $line,
                        'timestamp' => $this->extractTimestamp($line),
                        'level' => $this->extractLogLevel($line)
                    ];
                    $foundLines++;
                }
            }

            $currentLine--;
        }

        // 反转数组，使最新的日志在前面
        $result['lines'] = array_reverse($readLines);
        $result['filtered'] = count($readLines);

        return $result;
    }

    /**
     * 从日志行中提取时间戳
     *
     * @param string $line
     * @return string
     */
    protected function extractTimestamp(string $line): string
    {
        // 匹配ThinkPHP日志格式：[2024-01-01T12:00:00+08:00]
        if (preg_match('/\[(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}[+\-]\d{2}:\d{2})\]/', $line, $matches)) {
            return date('Y-m-d H:i:s', strtotime($matches[1]));
        }

        // 匹配简单格式：[2024-01-01 12:00:00]
        if (preg_match('/\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\]/', $line, $matches)) {
            return $matches[1];
        }

        return '';
    }

    /**
     * 从日志行中提取日志级别
     *
     * @param string $line
     * @return string
     */
    protected function extractLogLevel(string $line): string
    {
        $levels = ['DEBUG', 'INFO', 'NOTICE', 'WARNING', 'ERROR', 'CRITICAL', 'ALERT', 'EMERGENCY'];

        foreach ($levels as $level) {
            if (stripos($line, $level) !== false) {
                return strtolower($level);
            }
        }

        return 'info';
    }
}
