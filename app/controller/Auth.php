<?php

namespace app\controller;

use app\BaseController;
use app\service\AuthenticationService;
use app\service\RegistrationService;
use think\facade\Db;
use think\facade\Session;
use think\facade\View;
use think\facade\Log;
use think\Response;
use think\facade\Validate;

class Auth extends BaseController
{
    /**
     * 显示登录页面
     */
    public function login()
    {
        // 如果已经登录，跳转到商城
        if (Session::has('user_id')) {
            return redirect('/shop');
        }
        
        return View::fetch('auth/login');
    }
    
    /**
     * 处理登录请求
     * 使用新的认证服务进行用户验证，支持管理员登录
     */
    public function doLogin()
    {
        // 检查请求类型，支持JSON和表单数据
        $contentType = $this->request->header('content-type', '');

        if (strpos($contentType, 'application/json') !== false) {
            // JSON请求
            $input = json_decode($this->request->getInput(), true);
            $username = $input['username'] ?? '';
            $password = $input['password'] ?? '';
        } else {
            // 表单请求
            $username = $this->request->post('username', '');
            $password = $this->request->post('password', '');
        }

        // 首先尝试管理员登录
        $adminResult = $this->tryAdminLogin($username, $password);
        if ($adminResult) {
            return $adminResult;
        }

        // 如果不是管理员，使用普通用户认证服务
        $authService = new AuthenticationService();
        $authResult = $authService->authenticate($username, $password);

        // 如果认证成功，设置session
        if ($authResult['code'] === 200) {
            $userData = $authResult['data'];

            // 确保session已启动
            if (session_status() === PHP_SESSION_NONE) {
                session_start();
            }

            Session::set('user_id', $userData['user_id']);
            Session::set('user_info', [
                'id' => $userData['user_id'],
                'username' => $userData['username']
            ]);

            // 强制保存session
            Session::save();

            // 异步预热购买历史数据，提升首次查询速度
            try {
                \app\service\PurchaseHistoryPreloader::preloadUserData($userData['user_id']);
            } catch (\Exception $e) {
                // 预热失败不影响登录，只记录日志
                \think\facade\Log::warning("用户登录后预热数据失败: " . $e->getMessage());
            }

            return json([
                'code' => 200,
                'message' => '登录成功',
                'data' => [
                    'redirect' => '/shop'
                ]
            ]);
        }

        // 认证失败，返回错误信息
        return json($authResult);
    }

    /**
     * 尝试管理员登录
     *
     * @param string $username 用户名
     * @param string $password 密码
     * @return Response|null
     */
    private function tryAdminLogin(string $username, string $password): ?Response
    {
        try {
            $configPath = app()->getConfigPath() . 'admin_users.php';

            if (!file_exists($configPath)) {
                return null;
            }

            $admins = include $configPath;

            // 检查用户是否存在
            if (!isset($admins[$username])) {
                return null;
            }

            $admin = $admins[$username];

            // 检查账户状态
            if (isset($admin['status']) && $admin['status'] != 1) {
                return json([
                    'code' => 403,
                    'message' => '账户已被禁用',
                    'data' => null
                ]);
            }

            // 验证密码
            if (!password_verify($password, $admin['password'])) {
                return null; // 密码错误，继续尝试普通用户登录
            }

            // 管理员登录成功，设置session
            // 确保session已启动
            if (session_status() === PHP_SESSION_NONE) {
                session_start();
            }

            // 获取当前session ID
            $sessionId = session_id();
            Log::info('登录时session ID: ' . $sessionId);

            // 设置ThinkPHP Session
            Session::set('user_id', $username);
            Session::set('user_info', [
                'id' => $username,
                'username' => $username,
                'is_admin' => true,
                'admin_level' => $admin['level'] ?? 2
            ]);

            // 也设置原生PHP session作为备份
            $_SESSION['user_id'] = $username;
            $_SESSION['user_info'] = [
                'id' => $username,
                'username' => $username,
                'is_admin' => true,
                'admin_level' => $admin['level'] ?? 2
            ];

            // 强制保存session
            Session::save();

            // 验证session是否保存成功
            Log::info('登录后session验证: user_id=' . Session::get('user_id') . ', native_user_id=' . ($_SESSION['user_id'] ?? 'null'));

            // 更新最后登录时间
            $admins[$username]['last_login'] = date('Y-m-d H:i:s');
            $configContent = "<?php\nreturn " . var_export($admins, true) . ";\n";
            file_put_contents($configPath, $configContent);

            Log::info('管理员登录成功', [
                'admin' => $username,
                'level' => $admin['level'] ?? 2
            ]);

            // 异步预热购买历史数据，提升首次查询速度
            try {
                \app\service\PurchaseHistoryPreloader::preloadUserData($username);
            } catch (\Exception $e) {
                // 预热失败不影响登录，只记录日志
                \think\facade\Log::warning("管理员登录后预热数据失败: " . $e->getMessage());
            }

            return json([
                'code' => 200,
                'message' => '管理员登录成功',
                'data' => [
                    'redirect' => '/admin'
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('管理员登录失败: ' . $e->getMessage());
            return null;
        }
    }
    
    /**
     * 退出登录
     */
    public function logout()
    {
        Session::clear();
        return redirect('/login');
    }
    
    /**
     * 检查登录状态
     */
    public function checkLogin()
    {
        $isLoggedIn = Session::has('user_id');
        return json([
            'code' => 200,
            'message' => 'success',
            'data' => [
                'logged_in' => $isLoggedIn,
                'user_info' => Session::get('user_info', [])
            ]
        ]);
    }
    
    /**
     * 显示设置昵称页面（使用ThinkPHP模板）
     */
    public function setNickname()
    {
        // 检查登录状态
        if (!Session::has('user_id')) {
            return redirect('/login');
        }

        $userId = Session::get('user_id');

        // 检查是否已经有昵称
        $nicknameData = Db::table('user_nickname')
            ->where('user_id', $userId)
            ->find();

        if ($nicknameData) {
            return redirect('/shop');
        }

        return View::fetch('auth/set_nickname');
    }
    
    /**
     * 处理设置昵称请求
     */
    public function doSetNickname()
    {
        // 检查登录状态
        if (!Session::has('user_id')) {
            return json([
                'code' => 401,
                'message' => '请先登录',
                'data' => null
            ]);
        }
        
        $nickname = $this->request->post('nickname', '');
        
        if (empty($nickname)) {
            return json([
                'code' => 400,
                'message' => '昵称不能为空',
                'data' => null
            ]);
        }
        
        if (strlen($nickname) > 20) {
            return json([
                'code' => 400,
                'message' => '昵称长度不能超过20个字符',
                'data' => null
            ]);
        }
        
        $userId = Session::get('user_id');
        
        try {
            // 检查昵称是否已存在
            $existingNickname = Db::table('user_nickname')
                ->where('nickname', $nickname)
                ->find();
                
            if ($existingNickname) {
                return json([
                    'code' => 400,
                    'message' => '昵称已被使用，请选择其他昵称',
                    'data' => null
                ]);
            }
            
            // 检查用户是否已经设置过昵称
            $userNickname = Db::table('user_nickname')
                ->where('user_id', $userId)
                ->find();
                
            if ($userNickname) {
                // 更新昵称
                Db::table('user_nickname')
                    ->where('user_id', $userId)
                    ->update([
                        'nickname' => $nickname,
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
            } else {
                // 插入新昵称
                Db::table('user_nickname')
                    ->insert([
                        'user_id' => $userId,
                        'nickname' => $nickname,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
            }
            
            return json([
                'code' => 200,
                'message' => '昵称设置成功',
                'data' => [
                    'redirect' => '/shop'
                ]
            ]);
            
        } catch (\Exception $e) {
            Log::error('设置昵称失败: ' . $e->getMessage());
            return json([
                'code' => 500,
                'message' => '设置昵称失败，请稍后重试',
                'data' => null
            ]);
        }
    }
    
    /**
     * 显示注册页面（使用ThinkPHP模板）
     */
    public function register()
    {
        // 如果已经登录，跳转到商城
        if (Session::has('user_id')) {
            return redirect('/shop');
        }

        return View::fetch('auth/register');
    }
    
    /**
     * 处理注册请求
     */
    public function doRegister()
    {
        try {
            // 获取JSON数据
            $input = $this->request->getContent();
            $jsonData = json_decode($input, true);

            if (!$jsonData) {
                // 如果不是JSON，尝试获取POST数据
                $data = [
                    'username' => $this->request->post('username', ''),
                    'password' => $this->request->post('password', ''),
                    'confirm_password' => $this->request->post('confirmPassword', ''), // 兼容前端字段名
                    'email' => $this->request->post('email', '')
                ];
            } else {
                // JSON数据处理
                $data = [
                    'username' => $jsonData['username'] ?? '',
                    'password' => $jsonData['password'] ?? '',
                    'confirm_password' => $jsonData['confirmPassword'] ?? '', // 兼容前端字段名
                    'email' => $jsonData['email'] ?? ''
                ];
            }
            
            // 数据验证
            $validate = Validate::rule([
                'username' => 'require|length:3,16|alphaNum',
                'password' => 'require|length:6,20',
                'confirm_password' => 'require|confirm:password',
                'email' => 'require|email'
            ])->message([
                'username.require' => '游戏账号不能为空',
                'username.length' => '游戏账号长度必须在3-16位之间',
                'username.alphaNum' => '游戏账号只能包含字母和数字',
                'password.require' => '游戏密码不能为空',
                'password.length' => '游戏密码长度必须在6-20位之间',
                'confirm_password.require' => '确认密码不能为空',
                'confirm_password.confirm' => '两次输入的密码不一致',
                'email.require' => '邮箱不能为空',
                'email.email' => '邮箱格式不正确'
            ]);
            
            if (!$validate->check($data)) {
                return json([
                    'code' => 400,
                    'message' => $validate->getError(),
                    'data' => null
                ]);
            }
            
            // 使用注册服务进行用户注册
            $registrationService = new RegistrationService();
            $result = $registrationService->register($data);
            
            return json($result);
            
        } catch (\Exception $e) {
            Log::error('用户注册失败: ' . $e->getMessage());
            return json([
                'code' => 500,
                'message' => '注册失败，请稍后重试',
                'data' => null
            ]);
        }
    }

    /**
     * 检查用户名是否可用
     */
    public function checkUsername()
    {
        try {
            // 获取JSON数据
            $input = $this->request->getContent();
            $jsonData = json_decode($input, true);

            $username = $jsonData['username'] ?? $this->request->post('username', '');

            if (empty($username)) {
                return json([
                    'code' => 400,
                    'message' => '用户名不能为空',
                    'data' => null
                ]);
            }

            // 基本格式验证
            if (strlen($username) < 3 || strlen($username) > 16) {
                return json([
                    'code' => 400,
                    'message' => '用户名长度必须在3-16位之间',
                    'data' => ['available' => false]
                ]);
            }

            if (!preg_match('/^[a-zA-Z0-9]+$/', $username)) {
                return json([
                    'code' => 400,
                    'message' => '用户名只能包含字母和数字',
                    'data' => ['available' => false]
                ]);
            }

            // 使用注册服务检查用户名是否存在
            $registrationService = new RegistrationService();
            $validator = new \app\service\UserRegistrationValidator();

            $isExists = $validator->isUsernameExists($username);
            $isForbidden = $validator->isForbiddenUsername($username);

            $available = !$isExists && !$isForbidden;

            return json([
                'code' => 200,
                'message' => $available ? '用户名可用' : '用户名不可用',
                'data' => [
                    'available' => $available,
                    'reason' => $isExists ? 'exists' : ($isForbidden ? 'forbidden' : null)
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('检查用户名失败: ' . $e->getMessage());
            return json([
                'code' => 500,
                'message' => '检查失败，请稍后重试',
                'data' => null
            ]);
        }
    }
}