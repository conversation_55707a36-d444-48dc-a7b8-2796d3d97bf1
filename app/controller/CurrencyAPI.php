<?php

namespace app\controller;

use app\BaseController;
use think\facade\Db;
use think\facade\Session;
use think\facade\Log;
use think\Response;
use app\service\IdTableService;

class CurrencyAPI extends BaseController
{
    /**
     * IdTable服务实例
     */
    private IdTableService $idTableService;

    /**
     * 构造函数
     */
    public function __construct(\think\App $app)
    {
        parent::__construct($app);
        $this->idTableService = new IdTableService();
    }
    /**
     * 检查登录状态
     */
    private function checkAuth()
    {
        $authCheck = \app\common\AuthHelper::checkLoginForApi();
        return $authCheck ? $authCheck->getData() : null;
    }

    /**
     * 检查用户是否为管理员
     * @param string $userId 用户ID
     * @return bool
     */
    private function isAdmin(string $userId): bool
    {
        return \app\common\AuthHelper::isAdmin($userId);
    }

    /**
     * 获取用户货币余额
     * GET /currency/balance
     */
    public function getBalance()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) {
            return json($authCheck);
        }

        $userId = Session::get('user_id');
        
        try {
            // 生成缓存键
            $cacheKey = 'user_balance:' . $userId;
            
            // 尝试从缓存获取余额数据
            $balanceData = \app\service\CacheService::get($cacheKey);
            
            if ($balanceData === null) {
                // 缓存不存在，从数据库获取
                $balanceData = $this->fetchBalanceFromDatabase($userId);
                
                // 缓存余额数据（缓存5分钟）
                \app\service\CacheService::set($cacheKey, $balanceData, 300);
            }

            $response = \app\common\ApiResponse::success($balanceData, '获取成功');
            
            return $response;
        } catch (\Exception $e) {
            Log::error('获取用户余额失败: ' . $e->getMessage() . ' 文件: ' . $e->getFile() . ' 行号: ' . $e->getLine());
            Log::error('错误堆栈: ' . $e->getTraceAsString());
            return \app\common\ApiResponse::error('获取余额失败: ' . $e->getMessage());
        }
    }

    /**
     * 从数据库获取用户余额数据
     * @param string $userId 用户ID
     * @return array 余额数据
     */
    private function fetchBalanceFromDatabase(string $userId): array
    {
        // 获取C币余额、昵称和游戏账号（将字符串user_id转换为数字）
        $numericUserId = is_numeric($userId) ? (int)$userId : (int)preg_replace('/[^0-9]/', '', $userId);
        $cCoinData = Db::connect('mysql_no_prefix')
            ->table('c_coin')
            ->where('user_id', $numericUserId)
            ->field('balance, nickname, game_account')
            ->find();
        
        $cCoinBalance = $cCoinData ? $cCoinData['balance'] : 0;
        $userNickname = $cCoinData ? $cCoinData['nickname'] : null;
        $gameAccount = $cCoinData ? $cCoinData['game_account'] : null;

        // 获取泡点余额（使用优化的IdTableService）
        $coinBalance = $this->idTableService->getUserPoint($userId);

        // 获取积分余额（从seal_web数据库的sealmember表）
        // 直接使用用户ID查询积分余额
        $silverBalance = Db::connect('seal_web')
            ->table('sealmember')
            ->where('id', $userId)
            ->value('hahapoint');
        $silverBalance = $silverBalance ?: 0;

        return [
            'c_coin' => [
                'balance' => $cCoinBalance,
                'nickname' => $userNickname,
                'game_account' => $gameAccount,
                'user_id' => $userId
            ],
            'coin' => $coinBalance,      // 泡点
            'silver' => $silverBalance   // 积分
        ];
    }

    /**
     * 清除用户余额缓存
     * @param string $userId 用户ID
     */
    private function clearUserBalanceCache(string $userId): void
    {
        $cacheKey = 'user_balance:' . $userId;
        \app\service\CacheService::delete($cacheKey);
    }

    /**
     * 修改C币余额
     * POST /currency/c_coin
     * 参数: amount(数量), type(1=增加,2=扣除), description(描述), target_account(目标账号，可选)
     */
    public function modifyCCoin()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) {
            return json($authCheck);
        }

        $amount = $this->request->post('amount');
        $type = $this->request->post('type', 1); // 1=增加, 2=扣除
        $description = $this->request->post('description', '');
        $targetAccount = $this->request->post('target_account', ''); // 目标账号
        
        $currentUserId = Session::get('user_id');
        
        // 如果指定了目标账号，需要验证权限
        if (!empty($targetAccount)) {
            // 检查是否为管理员权限
            if (!$this->isAdmin($currentUserId)) {
                return \app\common\ApiResponse::forbidden('权限不足，无法操作其他用户账号');
            }
            $userId = $targetAccount;
        } else {
            $userId = $currentUserId;
        }

        if (empty($amount) || !is_numeric($amount) || $amount <= 0) {
            return \app\common\ApiResponse::badRequest('请输入有效的金额');
        }

        if (!in_array($type, [1, 2])) {
            return \app\common\ApiResponse::badRequest('无效的操作类型');
        }

        try {
            Db::startTrans();

            // 获取当前余额 - 支持通过账号查询
            // 首先尝试通过game_account字段查询（账号）
            $currentBalance = Db::name('c_coin')
                ->where('game_account', $userId)
                ->value('balance');
            
            // 如果通过账号没找到，再尝试通过user_id字段查询（数字ID）
            if ($currentBalance === null) {
                $numericUserId = is_numeric($userId) ? (int)$userId : (int)preg_replace('/[^0-9]/', '', $userId);
                $currentBalance = Db::name('c_coin')
                    ->where('user_id', $numericUserId)
                    ->value('balance');
            }
            $currentBalance = $currentBalance ?: 0;

            // 计算新余额
            $newBalance = $type == 1 ? $currentBalance + $amount : $currentBalance - $amount;

            // 检查余额是否足够（扣除时）
            if ($type == 2 && $newBalance < 0) {
                Db::rollback();
                return \app\common\ApiResponse::badRequest('C币余额不足');
            }

            // 更新或插入C币记录 - 支持通过账号操作
            // 首先检查是否存在game_account记录
            $existingRecord = Db::name('c_coin')
                ->where('game_account', $userId)
                ->find();
            
            if ($existingRecord) {
                // 通过账号更新
                Db::name('c_coin')
                    ->where('game_account', $userId)
                    ->update(['balance' => $newBalance, 'updated_at' => date('Y-m-d H:i:s')]);
            } else {
                // 检查是否存在user_id记录
                $numericUserId = is_numeric($userId) ? (int)$userId : (int)preg_replace('/[^0-9]/', '', $userId);
                $existingUserIdRecord = Db::name('c_coin')
                    ->where('user_id', $numericUserId)
                    ->find();
                
                if ($existingUserIdRecord) {
                    // 通过user_id更新，同时添加game_account
                    Db::name('c_coin')
                        ->where('user_id', $numericUserId)
                        ->update([
                            'balance' => $newBalance, 
                            'game_account' => $userId,
                            'updated_at' => date('Y-m-d H:i:s')
                        ]);
                } else {
                    // 创建新记录，同时保存账号和数字ID
                    Db::name('c_coin')
                        ->insert([
                            'user_id' => $numericUserId,
                            'game_account' => $userId,
                            'nickname' => $userId,
                            'balance' => $newBalance,
                            'created_at' => date('Y-m-d H:i:s'),
                            'updated_at' => date('Y-m-d H:i:s')
                        ]);
                }
            }

            // 记录交易 - 同时记录账号和数字ID
            $numericUserId = is_numeric($userId) ? (int)$userId : (int)preg_replace('/[^0-9]/', '', $userId);
            
            // 先检查 c_coin_transactions 表结构
            $transactionData = [
                'user_id' => $numericUserId,
                'type' => $type,
                'amount' => $amount,
                'balance_before' => $currentBalance,
                'balance_after' => $newBalance,
                'description' => $description,
                'created_at' => date('Y-m-d H:i:s')
            ];
            
            // 使用原生SQL插入交易记录，包含game_account字段
            $sql = "INSERT INTO c_coin_transactions (user_id, game_account, type, amount, balance_before, balance_after, description, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
            Db::execute($sql, [
                $numericUserId,
                $userId,
                $type,
                $amount,
                $currentBalance,
                $newBalance,
                $description,
                date('Y-m-d H:i:s')
            ]);

            Db::commit();
            
            // 清除用户余额缓存
            $this->clearUserBalanceCache($userId);

            return \app\common\ApiResponse::success(
                [
                    'user_id' => $userId,
                    'amount' => $amount,
                    'type' => $type,
                    'balance_before' => $currentBalance,
                    'balance_after' => $newBalance,
                    'description' => $description
                ],
                $type == 1 ? 'C币增加成功' : 'C币扣除成功'
            );
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('C币操作失败: ' . $e->getMessage());
            return \app\common\ApiResponse::error('C币操作失败');
        }
    }

    /**
     * 修改泡点余额
     * POST /currency/coin
     * 参数: amount(数量), type(1=增加,2=扣除), description(描述)
     */
    public function modifyCoin()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) {
            return json($authCheck);
        }

        $amount = $this->request->post('amount');
        $type = $this->request->post('type', 1); // 1=增加, 2=扣除
        $description = $this->request->post('description', '');
        $userId = Session::get('user_id');

        if (empty($amount) || !is_numeric($amount) || $amount <= 0) {
            return \app\common\ApiResponse::badRequest('请输入有效的金额');
        }

        if (!in_array($type, [1, 2])) {
            return \app\common\ApiResponse::badRequest('无效的操作类型');
        }

        try {
            Db::startTrans();

            // 获取当前泡点余额（从idtable1-5表）
            $currentBalance = 0;
            $userTables = [];
            for ($i = 1; $i <= 5; $i++) {
                $tablePoint = Db::connect('seal_member')
                    ->table('idtable' . $i)
                    ->where('id', $userId)
                    ->value('point');
                if ($tablePoint !== null) {
                    $userTables[$i] = $tablePoint;
                    $currentBalance += $tablePoint;
                }
            }

            // 计算新余额
            $newBalance = $type == 1 ? $currentBalance + $amount : $currentBalance - $amount;

            // 检查余额是否足够（扣除时）
            if ($type == 2 && $newBalance < 0) {
                Db::rollback();
                return \app\common\ApiResponse::badRequest('泡点余额不足');
            }

            // 更新泡点记录（优先更新第一个找到的表）
            $updated = false;
            for ($i = 1; $i <= 5; $i++) {
                if (isset($userTables[$i])) {
                    Db::connect('seal_member')
                        ->table('idtable' . $i)
                        ->where('id', $userId)
                        ->update(['point' => $newBalance]);
                    $updated = true;
                    break;
                }
            }

            // 如果没有找到用户记录，在idtable1中创建
            if (!$updated) {
                Db::connect('seal_member')
                    ->table('idtable1')
                    ->insert([
                        'id' => $userId,
                        'point' => $newBalance
                    ]);
            }

            Db::commit();
            
            // 清除用户余额缓存
            $this->clearUserBalanceCache($userId);

            return \app\common\ApiResponse::success(
                [
                    'user_id' => $userId,
                    'amount' => $amount,
                    'type' => $type,
                    'balance_before' => $currentBalance,
                    'balance_after' => $newBalance,
                    'description' => $description
                ],
                $type == 1 ? '泡点增加成功' : '泡点扣除成功'
            );
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('泡点操作失败: ' . $e->getMessage());
            return \app\common\ApiResponse::error('泡点操作失败');
        }
    }

    /**
     * 修改积分余额
     * POST /currency/silver
     * 参数: amount(数量), type(1=增加,2=扣除), description(描述)
     */
    public function modifySilver()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) {
            return json($authCheck);
        }

        $amount = $this->request->post('amount');
        $type = $this->request->post('type', 1); // 1=增加, 2=扣除
        $description = $this->request->post('description', '');
        $userId = Session::get('user_id');

        if (empty($amount) || !is_numeric($amount) || $amount <= 0) {
            return \app\common\ApiResponse::badRequest('请输入有效的金额');
        }

        if (!in_array($type, [1, 2])) {
            return \app\common\ApiResponse::badRequest('无效的操作类型');
        }

        try {
            Db::startTrans();

            // 获取当前积分余额（从seal_web数据库的sealmember表）
            $currentBalance = Db::connect('seal_web')
                ->table('sealmember')
                ->where('id', $userId)
                ->value('hahapoint');
            $currentBalance = $currentBalance ?: 0;

            // 计算新余额
            $newBalance = $type == 1 ? $currentBalance + $amount : $currentBalance - $amount;

            // 检查余额是否足够（扣除时）
            if ($type == 2 && $newBalance < 0) {
                Db::rollback();
                return \app\common\ApiResponse::badRequest('积分余额不足');
            }

            // 更新积分记录
            $userExists = Db::connect('seal_web')
                ->table('sealmember')
                ->where('id', $userId)
                ->find();

            if ($userExists) {
                Db::connect('seal_web')
                    ->table('sealmember')
                    ->where('id', $userId)
                    ->update(['hahapoint' => $newBalance]);
            } else {
                Db::connect('seal_web')
                    ->table('sealmember')
                    ->insert([
                        'id' => $userId,
                        'hahapoint' => $newBalance
                    ]);
            }

            Db::commit();
            
            // 清除用户余额缓存
            $this->clearUserBalanceCache($userId);

            return \app\common\ApiResponse::success(
                [
                    'user_id' => $userId,
                    'amount' => $amount,
                    'type' => $type,
                    'balance_before' => $currentBalance,
                    'balance_after' => $newBalance,
                    'description' => $description
                ],
                $type == 1 ? '积分增加成功' : '积分扣除成功'
            );
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('积分操作失败: ' . $e->getMessage());
            return \app\common\ApiResponse::error('积分操作失败');
        }
    }

    /**
     * 批量货币操作（用于购买商品时的扣除）
     * POST /currency/batch-deduct
     * 参数: operations[{type: 'c_coin|coin|silver', amount: 100}], description
     */
    public function batchDeduct()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) {
            return json($authCheck);
        }

        $operations = $this->request->post('operations', []);
        $description = $this->request->post('description', '商品购买');
        $userId = Session::get('user_id');

        if (empty($operations) || !is_array($operations)) {
            return \app\common\ApiResponse::badRequest('请提供有效的操作列表');
        }

        try {
            Db::startTrans();

            $results = [];

            foreach ($operations as $operation) {
                $type = $operation['type'] ?? '';
                $amount = $operation['amount'] ?? 0;

                if (!in_array($type, ['c_coin', 'coin', 'silver']) || $amount <= 0) {
                    Db::rollback();
                    return \app\common\ApiResponse::badRequest('无效的操作参数');
                }

                // 根据类型执行相应的扣除操作
                switch ($type) {
                    case 'c_coin':
                        $result = $this->deductCCoin($userId, $amount, $description);
                        break;
                    case 'coin':
                        $result = $this->deductCoin($userId, $amount, $description);
                        break;
                    case 'silver':
                        $result = $this->deductSilver($userId, $amount, $description);
                        break;
                }

                if (!$result['success']) {
                    Db::rollback();
                    return \app\common\ApiResponse::badRequest($result['message']);
                }

                $results[] = $result;
            }

            Db::commit();
            
            // 清除用户余额缓存
            $this->clearUserBalanceCache($userId);

            return \app\common\ApiResponse::success($results, '批量扣除成功');
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('批量扣除失败: ' . $e->getMessage());
            return \app\common\ApiResponse::error('批量扣除失败');
        }
    }

    /**
     * 扣除C币（内部方法）
     * 支持通过账号或数字ID进行扣除
     */
    private function deductCCoin($userId, $amount, $description)
    {
        // 首先尝试通过game_account字段查询（账号）
        $currentBalance = Db::connect('mysql_no_prefix')
            ->table('c_coin')
            ->where('game_account', $userId)
            ->value('balance');
        
        $useGameAccount = true;
        
        // 如果通过账号没找到，再尝试通过user_id字段查询（数字ID）
        if ($currentBalance === null) {
            $numericUserId = is_numeric($userId) ? (int)$userId : (int)preg_replace('/[^0-9]/', '', $userId);
            $currentBalance = Db::connect('mysql_no_prefix')
                ->table('c_coin')
                ->where('user_id', $numericUserId)
                ->value('balance');
            $useGameAccount = false;
        }
        
        $currentBalance = $currentBalance ?: 0;

        if ($currentBalance < $amount) {
            return ['success' => false, 'message' => 'C币余额不足'];
        }

        $newBalance = $currentBalance - $amount;
        $numericUserId = is_numeric($userId) ? (int)$userId : (int)preg_replace('/[^0-9]/', '', $userId);

        // 根据查询方式选择更新方式
        if ($useGameAccount) {
            Db::connect('mysql_no_prefix')
                ->table('c_coin')
                ->where('game_account', $userId)
                ->update(['balance' => $newBalance, 'updated_at' => date('Y-m-d H:i:s')]);
        } else {
            Db::connect('mysql_no_prefix')
                ->table('c_coin')
                ->where('user_id', $numericUserId)
                ->update(['balance' => $newBalance, 'updated_at' => date('Y-m-d H:i:s')]);
        }

        // 记录交易 - 同时记录账号和数字ID
        Db::connect('mysql_no_prefix')
            ->table('c_coin_transactions')
            ->insert([
                'user_id' => $numericUserId,
                'game_account' => $userId,
                'type' => 2, // 扣除
                'amount' => $amount,
                'balance_before' => $currentBalance,
                'balance_after' => $newBalance,
                'description' => $description,
                'created_at' => date('Y-m-d H:i:s')
            ]);

        return [
            'success' => true,
            'type' => 'c_coin',
            'amount' => $amount,
            'balance_before' => $currentBalance,
            'balance_after' => $newBalance
        ];
    }

    /**
     * 扣除泡点（内部方法）
     */
    private function deductCoin($userId, $amount, $description)
    {
        // 获取当前泡点余额（从idtable1-5表）
        $currentBalance = 0;
        $userTables = [];
        for ($i = 1; $i <= 5; $i++) {
            $tablePoint = Db::connect('seal_member')
                ->table('idtable' . $i)
                ->where('id', $userId)
                ->value('point');
            if ($tablePoint !== null) {
                $userTables[$i] = $tablePoint;
                $currentBalance += $tablePoint;
            }
        }

        if ($currentBalance < $amount) {
            return ['success' => false, 'message' => '泡点余额不足'];
        }

        $newBalance = $currentBalance - $amount;

        // 更新泡点记录（优先更新第一个找到的表）
        for ($i = 1; $i <= 5; $i++) {
            if (isset($userTables[$i])) {
                Db::connect('seal_member')
                    ->table('idtable' . $i)
                    ->where('id', $userId)
                    ->update(['point' => $newBalance]);
                break;
            }
        }

        return [
            'success' => true,
            'type' => 'coin',
            'amount' => $amount,
            'balance_before' => $currentBalance,
            'balance_after' => $newBalance
        ];
    }

    /**
     * 扣除积分（内部方法）
     */
    private function deductSilver($userId, $amount, $description)
    {
        // 获取当前积分余额（从seal_web数据库的sealmember表）
        $currentBalance = Db::connect('seal_web')
            ->table('sealmember')
            ->where('id', $userId)
            ->value('hahapoint');
        $currentBalance = $currentBalance ?: 0;

        if ($currentBalance < $amount) {
            return ['success' => false, 'message' => '积分余额不足'];
        }

        $newBalance = $currentBalance - $amount;

        Db::connect('seal_web')
            ->table('sealmember')
            ->where('id', $userId)
            ->update(['hahapoint' => $newBalance]);

        return [
            'success' => true,
            'type' => 'silver',
            'amount' => $amount,
            'balance_before' => $currentBalance,
            'balance_after' => $newBalance
        ];
    }
}