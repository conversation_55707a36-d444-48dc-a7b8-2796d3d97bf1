<?php

namespace app\controller;

use app\BaseController;
use think\facade\View;
use think\facade\Log;
use app\controller\admin\OrderController;

class AdminOrders extends BaseController
{
    /**
     * 订单管理主页（新版ThinkPHP模板）
     */
    public function index()
    {
        try {
            // 获取订单统计数据
            $orderController = new OrderController($this->app);
            $orderController->request = $this->request;
            
            // 获取订单列表（第一页数据用于初始显示）
            $this->request = $this->request->withGet([
                'page' => 1,
                'limit' => 20
            ]);
            $orderController->request = $this->request;
            
            $ordersResponse = $orderController->getOrderList();
            $ordersData = json_decode($ordersResponse->getContent(), true);
            
            // 传递数据到视图
            View::assign('orders', $ordersData['data'] ?? []);
            View::assign('total_orders', $ordersData['total'] ?? 0);
            
            return View::fetch('admin/orders_new');
            
        } catch (\Exception $e) {
            Log::error('订单管理页面加载失败: ' . $e->getMessage());
            
            // 传递默认数据
            View::assign('orders', []);
            View::assign('total_orders', 0);
            
            return View::fetch('admin/orders_new');
        }
    }
    
    /**
     * 获取订单列表API
     */
    public function getOrderList()
    {
        try {
            $orderController = new OrderController($this->app);
            $orderController->request = $this->request;
            
            return $orderController->getOrderList();
            
        } catch (\Exception $e) {
            Log::error('获取订单列表失败: ' . $e->getMessage());
            return $this->systemError('获取订单列表失败');
        }
    }
    
    /**
     * 获取订单详情API
     */
    public function getOrderDetail()
    {
        try {
            $orderController = new OrderController($this->app);
            $orderController->request = $this->request;
            
            return $orderController->getOrderDetail();
            
        } catch (\Exception $e) {
            Log::error('获取订单详情失败: ' . $e->getMessage());
            return $this->systemError('获取订单详情失败');
        }
    }
    
    /**
     * 更新订单状态API
     */
    public function updateOrderStatus()
    {
        try {
            $orderController = new OrderController($this->app);
            $orderController->request = $this->request;
            
            return $orderController->updateOrderStatus();
            
        } catch (\Exception $e) {
            Log::error('更新订单状态失败: ' . $e->getMessage());
            return $this->systemError('更新订单状态失败');
        }
    }
    
    /**
     * 批量操作订单API
     */
    public function batchOperation()
    {
        try {
            $operation = $this->request->post('operation');
            $orderIds = $this->request->post('order_ids', []);
            
            if (empty($operation) || empty($orderIds)) {
                return $this->paramError('操作类型和订单ID不能为空');
            }
            
            $orderController = new OrderController($this->app);
            $orderController->request = $this->request;
            
            $results = [];
            $successCount = 0;
            $failCount = 0;
            
            foreach ($orderIds as $orderId) {
                try {
                    // 设置当前订单ID
                    $this->request = $this->request->withPost(['order_id' => $orderId]);
                    $orderController->request = $this->request;
                    
                    switch ($operation) {
                        case 'complete':
                            $this->request = $this->request->withPost(['status' => 'completed']);
                            $orderController->request = $this->request;
                            $response = $orderController->updateOrderStatus();
                            break;
                        case 'cancel':
                            $this->request = $this->request->withPost(['status' => 'cancelled']);
                            $orderController->request = $this->request;
                            $response = $orderController->updateOrderStatus();
                            break;
                        case 'refund':
                            $this->request = $this->request->withPost(['status' => 'refunded']);
                            $orderController->request = $this->request;
                            $response = $orderController->updateOrderStatus();
                            break;
                        default:
                            throw new \Exception('不支持的操作类型');
                    }
                    
                    $responseData = json_decode($response->getContent(), true);
                    if ($responseData['code'] === 200) {
                        $successCount++;
                        $results[] = ['order_id' => $orderId, 'status' => 'success'];
                    } else {
                        $failCount++;
                        $results[] = ['order_id' => $orderId, 'status' => 'failed', 'message' => $responseData['message']];
                    }
                    
                } catch (\Exception $e) {
                    $failCount++;
                    $results[] = ['order_id' => $orderId, 'status' => 'failed', 'message' => $e->getMessage()];
                }
            }
            
            return $this->success([
                'success_count' => $successCount,
                'fail_count' => $failCount,
                'results' => $results
            ], "批量操作完成，成功：{$successCount}，失败：{$failCount}");
            
        } catch (\Exception $e) {
            Log::error('批量操作订单失败: ' . $e->getMessage());
            return $this->systemError('批量操作失败');
        }
    }
    
    /**
     * 导出订单数据API
     */
    public function exportOrders()
    {
        try {
            $format = $this->request->get('format', 'csv');
            $filters = $this->request->get();
            
            // 获取所有订单数据
            $this->request = $this->request->withGet([
                'page' => 1,
                'limit' => 10000, // 大数量导出
                'search' => $filters['search'] ?? '',
                'status' => $filters['status'] ?? '',
                'start_date' => $filters['start_date'] ?? '',
                'end_date' => $filters['end_date'] ?? ''
            ]);
            
            $orderController = new OrderController($this->app);
            $orderController->request = $this->request;
            
            $ordersResponse = $orderController->getOrderList();
            $ordersData = json_decode($ordersResponse->getContent(), true);
            
            if ($ordersData['code'] !== 200) {
                return $this->systemError('获取订单数据失败');
            }
            
            $orders = $ordersData['data'] ?? [];
            
            if ($format === 'csv') {
                return $this->exportToCsv($orders);
            } elseif ($format === 'excel') {
                return $this->exportToExcel($orders);
            } else {
                return $this->paramError('不支持的导出格式');
            }
            
        } catch (\Exception $e) {
            Log::error('导出订单数据失败: ' . $e->getMessage());
            return $this->systemError('导出订单数据失败');
        }
    }
    
    /**
     * 导出为CSV格式
     */
    private function exportToCsv($orders)
    {
        $filename = 'orders_' . date('Y-m-d_H-i-s') . '.csv';
        
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename=' . $filename);
        
        $output = fopen('php://output', 'w');
        
        // 写入BOM以支持中文
        fwrite($output, "\xEF\xBB\xBF");
        
        // 写入表头
        fputcsv($output, ['订单ID', '用户账号', '商品名称', '数量', '总价', '状态', '创建时间']);
        
        // 写入数据
        foreach ($orders as $order) {
            fputcsv($output, [
                $order['id'] ?? '',
                $order['user_account'] ?? '',
                $order['item_name'] ?? '',
                $order['quantity'] ?? '',
                $order['total_price'] ?? '',
                $order['status_name'] ?? '',
                $order['created_at'] ?? ''
            ]);
        }
        
        fclose($output);
        exit;
    }
    
    /**
     * 导出为Excel格式（简化版）
     */
    private function exportToExcel($orders)
    {
        // 这里简化为CSV格式，实际项目中可以使用PhpSpreadsheet库生成真正的Excel文件
        return $this->exportToCsv($orders);
    }
    
    /**
     * 获取订单统计信息API
     */
    public function getOrderStats()
    {
        try {
            $stats = [
                'total_orders' => 0,
                'completed_orders' => 0,
                'pending_orders' => 0,
                'cancelled_orders' => 0,
                'total_revenue' => 0,
                'today_orders' => 0,
                'today_revenue' => 0
            ];
            
            // 获取总订单数
            $totalResult = \think\facade\Db::table('purchase_history')->count();
            $stats['total_orders'] = $totalResult;
            
            // 获取已完成订单数
            $completedResult = \think\facade\Db::table('purchase_history')->where('status', 'completed')->count();
            $stats['completed_orders'] = $completedResult;
            
            // 获取待处理订单数
            $pendingResult = \think\facade\Db::table('purchase_history')->where('status', 'pending')->count();
            $stats['pending_orders'] = $pendingResult;
            
            // 获取已取消订单数
            $cancelledResult = \think\facade\Db::table('purchase_history')->where('status', 'cancelled')->count();
            $stats['cancelled_orders'] = $cancelledResult;
            
            // 获取总收入
            $revenueResult = \think\facade\Db::table('purchase_history')
                ->where('status', 'completed')
                ->sum('total_price');
            $stats['total_revenue'] = $revenueResult ?: 0;
            
            // 获取今日订单数
            $todayOrdersResult = \think\facade\Db::table('purchase_history')
                ->whereTime('created_at', 'today')
                ->count();
            $stats['today_orders'] = $todayOrdersResult;
            
            // 获取今日收入
            $todayRevenueResult = \think\facade\Db::table('purchase_history')
                ->where('status', 'completed')
                ->whereTime('created_at', 'today')
                ->sum('total_price');
            $stats['today_revenue'] = $todayRevenueResult ?: 0;
            
            return $this->success($stats, '获取订单统计信息成功');
            
        } catch (\Exception $e) {
            Log::error('获取订单统计信息失败: ' . $e->getMessage());
            return $this->systemError('获取订单统计信息失败');
        }
    }
}
