<?php

namespace app\controller;

use app\BaseController;
use think\facade\Db;
use think\facade\Session;
use think\facade\View;
use think\facade\Log;
use think\Response;
use app\service\ItemService;

class Shop extends BaseController
{
    /**
     * 检查登录状态中间件
     */
    protected function checkAuth()
    {
        return \app\common\AuthHelper::checkLoginForPage();
    }

    /**
     * 获取商城设置
     */
    private function getShopSettings()
    {
        try {
            $configFile = app()->getConfigPath() . 'shop_settings.php';
            $settings = [];

            if (file_exists($configFile)) {
                $settings = include $configFile;
            }

            // 默认设置
            $defaultSettings = [
                'shop_enabled' => true,
                'items_per_page' => 12,
                'max_purchase_quantity' => 99,
                'maintenance_message' => '商城正在维护中，请稍后再试。'
            ];

            return array_merge($defaultSettings, $settings);
        } catch (\Exception $e) {
            Log::error('获取商城设置失败: ' . $e->getMessage());
            // 返回默认设置，商城启用
            return [
                'shop_enabled' => true,
                'items_per_page' => 12,
                'max_purchase_quantity' => 99,
                'maintenance_message' => '商城正在维护中，请稍后再试。'
            ];
        }
    }

    /**
     * 获取基本设置
     */
    private function getGeneralSettings()
    {
        try {
            $configFile = app()->getConfigPath() . 'general_settings.php';
            $settings = [];

            if (file_exists($configFile)) {
                $settings = include $configFile;
            }

            // 默认设置
            $defaultSettings = [
                'site_name' => '游戏商城',
                'site_url' => 'http://localhost',
                'site_description' => '',
                'timezone' => 'Asia/Shanghai',
                'language' => 'zh-CN',
                'maintenance_mode' => false,
                'admin_email' => ''
            ];

            return array_merge($defaultSettings, $settings);
        } catch (\Exception $e) {
            Log::error('获取基本设置失败: ' . $e->getMessage());
            // 返回默认设置
            return [
                'site_name' => '游戏商城',
                'site_url' => 'http://localhost',
                'site_description' => '',
                'timezone' => 'Asia/Shanghai',
                'language' => 'zh-CN',
                'maintenance_mode' => false,
                'admin_email' => ''
            ];
        }
    }

    /**
     * 获取支付设置
     */
    private function getPaymentSettings()
    {
        try {
            $configFile = app()->getConfigPath() . 'payment_settings.php';
            $settings = [];

            if (file_exists($configFile)) {
                $settings = include $configFile;
            }

            // 默认设置
            $defaultSettings = [
                'enable_ccoin' => true,
                'enable_coin' => true,
                'enable_silver' => true,
                'show_recharge_button' => true,
                'min_recharge_amount' => 1,
                'max_recharge_amount' => 10000
            ];

            return array_merge($defaultSettings, $settings);
        } catch (\Exception $e) {
            Log::error('获取支付设置失败: ' . $e->getMessage());
            // 返回默认设置
            return [
                'enable_ccoin' => true,
                'enable_coin' => true,
                'enable_silver' => true,
                'show_recharge_button' => true,
                'min_recharge_amount' => 1,
                'max_recharge_amount' => 10000
            ];
        }
    }
    
    /**
     * 商城主页（使用ThinkPHP模板）
     * 注意：登录状态和昵称验证已由 AuthMiddleware 中间件统一处理
     */
    public function index()
    {
        try {
            // 检查商城是否启用
            $shopSettings = $this->getShopSettings();
            if (!$shopSettings['shop_enabled']) {
                // 商城未启用，显示维护页面
                View::assign('maintenance_message', $shopSettings['maintenance_message'] ?? '商城正在维护中，请稍后再试。');
                return View::fetch('shop/maintenance');
            }

            // 获取基本设置
            $generalSettings = $this->getGeneralSettings();

            // 获取支付设置
            $paymentSettings = $this->getPaymentSettings();

            // 认证逻辑已由 AuthMiddleware 统一处理
            $userInfo = \app\middleware\AuthMiddleware::getCurrentUserInfo();
            $nickname = \app\middleware\AuthMiddleware::getCurrentUserNickname();

            // 传递数据到视图
            View::assign('user_info', $userInfo);
            View::assign('user_nickname', $nickname);
            View::assign('general_settings', $generalSettings);
            View::assign('shop_settings', $shopSettings);
            View::assign('payment_settings', $paymentSettings);

            // 添加调试信息
            Log::info('Shop::index - 开始渲染ThinkPHP模板');
            Log::info('Shop::index - 用户信息: ' . json_encode($userInfo));
            Log::info('Shop::index - 昵称: ' . $nickname);

            $result = View::fetch('shop/index');
            Log::info('Shop::index - ThinkPHP模板渲染成功');

            return $result;

        } catch (\Exception $e) {
            // 记录详细错误信息
            Log::error('Shop::index - ThinkPHP模板加载失败: ' . $e->getMessage());
            Log::error('Shop::index - 错误堆栈: ' . $e->getTraceAsString());

            // 返回详细错误信息
            return 'ThinkPHP模板加载失败: ' . $e->getMessage() . '<br><br>错误详情:<br>' . nl2br($e->getTraceAsString());
        }
    }
    
    /**
     * 获取商品列表API
     * 注意：登录状态和昵称验证已由 AuthMiddleware 中间件统一处理
     */
    public function getItems()
    {
        try {
            $page = (int)$this->request->get('page', 1);
            $limit = (int)$this->request->get('limit', 10);
            $category = $this->request->get('category', '');
            $search = $this->request->get('search', '');
            $sort = $this->request->get('sort', 'default');

            // 参数验证
            if ($page < 1) {
                return $this->paramError('页码必须大于0');
            }

            if ($limit < 1 || $limit > 100) {
                return $this->paramError('每页数量必须在1-100之间');
            }

            // 商城只显示上架的商品（status=1）
            $result = ItemService::getItems($page, $limit, $category, $search, '1', $sort);

            // 使用新的分页响应方法
            if (isset($result['items']) && isset($result['total'])) {
                return $this->paginate(
                    $result['items'],
                    $result['total'],
                    $page,
                    $limit,
                    '获取商品列表成功'
                );
            } else {
                return $this->success($result, '获取商品列表成功');
            }

        } catch (\Exception $e) {
            Log::error('获取商品列表失败: ' . $e->getMessage());
            return $this->systemError('获取商品列表失败，请稍后重试');
        }
    }
    
    /**
     * 通过API获取商品完整信息
     */
    private function getItemInfoFromAPI($itemId)
    {
        try {
            $apiUrl = \app\service\ApiService::getItemInfoUrl($itemId);
            $result = \app\service\ApiService::sendRequest($apiUrl);
            
            if ($result === false) {
                return json([
                    'code' => 500,
                    'message' => '获取商品信息失败',
                    'data' => null
                ]);
            }
            
            // $result已经是解析后的数组，无需再次解析
            $data = $result;
            
            if ($data && $data['code'] === 200 && isset($data['data'])) {
                return [
                    'icon' => $data['data']['icon'] ?? '/static/img/default.png',
                    'name' => $data['data']['name'] ?? '未知物品',
                    'explanation' => $data['data']['explanation'] ?? '暂无说明'
                ];
            }
            
            // API返回错误，返回默认值
            return [
                'icon' => '/static/img/default.png',
                'name' => '未知物品',
                'explanation' => '暂无说明'
            ];
            
        } catch (\Exception $e) {
            // 异常处理，返回默认值
            return [
                'icon' => '/static/img/default.png',
                'name' => '未知物品',
                'explanation' => '暂无说明'
            ];
        }
    }
    
    /**
     * 获取货币类型名称
     */
    private function getPriceTypeName($priceType)
    {
        return \app\service\ShopConfigService::getCurrencyName($priceType);
    }

    /**
     * 获取分类名称
     */
    private function getClassName($class)
    {
        return \app\service\ShopConfigService::getCategoryName($class);
    }
    
    /**
     * 获取限制类型名称
     */
    private function getRestrictionName($restriction)
    {
        $restrictions = [
            0 => '不限购',
            1 => '账号限购',
            2 => '每日限购',
            3 => '每周限购',
            4 => '每月限购'
        ];
        
        return $restrictions[$restriction] ?? '无限制';
    }
    
    /**
     * 获取商品详情API
     */
    public function getItemDetail($id = null)
    {
        try {
            $itemId = $id ?: $this->request->param('id');

            if (!$itemId) {
                return $this->paramError('商品ID不能为空');
            }

            $result = ItemService::getItemDetail($itemId);

            if ($result) {
                return $this->success($result, '获取商品详情成功');
            } else {
                return $this->notFound('商品不存在');
            }

        } catch (\Exception $e) {
            Log::error('获取商品详情失败: ' . $e->getMessage());
            return $this->systemError('获取商品详情失败，请稍后重试');
        }
    }

    /**
     * 购买商品
     */
    public function buyItem()
    {
        // 认证逻辑已由 AuthMiddleware 统一处理
        $itemId = $this->request->post('item_id');
        $quantity = $this->request->post('quantity', 1);
        $userId = \app\middleware\AuthMiddleware::getCurrentUserId();
        // 货币扣除回调，兼容原有 CurrencyAPI 扣费逻辑
        $deductCurrency = function($operations, $desc) use ($userId) {
            $currencyAPI = new \app\controller\CurrencyAPI($this->app);
            $newRequest = $this->request->withPost([
                'operations' => $operations,
                'description' => $desc
            ]);
            $currencyAPI->request = $newRequest;
            $deductResult = $currencyAPI->batchDeduct();
            return json_decode($deductResult->getContent(), true);
        };
        $result = \app\service\ItemService::buyItem($userId, $itemId, $quantity, $deductCurrency);

        // 将ItemService的响应格式转换为ApiResponse格式
        if ($result['code'] === 200) {
            return $this->success($result['data'], $result['message']);
        } else {
            return $this->error($result['message'], $result['code'], $result['data']);
        }
    }
    
    /**
     * 获取商城状态API
     * 用于维护页面检查商城是否恢复
     */
    public function getStatus()
    {
        try {
            $settings = $this->getShopSettings();

            return json([
                'success' => true,
                'message' => '获取状态成功',
                'data' => [
                    'shop_enabled' => $settings['shop_enabled'],
                    'maintenance_message' => $settings['maintenance_message'] ?? '商城正在维护中，请稍后再试。'
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取商城状态失败: ' . $e->getMessage());
            return json([
                'success' => false,
                'message' => '获取状态失败',
                'data' => [
                    'shop_enabled' => false,
                    'maintenance_message' => '系统错误，请稍后再试。'
                ]
            ]);
        }
    }

    // profile方法已删除
}