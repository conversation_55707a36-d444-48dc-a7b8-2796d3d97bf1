<?php

namespace app\controller;

use app\BaseController;
use think\facade\View;
use think\facade\Log;
use app\controller\admin\UserController;

class AdminUsers extends BaseController
{
    /**
     * 用户管理主页（新版ThinkPHP模板）
     */
    public function index()
    {
        try {
            // 获取用户统计数据
            $userController = new UserController($this->app);
            $userController->request = $this->request;
            
            // 获取用户列表（第一页数据用于初始显示）
            $this->request = $this->request->withGet([
                'page' => 1,
                'limit' => 20
            ]);
            $userController->request = $this->request;
            
            $usersResponse = $userController->getUserList();
            $usersData = json_decode($usersResponse->getContent(), true);
            
            // 传递数据到视图
            View::assign('users', $usersData['data'] ?? []);
            View::assign('total_users', $usersData['total'] ?? 0);
            
            return View::fetch('admin/users_new');
            
        } catch (\Exception $e) {
            Log::error('用户管理页面加载失败: ' . $e->getMessage());
            
            // 传递默认数据
            View::assign('users', []);
            View::assign('total_users', 0);
            
            return View::fetch('admin/users_new');
        }
    }
    
    /**
     * 获取用户列表API
     */
    public function getUserList()
    {
        try {
            $userController = new UserController($this->app);
            $userController->request = $this->request;
            
            return $userController->getUserList();
            
        } catch (\Exception $e) {
            Log::error('获取用户列表失败: ' . $e->getMessage());
            return $this->systemError('获取用户列表失败');
        }
    }
    
    /**
     * 获取用户详情API
     */
    public function getUserDetail()
    {
        try {
            $userController = new UserController($this->app);
            $userController->request = $this->request;
            
            return $userController->getUserDetail();
            
        } catch (\Exception $e) {
            Log::error('获取用户详情失败: ' . $e->getMessage());
            return $this->systemError('获取用户详情失败');
        }
    }
    
    /**
     * 更新用户昵称API
     */
    public function updateUserNickname()
    {
        try {
            $userController = new UserController($this->app);
            $userController->request = $this->request;

            return $userController->updateUserNickname();

        } catch (\Exception $e) {
            Log::error('更新用户昵称失败: ' . $e->getMessage());
            return $this->systemError('更新用户昵称失败');
        }
    }

    /**
     * 封禁用户API
     */
    public function banUser()
    {
        try {
            $userController = new UserController($this->app);
            $userController->request = $this->request;

            return $userController->banUser();

        } catch (\Exception $e) {
            Log::error('封禁用户失败: ' . $e->getMessage());
            return $this->systemError('封禁用户失败');
        }
    }

    /**
     * 解封用户API
     */
    public function unbanUser()
    {
        try {
            $userController = new UserController($this->app);
            $userController->request = $this->request;

            return $userController->unbanUser();

        } catch (\Exception $e) {
            Log::error('解封用户失败: ' . $e->getMessage());
            return $this->systemError('解封用户失败');
        }
    }
    
    /**
     * 批量操作用户API
     */
    public function batchOperation()
    {
        try {
            $operation = $this->request->post('operation');
            $userIds = $this->request->post('user_ids', []);
            
            if (empty($operation) || empty($userIds)) {
                return $this->paramError('操作类型和用户ID不能为空');
            }
            
            $userController = new UserController($this->app);
            $userController->request = $this->request;
            
            $results = [];
            $successCount = 0;
            $failCount = 0;
            
            foreach ($userIds as $userId) {
                try {
                    // 设置当前用户ID
                    $this->request = $this->request->withPost(['user_id' => $userId]);
                    $userController->request = $this->request;
                    
                    switch ($operation) {
                        case 'ban':
                            $response = $userController->banUser();
                            break;
                        case 'unban':
                            $response = $userController->unbanUser();
                            break;
                        default:
                            throw new \Exception('不支持的操作类型');
                    }
                    
                    $responseData = json_decode($response->getContent(), true);
                    if ($responseData['code'] === 200) {
                        $successCount++;
                        $results[] = ['user_id' => $userId, 'status' => 'success'];
                    } else {
                        $failCount++;
                        $results[] = ['user_id' => $userId, 'status' => 'failed', 'message' => $responseData['message']];
                    }
                    
                } catch (\Exception $e) {
                    $failCount++;
                    $results[] = ['user_id' => $userId, 'status' => 'failed', 'message' => $e->getMessage()];
                }
            }
            
            return $this->success([
                'success_count' => $successCount,
                'fail_count' => $failCount,
                'results' => $results
            ], "批量操作完成，成功：{$successCount}，失败：{$failCount}");
            
        } catch (\Exception $e) {
            Log::error('批量操作用户失败: ' . $e->getMessage());
            return $this->systemError('批量操作失败');
        }
    }
    
    /**
     * 导出用户数据API
     */
    public function exportUsers()
    {
        try {
            $format = $this->request->get('format', 'csv');
            $filters = $this->request->get();
            
            // 获取所有用户数据
            $this->request = $this->request->withGet([
                'page' => 1,
                'limit' => 10000, // 大数量导出
                'search' => $filters['search'] ?? '',
                'status' => $filters['status'] ?? ''
            ]);
            
            $userController = new UserController($this->app);
            $userController->request = $this->request;
            
            $usersResponse = $userController->getUserList();
            $usersData = json_decode($usersResponse->getContent(), true);
            
            if ($usersData['code'] !== 200) {
                return $this->systemError('获取用户数据失败');
            }
            
            $users = $usersData['data'] ?? [];
            
            if ($format === 'csv') {
                return $this->exportToCsv($users);
            } elseif ($format === 'excel') {
                return $this->exportToExcel($users);
            } else {
                return $this->paramError('不支持的导出格式');
            }
            
        } catch (\Exception $e) {
            Log::error('导出用户数据失败: ' . $e->getMessage());
            return $this->systemError('导出用户数据失败');
        }
    }
    
    /**
     * 导出为CSV格式
     */
    private function exportToCsv($users)
    {
        $filename = 'users_' . date('Y-m-d_H-i-s') . '.csv';
        
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename=' . $filename);
        
        $output = fopen('php://output', 'w');
        
        // 写入BOM以支持中文
        fwrite($output, "\xEF\xBB\xBF");
        
        // 写入表头
        fputcsv($output, ['用户ID', '账号', '昵称', '状态', '注册时间', '最后登录']);
        
        // 写入数据
        foreach ($users as $user) {
            fputcsv($output, [
                $user['id'] ?? '',
                $user['account'] ?? '',
                $user['nickname'] ?? '',
                $user['status'] == 1 ? '正常' : '封禁',
                $user['created_at'] ?? '',
                $user['last_login'] ?? ''
            ]);
        }
        
        fclose($output);
        exit;
    }
    
    /**
     * 导出为Excel格式（简化版，实际项目中可使用PhpSpreadsheet）
     */
    private function exportToExcel($users)
    {
        // 这里简化为CSV格式，实际项目中可以使用PhpSpreadsheet库生成真正的Excel文件
        return $this->exportToCsv($users);
    }
    
    /**
     * 获取用户统计信息API
     */
    public function getUserStats()
    {
        try {
            // 这里可以添加用户统计逻辑
            $stats = [
                'total_users' => 0,
                'active_users' => 0,
                'banned_users' => 0,
                'new_users_today' => 0
            ];
            
            // 获取总用户数
            $totalResult = \think\facade\Db::table('accounts')->count();
            $stats['total_users'] = $totalResult;
            
            // 获取正常用户数
            $activeResult = \think\facade\Db::table('accounts')->where('status', 1)->count();
            $stats['active_users'] = $activeResult;
            
            // 获取封禁用户数
            $bannedResult = \think\facade\Db::table('accounts')->where('status', 0)->count();
            $stats['banned_users'] = $bannedResult;
            
            // 获取今日新增用户数
            $todayResult = \think\facade\Db::table('accounts')
                ->whereTime('created_at', 'today')
                ->count();
            $stats['new_users_today'] = $todayResult;
            
            return $this->success($stats, '获取用户统计信息成功');
            
        } catch (\Exception $e) {
            Log::error('获取用户统计信息失败: ' . $e->getMessage());
            return $this->systemError('获取用户统计信息失败');
        }
    }

    /**
     * 用户状态管理页面
     */
    public function userStatus()
    {
        try {
            // 设置页面数据
            View::assign([
                'pageTitle' => '用户状态管理',
                'pageIcon' => 'bi bi-person-check',
                'pageDescription' => '管理用户状态，查看封禁记录和在线状态'
            ]);

            return View::fetch('admin/user_status_new');
        } catch (\Exception $e) {
            Log::error('用户状态管理页面加载失败: ' . $e->getMessage());
            return $this->error('页面加载失败');
        }
    }
}
