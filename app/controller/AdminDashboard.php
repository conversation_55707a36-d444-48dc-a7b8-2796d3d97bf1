<?php

namespace app\controller;

use app\BaseController;
use think\facade\View;
use think\facade\Log;
use app\controller\admin\DashboardController;
use app\service\AdminManagementService;
use app\common\AuthHelper;

class AdminDashboard extends BaseController
{
    /**
     * 仪表盘主页（新版ThinkPHP模板）
     */
    public function index()
    {
        try {
            // 快速认证检查：如果用户已登录且是管理员，直接渲染页面
            $userId = \think\facade\Session::get('user_id');
            if (!$userId || !AuthHelper::isAdmin($userId)) {
                return redirect('/login');
            }

            // 传递数据到视图
            View::assign('user_nickname', '管理员');
            View::assign('pageTitle', '仪表盘');
            View::assign('pageIcon', 'bi bi-speedometer2');

            return View::fetch('admin/index');

        } catch (\Exception $e) {
            // 错误处理
            Log::error('仪表盘加载失败: ' . $e->getMessage());

            // 返回错误页面或默认数据
            View::assign('user_nickname', '管理员');
            View::assign('pageTitle', '仪表盘');
            View::assign('pageIcon', 'bi bi-speedometer2');

            return View::fetch('admin/index');
        }
    }

    /**
     * 检查管理员权限API
     *
     * @return \think\Response
     */
    public function checkAdmin()
    {
        try {
            // 检查用户是否已登录
            $userId = \think\facade\Session::get('user_id');
            $userInfo = \think\facade\Session::get('user_info');

            if (!$userId || !$userInfo) {
                return json([
                    'code' => 401,
                    'message' => '用户未登录',
                    'data' => ['is_admin' => false]
                ]);
            }

            // 检查是否为管理员
            $isAdmin = AuthHelper::isAdmin($userId);

            return json([
                'code' => 200,
                'message' => 'success',
                'data' => [
                    'is_admin' => $isAdmin,
                    'user_id' => $userId,
                    'username' => $userInfo['username'] ?? $userInfo['id'] ?? ''
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('检查管理员权限失败: ' . $e->getMessage());
            return json([
                'code' => 500,
                'message' => '服务器内部错误',
                'data' => ['is_admin' => false]
            ]);
        }
    }

    /**
     * 测试仪表盘页面（无需认证）
     */
    public function testIndex()
    {
        try {
            // 传递测试数据到视图
            View::assign('user_nickname', '测试管理员');

            return View::fetch('admin/index');

        } catch (\Exception $e) {
            // 如果模板加载失败，返回错误信息
            return 'ThinkPHP管理员模板加载失败: ' . $e->getMessage() . '<br>错误文件: ' . $e->getFile() . '<br>错误行号: ' . $e->getLine();
        }
    }

    /**
     * 新版仪表盘页面（响应式设计）
     */
    public function dashboardV2()
    {
        try {
            // 传递数据到视图
            View::assign('user_nickname', '管理员');
            View::assign('pageTitle', '仪表盘');
            View::assign('pageIcon', 'bi bi-speedometer2');

            return View::fetch('admin/index_v2');

        } catch (\Exception $e) {
            // 如果模板加载失败，返回错误信息
            return '新版仪表盘模板加载失败: ' . $e->getMessage() . '<br>错误文件: ' . $e->getFile() . '<br>错误行号: ' . $e->getLine();
        }
    }

    /**
     * 获取仪表盘统计数据API
     */
    public function getStats()
    {
        try {
            $dashboardController = new DashboardController($this->app);
            $dashboardController->request = $this->request;
            
            return $dashboardController->getStats();
            
        } catch (\Exception $e) {
            Log::error('获取仪表盘统计数据失败: ' . $e->getMessage());
            return $this->systemError('获取统计数据失败');
        }
    }
    
    /**
     * 获取快速统计数据API
     */
    public function getFastStats()
    {
        try {
            $dashboardController = new DashboardController($this->app);
            $dashboardController->request = $this->request;

            return $dashboardController->getFastStats();

        } catch (\Exception $e) {
            Log::error('获取快速统计数据失败: ' . $e->getMessage());
            return $this->systemError('获取快速统计数据失败');
        }
    }

    /**
     * 获取扁平结构的统计数据API（用于前端显示）
     */
    public function getStatsFlat()
    {
        try {
            // 临时跳过认证检查进行测试
            Log::info('getStatsFlat API 被调用');

            // 使用AdminManagementService获取扁平结构的数据（禁用缓存以获取最新数据）
            $adminService = new AdminManagementService();
            Log::info('AdminManagementService 实例化成功');

            $stats = $adminService->getAdminStats(false); // 禁用缓存
            Log::info('获取统计数据成功: ' . json_encode($stats));

            return json([
                'success' => true,
                'code' => 200,
                'data' => $stats,
                'message' => '获取统计数据成功'
            ]);

        } catch (\Exception $e) {
            Log::error('获取扁平统计数据失败: ' . $e->getMessage());
            Log::error('错误堆栈: ' . $e->getTraceAsString());
            return json([
                'success' => false,
                'code' => 500,
                'data' => null,
                'message' => '获取统计数据失败: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 异步更新统计数据API
     */
    public function updateStatsAsync()
    {
        try {
            $dashboardController = new DashboardController($this->app);
            $dashboardController->request = $this->request;
            
            return $dashboardController->updateStatsAsync();
            
        } catch (\Exception $e) {
            Log::error('异步更新统计数据失败: ' . $e->getMessage());
            return $this->systemError('更新统计数据失败');
        }
    }
    
    /**
     * 获取系统监控数据API
     */
    public function getSystemMonitor()
    {
        try {
            // 获取系统信息
            $systemInfo = [
                'cpu_usage' => $this->getCpuUsage(),
                'memory_usage' => $this->getMemoryUsage(),
                'disk_usage' => $this->getDiskUsage(),
                'network_usage' => $this->getNetworkUsage(),
                'system_info' => $this->getSystemInfo(),
                'database_info' => $this->getDatabaseInfo(),
                'services_status' => $this->getServicesStatus()
            ];
            
            return $this->success($systemInfo, '获取系统监控数据成功');
            
        } catch (\Exception $e) {
            Log::error('获取系统监控数据失败: ' . $e->getMessage());
            return $this->systemError('获取系统监控数据失败');
        }
    }
    
    /**
     * 获取CPU使用率
     */
    private function getCpuUsage()
    {
        try {
            if (function_exists('shell_exec')) {
                $load = shell_exec("cat /proc/loadavg");
                if ($load) {
                    $loadArray = explode(' ', trim($load));
                    $cpuUsage = floatval($loadArray[0]) * 100;
                    return min($cpuUsage, 100);
                }
            }
            return 0;
        } catch (\Exception $e) {
            return 0;
        }
    }
    
    /**
     * 获取内存使用率
     */
    private function getMemoryUsage()
    {
        try {
            if (function_exists('shell_exec')) {
                $memInfo = shell_exec("cat /proc/meminfo");
                if ($memInfo) {
                    preg_match('/MemTotal:\s+(\d+)/', $memInfo, $totalMatch);
                    preg_match('/MemAvailable:\s+(\d+)/', $memInfo, $availableMatch);
                    
                    if ($totalMatch && $availableMatch) {
                        $total = intval($totalMatch[1]);
                        $available = intval($availableMatch[1]);
                        $used = $total - $available;
                        return round(($used / $total) * 100, 2);
                    }
                }
            }
            return 0;
        } catch (\Exception $e) {
            return 0;
        }
    }
    
    /**
     * 获取磁盘使用率
     */
    private function getDiskUsage()
    {
        try {
            $total = disk_total_space('/');
            $free = disk_free_space('/');
            if ($total && $free) {
                $used = $total - $free;
                return round(($used / $total) * 100, 2);
            }
            return 0;
        } catch (\Exception $e) {
            return 0;
        }
    }
    
    /**
     * 获取网络使用率（模拟数据）
     */
    private function getNetworkUsage()
    {
        // 这里返回模拟数据，实际项目中可以通过系统命令获取真实网络使用情况
        return rand(5, 30);
    }
    
    /**
     * 获取系统信息
     */
    private function getSystemInfo()
    {
        return [
            'os' => PHP_OS,
            'php_version' => PHP_VERSION,
            'mysql_version' => $this->getMysqlVersion(),
            'uptime' => $this->getSystemUptime()
        ];
    }
    
    /**
     * 获取MySQL版本
     */
    private function getMysqlVersion()
    {
        try {
            $result = \think\facade\Db::query("SELECT VERSION() as version");
            return $result[0]['version'] ?? 'Unknown';
        } catch (\Exception $e) {
            return 'Unknown';
        }
    }
    
    /**
     * 获取系统运行时间
     */
    private function getSystemUptime()
    {
        try {
            if (function_exists('shell_exec')) {
                $uptime = shell_exec('uptime -p');
                return trim($uptime) ?: 'Unknown';
            }
            return 'Unknown';
        } catch (\Exception $e) {
            return 'Unknown';
        }
    }
    
    /**
     * 获取数据库信息
     */
    private function getDatabaseInfo()
    {
        try {
            // 获取数据库大小
            $sizeResult = \think\facade\Db::query("
                SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb 
                FROM information_schema.tables 
                WHERE table_schema = DATABASE()
            ");
            
            // 获取表数量
            $tablesResult = \think\facade\Db::query("
                SELECT COUNT(*) as table_count 
                FROM information_schema.tables 
                WHERE table_schema = DATABASE()
            ");
            
            // 获取连接数
            $connectionsResult = \think\facade\Db::query("SHOW STATUS LIKE 'Threads_connected'");
            
            return [
                'size' => ($sizeResult[0]['size_mb'] ?? 0) . ' MB',
                'tables' => $tablesResult[0]['table_count'] ?? 0,
                'connections' => $connectionsResult[0]['Value'] ?? 0
            ];
            
        } catch (\Exception $e) {
            return [
                'size' => 'Unknown',
                'tables' => 0,
                'connections' => 0
            ];
        }
    }
    
    /**
     * 获取服务状态
     */
    private function getServicesStatus()
    {
        return [
            'web' => 'online',
            'database' => $this->checkDatabaseStatus(),
            'cache' => $this->checkCacheStatus(),
            'queue' => 'offline' // 假设队列服务未启用
        ];
    }
    
    /**
     * 检查数据库状态
     */
    private function checkDatabaseStatus()
    {
        try {
            \think\facade\Db::query("SELECT 1");
            return 'online';
        } catch (\Exception $e) {
            return 'offline';
        }
    }
    
    /**
     * 检查缓存状态
     */
    private function checkCacheStatus()
    {
        try {
            \think\facade\Cache::set('health_check', 'ok', 10);
            $result = \think\facade\Cache::get('health_check');
            return $result === 'ok' ? 'online' : 'offline';
        } catch (\Exception $e) {
            return 'offline';
        }
    }

    /**
     * 获取仪表盘基础统计数据
     */
    public function getDashboardStats()
    {
        try {
            // 检查管理员权限
            $userId = \think\facade\Session::get('user_id');
            if (!$userId || !AuthHelper::isAdmin($userId)) {
                return json(['code' => 401, 'message' => '权限不足']);
            }

            // 获取用户总数（从idtable1-5统计，与用户管理页面保持一致）
            $totalUsers = 0;
            for ($i = 1; $i <= 5; $i++) {
                try {
                    $count = \think\facade\Db::connect('seal_member')
                        ->table('idtable' . $i)
                        ->count();
                    $totalUsers += $count;
                } catch (\Exception $e) {
                    // 如果某个表不存在，跳过
                    Log::warning("统计用户数据时表 idtable{$i} 不存在: " . $e->getMessage());
                }
            }

            // 获取商品数量
            $totalItems = \think\facade\Db::connect('seal_web')
                ->table('iteminfo')
                ->count();

            // 获取总订单数
            $totalOrders = \think\facade\Db::connect('seal_web')
                ->table('purchase_history')
                ->count();

            // 获取在线用户数（从idtable1-5统计，30分钟内登录的用户）
            $onlineUsers = 0;
            $thirtyMinutesAgo = date('Y-m-d H:i:s', time() - 1800);
            for ($i = 1; $i <= 5; $i++) {
                try {
                    $count = \think\facade\Db::connect('seal_member')
                        ->table('idtable' . $i)
                        ->where('LoginDate', '>', $thirtyMinutesAgo)
                        ->count();
                    $onlineUsers += $count;
                } catch (\Exception $e) {
                    // 如果某个表不存在或字段不存在，跳过
                    Log::warning("统计在线用户数据时表 idtable{$i} 查询失败: " . $e->getMessage());
                }
            }

            return json([
                'code' => 200,
                'message' => 'success',
                'data' => [
                    'totalUsers' => $totalUsers,
                    'totalItems' => $totalItems,
                    'totalOrders' => $totalOrders,
                    'onlineUsers' => $onlineUsers
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('获取仪表盘统计数据失败: ' . $e->getMessage());
            return json([
                'code' => 500,
                'message' => '获取数据失败',
                'data' => [
                    'totalUsers' => 0,
                    'totalItems' => 0,
                    'totalOrders' => 0,
                    'onlineUsers' => 0
                ]
            ]);
        }
    }

    /**
     * 获取订单统计数据
     */
    public function getOrderStats()
    {
        try {
            // 检查管理员权限
            $userId = \think\facade\Session::get('user_id');
            if (!$userId || !AuthHelper::isAdmin($userId)) {
                return json(['code' => 401, 'message' => '权限不足']);
            }

            $today = date('Y-m-d');
            $yesterday = date('Y-m-d', strtotime('-1 day'));

            // 总订单数
            $totalOrders = \think\facade\Db::connect('seal_web')
                ->table('purchase_history')
                ->count();

            // 今日订单数
            $todayOrders = \think\facade\Db::connect('seal_web')
                ->table('purchase_history')
                ->whereTime('purchase_time', 'today')
                ->count();

            // 昨日订单数
            $yesterdayOrders = \think\facade\Db::connect('seal_web')
                ->table('purchase_history')
                ->whereTime('purchase_time', 'yesterday')
                ->count();

            // 今日销售额
            $todaySales = \think\facade\Db::connect('seal_web')
                ->table('purchase_history')
                ->whereTime('purchase_time', 'today')
                ->sum('total_price') ?: 0;

            return json([
                'code' => 200,
                'message' => 'success',
                'data' => [
                    'totalOrders' => $totalOrders,
                    'todayOrders' => $todayOrders,
                    'yesterdayOrders' => $yesterdayOrders,
                    'todaySales' => $todaySales
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('获取订单统计数据失败: ' . $e->getMessage());
            return json([
                'code' => 500,
                'message' => '获取数据失败',
                'data' => [
                    'totalOrders' => 0,
                    'todayOrders' => 0,
                    'yesterdayOrders' => 0,
                    'todaySales' => 0
                ]
            ]);
        }
    }

    /**
     * 获取游戏货币统计数据
     */
    public function getCurrencyStats()
    {
        try {
            // 检查管理员权限
            $userId = \think\facade\Session::get('user_id');
            if (!$userId || !AuthHelper::isAdmin($userId)) {
                return json(['code' => 401, 'message' => '权限不足']);
            }

            $bubblePoints = 0;
            $credits = 0;
            $cCoins = 0;

            // 统计所有idtable中的泡点（正确字段名是point）
            for ($i = 1; $i <= 5; $i++) {
                try {
                    $pointSum = \think\facade\Db::connect('seal_member')
                        ->table('idtable' . $i)
                        ->where('id', 'not like', 'gm%')
                        ->sum('point');

                    $bubblePoints += $pointSum ?: 0;
                } catch (\Exception $e) {
                    Log::warning("查询 idtable{$i} 泡点失败: " . $e->getMessage());
                    continue;
                }
            }

            // 统计积分（从sealmember表的hahapoint字段）
            try {
                $credits = \think\facade\Db::connect('seal_web')
                    ->table('sealmember')
                    ->where('id', 'not like', 'gm%')
                    ->sum('hahapoint') ?: 0;
            } catch (\Exception $e) {
                Log::warning("查询积分失败: " . $e->getMessage());
            }

            // 统计C币（从c_coin表）
            try {
                $cCoins = \think\facade\Db::connect('seal_web')
                    ->table('c_coin')
                    ->where('game_account', 'not like', 'gm%')
                    ->sum('balance') ?: 0;
            } catch (\Exception $e) {
                Log::warning("查询C币失败: " . $e->getMessage());
            }

            return json([
                'code' => 200,
                'message' => 'success',
                'data' => [
                    'bubblePoints' => $bubblePoints,
                    'credits' => $credits,
                    'cCoins' => $cCoins
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('获取货币统计数据失败: ' . $e->getMessage());
            return json([
                'code' => 500,
                'message' => '获取数据失败',
                'data' => [
                    'bubblePoints' => 0,
                    'credits' => 0,
                    'cCoins' => 0
                ]
            ]);
        }
    }
}
