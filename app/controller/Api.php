<?php

namespace app\controller;

use app\BaseController;
use think\facade\Db;
use think\Response;

class Api extends BaseController
{
    // 数据库配置 - 从环境变量读取
    private $host;
    private $dbname;
    private $username;
    private $password;
    private $pdo;

    public function initialize()
    {
        parent::initialize();

        // 从环境变量读取数据库配置
        $this->host = env('DB_HOST', '');
        $this->dbname = env('DB_NAME', '');
        $this->username = env('DB_USER', '');
        $this->password = env('DB_PASS', '');

        // 设置响应头
        header('Content-Type: application/json');
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type');

        // 处理OPTIONS请求
        if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
            exit(0);
        }

        // 初始化数据库连接
        try {
            $this->pdo = new \PDO("mysql:host={$this->host};dbname={$this->dbname};charset=utf8", $this->username, $this->password);
            $this->pdo->setAttribute(\PDO::ATTR_ERRMODE, \PDO::ERRMODE_EXCEPTION);
        } catch (\PDOException $e) {
            return json(['code' => 500, 'message' => '数据库连接失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 获取商品图标
     */
    public function getItemIcon()
    {
        try {
            $method = $this->request->method();
            
            if ($method === 'GET') {
                // 单个商品图标获取
                $itemId = $this->request->get('item_id');
                
                if ($itemId === null) {
                    return json([
                        'code' => 400,
                        'message' => '缺少item_id参数'
                    ]);
                }
                
                $icon = $this->getSingleItemIcon($itemId);
                
                return json([
                    'code' => 200,
                    'data' => [
                        'item_id' => $itemId,
                        'icon' => $icon
                    ]
                ]);
                
            } elseif ($method === 'POST') {
                // 批量商品图标获取
                $input = $this->request->getContent();
                $data = json_decode($input, true);
                
                if (!isset($data['item_ids']) || !is_array($data['item_ids'])) {
                    return json([
                        'code' => 400,
                        'message' => '缺少item_ids参数或格式错误'
                    ]);
                }
                
                $itemIds = array_filter($data['item_ids'], function($id) {
                    return !empty($id) && is_numeric($id);
                });
                
                if (empty($itemIds)) {
                    return json([
                        'code' => 400,
                        'message' => '没有有效的商品ID'
                    ]);
                }
                
                $icons = $this->getBatchItemIcons($itemIds);
                
                return json([
                    'code' => 200,
                    'data' => $icons
                ]);
                
            } else {
                return json([
                    'code' => 405,
                    'message' => '不支持的请求方法'
                ]);
            }
            
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '服务器错误: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取商品信息（图标、名称、说明）
     */
    public function getItemInfo()
    {
        try {
            $method = $this->request->method();
            
            if ($method === 'GET') {
                // 单个商品信息获取
                $itemId = $this->request->get('item_id');
                $fieldsParam = $this->request->get('fields', '');
                
                if ($itemId === null) {
                    return json([
                        'code' => 400,
                        'message' => '缺少item_id参数'
                    ]);
                }
                
                $fields = $this->parseFields($fieldsParam);
                $info = $this->getSingleItemInfo($itemId, $fields);
                
                return json([
                    'code' => 200,
                    'data' => array_merge(['item_id' => $itemId], $info)
                ]);
                
            } elseif ($method === 'POST') {
                // 批量商品信息获取
                $input = $this->request->getContent();
                $data = json_decode($input, true);
                
                if (!isset($data['item_ids']) || !is_array($data['item_ids'])) {
                    return json([
                        'code' => 400,
                        'message' => '缺少item_ids参数或格式错误'
                    ]);
                }
                
                $itemIds = array_filter($data['item_ids'], function($id) {
                    return !empty($id) && is_numeric($id);
                });
                
                if (empty($itemIds)) {
                    return json([
                        'code' => 400,
                        'message' => '没有有效的商品ID'
                    ]);
                }
                
                $fields = $this->parseFields($data['fields'] ?? '');
                $infos = $this->getBatchItemInfos($itemIds, $fields);
                
                return json([
                    'code' => 200,
                    'data' => $infos
                ]);
                
            } else {
                return json([
                    'code' => 405,
                    'message' => '不支持的请求方法'
                ]);
            }
            
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '服务器错误: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取单个商品图标
     */
    private function getSingleItemIcon($itemId)
    {
        $defaultIcon = '/static/img/default.png';
        
        if (empty($itemId)) {
            return $defaultIcon;
        }
        
        try {
            $stmt = $this->pdo->prepare("SELECT Icon FROM iteminfo WHERE ItemID = ?");
            $stmt->execute([$itemId]);
            $result = $stmt->fetch(\PDO::FETCH_ASSOC);
            
            if ($result && !empty($result['Icon'])) {
                return '/static/img/' . $result['Icon'] . '.png';
            }
            
            return $defaultIcon;
        } catch (\PDOException $e) {
            return $defaultIcon;
        }
    }

    /**
     * 批量获取商品图标
     */
    private function getBatchItemIcons($itemIds)
    {
        $icons = [];
        $defaultIcon = '/static/img/default.png';
        
        if (empty($itemIds)) {
            return $icons;
        }
        
        try {
            // 构建IN查询
            $placeholders = str_repeat('?,', count($itemIds) - 1) . '?';
            $stmt = $this->pdo->prepare("SELECT ItemID, Icon FROM iteminfo WHERE ItemID IN ($placeholders)");
            $stmt->execute($itemIds);
            $results = $stmt->fetchAll(\PDO::FETCH_ASSOC);
            
            // 创建ID到图标的映射
            $iconMap = [];
            foreach ($results as $row) {
                if (!empty($row['Icon'])) {
                    $iconMap[$row['ItemID']] = '/static/img/' . $row['Icon'] . '.png';
                }
            }
            
            // 为所有请求的ID分配图标（没找到的使用默认图标）
            foreach ($itemIds as $itemId) {
                $icons[$itemId] = $iconMap[$itemId] ?? $defaultIcon;
            }
            
            return $icons;
        } catch (\PDOException $e) {
            // 出错时为所有ID返回默认图标
            foreach ($itemIds as $itemId) {
                $icons[$itemId] = $defaultIcon;
            }
            return $icons;
        }
    }

    /**
     * 获取单个商品信息
     */
    private function getSingleItemInfo($itemId, $fields = ['icon', 'name', 'explanation'])
    {
        $defaultInfo = [
            'icon' => '/static/img/default.png',
            'name' => '未知物品',
            'explanation' => '暂无说明'
        ];
        
        if (empty($itemId)) {
            return array_intersect_key($defaultInfo, array_flip($fields));
        }
        
        try {
            // 构建查询字段
            $selectFields = [];
            if (in_array('icon', $fields)) $selectFields[] = 'Icon';
            if (in_array('name', $fields)) $selectFields[] = 'ItemName';
            if (in_array('explanation', $fields)) $selectFields[] = 'Explanation';
            
            if (empty($selectFields)) {
                return [];
            }
            
            $sql = "SELECT " . implode(', ', $selectFields) . " FROM iteminfo WHERE ItemID = ?";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$itemId]);
            $result = $stmt->fetch(\PDO::FETCH_ASSOC);
            
            $info = [];
            
            if ($result) {
                if (in_array('icon', $fields)) {
                    $info['icon'] = !empty($result['Icon']) ? '/static/img/' . $result['Icon'] . '.png' : $defaultInfo['icon'];
                }
                if (in_array('name', $fields)) {
                    $info['name'] = !empty($result['ItemName']) ? $result['ItemName'] : $defaultInfo['name'];
                }
                if (in_array('explanation', $fields)) {
                    $info['explanation'] = !empty($result['Explanation']) ? $result['Explanation'] : $defaultInfo['explanation'];
                }
            } else {
                // 没找到记录，返回默认值
                $info = array_intersect_key($defaultInfo, array_flip($fields));
            }
            
            return $info;
        } catch (\PDOException $e) {
            return array_intersect_key($defaultInfo, array_flip($fields));
        }
    }

    /**
     * 批量获取商品信息
     */
    private function getBatchItemInfos($itemIds, $fields = ['icon', 'name', 'explanation'])
    {
        $infos = [];
        $defaultInfo = [
            'icon' => '/static/img/default.png',
            'name' => '未知物品',
            'explanation' => '暂无说明'
        ];
        
        if (empty($itemIds)) {
            return $infos;
        }
        
        try {
            // 构建查询字段
            $selectFields = ['ItemID'];
            if (in_array('icon', $fields)) $selectFields[] = 'Icon';
            if (in_array('name', $fields)) $selectFields[] = 'ItemName';
            if (in_array('explanation', $fields)) $selectFields[] = 'Explanation';
            
            // 构建IN查询
            $placeholders = str_repeat('?,', count($itemIds) - 1) . '?';
            $sql = "SELECT " . implode(', ', $selectFields) . " FROM iteminfo WHERE ItemID IN ($placeholders)";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($itemIds);
            $results = $stmt->fetchAll(\PDO::FETCH_ASSOC);
            
            // 创建ID到信息的映射
            $infoMap = [];
            foreach ($results as $row) {
                $itemId = $row['ItemID'];
                $info = [];
                
                if (in_array('icon', $fields)) {
                    $info['icon'] = !empty($row['Icon']) ? '/static/img/' . $row['Icon'] . '.png' : $defaultInfo['icon'];
                }
                if (in_array('name', $fields)) {
                    $info['name'] = !empty($row['ItemName']) ? $row['ItemName'] : $defaultInfo['name'];
                }
                if (in_array('explanation', $fields)) {
                    $info['explanation'] = !empty($row['Explanation']) ? $row['Explanation'] : $defaultInfo['explanation'];
                }
                
                $infoMap[$itemId] = $info;
            }
            
            // 为所有请求的ID分配信息（没找到的使用默认信息）
            foreach ($itemIds as $itemId) {
                $infos[$itemId] = $infoMap[$itemId] ?? array_intersect_key($defaultInfo, array_flip($fields));
            }
            
            return $infos;
        } catch (\PDOException $e) {
            // 出错时为所有ID返回默认信息
            $defaultFields = array_intersect_key($defaultInfo, array_flip($fields));
            foreach ($itemIds as $itemId) {
                $infos[$itemId] = $defaultFields;
            }
            return $infos;
        }
    }

    /**
     * 解析请求的字段参数
     */
    private function parseFields($fieldsParam)
    {
        $availableFields = ['icon', 'name', 'explanation'];
        
        if (empty($fieldsParam)) {
            return $availableFields; // 默认返回所有字段
        }
        
        $requestedFields = array_map('trim', explode(',', $fieldsParam));
        $validFields = array_intersect($requestedFields, $availableFields);
        
        return empty($validFields) ? $availableFields : $validFields;
    }
}