<?php

namespace app\exception;

use think\db\exception\DataNotFoundException;
use think\db\exception\ModelNotFoundException;
use think\exception\Handle;
use think\exception\HttpException;
use think\exception\HttpResponseException;
use think\exception\ValidateException;
use think\facade\Log;
use think\facade\Env;
use think\Request;
use think\Response;
use Throwable;
use app\common\ApiResponse;

/**
 * 全局异常处理器
 * 统一处理系统异常和业务异常，提供标准化的错误响应
 * 
 * 设计原则：
 * 1. 单一职责原则：专门负责异常处理和响应
 * 2. 开闭原则：易于扩展新的异常类型处理
 * 3. 依赖倒置原则：依赖抽象的异常接口
 */
class GlobalExceptionHandler extends Handle
{
    /**
     * 不需要记录信息（日志）的异常类型
     * @var array
     */
    protected $ignoreReport = [
        HttpException::class,
        HttpResponseException::class,
        ModelNotFoundException::class,
        DataNotFoundException::class,
        ValidateException::class,
    ];

    /**
     * 记录异常信息
     * 
     * @param Throwable $exception
     * @return void
     */
    public function report(Throwable $exception): void
    {
        // 检查是否需要记录日志
        if (!$this->isIgnoreReport($exception)) {
            // 记录详细的异常信息
            $this->logException($exception);
        }
    }

    /**
     * 渲染异常为响应
     * 
     * @param Request $request
     * @param Throwable $e
     * @return Response
     */
    public function render($request, Throwable $e): Response
    {
        // 如果是AJAX请求或API请求，返回JSON格式
        if ($request->isAjax() || $this->isApiRequest($request)) {
            return $this->renderApiException($request, $e);
        }

        // 其他情况使用父类处理
        return parent::render($request, $e);
    }

    /**
     * 渲染API异常响应
     * 
     * @param Request $request
     * @param Throwable $e
     * @return Response
     */
    protected function renderApiException(Request $request, Throwable $e): Response
    {
        // HTTP异常
        if ($e instanceof HttpException) {
            return $this->handleHttpException($e);
        }

        // 验证异常
        if ($e instanceof ValidateException) {
            return $this->handleValidateException($e);
        }

        // 数据库异常
        if ($e instanceof DataNotFoundException || $e instanceof ModelNotFoundException) {
            return $this->handleDataNotFoundException($e);
        }

        // 自定义业务异常
        if ($e instanceof BusinessException) {
            return $this->handleBusinessException($e);
        }

        // 其他系统异常
        return $this->handleSystemException($e);
    }

    /**
     * 处理HTTP异常
     * 
     * @param HttpException $e
     * @return Response
     */
    protected function handleHttpException(HttpException $e): Response
    {
        $statusCode = $e->getStatusCode();
        
        switch ($statusCode) {
            case 401:
                return ApiResponse::unauthorized($e->getMessage() ?: '未授权访问');
            case 403:
                return ApiResponse::forbidden($e->getMessage() ?: '权限不足');
            case 404:
                return ApiResponse::notFound($e->getMessage() ?: '请求的资源不存在');
            case 405:
                return ApiResponse::methodNotAllowed($e->getMessage() ?: '请求方法不允许');
            case 429:
                return ApiResponse::error('请求过于频繁，请稍后重试', 429);
            default:
                return ApiResponse::error($e->getMessage() ?: '请求错误', $statusCode);
        }
    }

    /**
     * 处理验证异常
     * 
     * @param ValidateException $e
     * @return Response
     */
    protected function handleValidateException(ValidateException $e): Response
    {
        $errors = [];
        $message = $e->getError();
        
        // 如果是数组形式的错误信息，格式化为标准格式
        if (is_array($message)) {
            $errors = $message;
            $message = '数据验证失败';
        } else {
            $errors = [$message];
        }

        return ApiResponse::validationError($errors, $message);
    }

    /**
     * 处理数据不存在异常
     * 
     * @param Throwable $e
     * @return Response
     */
    protected function handleDataNotFoundException(Throwable $e): Response
    {
        return ApiResponse::notFound('请求的数据不存在');
    }

    /**
     * 处理业务异常
     * 
     * @param BusinessException $e
     * @return Response
     */
    protected function handleBusinessException(BusinessException $e): Response
    {
        return ApiResponse::businessError(
            $e->getMessage(),
            $e->getErrorCode(),
            $e->getData()
        );
    }

    /**
     * 处理系统异常
     * 
     * @param Throwable $e
     * @return Response
     */
    protected function handleSystemException(Throwable $e): Response
    {
        // 在开发环境显示详细错误信息
        if (Env::get('APP_DEBUG', false)) {
            $data = [
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ];
            return ApiResponse::systemError($e->getMessage(), $data);
        }

        // 生产环境返回通用错误信息
        return ApiResponse::systemError('系统异常，请稍后重试');
    }

    /**
     * 记录异常日志
     * 
     * @param Throwable $exception
     * @return void
     */
    protected function logException(Throwable $exception): void
    {
        $context = [
            'exception' => get_class($exception),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'message' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
            'url' => request()->url(true),
            'method' => request()->method(),
            'ip' => request()->ip(),
            'user_agent' => request()->header('User-Agent'),
            'request_data' => $this->getRequestData()
        ];

        Log::error('系统异常: ' . $exception->getMessage(), $context);
    }

    /**
     * 获取请求数据
     * 
     * @return array
     */
    protected function getRequestData(): array
    {
        $request = request();
        $data = [];

        // GET参数
        if (!empty($request->get())) {
            $data['get'] = $request->get();
        }

        // POST参数（过滤敏感信息）
        if (!empty($request->post())) {
            $post = $request->post();
            $sensitiveFields = ['password', 'password_confirmation', 'token', 'secret'];
            
            foreach ($sensitiveFields as $field) {
                if (isset($post[$field])) {
                    $post[$field] = '***';
                }
            }
            
            $data['post'] = $post;
        }

        return $data;
    }

    /**
     * 判断是否为API请求
     * 
     * @param Request $request
     * @return bool
     */
    protected function isApiRequest(Request $request): bool
    {
        // 检查请求路径是否包含api
        if (strpos($request->pathinfo(), '/api/') !== false) {
            return true;
        }

        // 检查Accept头是否包含application/json
        $accept = $request->header('Accept', '');
        if (strpos($accept, 'application/json') !== false) {
            return true;
        }

        // 检查Content-Type是否为application/json
        $contentType = $request->header('Content-Type', '');
        if (strpos($contentType, 'application/json') !== false) {
            return true;
        }

        return false;
    }

    /**
     * 检查是否忽略报告
     * 
     * @param Throwable $exception
     * @return bool
     */
    protected function isIgnoreReport(Throwable $exception): bool
    {
        foreach ($this->ignoreReport as $type) {
            if ($exception instanceof $type) {
                return true;
            }
        }

        return false;
    }
}
