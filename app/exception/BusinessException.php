<?php

namespace app\exception;

use Exception;

/**
 * 业务异常类
 * 用于处理业务逻辑中的异常情况
 * 
 * 设计原则：
 * 1. 单一职责原则：专门处理业务逻辑异常
 * 2. 开闭原则：可以扩展不同类型的业务异常
 * 3. 里氏替换原则：可以替换标准Exception
 */
class BusinessException extends Exception
{
    /**
     * 错误代码
     * @var string
     */
    protected $errorCode;

    /**
     * 额外数据
     * @var mixed
     */
    protected $data;

    /**
     * 业务异常常量
     */
    const USER_NOT_FOUND = 'USER_NOT_FOUND';
    const USER_NOT_LOGIN = 'USER_NOT_LOGIN';
    const USER_PERMISSION_DENIED = 'USER_PERMISSION_DENIED';
    const ITEM_NOT_FOUND = 'ITEM_NOT_FOUND';
    const ITEM_OUT_OF_STOCK = 'ITEM_OUT_OF_STOCK';
    const INSUFFICIENT_BALANCE = 'INSUFFICIENT_BALANCE';
    const PURCHASE_LIMIT_EXCEEDED = 'PURCHASE_LIMIT_EXCEEDED';
    const ORDER_NOT_FOUND = 'ORDER_NOT_FOUND';
    const ORDER_STATUS_ERROR = 'ORDER_STATUS_ERROR';
    const PAYMENT_FAILED = 'PAYMENT_FAILED';
    const DELIVERY_FAILED = 'DELIVERY_FAILED';
    const CACHE_ERROR = 'CACHE_ERROR';
    const DATABASE_ERROR = 'DATABASE_ERROR';
    const API_ERROR = 'API_ERROR';
    const VALIDATION_ERROR = 'VALIDATION_ERROR';
    const RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED';

    /**
     * 构造函数
     * 
     * @param string $message 异常消息
     * @param string $errorCode 错误代码
     * @param mixed $data 额外数据
     * @param int $code 异常代码
     * @param Exception|null $previous 前一个异常
     */
    public function __construct(
        string $message = '',
        string $errorCode = '',
        $data = null,
        int $code = 0,
        ?Exception $previous = null
    ) {
        parent::__construct($message, $code, $previous);
        $this->errorCode = $errorCode;
        $this->data = $data;
    }

    /**
     * 获取错误代码
     * 
     * @return string
     */
    public function getErrorCode(): string
    {
        return $this->errorCode;
    }

    /**
     * 获取额外数据
     * 
     * @return mixed
     */
    public function getData()
    {
        return $this->data;
    }

    /**
     * 设置额外数据
     * 
     * @param mixed $data
     * @return self
     */
    public function setData($data): self
    {
        $this->data = $data;
        return $this;
    }

    /**
     * 静态工厂方法 - 用户相关异常
     */

    /**
     * 用户未找到异常
     * 
     * @param string $message
     * @param mixed $data
     * @return static
     */
    public static function userNotFound(string $message = '用户不存在', $data = null): self
    {
        return new static($message, self::USER_NOT_FOUND, $data);
    }

    /**
     * 用户未登录异常
     * 
     * @param string $message
     * @return static
     */
    public static function userNotLogin(string $message = '请先登录'): self
    {
        return new static($message, self::USER_NOT_LOGIN);
    }

    /**
     * 用户权限不足异常
     * 
     * @param string $message
     * @return static
     */
    public static function permissionDenied(string $message = '权限不足'): self
    {
        return new static($message, self::USER_PERMISSION_DENIED);
    }

    /**
     * 静态工厂方法 - 商品相关异常
     */

    /**
     * 商品未找到异常
     * 
     * @param string $message
     * @param mixed $data
     * @return static
     */
    public static function itemNotFound(string $message = '商品不存在', $data = null): self
    {
        return new static($message, self::ITEM_NOT_FOUND, $data);
    }

    /**
     * 商品库存不足异常
     * 
     * @param string $message
     * @param mixed $data
     * @return static
     */
    public static function itemOutOfStock(string $message = '商品库存不足', $data = null): self
    {
        return new static($message, self::ITEM_OUT_OF_STOCK, $data);
    }

    /**
     * 静态工厂方法 - 购买相关异常
     */

    /**
     * 余额不足异常
     * 
     * @param string $message
     * @param mixed $data
     * @return static
     */
    public static function insufficientBalance(string $message = '余额不足', $data = null): self
    {
        return new static($message, self::INSUFFICIENT_BALANCE, $data);
    }

    /**
     * 购买限制异常
     * 
     * @param string $message
     * @param mixed $data
     * @return static
     */
    public static function purchaseLimitExceeded(string $message = '超出购买限制', $data = null): self
    {
        return new static($message, self::PURCHASE_LIMIT_EXCEEDED, $data);
    }

    /**
     * 静态工厂方法 - 订单相关异常
     */

    /**
     * 订单未找到异常
     * 
     * @param string $message
     * @param mixed $data
     * @return static
     */
    public static function orderNotFound(string $message = '订单不存在', $data = null): self
    {
        return new static($message, self::ORDER_NOT_FOUND, $data);
    }

    /**
     * 订单状态错误异常
     * 
     * @param string $message
     * @param mixed $data
     * @return static
     */
    public static function orderStatusError(string $message = '订单状态错误', $data = null): self
    {
        return new static($message, self::ORDER_STATUS_ERROR, $data);
    }

    /**
     * 支付失败异常
     * 
     * @param string $message
     * @param mixed $data
     * @return static
     */
    public static function paymentFailed(string $message = '支付失败', $data = null): self
    {
        return new static($message, self::PAYMENT_FAILED, $data);
    }

    /**
     * 发货失败异常
     * 
     * @param string $message
     * @param mixed $data
     * @return static
     */
    public static function deliveryFailed(string $message = '发货失败', $data = null): self
    {
        return new static($message, self::DELIVERY_FAILED, $data);
    }

    /**
     * 静态工厂方法 - 系统相关异常
     */

    /**
     * 缓存错误异常
     * 
     * @param string $message
     * @param mixed $data
     * @return static
     */
    public static function cacheError(string $message = '缓存操作失败', $data = null): self
    {
        return new static($message, self::CACHE_ERROR, $data);
    }

    /**
     * 数据库错误异常
     * 
     * @param string $message
     * @param mixed $data
     * @return static
     */
    public static function databaseError(string $message = '数据库操作失败', $data = null): self
    {
        return new static($message, self::DATABASE_ERROR, $data);
    }

    /**
     * API错误异常
     * 
     * @param string $message
     * @param mixed $data
     * @return static
     */
    public static function apiError(string $message = 'API调用失败', $data = null): self
    {
        return new static($message, self::API_ERROR, $data);
    }

    /**
     * 验证错误异常
     * 
     * @param string $message
     * @param mixed $data
     * @return static
     */
    public static function validationError(string $message = '数据验证失败', $data = null): self
    {
        return new static($message, self::VALIDATION_ERROR, $data);
    }

    /**
     * 频率限制异常
     * 
     * @param string $message
     * @param mixed $data
     * @return static
     */
    public static function rateLimitExceeded(string $message = '请求过于频繁', $data = null): self
    {
        return new static($message, self::RATE_LIMIT_EXCEEDED, $data);
    }
}
