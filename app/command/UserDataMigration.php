<?php

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;
use think\facade\Log;

/**
 * 用户数据迁移命令
 * 用于修复错误分配到表中的用户数据
 */
class UserDataMigration extends Command
{
    protected function configure()
    {
        $this->setName('user:migrate')
            ->setDescription('迁移错误分配的用户数据到正确的idtable表中');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('开始检查用户数据分配...');
        
        $migratedCount = 0;
        $errorCount = 0;
        
        try {
            // 检查每个表中的用户数据
            for ($tableNum = 1; $tableNum <= 5; $tableNum++) {
                $output->writeln("检查 idtable{$tableNum}...");
                
                $users = Db::connect('seal_member')
                    ->table('idtable' . $tableNum)
                    ->select();
                
                foreach ($users as $user) {
                    $correctTable = $this->getCorrectTableNumber($user['id']);
                    
                    if ($correctTable !== $tableNum) {
                        $output->writeln("发现错误分配: {$user['id']} 当前在 idtable{$tableNum}，应该在 idtable{$correctTable}");
                        
                        // 执行迁移
                        if ($this->migrateUser($user, $tableNum, $correctTable)) {
                            $migratedCount++;
                            $output->writeln("✓ 成功迁移用户: {$user['id']}");
                        } else {
                            $errorCount++;
                            $output->writeln("✗ 迁移失败: {$user['id']}");
                        }
                    }
                }
            }
            
            $output->writeln("\n迁移完成!");
            $output->writeln("成功迁移: {$migratedCount} 个用户");
            $output->writeln("迁移失败: {$errorCount} 个用户");
            
        } catch (\Exception $e) {
            $output->writeln("迁移过程中发生错误: " . $e->getMessage());
            Log::error('用户数据迁移失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 根据用户名确定正确的表号
     */
    private function getCorrectTableNumber(string $username): int
    {
        $firstLetter = strtolower(substr($username, 0, 1));
        
        if ($firstLetter >= 'a' && $firstLetter <= 'd') {
            return 1;
        } elseif ($firstLetter >= 'e' && $firstLetter <= 'i') {
            return 2;
        } elseif ($firstLetter >= 'j' && $firstLetter <= 'n') {
            return 3;
        } elseif ($firstLetter >= 'o' && $firstLetter <= 's') {
            return 4;
        } elseif ($firstLetter >= 't' && $firstLetter <= 'z') {
            return 5;
        } else {
            return 1; // 默认分配到表1
        }
    }
    
    /**
     * 迁移用户数据
     */
    private function migrateUser(array $user, int $fromTable, int $toTable): bool
    {
        try {
            Db::startTrans();
            
            // 检查目标表中是否已存在该用户
            $existingUser = Db::connect('seal_member')
                ->table('idtable' . $toTable)
                ->where('id', $user['id'])
                ->find();
                
            if ($existingUser) {
                // 如果目标表中已存在，删除源表中的记录
                Db::connect('seal_member')
                    ->table('idtable' . $fromTable)
                    ->where('id', $user['id'])
                    ->delete();
            } else {
                // 插入到目标表
                Db::connect('seal_member')
                    ->table('idtable' . $toTable)
                    ->insert($user);
                    
                // 从源表删除
                Db::connect('seal_member')
                    ->table('idtable' . $fromTable)
                    ->where('id', $user['id'])
                    ->delete();
            }
            
            Db::commit();
            return true;
            
        } catch (\Exception $e) {
            Db::rollback();
            Log::error("迁移用户 {$user['id']} 失败: " . $e->getMessage());
            return false;
        }
    }
}