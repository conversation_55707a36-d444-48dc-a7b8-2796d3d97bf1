<?php

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;
use think\facade\Log;
use app\service\ItemDeliveryService;
use app\model\PurchaseOrder;
use Exception;

/**
 * 物品发送命令行工具
 * 用于处理物品发送队列和失败订单重试
 * 
 * 设计原则：
 * 1. 单一职责原则：专门处理物品发送相关的命令行任务
 * 2. 开闭原则：易于扩展新的命令功能
 * 3. 依赖注入原则：注入必要的服务
 */
class ItemDelivery extends Command
{
    /**
     * 命令配置
     */
    protected function configure()
    {
        $this->setName('item:delivery')
            ->setDescription('物品发送系统命令行工具')
            ->addOption('action', 'a', \think\console\input\Option::VALUE_REQUIRED, '执行的操作：process|retry|status|cleanup')
            ->addOption('limit', 'l', \think\console\input\Option::VALUE_OPTIONAL, '处理数量限制', 50)
            ->addOption('order-id', 'o', \think\console\input\Option::VALUE_OPTIONAL, '指定订单ID')
            ->addOption('user-id', 'u', \think\console\input\Option::VALUE_OPTIONAL, '指定用户ID')
            ->addOption('force', 'f', \think\console\input\Option::VALUE_NONE, '强制执行')
            ->addOption('dry-run', 'd', \think\console\input\Option::VALUE_NONE, '试运行模式');
    }

    /**
     * 执行命令
     */
    protected function execute(Input $input, Output $output)
    {
        $action = $input->getOption('action');
        $limit = intval($input->getOption('limit'));
        $orderId = $input->getOption('order-id');
        $userId = $input->getOption('user-id');
        $force = $input->getOption('force');
        $dryRun = $input->getOption('dry-run');

        $output->writeln('<info>物品发送系统命令行工具</info>');
        $output->writeln('<comment>执行操作: ' . $action . '</comment>');

        if ($dryRun) {
            $output->writeln('<warning>试运行模式，不会实际执行操作</warning>');
        }

        try {
            switch ($action) {
                case 'process':
                    $this->processOrders($output, $limit, $dryRun);
                    break;

                case 'retry':
                    $this->retryFailedOrders($output, $limit, $orderId, $force, $dryRun);
                    break;

                case 'status':
                    $this->showStatus($output, $userId);
                    break;

                case 'cleanup':
                    $this->cleanupOldData($output, $force, $dryRun);
                    break;

                default:
                    $output->writeln('<e>未知操作: ' . $action . '</e>');
                    $output->writeln('<info>可用操作: process, retry, status, cleanup</info>');
                    return 1;
            }

            $output->writeln('<info>操作完成</info>');
            return 0;

        } catch (Exception $e) {
            $output->writeln('<e>执行失败: ' . $e->getMessage() . '</e>');
            Log::error('命令执行失败: ' . $e->getMessage());
            return 1;
        }
    }

    /**
     * 处理待发送订单
     */
    private function processOrders(Output $output, int $limit, bool $dryRun): void
    {
        $output->writeln('<info>开始处理待发送订单...</info>');

        // 获取待处理订单
        $orders = PurchaseOrder::where('status', PurchaseOrder::STATUS_PENDING)
            ->order('create_time', 'asc')
            ->limit($limit)
            ->select()
            ->toArray();

        if (empty($orders)) {
            $output->writeln('<comment>没有待处理的订单</comment>');
            return;
        }

        $output->writeln('<info>找到 ' . count($orders) . ' 个待处理订单</info>');

        $deliveryService = new ItemDeliveryService();
        $successCount = 0;
        $failCount = 0;

        foreach ($orders as $order) {
            $output->write('处理订单 ' . $order['order_no'] . '... ');

            if ($dryRun) {
                $output->writeln('<comment>试运行</comment>');
                continue;
            }

            try {
                // 更新订单状态为处理中
                PurchaseOrder::updateOrderStatus($order['id'], PurchaseOrder::STATUS_PROCESSING);

                // 发送物品到游戏
                $result = $deliveryService->deliverItemToGame(
                    $order['user_id'],
                    $order['server_id'] ?? 1, // 如果server_id不存在则使用默认值1
                    $order['item_id'],
                    $order['quantity']
                );

                if ($result['success']) {
                    // 更新订单状态为已完成
                    PurchaseOrder::updateOrderStatus(
                        $order['id'],
                        PurchaseOrder::STATUS_COMPLETED,
                        [
                            'unique_num' => $result['unique_num'],
                            'delivery_time' => date('Y-m-d H:i:s')
                        ]
                    );
                    $output->writeln('<info>成功</info>');
                    $successCount++;
                } else {
                    // 更新订单状态为失败
                    PurchaseOrder::updateOrderStatus(
                        $order['id'],
                        PurchaseOrder::STATUS_FAILED,
                        ['error_message' => $result['message']]
                    );
                    $output->writeln('<e>失败: ' . $result['message'] . '</e>');
                    $failCount++;
                }

            } catch (Exception $e) {
                PurchaseOrder::updateOrderStatus(
                    $order['id'],
                    PurchaseOrder::STATUS_FAILED,
                    ['error_message' => $e->getMessage()]
                );
                $output->writeln('<e>异常: ' . $e->getMessage() . '</e>');
                $failCount++;
            }

            // 短暂延迟，避免过于频繁的请求
            usleep(100000); // 0.1秒
        }

        $output->writeln('<info>处理完成: 成功 ' . $successCount . ' 个，失败 ' . $failCount . ' 个</info>');
    }

    /**
     * 重试失败订单
     */
    private function retryFailedOrders(Output $output, int $limit, ?string $orderId, bool $force, bool $dryRun): void
    {
        $output->writeln('<info>开始重试失败订单...</info>');

        $query = PurchaseOrder::where('status', PurchaseOrder::STATUS_FAILED);

        if ($orderId) {
            $query->where('id', $orderId);
        } else {
            if (!$force) {
                $query->where('retry_count', '<', 3);
            }
            $query->order('create_time', 'asc')->limit($limit);
        }

        $orders = $query->select()->toArray();

        if (empty($orders)) {
            $output->writeln('<comment>没有需要重试的订单</comment>');
            return;
        }

        $output->writeln('<info>找到 ' . count($orders) . ' 个需要重试的订单</info>');

        $deliveryService = new ItemDeliveryService();
        $successCount = 0;
        $failCount = 0;

        foreach ($orders as $order) {
            $output->write('重试订单 ' . $order['order_no'] . ' (第' . ($order['retry_count'] + 1) . '次)... ');

            if ($dryRun) {
                $output->writeln('<comment>试运行</comment>');
                continue;
            }

            try {
                // 增加重试次数
                PurchaseOrder::incrementRetryCount($order['id']);

                // 更新订单状态为处理中
                PurchaseOrder::updateOrderStatus($order['id'], PurchaseOrder::STATUS_PROCESSING);

                // 重新发送物品
                $result = $deliveryService->deliverItemToGame(
                    $order['user_id'],
                    $order['item_id'], 
                    $order['server_id'] ?? 1, // 如果server_id不存在则使用默认值1
                    $order['quantity']
                );

                if ($result['success']) {
                    PurchaseOrder::updateOrderStatus(
                        $order['id'],
                        PurchaseOrder::STATUS_COMPLETED,
                        [
                            'unique_num' => $result['unique_num'],
                            'delivery_time' => date('Y-m-d H:i:s'),
                            'error_message' => null
                        ]
                    );
                    $output->writeln('<info>成功</info>');
                    $successCount++;
                } else {
                    PurchaseOrder::updateOrderStatus(
                        $order['id'],
                        PurchaseOrder::STATUS_FAILED,
                        ['error_message' => $result['message']]
                    );
                    $output->writeln('<e>失败: ' . $result['message'] . '</e>');
                    $failCount++;
                }

            } catch (Exception $e) {
                PurchaseOrder::updateOrderStatus(
                    $order['id'],
                    PurchaseOrder::STATUS_FAILED,
                    ['error_message' => $e->getMessage()]
                );
                $output->writeln('<e>异常: ' . $e->getMessage() . '</e>');
                $failCount++;
            }

            usleep(200000); // 0.2秒延迟
        }

        $output->writeln('<info>重试完成: 成功 ' . $successCount . ' 个，失败 ' . $failCount . ' 个</info>');
    }

    /**
     * 显示系统状态
     */
    private function showStatus(Output $output, ?string $userId): void
    {
        $output->writeln('<info>系统状态统计</info>');
        $output->writeln('==================');

        // 总体统计
        $totalOrders = PurchaseOrder::count();
        $pendingOrders = PurchaseOrder::where('status', PurchaseOrder::STATUS_PENDING)->count();
        $processingOrders = PurchaseOrder::where('status', PurchaseOrder::STATUS_PROCESSING)->count();
        $completedOrders = PurchaseOrder::where('status', PurchaseOrder::STATUS_COMPLETED)->count();
        $failedOrders = PurchaseOrder::where('status', PurchaseOrder::STATUS_FAILED)->count();

        $output->writeln('总订单数: ' . $totalOrders);
        $output->writeln('待处理: ' . $pendingOrders);
        $output->writeln('处理中: ' . $processingOrders);
        $output->writeln('已完成: ' . $completedOrders);
        $output->writeln('失败: ' . $failedOrders);

        // 今日统计
        $today = date('Y-m-d');
        $todayOrders = PurchaseOrder::whereTime('create_time', 'between', [$today . ' 00:00:00', $today . ' 23:59:59'])->count();
        $todayCompleted = PurchaseOrder::where('status', PurchaseOrder::STATUS_COMPLETED)
            ->whereTime('create_time', 'between', [$today . ' 00:00:00', $today . ' 23:59:59'])
            ->count();

        $output->writeln('');
        $output->writeln('今日订单: ' . $todayOrders);
        $output->writeln('今日完成: ' . $todayCompleted);
        $output->writeln('今日成功率: ' . ($todayOrders > 0 ? round($todayCompleted / $todayOrders * 100, 2) : 0) . '%');

        // 用户统计
        if ($userId) {
            $output->writeln('');
            $output->writeln('用户 ' . $userId . ' 统计:');
            $userOrders = PurchaseOrder::where('user_id', $userId)->count();
            $userCompleted = PurchaseOrder::where('user_id', $userId)
                ->where('status', PurchaseOrder::STATUS_COMPLETED)
                ->count();
            $output->writeln('总订单: ' . $userOrders);
            $output->writeln('已完成: ' . $userCompleted);
        }

        // 错误统计
        $errorStats = PurchaseOrder::where('status', PurchaseOrder::STATUS_FAILED)
            ->where('error_message', '<>', '')
            ->field('error_message, count(*) as count')
            ->group('error_message')
            ->order('count', 'desc')
            ->limit(5)
            ->select()
            ->toArray();

        if (!empty($errorStats)) {
            $output->writeln('');
            $output->writeln('主要错误类型:');
            foreach ($errorStats as $error) {
                $output->writeln('- ' . $error['error_message'] . ' (' . $error['count'] . '次)');
            }
        }
    }

    /**
     * 清理旧数据
     */
    private function cleanupOldData(Output $output, bool $force, bool $dryRun): void
    {
        $output->writeln('<info>开始清理旧数据...</info>');

        $retentionDays = config('purchase.log.log_retention_days', 30);
        $cutoffDate = date('Y-m-d H:i:s', strtotime('-' . $retentionDays . ' days'));

        $output->writeln('清理 ' . $cutoffDate . ' 之前的数据');

        if (!$force && !$dryRun) {
            $output->writeln('<warning>请使用 --force 参数确认清理操作</warning>');
            return;
        }

        // 清理已完成的订单（保留失败的订单用于分析）
        $query = PurchaseOrder::where('status', PurchaseOrder::STATUS_COMPLETED)
            ->where('create_time', '<', $cutoffDate);

        $count = $query->count();
        $output->writeln('将清理 ' . $count . ' 个已完成的订单');

        if (!$dryRun && $count > 0) {
            $deleted = $query->delete();
            $output->writeln('<info>已清理 ' . $deleted . ' 个订单</info>');
        }

        // 清理发送日志
        $logQuery = Db::name('item_delivery_logs')
            ->where('create_time', '<', $cutoffDate);

        $logCount = $logQuery->count();
        $output->writeln('将清理 ' . $logCount . ' 条发送日志');

        if (!$dryRun && $logCount > 0) {
            $deletedLogs = $logQuery->delete();
            $output->writeln('<info>已清理 ' . $deletedLogs . ' 条发送日志</info>');
        }
    }
}
