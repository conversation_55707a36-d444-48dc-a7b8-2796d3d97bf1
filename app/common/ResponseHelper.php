<?php

namespace app\common;

use think\Response;

/**
 * 响应助手类
 * 提供通用的响应格式化方法，减少代码重复
 * 
 * 设计原则：
 * 1. 单一职责原则：专门负责响应格式化
 * 2. 开闭原则：易于扩展新的响应格式
 * 3. 依赖倒置原则：依赖抽象的响应接口
 */
class ResponseHelper
{
    /**
     * 成功响应
     * 
     * @param mixed $data 响应数据
     * @param string $message 响应消息
     * @param int $code 响应代码
     * @return Response
     */
    public static function success($data = null, string $message = 'success', int $code = 0): Response
    {
        return json([
            'success' => true,
            'code' => $code,
            'message' => $message,
            'data' => $data,
            'timestamp' => time()
        ]);
    }
    
    /**
     * 错误响应
     * 
     * @param string $message 错误消息
     * @param int $code 错误代码
     * @param mixed $data 错误数据
     * @return Response
     */
    public static function error(string $message = 'error', int $code = 1, $data = null): Response
    {
        return json([
            'success' => false,
            'code' => $code,
            'message' => $message,
            'data' => $data,
            'timestamp' => time()
        ]);
    }
    
    /**
     * 分页响应
     * 
     * @param array $list 数据列表
     * @param int $total 总数
     * @param int $page 当前页
     * @param int $limit 每页数量
     * @param string $message 响应消息
     * @return Response
     */
    public static function paginated(array $list, int $total, int $page, int $limit, string $message = 'success'): Response
    {
        return self::success([
            'list' => $list,
            'pagination' => [
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'total_pages' => ceil($total / $limit),
                'has_next' => $page * $limit < $total,
                'has_prev' => $page > 1
            ]
        ], $message);
    }
    
    /**
     * 列表响应
     * 
     * @param array $list 数据列表
     * @param string $message 响应消息
     * @return Response
     */
    public static function list(array $list, string $message = 'success'): Response
    {
        return self::success([
            'list' => $list,
            'count' => count($list)
        ], $message);
    }
    
    /**
     * 详情响应
     * 
     * @param array $detail 详情数据
     * @param string $message 响应消息
     * @return Response
     */
    public static function detail(array $detail, string $message = 'success'): Response
    {
        return self::success($detail, $message);
    }
    
    /**
     * 创建成功响应
     * 
     * @param mixed $id 创建的记录ID
     * @param string $message 响应消息
     * @return Response
     */
    public static function created($id = null, string $message = '创建成功'): Response
    {
        return self::success([
            'id' => $id,
            'created' => true
        ], $message);
    }
    
    /**
     * 更新成功响应
     * 
     * @param bool $updated 是否更新成功
     * @param string $message 响应消息
     * @return Response
     */
    public static function updated(bool $updated = true, string $message = '更新成功'): Response
    {
        return self::success([
            'updated' => $updated
        ], $message);
    }
    
    /**
     * 删除成功响应
     * 
     * @param bool $deleted 是否删除成功
     * @param string $message 响应消息
     * @return Response
     */
    public static function deleted(bool $deleted = true, string $message = '删除成功'): Response
    {
        return self::success([
            'deleted' => $deleted
        ], $message);
    }
    
    /**
     * 批量操作响应
     * 
     * @param int $total 总数
     * @param int $success 成功数
     * @param int $failed 失败数
     * @param array $errors 错误信息
     * @param string $message 响应消息
     * @return Response
     */
    public static function batch(int $total, int $success, int $failed = 0, array $errors = [], string $message = '批量操作完成'): Response
    {
        return self::success([
            'total' => $total,
            'success' => $success,
            'failed' => $failed,
            'success_rate' => $total > 0 ? round($success / $total * 100, 2) : 0,
            'errors' => $errors
        ], $message);
    }
    
    /**
     * 统计响应
     * 
     * @param array $stats 统计数据
     * @param string $message 响应消息
     * @return Response
     */
    public static function stats(array $stats, string $message = 'success'): Response
    {
        return self::success([
            'stats' => $stats,
            'generated_at' => date('Y-m-d H:i:s')
        ], $message);
    }
    
    /**
     * 验证失败响应
     * 
     * @param string $message 错误消息
     * @param array $errors 验证错误详情
     * @return Response
     */
    public static function validationError(string $message = '数据验证失败', array $errors = []): Response
    {
        return self::error($message, 422, [
            'validation_errors' => $errors
        ]);
    }
    
    /**
     * 未授权响应
     * 
     * @param string $message 错误消息
     * @return Response
     */
    public static function unauthorized(string $message = '未授权访问'): Response
    {
        return self::error($message, 401);
    }
    
    /**
     * 权限不足响应
     * 
     * @param string $message 错误消息
     * @return Response
     */
    public static function forbidden(string $message = '权限不足'): Response
    {
        return self::error($message, 403);
    }
    
    /**
     * 资源不存在响应
     * 
     * @param string $message 错误消息
     * @return Response
     */
    public static function notFound(string $message = '资源不存在'): Response
    {
        return self::error($message, 404);
    }
    
    /**
     * 请求方法不允许响应
     * 
     * @param string $message 错误消息
     * @return Response
     */
    public static function methodNotAllowed(string $message = '请求方法不允许'): Response
    {
        return self::error($message, 405);
    }
    
    /**
     * 服务器内部错误响应
     * 
     * @param string $message 错误消息
     * @param mixed $debug 调试信息
     * @return Response
     */
    public static function serverError(string $message = '服务器内部错误', $debug = null): Response
    {
        $data = null;
        if ($debug && env('APP_DEBUG', false)) {
            $data = ['debug' => $debug];
        }
        
        return self::error($message, 500, $data);
    }
    
    /**
     * 业务逻辑错误响应
     * 
     * @param string $message 错误消息
     * @param int $code 业务错误代码
     * @param mixed $data 错误数据
     * @return Response
     */
    public static function businessError(string $message, int $code = 1001, $data = null): Response
    {
        return self::error($message, $code, $data);
    }
    
    /**
     * 重定向响应
     * 
     * @param string $url 重定向URL
     * @param string $message 消息
     * @return Response
     */
    public static function redirect(string $url, string $message = '重定向'): Response
    {
        return self::success([
            'redirect' => $url
        ], $message);
    }
    
    /**
     * 文件下载响应
     * 
     * @param string $filePath 文件路径
     * @param string $fileName 下载文件名
     * @return Response
     */
    public static function download(string $filePath, string $fileName = ''): Response
    {
        if (!file_exists($filePath)) {
            return self::notFound('文件不存在');
        }
        
        if (empty($fileName)) {
            $fileName = basename($filePath);
        }
        
        return download($filePath, $fileName);
    }
    
    /**
     * 兼容旧版本响应格式
     * 
     * @param mixed $data 响应数据
     * @param string $message 响应消息
     * @param int $code 响应代码（200表示成功）
     * @return Response
     */
    public static function legacy($data = null, string $message = 'success', int $code = 200): Response
    {
        return json([
            'code' => $code,
            'message' => $message,
            'data' => $data
        ]);
    }
    
    /**
     * 根据条件返回成功或错误响应
     * 
     * @param bool $condition 条件
     * @param mixed $successData 成功时的数据
     * @param string $successMessage 成功消息
     * @param string $errorMessage 错误消息
     * @param int $errorCode 错误代码
     * @return Response
     */
    public static function conditional(
        bool $condition,
        $successData = null,
        string $successMessage = '操作成功',
        string $errorMessage = '操作失败',
        int $errorCode = 1
    ): Response {
        if ($condition) {
            return self::success($successData, $successMessage);
        } else {
            return self::error($errorMessage, $errorCode);
        }
    }
}
