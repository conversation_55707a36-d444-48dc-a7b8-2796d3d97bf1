<?php

namespace app\common;

/**
 * 数据验证助手类
 * 提供通用的数据验证方法，减少代码重复
 * 
 * 设计原则：
 * 1. 单一职责原则：专门负责数据验证
 * 2. 开闭原则：易于扩展新的验证规则
 * 3. 里氏替换原则：验证方法可以被子类重写
 */
class ValidationHelper
{
    /**
     * 验证必填字段
     * 
     * @param array $data 数据
     * @param array $required 必填字段列表
     * @param string $prefix 错误消息前缀
     * @throws \Exception
     */
    public static function validateRequired(array $data, array $required, string $prefix = '字段'): void
    {
        foreach ($required as $field) {
            if (!isset($data[$field]) || $data[$field] === '' || $data[$field] === null) {
                throw new \Exception("{$prefix} {$field} 不能为空");
            }
        }
    }
    
    /**
     * 验证字段类型
     * 
     * @param array $data 数据
     * @param array $types 字段类型映射 ['field' => 'type']
     * @throws \Exception
     */
    public static function validateTypes(array $data, array $types): void
    {
        foreach ($types as $field => $type) {
            if (!isset($data[$field])) {
                continue;
            }
            
            $value = $data[$field];
            $isValid = false;
            
            switch ($type) {
                case 'int':
                case 'integer':
                    $isValid = is_numeric($value) && is_int($value + 0);
                    break;
                case 'float':
                case 'double':
                    $isValid = is_numeric($value);
                    break;
                case 'string':
                    $isValid = is_string($value);
                    break;
                case 'bool':
                case 'boolean':
                    $isValid = is_bool($value) || in_array($value, [0, 1, '0', '1', 'true', 'false']);
                    break;
                case 'array':
                    $isValid = is_array($value);
                    break;
                case 'email':
                    $isValid = filter_var($value, FILTER_VALIDATE_EMAIL) !== false;
                    break;
                case 'url':
                    $isValid = filter_var($value, FILTER_VALIDATE_URL) !== false;
                    break;
                default:
                    $isValid = true; // 未知类型跳过验证
            }
            
            if (!$isValid) {
                throw new \Exception("字段 {$field} 类型不正确，期望类型：{$type}");
            }
        }
    }
    
    /**
     * 验证字段长度
     * 
     * @param array $data 数据
     * @param array $lengths 长度限制 ['field' => ['min' => 1, 'max' => 100]]
     * @throws \Exception
     */
    public static function validateLengths(array $data, array $lengths): void
    {
        foreach ($lengths as $field => $limit) {
            if (!isset($data[$field])) {
                continue;
            }
            
            $value = (string)$data[$field];
            $length = mb_strlen($value, 'UTF-8');
            
            if (isset($limit['min']) && $length < $limit['min']) {
                throw new \Exception("字段 {$field} 长度不能少于 {$limit['min']} 个字符");
            }
            
            if (isset($limit['max']) && $length > $limit['max']) {
                throw new \Exception("字段 {$field} 长度不能超过 {$limit['max']} 个字符");
            }
        }
    }
    
    /**
     * 验证数值范围
     * 
     * @param array $data 数据
     * @param array $ranges 范围限制 ['field' => ['min' => 0, 'max' => 100]]
     * @throws \Exception
     */
    public static function validateRanges(array $data, array $ranges): void
    {
        foreach ($ranges as $field => $range) {
            if (!isset($data[$field])) {
                continue;
            }
            
            $value = $data[$field];
            
            if (!is_numeric($value)) {
                throw new \Exception("字段 {$field} 必须是数值");
            }
            
            $numValue = floatval($value);
            
            if (isset($range['min']) && $numValue < $range['min']) {
                throw new \Exception("字段 {$field} 不能小于 {$range['min']}");
            }
            
            if (isset($range['max']) && $numValue > $range['max']) {
                throw new \Exception("字段 {$field} 不能大于 {$range['max']}");
            }
        }
    }
    
    /**
     * 验证枚举值
     * 
     * @param array $data 数据
     * @param array $enums 枚举限制 ['field' => [1, 2, 3]]
     * @throws \Exception
     */
    public static function validateEnums(array $data, array $enums): void
    {
        foreach ($enums as $field => $allowedValues) {
            if (!isset($data[$field])) {
                continue;
            }
            
            $value = $data[$field];
            
            if (!in_array($value, $allowedValues, true)) {
                $allowedStr = implode(', ', $allowedValues);
                throw new \Exception("字段 {$field} 值不正确，允许的值：{$allowedStr}");
            }
        }
    }
    
    /**
     * 验证正则表达式
     * 
     * @param array $data 数据
     * @param array $patterns 正则模式 ['field' => '/pattern/']
     * @throws \Exception
     */
    public static function validatePatterns(array $data, array $patterns): void
    {
        foreach ($patterns as $field => $pattern) {
            if (!isset($data[$field])) {
                continue;
            }
            
            $value = (string)$data[$field];
            
            if (!preg_match($pattern, $value)) {
                throw new \Exception("字段 {$field} 格式不正确");
            }
        }
    }
    
    /**
     * 综合验证方法
     * 
     * @param array $data 数据
     * @param array $rules 验证规则
     * @throws \Exception
     */
    public static function validate(array $data, array $rules): void
    {
        // 验证必填字段
        if (isset($rules['required'])) {
            self::validateRequired($data, $rules['required']);
        }
        
        // 验证字段类型
        if (isset($rules['types'])) {
            self::validateTypes($data, $rules['types']);
        }
        
        // 验证字段长度
        if (isset($rules['lengths'])) {
            self::validateLengths($data, $rules['lengths']);
        }
        
        // 验证数值范围
        if (isset($rules['ranges'])) {
            self::validateRanges($data, $rules['ranges']);
        }
        
        // 验证枚举值
        if (isset($rules['enums'])) {
            self::validateEnums($data, $rules['enums']);
        }
        
        // 验证正则表达式
        if (isset($rules['patterns'])) {
            self::validatePatterns($data, $rules['patterns']);
        }
    }
    
    /**
     * 验证商品数据
     * 
     * @param array $data 商品数据
     * @throws \Exception
     */
    public static function validateItemData(array $data): void
    {
        $rules = [
            'required' => ['name', 'price', 'price_type', 'class', 'status'],
            'types' => [
                'name' => 'string',
                'price' => 'float',
                'price_type' => 'int',
                'class' => 'string',
                'limit_quantity' => 'int',
                'restriction' => 'int',
                'status' => 'int'
            ],
            'lengths' => [
                'name' => ['min' => 1, 'max' => 100],
                'class' => ['min' => 1, 'max' => 50]
            ],
            'ranges' => [
                'price' => ['min' => 0],
                'limit_quantity' => ['min' => 0],
                'restriction' => ['min' => 0, 'max' => 2],
                'status' => ['min' => 0, 'max' => 1]
            ],
            'enums' => [
                'price_type' => [1, 2, 3], // 1:C币, 2:泡点, 3:RMB
                'restriction' => [0, 1, 2], // 0:无限制, 1:每日限制, 2:总量限制
                'status' => [0, 1] // 0:下架, 1:上架
            ]
        ];
        
        self::validate($data, $rules);
    }
    
    /**
     * 验证用户数据
     * 
     * @param array $data 用户数据
     * @throws \Exception
     */
    public static function validateUserData(array $data): void
    {
        $rules = [
            'required' => ['username'],
            'types' => [
                'username' => 'string',
                'password' => 'string',
                'email' => 'email'
            ],
            'lengths' => [
                'username' => ['min' => 3, 'max' => 20],
                'password' => ['min' => 6, 'max' => 50]
            ],
            'patterns' => [
                'username' => '/^[a-zA-Z0-9_]+$/' // 只允许字母、数字、下划线
            ]
        ];
        
        self::validate($data, $rules);
    }
    
    /**
     * 验证订单数据
     * 
     * @param array $data 订单数据
     * @throws \Exception
     */
    public static function validateOrderData(array $data): void
    {
        $rules = [
            'required' => ['user_id', 'item_id', 'quantity'],
            'types' => [
                'user_id' => 'string',
                'item_id' => 'int',
                'quantity' => 'int',
                'total_price' => 'float'
            ],
            'ranges' => [
                'item_id' => ['min' => 1],
                'quantity' => ['min' => 1, 'max' => 999],
                'total_price' => ['min' => 0]
            ]
        ];
        
        self::validate($data, $rules);
    }
    
    /**
     * 清理和转换数据类型
     * 
     * @param array $data 原始数据
     * @param array $types 类型映射
     * @return array 清理后的数据
     */
    public static function sanitizeData(array $data, array $types): array
    {
        $sanitized = [];
        
        foreach ($data as $key => $value) {
            if (!isset($types[$key])) {
                $sanitized[$key] = $value;
                continue;
            }
            
            $type = $types[$key];
            
            switch ($type) {
                case 'int':
                case 'integer':
                    $sanitized[$key] = intval($value);
                    break;
                case 'float':
                case 'double':
                    $sanitized[$key] = floatval($value);
                    break;
                case 'string':
                    $sanitized[$key] = trim((string)$value);
                    break;
                case 'bool':
                case 'boolean':
                    $sanitized[$key] = in_array($value, [1, '1', 'true', true], true);
                    break;
                case 'array':
                    $sanitized[$key] = is_array($value) ? $value : [];
                    break;
                default:
                    $sanitized[$key] = $value;
            }
        }
        
        return $sanitized;
    }
}
