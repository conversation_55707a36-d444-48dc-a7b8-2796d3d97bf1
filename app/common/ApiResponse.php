<?php

namespace app\common;

use think\Response;
use think\facade\Env;

/**
 * 统一API响应类
 * 提供标准化的API响应格式，确保所有接口返回格式一致
 * 
 * 设计原则：
 * 1. 单一职责原则：专门负责API响应格式化
 * 2. 开闭原则：易于扩展新的响应类型
 * 3. 里氏替换原则：可以替换原有的json()响应
 */
class ApiResponse
{
    /**
     * HTTP状态码常量
     */
    const HTTP_OK = 200;
    const HTTP_BAD_REQUEST = 400;
    const HTTP_UNAUTHORIZED = 401;
    const HTTP_FORBIDDEN = 403;
    const HTTP_NOT_FOUND = 404;
    const HTTP_METHOD_NOT_ALLOWED = 405;
    const HTTP_INTERNAL_SERVER_ERROR = 500;
    const HTTP_SERVICE_UNAVAILABLE = 503;

    /**
     * 业务状态码常量
     */
    const CODE_SUCCESS = 0;
    const CODE_ERROR = 1;
    const CODE_PARAM_ERROR = 2;
    const CODE_UNAUTHORIZED = 3;
    const CODE_FORBIDDEN = 4;
    const CODE_BUSINESS_ERROR = 5;
    const CODE_SYSTEM_ERROR = 6;
    const CODE_NOT_FOUND = 7;
    const CODE_METHOD_NOT_ALLOWED = 8;
    const CODE_SERVICE_UNAVAILABLE = 9;



    /**
     * 成功响应
     * 
     * @param mixed $data 响应数据
     * @param string $message 响应消息
     * @param int $code 业务状态码
     * @param int $httpCode HTTP状态码
     * @return Response
     */
    public static function success($data = [], string $message = '操作成功', int $code = self::CODE_SUCCESS, int $httpCode = self::HTTP_OK): Response
    {
        return self::response(true, $code, $message, $data, $httpCode);
    }

    /**
     * 失败响应
     * 
     * @param string $message 错误消息
     * @param int $code 业务状态码
     * @param mixed $data 响应数据
     * @param int $httpCode HTTP状态码
     * @return Response
     */
    public static function error(string $message = '操作失败', int $code = 6, $data = null, int $httpCode = self::HTTP_OK): Response
    {
        return self::response(false, $code, $message, $data, $httpCode);
    }

    /**
     * 参数错误响应
     * 
     * @param string $message 错误消息
     * @param mixed $data 响应数据
     * @return Response
     */
    public static function paramError(string $message = '参数错误', $data = null): Response
    {
        return self::error($message, self::CODE_PARAM_ERROR, $data, self::HTTP_BAD_REQUEST);
    }

    /**
     * 未授权响应
     * 
     * @param string $message 错误消息
     * @return Response
     */
    public static function unauthorized(string $message = '请先登录'): Response
    {
        return self::error($message, self::CODE_UNAUTHORIZED, null, self::HTTP_UNAUTHORIZED);
    }

    /**
     * 权限不足响应
     * 
     * @param string $message 错误消息
     * @return Response
     */
    public static function forbidden(string $message = '权限不足'): Response
    {
        return self::error($message, self::CODE_FORBIDDEN, null, self::HTTP_FORBIDDEN);
    }

    /**
     * 资源不存在响应
     * 
     * @param string $message 错误消息
     * @return Response
     */
    public static function notFound(string $message = '资源不存在'): Response
    {
        return self::error($message, self::CODE_NOT_FOUND, null, self::HTTP_NOT_FOUND);
    }

    /**
     * 方法不允许响应
     * 
     * @param string $message 错误消息
     * @return Response
     */
    public static function methodNotAllowed(string $message = '请求方法不允许'): Response
    {
        return self::error($message, self::CODE_METHOD_NOT_ALLOWED, null, self::HTTP_METHOD_NOT_ALLOWED);
    }

    /**
     * 系统错误响应
     * 
     * @param string $message 错误消息
     * @param mixed $data 响应数据
     * @return Response
     */
    public static function systemError(string $message = '系统异常，请稍后重试', $data = null): Response
    {
        return self::error($message, 6, $data, self::HTTP_INTERNAL_SERVER_ERROR);
    }

    /**
     * 服务不可用响应
     * 
     * @param string $message 错误消息
     * @return Response
     */
    public static function serviceUnavailable(string $message = '服务暂时不可用'): Response
    {
        return self::error($message, self::CODE_SERVICE_UNAVAILABLE, null, self::HTTP_SERVICE_UNAVAILABLE);
    }

    /**
     * 业务错误响应
     *
     * @param string $message 错误消息
     * @param string $errorCode 业务错误代码
     * @param mixed $data 额外数据
     * @return Response
     */
    public static function businessError(string $message, string $errorCode = '', $data = null): Response
    {
        $responseData = [
            'success' => false,
            'code' => self::CODE_BUSINESS_ERROR,
            'message' => $message,
            'data' => $data,
            'error_code' => $errorCode,
            'timestamp' => time(),
            'request_id' => self::generateRequestId()
        ];

        if (Env::get('APP_DEBUG', false)) {
            $responseData['debug'] = [
                'error_code' => $errorCode,
                'trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 5)
            ];
        }

        return json($responseData, 400);
    }



    /**
     * 分页数据成功响应
     *
     * @param array $list 数据列表
     * @param int $total 总数
     * @param int $page 当前页
     * @param int $limit 每页数量
     * @param string $message 响应消息
     * @return Response
     */
    public static function paginate(array $list, int $total, int $page, int $limit, string $message = '获取成功'): Response
    {
        $data = [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => $limit > 0 ? ceil($total / $limit) : 0
        ];

        return self::success($data, $message);
    }

    /**
     * 基础响应方法
     * 
     * @param bool $success 是否成功
     * @param int $code 业务状态码
     * @param string $message 响应消息
     * @param mixed $data 响应数据
     * @param int $httpCode HTTP状态码
     * @return Response
     */
    private static function response(bool $success, int $code, string $message, $data, int $httpCode): Response
    {
        $responseData = [
            'success' => $success,
            'code' => $code,
            'message' => $message,
            'data' => $data,
            'timestamp' => time(),
            'request_id' => self::generateRequestId()
        ];

        // 在调试模式下添加额外信息
        if (Env::get('APP_DEBUG', false)) {
            $responseData['debug'] = [
                'memory_usage' => memory_get_usage(true),
                'execution_time' => microtime(true) - (defined('APP_START_TIME') ? APP_START_TIME : $_SERVER['REQUEST_TIME_FLOAT']),
                'sql_count' => self::getSqlCount()
            ];
        }

        return json($responseData, $httpCode);
    }

    /**
     * 生成请求ID
     * 
     * @return string
     */
    private static function generateRequestId(): string
    {
        return uniqid('req_', true);
    }

    /**
     * 获取SQL查询次数（如果可用）
     * 
     * @return int
     */
    private static function getSqlCount(): int
    {
        try {
            // 尝试获取ThinkPHP的SQL查询统计
            if (class_exists('\think\facade\Db')) {
                return \think\facade\Db::getQueryTimes();
            }
        } catch (\Exception $e) {
            // 忽略错误
        }
        
        return 0;
    }

    /**
     * 创建带有验证错误的响应
     * 
     * @param array $errors 验证错误信息
     * @param string $message 主要错误消息
     * @return Response
     */
    public static function validationError(array $errors, string $message = '数据验证失败'): Response
    {
        return self::paramError($message, ['validation_errors' => $errors]);
    }



    /**
     * 创建资源创建成功的响应
     * 
     * @param mixed $data 创建的资源数据
     * @param string $message 成功消息
     * @return Response
     */
    public static function created($data = [], string $message = '创建成功'): Response
    {
        return self::success($data, $message, 201, 201);
    }

    /**
     * 创建资源更新成功的响应
     * 
     * @param mixed $data 更新的资源数据
     * @param string $message 成功消息
     * @return Response
     */
    public static function updated($data = [], string $message = '更新成功'): Response
    {
        return self::success($data, $message);
    }

    /**
     * 创建资源删除成功的响应
     * 
     * @param string $message 成功消息
     * @return Response
     */
    public static function deleted(string $message = '删除成功'): Response
    {
        return self::success([], $message);
    }
}
