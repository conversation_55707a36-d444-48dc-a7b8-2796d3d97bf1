<?php

namespace app\common;

use think\facade\Session;
use think\facade\Db;
use think\facade\Log;
use think\Response;

/**
 * 认证助手类
 * 提供通用的认证相关方法，减少代码重复
 * 
 * 设计原则：
 * 1. 单一职责原则：专门负责认证相关的通用功能
 * 2. 开闭原则：易于扩展新的认证方法
 * 3. 依赖倒置原则：依赖抽象而非具体实现
 */
class AuthHelper
{
    /**
     * 管理员用户列表
     */
    private static array $adminUsers = ['gm02', 'admin', 'root'];
    
    /**
     * 检查用户是否已登录
     * 
     * @return bool
     */
    public static function isLoggedIn(): bool
    {
        return Session::has('user_id');
    }
    
    /**
     * 获取当前登录用户ID
     * 
     * @return string|null
     */
    public static function getCurrentUserId(): ?string
    {
        return Session::get('user_id');
    }
    
    /**
     * 获取当前登录用户信息
     * 
     * @return array|null
     */
    public static function getCurrentUserInfo(): ?array
    {
        return Session::get('user_info');
    }
    
    /**
     * 检查用户是否有昵称
     * 
     * @param string|null $userId 用户ID，为空时使用当前登录用户
     * @return bool
     */
    public static function hasNickname(?string $userId = null): bool
    {
        $userId = $userId ?: self::getCurrentUserId();
        
        if (!$userId) {
            return false;
        }
        
        try {
            $nicknameData = Db::table('user_nickname')
                ->where('user_id', $userId)
                ->find();
            
            return !empty($nicknameData);
        } catch (\Exception $e) {
            Log::error('检查用户昵称失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 获取用户昵称
     * 
     * @param string|null $userId 用户ID，为空时使用当前登录用户
     * @return string|null
     */
    public static function getUserNickname(?string $userId = null): ?string
    {
        $userId = $userId ?: self::getCurrentUserId();
        
        if (!$userId) {
            return null;
        }
        
        try {
            $nicknameData = Db::table('user_nickname')
                ->where('user_id', $userId)
                ->find();
            
            return $nicknameData['nickname'] ?? null;
        } catch (\Exception $e) {
            Log::error('获取用户昵称失败: ' . $e->getMessage());
            return null;
        }
    }
    
    /**
     * 检查用户是否为管理员
     *
     * @param string|null $userId 用户ID，为空时使用当前登录用户
     * @return bool
     */
    public static function isAdmin(?string $userId = null): bool
    {
        $userId = $userId ?: self::getCurrentUserId();

        if (!$userId) {
            return false;
        }

        // 快速检查：如果是已知的管理员账号，直接返回true（避免数据库查询）
        if (in_array($userId, self::$adminUsers)) {
            return true;
        }

        try {
            // 1. 检查session中是否已标记为管理员
            $userInfo = Session::get('user_info');
            if ($userInfo && isset($userInfo['is_admin']) && $userInfo['is_admin']) {
                return true;
            }

            // 2. 检查新的管理员配置文件
            $configPath = app()->getConfigPath() . 'admin_users.php';
            if (file_exists($configPath)) {
                $admins = include $configPath;
                if (isset($admins[$userId]) && ($admins[$userId]['status'] ?? 1) == 1) {
                    // 在session中标记为管理员，避免重复查询
                    if ($userInfo) {
                        $userInfo['is_admin'] = true;
                        Session::set('user_info', $userInfo);
                    }
                    return true;
                }
            }

            // 3. 检查是否在硬编码管理员列表中
            if (in_array($userId, self::$adminUsers)) {
                // 在session中标记为管理员，避免重复查询
                if ($userInfo) {
                    $userInfo['is_admin'] = true;
                    Session::set('user_info', $userInfo);
                }
                return true;
            }

            // 4. 从数据库检查用户角色（最后的备选方案）
            $userRole = Db::connect('seal_web')
                ->table('sealmember')
                ->where('id', $userId)
                ->value('role');

            $isAdmin = $userRole === 'admin' || $userRole === 'gm';

            // 如果是管理员，在session中标记，避免重复查询
            if ($isAdmin && $userInfo) {
                $userInfo['is_admin'] = true;
                Session::set('user_info', $userInfo);
            }

            return $isAdmin;
        } catch (\Exception $e) {
            Log::error('检查管理员权限失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 检查登录状态并返回响应（用于API）
     * 
     * @param string $message 错误消息
     * @return Response|null
     */
    public static function checkLoginForApi(string $message = '请先登录'): ?Response
    {
        if (!self::isLoggedIn()) {
            return ApiResponse::unauthorized($message);
        }
        
        return null;
    }
    
    /**
     * 检查登录状态并返回响应（用于页面）
     * 
     * @param string $redirectUrl 重定向URL
     * @return Response|null
     */
    public static function checkLoginForPage(string $redirectUrl = '/login'): ?Response
    {
        if (!self::isLoggedIn()) {
            return redirect($redirectUrl);
        }
        
        return null;
    }
    
    /**
     * 检查管理员权限并返回响应（用于API）
     * 
     * @param string $message 错误消息
     * @return Response|null
     */
    public static function checkAdminForApi(string $message = '权限不足，仅限管理员访问'): ?Response
    {
        // 先检查登录状态
        $loginCheck = self::checkLoginForApi();
        if ($loginCheck) {
            return $loginCheck;
        }
        
        // 检查管理员权限
        if (!self::isAdmin()) {
            return ApiResponse::forbidden($message);
        }
        
        return null;
    }
    
    /**
     * 检查管理员权限并返回响应（用于页面）
     * 
     * @param string $redirectUrl 重定向URL
     * @return Response|null
     */
    public static function checkAdminForPage(string $redirectUrl = '/login'): ?Response
    {
        // 先检查登录状态
        $loginCheck = self::checkLoginForPage($redirectUrl);
        if ($loginCheck) {
            return $loginCheck;
        }
        
        // 检查管理员权限
        if (!self::isAdmin()) {
            return redirect('/403'); // 权限不足页面
        }
        
        return null;
    }
    
    /**
     * 检查昵称并返回响应（用于API）
     * 
     * @param string $message 错误消息
     * @return Response|null
     */
    public static function checkNicknameForApi(string $message = '请先设置昵称'): ?Response
    {
        // 先检查登录状态
        $loginCheck = self::checkLoginForApi();
        if ($loginCheck) {
            return $loginCheck;
        }
        
        // 检查昵称
        if (!self::hasNickname()) {
            return json([
                'code' => 402,
                'message' => $message,
                'data' => ['redirect' => '/auth/setNickname']
            ]);
        }
        
        return null;
    }
    
    /**
     * 检查昵称并返回响应（用于页面）
     * 
     * @param string $redirectUrl 重定向URL
     * @return Response|null
     */
    public static function checkNicknameForPage(string $redirectUrl = '/auth/setNickname'): ?Response
    {
        // 先检查登录状态
        $loginCheck = self::checkLoginForPage();
        if ($loginCheck) {
            return $loginCheck;
        }
        
        // 检查昵称
        if (!self::hasNickname()) {
            return redirect($redirectUrl);
        }
        
        return null;
    }
    
    /**
     * 完整的认证检查（登录 + 昵称）用于API
     * 
     * @return Response|null
     */
    public static function checkFullAuthForApi(): ?Response
    {
        // 检查登录状态
        $loginCheck = self::checkLoginForApi();
        if ($loginCheck) {
            return $loginCheck;
        }
        
        // 检查昵称
        return self::checkNicknameForApi();
    }
    
    /**
     * 完整的认证检查（登录 + 昵称）用于页面
     * 
     * @return Response|null
     */
    public static function checkFullAuthForPage(): ?Response
    {
        // 检查登录状态
        $loginCheck = self::checkLoginForPage();
        if ($loginCheck) {
            return $loginCheck;
        }
        
        // 检查昵称
        return self::checkNicknameForPage();
    }
    
    /**
     * 设置用户登录状态
     * 
     * @param string $userId 用户ID
     * @param array $userInfo 用户信息
     */
    public static function setUserLogin(string $userId, array $userInfo = []): void
    {
        Session::set('user_id', $userId);
        Session::set('user_info', array_merge([
            'id' => $userId,
            'username' => $userId
        ], $userInfo));
    }
    
    /**
     * 清除用户登录状态
     */
    public static function clearUserLogin(): void
    {
        Session::clear();
    }
    
    /**
     * 添加管理员用户
     * 
     * @param string $userId 用户ID
     */
    public static function addAdminUser(string $userId): void
    {
        if (!in_array($userId, self::$adminUsers)) {
            self::$adminUsers[] = $userId;
        }
    }
    
    /**
     * 移除管理员用户
     * 
     * @param string $userId 用户ID
     */
    public static function removeAdminUser(string $userId): void
    {
        $key = array_search($userId, self::$adminUsers);
        if ($key !== false) {
            unset(self::$adminUsers[$key]);
            self::$adminUsers = array_values(self::$adminUsers); // 重新索引
        }
    }
    
    /**
     * 获取管理员用户列表
     * 
     * @return array
     */
    public static function getAdminUsers(): array
    {
        return self::$adminUsers;
    }
}
