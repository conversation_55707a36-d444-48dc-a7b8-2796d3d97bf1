<?php

namespace app\common;

use think\facade\Cache;
use think\facade\Log;

/**
 * 缓存助手类
 * 提供通用的缓存操作方法，减少代码重复
 * 
 * 设计原则：
 * 1. 单一职责原则：专门负责缓存操作
 * 2. 开闭原则：易于扩展新的缓存策略
 * 3. 依赖倒置原则：依赖抽象的缓存接口
 */
class CacheHelper
{
    /**
     * 默认缓存时间（秒）
     */
    const DEFAULT_TTL = 1800; // 30分钟
    
    /**
     * 短期缓存时间（秒）
     */
    const SHORT_TTL = 300; // 5分钟
    
    /**
     * 长期缓存时间（秒）
     */
    const LONG_TTL = 3600; // 1小时
    
    /**
     * 获取缓存，如果不存在则执行回调函数并缓存结果
     * 
     * @param string $key 缓存键
     * @param callable $callback 回调函数
     * @param int $ttl 缓存时间
     * @param mixed $default 默认值
     * @return mixed
     */
    public static function remember(string $key, callable $callback, int $ttl = self::DEFAULT_TTL, $default = null)
    {
        try {
            // 尝试从缓存获取
            $cached = Cache::get($key);
            if ($cached !== null) {
                return $cached;
            }
            
            // 执行回调函数获取数据
            $result = $callback();
            
            // 如果结果为null或false，使用默认值
            if ($result === null || $result === false) {
                $result = $default;
            }
            
            // 缓存结果
            if ($result !== null) {
                Cache::set($key, $result, $ttl);
            }
            
            return $result;
        } catch (\Exception $e) {
            Log::error('缓存操作失败: ' . $e->getMessage());
            
            // 缓存失败时直接执行回调
            try {
                return $callback();
            } catch (\Exception $callbackException) {
                Log::error('回调执行失败: ' . $callbackException->getMessage());
                return $default;
            }
        }
    }
    
    /**
     * 设置缓存
     * 
     * @param string $key 缓存键
     * @param mixed $value 缓存值
     * @param int $ttl 缓存时间
     * @return bool
     */
    public static function set(string $key, $value, int $ttl = self::DEFAULT_TTL): bool
    {
        try {
            return Cache::set($key, $value, $ttl);
        } catch (\Exception $e) {
            Log::error('设置缓存失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 获取缓存
     * 
     * @param string $key 缓存键
     * @param mixed $default 默认值
     * @return mixed
     */
    public static function get(string $key, $default = null)
    {
        try {
            $value = Cache::get($key);
            return $value !== null ? $value : $default;
        } catch (\Exception $e) {
            Log::error('获取缓存失败: ' . $e->getMessage());
            return $default;
        }
    }
    
    /**
     * 删除缓存
     * 
     * @param string $key 缓存键
     * @return bool
     */
    public static function delete(string $key): bool
    {
        try {
            return Cache::delete($key);
        } catch (\Exception $e) {
            Log::error('删除缓存失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 检查缓存是否存在
     * 
     * @param string $key 缓存键
     * @return bool
     */
    public static function has(string $key): bool
    {
        try {
            return Cache::has($key);
        } catch (\Exception $e) {
            Log::error('检查缓存失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 清除所有缓存
     * 
     * @return bool
     */
    public static function clear(): bool
    {
        try {
            return Cache::clear();
        } catch (\Exception $e) {
            Log::error('清除缓存失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 根据模式删除缓存
     * 
     * @param string $pattern 模式
     * @return bool
     */
    public static function deleteByPattern(string $pattern): bool
    {
        try {
            // 获取所有匹配的键
            $keys = self::getKeysByPattern($pattern);
            
            if (empty($keys)) {
                return true;
            }
            
            // 批量删除
            foreach ($keys as $key) {
                Cache::delete($key);
            }
            
            return true;
        } catch (\Exception $e) {
            Log::error('按模式删除缓存失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 根据模式获取缓存键列表
     * 
     * @param string $pattern 模式
     * @return array
     */
    public static function getKeysByPattern(string $pattern): array
    {
        try {
            // 这里需要根据具体的缓存驱动实现
            // Redis驱动可以使用KEYS命令
            // 文件驱动需要扫描文件
            
            // 简化实现：返回空数组
            // 实际项目中需要根据缓存驱动类型实现
            return [];
        } catch (\Exception $e) {
            Log::error('获取缓存键失败: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * 缓存标签操作
     * 
     * @param string|array $tags 标签
     * @return \think\cache\TagSet
     */
    public static function tags($tags)
    {
        return Cache::tag($tags);
    }
    
    /**
     * 清除指定标签的缓存
     * 
     * @param string|array $tags 标签
     * @return bool
     */
    public static function clearByTags($tags): bool
    {
        try {
            Cache::tag($tags)->clear();
            return true;
        } catch (\Exception $e) {
            Log::error('按标签清除缓存失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 构建缓存键
     * 
     * @param string $prefix 前缀
     * @param mixed $params 参数
     * @return string
     */
    public static function buildKey(string $prefix, $params = null): string
    {
        $key = $prefix;
        
        if ($params !== null) {
            if (is_array($params) || is_object($params)) {
                $key .= ':' . md5(serialize($params));
            } else {
                $key .= ':' . $params;
            }
        }
        
        return $key;
    }
    
    /**
     * 分页缓存键构建
     * 
     * @param string $prefix 前缀
     * @param int $page 页码
     * @param int $limit 每页数量
     * @param array $conditions 条件
     * @param array $options 选项
     * @return string
     */
    public static function buildPaginationKey(string $prefix, int $page, int $limit, array $conditions = [], array $options = []): string
    {
        $params = [
            'page' => $page,
            'limit' => $limit,
            'conditions' => $conditions,
            'options' => $options
        ];
        
        return self::buildKey($prefix . ':pagination', $params);
    }
    
    /**
     * 列表缓存键构建
     * 
     * @param string $prefix 前缀
     * @param array $conditions 条件
     * @param array $options 选项
     * @return string
     */
    public static function buildListKey(string $prefix, array $conditions = [], array $options = []): string
    {
        $params = [
            'conditions' => $conditions,
            'options' => $options
        ];
        
        return self::buildKey($prefix . ':list', $params);
    }
    
    /**
     * 详情缓存键构建
     * 
     * @param string $prefix 前缀
     * @param mixed $id ID
     * @return string
     */
    public static function buildDetailKey(string $prefix, $id): string
    {
        return self::buildKey($prefix . ':detail', $id);
    }
    
    /**
     * 统计缓存键构建
     * 
     * @param string $prefix 前缀
     * @param array $conditions 条件
     * @return string
     */
    public static function buildStatsKey(string $prefix, array $conditions = []): string
    {
        return self::buildKey($prefix . ':stats', $conditions);
    }
    
    /**
     * 批量获取缓存
     * 
     * @param array $keys 缓存键数组
     * @return array
     */
    public static function getMultiple(array $keys): array
    {
        $result = [];
        
        foreach ($keys as $key) {
            $result[$key] = self::get($key);
        }
        
        return $result;
    }
    
    /**
     * 批量设置缓存
     * 
     * @param array $values 键值对数组
     * @param int $ttl 缓存时间
     * @return bool
     */
    public static function setMultiple(array $values, int $ttl = self::DEFAULT_TTL): bool
    {
        try {
            foreach ($values as $key => $value) {
                Cache::set($key, $value, $ttl);
            }
            return true;
        } catch (\Exception $e) {
            Log::error('批量设置缓存失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 缓存统计信息
     * 
     * @return array
     */
    public static function getStats(): array
    {
        try {
            // 这里需要根据具体的缓存驱动实现
            // 返回缓存统计信息
            return [
                'driver' => config('cache.default'),
                'status' => 'active',
                'memory_usage' => 0,
                'hit_rate' => 0
            ];
        } catch (\Exception $e) {
            Log::error('获取缓存统计失败: ' . $e->getMessage());
            return [];
        }
    }
}
