<?php

namespace app\common;

use think\facade\Db;
use think\facade\Log;

/**
 * 数据库助手类
 * 提供通用的数据库操作方法，减少代码重复
 * 
 * 设计原则：
 * 1. 单一职责原则：专门负责数据库操作
 * 2. 开闭原则：易于扩展新的数据库操作
 * 3. 依赖倒置原则：依赖抽象的数据库接口
 */
class DatabaseHelper
{
    /**
     * 安全地执行数据库操作
     * 
     * @param callable $callback 数据库操作回调
     * @param string $errorMessage 错误消息
     * @param mixed $defaultValue 默认返回值
     * @return mixed
     */
    public static function safeExecute(callable $callback, string $errorMessage = '数据库操作失败', $defaultValue = null)
    {
        try {
            return $callback();
        } catch (\Exception $e) {
            Log::error($errorMessage . ': ' . $e->getMessage());
            return $defaultValue;
        }
    }
    
    /**
     * 事务执行
     * 
     * @param callable $callback 事务操作回调
     * @param string $connection 数据库连接名
     * @return mixed
     * @throws \Exception
     */
    public static function transaction(callable $callback, string $connection = 'default')
    {
        $db = Db::connect($connection);
        
        $db->startTrans();
        
        try {
            $result = $callback($db);
            $db->commit();
            return $result;
        } catch (\Exception $e) {
            $db->rollback();
            throw $e;
        }
    }
    
    /**
     * 分页查询
     * 
     * @param string $table 表名
     * @param int $page 页码
     * @param int $limit 每页数量
     * @param array $conditions 查询条件
     * @param array $options 查询选项
     * @param string $connection 数据库连接名
     * @return array
     */
    public static function paginate(
        string $table,
        int $page = 1,
        int $limit = 20,
        array $conditions = [],
        array $options = [],
        string $connection = 'default'
    ): array {
        return self::safeExecute(function() use ($table, $page, $limit, $conditions, $options, $connection) {
            $query = Db::connect($connection)->table($table);
            
            // 应用查询条件
            self::applyConditions($query, $conditions);
            
            // 获取总数
            $total = $query->count();
            
            // 应用查询选项
            self::applyOptions($query, $options);
            
            // 分页查询
            $list = $query->page($page, $limit)->select()->toArray();
            
            return [
                'list' => $list,
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'total_pages' => ceil($total / $limit),
                'has_next' => $page * $limit < $total,
                'has_prev' => $page > 1
            ];
        }, '分页查询失败', [
            'list' => [],
            'total' => 0,
            'page' => $page,
            'limit' => $limit,
            'total_pages' => 0,
            'has_next' => false,
            'has_prev' => false
        ]);
    }
    
    /**
     * 批量插入
     * 
     * @param string $table 表名
     * @param array $dataList 数据列表
     * @param string $connection 数据库连接名
     * @return bool
     */
    public static function batchInsert(string $table, array $dataList, string $connection = 'default'): bool
    {
        if (empty($dataList)) {
            return true;
        }
        
        return self::safeExecute(function() use ($table, $dataList, $connection) {
            return Db::connect($connection)->table($table)->insertAll($dataList) > 0;
        }, '批量插入失败', false);
    }
    
    /**
     * 批量更新
     * 
     * @param string $table 表名
     * @param array $conditions 更新条件
     * @param array $data 更新数据
     * @param string $connection 数据库连接名
     * @return int 影响的行数
     */
    public static function batchUpdate(
        string $table,
        array $conditions,
        array $data,
        string $connection = 'default'
    ): int {
        return self::safeExecute(function() use ($table, $conditions, $data, $connection) {
            $query = Db::connect($connection)->table($table);
            self::applyConditions($query, $conditions);
            return $query->update($data);
        }, '批量更新失败', 0);
    }
    
    /**
     * 批量删除
     * 
     * @param string $table 表名
     * @param array $conditions 删除条件
     * @param string $connection 数据库连接名
     * @return int 影响的行数
     */
    public static function batchDelete(string $table, array $conditions, string $connection = 'default'): int
    {
        return self::safeExecute(function() use ($table, $conditions, $connection) {
            $query = Db::connect($connection)->table($table);
            self::applyConditions($query, $conditions);
            return $query->delete();
        }, '批量删除失败', 0);
    }
    
    /**
     * 统计查询
     * 
     * @param string $table 表名
     * @param array $conditions 查询条件
     * @param string $connection 数据库连接名
     * @return int
     */
    public static function count(string $table, array $conditions = [], string $connection = 'default'): int
    {
        return self::safeExecute(function() use ($table, $conditions, $connection) {
            $query = Db::connect($connection)->table($table);
            self::applyConditions($query, $conditions);
            return $query->count();
        }, '统计查询失败', 0);
    }
    
    /**
     * 检查记录是否存在
     * 
     * @param string $table 表名
     * @param array $conditions 查询条件
     * @param string $connection 数据库连接名
     * @return bool
     */
    public static function exists(string $table, array $conditions, string $connection = 'default'): bool
    {
        return self::count($table, $conditions, $connection) > 0;
    }
    
    /**
     * 获取单条记录
     * 
     * @param string $table 表名
     * @param array $conditions 查询条件
     * @param array $options 查询选项
     * @param string $connection 数据库连接名
     * @return array|null
     */
    public static function findOne(
        string $table,
        array $conditions = [],
        array $options = [],
        string $connection = 'default'
    ): ?array {
        return self::safeExecute(function() use ($table, $conditions, $options, $connection) {
            $query = Db::connect($connection)->table($table);
            self::applyConditions($query, $conditions);
            self::applyOptions($query, $options);
            return $query->find();
        }, '查询单条记录失败', null);
    }
    
    /**
     * 获取多条记录
     * 
     * @param string $table 表名
     * @param array $conditions 查询条件
     * @param array $options 查询选项
     * @param string $connection 数据库连接名
     * @return array
     */
    public static function findMany(
        string $table,
        array $conditions = [],
        array $options = [],
        string $connection = 'default'
    ): array {
        return self::safeExecute(function() use ($table, $conditions, $options, $connection) {
            $query = Db::connect($connection)->table($table);
            self::applyConditions($query, $conditions);
            self::applyOptions($query, $options);
            return $query->select()->toArray();
        }, '查询多条记录失败', []);
    }
    
    /**
     * 应用查询条件
     * 
     * @param \think\db\Query $query 查询对象
     * @param array $conditions 查询条件
     */
    public static function applyConditions(\think\db\Query $query, array $conditions): void
    {
        foreach ($conditions as $field => $value) {
            if ($value === null || $value === '') {
                continue;
            }
            
            if (is_array($value)) {
                // 数组条件，使用IN查询
                $query->whereIn($field, $value);
            } elseif (strpos($field, '_like') !== false) {
                // 模糊查询
                $realField = str_replace('_like', '', $field);
                $query->where($realField, 'like', '%' . $value . '%');
            } elseif (strpos($field, '_gt') !== false) {
                // 大于查询
                $realField = str_replace('_gt', '', $field);
                $query->where($realField, '>', $value);
            } elseif (strpos($field, '_gte') !== false) {
                // 大于等于查询
                $realField = str_replace('_gte', '', $field);
                $query->where($realField, '>=', $value);
            } elseif (strpos($field, '_lt') !== false) {
                // 小于查询
                $realField = str_replace('_lt', '', $field);
                $query->where($realField, '<', $value);
            } elseif (strpos($field, '_lte') !== false) {
                // 小于等于查询
                $realField = str_replace('_lte', '', $field);
                $query->where($realField, '<=', $value);
            } elseif (strpos($field, '_ne') !== false) {
                // 不等于查询
                $realField = str_replace('_ne', '', $field);
                $query->where($realField, '<>', $value);
            } else {
                // 等于查询
                $query->where($field, $value);
            }
        }
    }
    
    /**
     * 应用查询选项
     * 
     * @param \think\db\Query $query 查询对象
     * @param array $options 查询选项
     */
    public static function applyOptions(\think\db\Query $query, array $options): void
    {
        // 字段选择
        if (isset($options['field'])) {
            $query->field($options['field']);
        }
        
        // 排序
        if (isset($options['order'])) {
            if (is_array($options['order'])) {
                foreach ($options['order'] as $field => $direction) {
                    $query->order($field, $direction);
                }
            } else {
                $query->order($options['order']);
            }
        }
        
        // 分组
        if (isset($options['group'])) {
            $query->group($options['group']);
        }
        
        // HAVING条件
        if (isset($options['having'])) {
            $query->having($options['having']);
        }
        
        // 限制数量
        if (isset($options['limit'])) {
            $query->limit($options['limit']);
        }
        
        // 关联查询
        if (isset($options['join'])) {
            foreach ($options['join'] as $join) {
                $query->join($join['table'], $join['on'], $join['type'] ?? 'INNER');
            }
        }
    }
    
    /**
     * 获取聚合值
     * 
     * @param string $table 表名
     * @param string $function 聚合函数 (sum, avg, max, min)
     * @param string $field 字段名
     * @param array $conditions 查询条件
     * @param string $connection 数据库连接名
     * @return mixed
     */
    public static function aggregate(
        string $table,
        string $function,
        string $field,
        array $conditions = [],
        string $connection = 'default'
    ) {
        return self::safeExecute(function() use ($table, $function, $field, $conditions, $connection) {
            $query = Db::connect($connection)->table($table);
            self::applyConditions($query, $conditions);
            
            switch (strtolower($function)) {
                case 'sum':
                    return $query->sum($field);
                case 'avg':
                    return $query->avg($field);
                case 'max':
                    return $query->max($field);
                case 'min':
                    return $query->min($field);
                default:
                    throw new \InvalidArgumentException("不支持的聚合函数: {$function}");
            }
        }, "聚合查询失败", 0);
    }
}
