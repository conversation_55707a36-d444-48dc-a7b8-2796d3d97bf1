<?php

namespace app\cron;

use think\facade\Log;
use think\facade\Db;
use app\service\ItemDeliveryService;
use app\model\PurchaseOrder;
use Exception;

/**
 * 物品发送定时任务
 * 自动处理待发送的订单和重试失败的订单
 * 
 * 设计原则：
 * 1. 单一职责原则：专门处理定时发送任务
 * 2. 可靠性原则：包含错误处理和重试机制
 * 3. 性能原则：批量处理和合理的执行间隔
 */
class ItemDeliveryCron
{
    /**
     * 物品发送服务
     */
    private $deliveryService;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->deliveryService = new ItemDeliveryService();
    }

    /**
     * 处理待发送订单（每分钟执行）
     * 
     * @return void
     */
    public function processPendingOrders(): void
    {
        try {
            $startTime = microtime(true);
            Log::info('开始处理待发送订单');

            // 获取配置
            $batchSize = config('purchase.delivery.batch_size', 50);
            $maxRetryCount = config('purchase.delivery.max_retry_count', 3);

            // 获取待处理订单
            $orders = PurchaseOrder::where('status', PurchaseOrder::STATUS_PENDING)
                ->where('retry_count', '<', $maxRetryCount)
                ->order('create_time', 'asc')
                ->limit($batchSize)
                ->select()
                ->toArray();

            if (empty($orders)) {
                Log::info('没有待处理的订单');
                return;
            }

            Log::info('找到 ' . count($orders) . ' 个待处理订单');

            $successCount = 0;
            $failCount = 0;

            foreach ($orders as $order) {
                try {
                    // 更新订单状态为处理中
                    PurchaseOrder::updateOrderStatus($order['id'], PurchaseOrder::STATUS_PROCESSING);

                    // 发送物品到游戏
                    $result = $this->deliveryService->deliverItemToGame(
                        $order['user_id'],
                        $order['item_id'],
                        $order['quantity']
                    );

                    if ($result['success']) {
                        // 更新订单状态为已完成
                        PurchaseOrder::updateOrderStatus(
                            $order['id'],
                            PurchaseOrder::STATUS_COMPLETED,
                            [
                                'unique_num' => $result['unique_num'],
                                'delivery_time' => date('Y-m-d H:i:s')
                            ]
                        );
                        $successCount++;
                        Log::info('订单 ' . $order['order_no'] . ' 发送成功');
                    } else {
                        // 增加重试次数
                        PurchaseOrder::incrementRetryCount($order['id']);
                        
                        // 更新订单状态为失败
                        PurchaseOrder::updateOrderStatus(
                            $order['id'],
                            PurchaseOrder::STATUS_FAILED,
                            ['error_message' => $result['message']]
                        );
                        $failCount++;
                        Log::error('订单 ' . $order['order_no'] . ' 发送失败: ' . $result['message']);
                    }

                } catch (Exception $e) {
                    // 增加重试次数
                    PurchaseOrder::incrementRetryCount($order['id']);
                    
                    // 更新订单状态为失败
                    PurchaseOrder::updateOrderStatus(
                        $order['id'],
                        PurchaseOrder::STATUS_FAILED,
                        ['error_message' => $e->getMessage()]
                    );
                    $failCount++;
                    Log::error('订单 ' . $order['order_no'] . ' 处理异常: ' . $e->getMessage());
                }

                // 短暂延迟，避免过于频繁的请求
                usleep(100000); // 0.1秒
            }

            $endTime = microtime(true);
            $executionTime = round($endTime - $startTime, 2);

            Log::info('处理完成: 成功 ' . $successCount . ' 个，失败 ' . $failCount . ' 个，耗时 ' . $executionTime . ' 秒');

        } catch (Exception $e) {
            Log::error('处理待发送订单异常: ' . $e->getMessage());
        }
    }

    /**
     * 重试失败订单（每5分钟执行）
     * 
     * @return void
     */
    public function retryFailedOrders(): void
    {
        try {
            $startTime = microtime(true);
            Log::info('开始重试失败订单');

            // 获取配置
            $batchSize = config('purchase.delivery.batch_size', 20);
            $maxRetryCount = config('purchase.delivery.max_retry_count', 3);
            $retryInterval = config('purchase.delivery.retry_interval', 30);

            // 获取需要重试的失败订单
            $retryTime = date('Y-m-d H:i:s', time() - $retryInterval);
            $orders = PurchaseOrder::where('status', PurchaseOrder::STATUS_FAILED)
                ->where('retry_count', '<', $maxRetryCount)
                ->where('update_time', '<', $retryTime)
                ->order('create_time', 'asc')
                ->limit($batchSize)
                ->select()
                ->toArray();

            if (empty($orders)) {
                Log::info('没有需要重试的失败订单');
                return;
            }

            Log::info('找到 ' . count($orders) . ' 个需要重试的订单');

            $successCount = 0;
            $failCount = 0;

            foreach ($orders as $order) {
                try {
                    // 增加重试次数
                    PurchaseOrder::incrementRetryCount($order['id']);

                    // 更新订单状态为处理中
                    PurchaseOrder::updateOrderStatus($order['id'], PurchaseOrder::STATUS_PROCESSING);

                    // 重新发送物品
                    $result = $this->deliveryService->deliverItemToGame(
                        $order['user_id'],
                        $order['item_id'],
                        $order['quantity']
                    );

                    if ($result['success']) {
                        PurchaseOrder::updateOrderStatus(
                            $order['id'],
                            PurchaseOrder::STATUS_COMPLETED,
                            [
                                'unique_num' => $result['unique_num'],
                                'delivery_time' => date('Y-m-d H:i:s'),
                                'error_message' => null
                            ]
                        );
                        $successCount++;
                        Log::info('订单 ' . $order['order_no'] . ' 重试成功');
                    } else {
                        PurchaseOrder::updateOrderStatus(
                            $order['id'],
                            PurchaseOrder::STATUS_FAILED,
                            ['error_message' => $result['message']]
                        );
                        $failCount++;
                        Log::error('订单 ' . $order['order_no'] . ' 重试失败: ' . $result['message']);
                    }

                } catch (Exception $e) {
                    PurchaseOrder::updateOrderStatus(
                        $order['id'],
                        PurchaseOrder::STATUS_FAILED,
                        ['error_message' => $e->getMessage()]
                    );
                    $failCount++;
                    Log::error('订单 ' . $order['order_no'] . ' 重试异常: ' . $e->getMessage());
                }

                // 延迟更长时间，避免重试过于频繁
                usleep(200000); // 0.2秒
            }

            $endTime = microtime(true);
            $executionTime = round($endTime - $startTime, 2);

            Log::info('重试完成: 成功 ' . $successCount . ' 个，失败 ' . $failCount . ' 个，耗时 ' . $executionTime . ' 秒');

        } catch (Exception $e) {
            Log::error('重试失败订单异常: ' . $e->getMessage());
        }
    }

    /**
     * 清理超时订单（每小时执行）
     * 
     * @return void
     */
    public function cleanupTimeoutOrders(): void
    {
        try {
            Log::info('开始清理超时订单');

            $timeoutMinutes = config('purchase.order.timeout_minutes', 30);
            $timeoutTime = date('Y-m-d H:i:s', time() - $timeoutMinutes * 60);

            // 清理超时的处理中订单
            $timeoutOrders = PurchaseOrder::where('status', PurchaseOrder::STATUS_PROCESSING)
                ->where('update_time', '<', $timeoutTime)
                ->select()
                ->toArray();

            if (empty($timeoutOrders)) {
                Log::info('没有超时的订单');
                return;
            }

            Log::info('找到 ' . count($timeoutOrders) . ' 个超时订单');

            foreach ($timeoutOrders as $order) {
                // 将超时订单标记为失败，等待重试
                PurchaseOrder::updateOrderStatus(
                    $order['id'],
                    PurchaseOrder::STATUS_FAILED,
                    ['error_message' => '订单处理超时']
                );
                Log::warning('订单 ' . $order['order_no'] . ' 处理超时，已标记为失败');
            }

            Log::info('超时订单清理完成');

        } catch (Exception $e) {
            Log::error('清理超时订单异常: ' . $e->getMessage());
        }
    }

    /**
     * 生成统计报告（每天执行）
     * 
     * @return void
     */
    public function generateDailyReport(): void
    {
        try {
            Log::info('开始生成每日统计报告');

            $yesterday = date('Y-m-d', strtotime('-1 day'));
            $startTime = $yesterday . ' 00:00:00';
            $endTime = $yesterday . ' 23:59:59';

            // 统计昨日数据
            $totalOrders = PurchaseOrder::whereTime('create_time', 'between', [$startTime, $endTime])->count();
            $completedOrders = PurchaseOrder::where('status', PurchaseOrder::STATUS_COMPLETED)
                ->whereTime('create_time', 'between', [$startTime, $endTime])
                ->count();
            $failedOrders = PurchaseOrder::where('status', PurchaseOrder::STATUS_FAILED)
                ->whereTime('create_time', 'between', [$startTime, $endTime])
                ->count();

            $successRate = $totalOrders > 0 ? round($completedOrders / $totalOrders * 100, 2) : 0;

            // 按货币类型统计
            $currencyStats = PurchaseOrder::where('status', PurchaseOrder::STATUS_COMPLETED)
                ->whereTime('create_time', 'between', [$startTime, $endTime])
                ->field('currency_type, sum(total_price) as total_amount, count(*) as order_count')
                ->group('currency_type')
                ->select()
                ->toArray();

            // 生成报告
            $report = [
                'date' => $yesterday,
                'total_orders' => $totalOrders,
                'completed_orders' => $completedOrders,
                'failed_orders' => $failedOrders,
                'success_rate' => $successRate . '%',
                'currency_stats' => $currencyStats
            ];

            Log::info('每日统计报告: ' . json_encode($report, JSON_UNESCAPED_UNICODE));

            // 如果启用了通知，可以在这里发送报告
            if (config('purchase.notification.enable_email', false)) {
                $this->sendDailyReportEmail($report);
            }

        } catch (Exception $e) {
            Log::error('生成每日统计报告异常: ' . $e->getMessage());
        }
    }

    /**
     * 清理旧数据（每周执行）
     * 
     * @return void
     */
    public function cleanupOldData(): void
    {
        try {
            Log::info('开始清理旧数据');

            $retentionDays = config('purchase.log.log_retention_days', 30);
            $cutoffDate = date('Y-m-d H:i:s', strtotime('-' . $retentionDays . ' days'));

            // 清理已完成的旧订单
            $deletedOrders = PurchaseOrder::where('status', PurchaseOrder::STATUS_COMPLETED)
                ->where('create_time', '<', $cutoffDate)
                ->delete();

            // 清理发送日志
            $deletedLogs = Db::name('item_delivery_logs')
                ->where('create_time', '<', $cutoffDate)
                ->delete();

            // 清理余额变动记录
            $deletedBalanceLogs = Db::name('user_balance_logs')
                ->where('create_time', '<', $cutoffDate)
                ->delete();

            Log::info('数据清理完成: 订单 ' . $deletedOrders . ' 个，日志 ' . $deletedLogs . ' 条，余额记录 ' . $deletedBalanceLogs . ' 条');

        } catch (Exception $e) {
            Log::error('清理旧数据异常: ' . $e->getMessage());
        }
    }

    /**
     * 健康检查（每10分钟执行）
     * 
     * @return void
     */
    public function healthCheck(): void
    {
        try {
            // 检查数据库连接
            $localDbStatus = $this->checkDatabaseConnection('default');
            $remoteDbStatus = $this->checkDatabaseConnection('remote');

            // 检查待处理订单数量
            $pendingCount = PurchaseOrder::where('status', PurchaseOrder::STATUS_PENDING)->count();
            $processingCount = PurchaseOrder::where('status', PurchaseOrder::STATUS_PROCESSING)->count();

            // 检查失败订单数量
            $failedCount = PurchaseOrder::where('status', PurchaseOrder::STATUS_FAILED)
                ->where('retry_count', '>=', config('purchase.delivery.max_retry_count', 3))
                ->count();

            $healthStatus = [
                'timestamp' => date('Y-m-d H:i:s'),
                'local_db' => $localDbStatus ? 'OK' : 'ERROR',
                'remote_db' => $remoteDbStatus ? 'OK' : 'ERROR',
                'pending_orders' => $pendingCount,
                'processing_orders' => $processingCount,
                'failed_orders' => $failedCount
            ];

            // 记录健康状态
            Log::info('系统健康检查: ' . json_encode($healthStatus, JSON_UNESCAPED_UNICODE));

            // 如果有异常情况，发送警告
            if (!$localDbStatus || !$remoteDbStatus || $failedCount > 10) {
                Log::warning('系统健康检查发现异常: ' . json_encode($healthStatus, JSON_UNESCAPED_UNICODE));
            }

        } catch (Exception $e) {
            Log::error('健康检查异常: ' . $e->getMessage());
        }
    }

    /**
     * 检查数据库连接
     * 
     * @param string $connection 连接名称
     * @return bool 连接状态
     */
    private function checkDatabaseConnection(string $connection): bool
    {
        try {
            Db::connect($connection)->query('SELECT 1');
            return true;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * 发送每日报告邮件
     * 
     * @param array $report 报告数据
     * @return void
     */
    private function sendDailyReportEmail(array $report): void
    {
        try {
            // 这里可以实现邮件发送逻辑
            // 暂时只记录日志
            Log::info('每日报告邮件发送: ' . json_encode($report, JSON_UNESCAPED_UNICODE));
        } catch (Exception $e) {
            Log::error('发送每日报告邮件失败: ' . $e->getMessage());
        }
    }
}