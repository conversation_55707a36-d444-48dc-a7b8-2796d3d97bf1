<?php

namespace app\cron;

use think\facade\Db;
use think\facade\Log;
use think\facade\Config;
use Exception;

/**
 * 充值订单定时任务
 * 负责处理充值订单的超时取消等定时任务
 * 
 * 设计原则：
 * 1. 单一职责原则：专门处理充值订单相关的定时任务
 * 2. 可靠性原则：确保超时订单能够及时被取消
 * 3. 性能原则：批量处理，避免频繁的数据库操作
 */
class RechargeOrderCron
{
    /**
     * 订单状态常量
     */
    const STATUS_PENDING = 0;    // 待支付
    const STATUS_PAID = 1;       // 已支付
    const STATUS_FAILED = 2;     // 已失败
    const STATUS_CANCELLED = 3;  // 已取消

    /**
     * 清理超时的充值订单（每5分钟执行）
     * 
     * @return void
     */
    public function cleanupTimeoutOrders(): void
    {
        try {
            $startTime = microtime(true);
            Log::info('开始清理超时充值订单');

            // 获取订单超时时间配置（分钟）
            $timeoutMinutes = Config::get('purchase.order.timeout_minutes', 30);
            $timeoutTime = date('Y-m-d H:i:s', time() - $timeoutMinutes * 60);

            // 查找超时的待支付订单
            $timeoutOrders = Db::table('recharge_orders')
                ->where('status', self::STATUS_PENDING)
                ->where('create_time', '<', $timeoutTime)
                ->field('id,out_trade_no,user_id,currency_type,money,recharge_amount,create_time')
                ->select()
                ->toArray();

            if (empty($timeoutOrders)) {
                Log::info('没有超时的充值订单');
                return;
            }

            Log::info('找到 ' . count($timeoutOrders) . ' 个超时充值订单');

            $cancelledCount = 0;
            $failedCount = 0;

            foreach ($timeoutOrders as $order) {
                try {
                    // 开启事务
                    Db::startTrans();

                    // 更新订单状态为已取消
                    $updateResult = Db::table('recharge_orders')
                        ->where('id', $order['id'])
                        ->where('status', self::STATUS_PENDING) // 再次确认状态，避免并发问题
                        ->update([
                            'status' => self::STATUS_CANCELLED,
                            'remark' => '订单超时自动取消',
                            'update_time' => date('Y-m-d H:i:s')
                        ]);

                    if ($updateResult > 0) {
                        // 记录取消日志
                        $this->logOrderCancellation($order, '订单超时自动取消');
                        
                        $cancelledCount++;
                        Log::info('充值订单 ' . $order['out_trade_no'] . ' 超时自动取消');
                    }

                    // 提交事务
                    Db::commit();

                } catch (Exception $e) {
                    // 回滚事务
                    Db::rollback();
                    $failedCount++;
                    Log::error('取消超时充值订单失败: ' . $order['out_trade_no'] . ', 错误: ' . $e->getMessage());
                }

                // 短暂延迟，避免过于频繁的数据库操作
                usleep(50000); // 0.05秒
            }

            $endTime = microtime(true);
            $executionTime = round($endTime - $startTime, 2);

            Log::info('超时充值订单清理完成: 成功取消 ' . $cancelledCount . ' 个，失败 ' . $failedCount . ' 个，耗时 ' . $executionTime . ' 秒');

        } catch (Exception $e) {
            Log::error('清理超时充值订单异常: ' . $e->getMessage());
        }
    }

    /**
     * 检查待支付订单状态（每10分钟执行）
     * 主动查询支付平台，更新订单状态
     * 
     * @return void
     */
    public function checkPendingOrderStatus(): void
    {
        try {
            Log::info('开始检查待支付充值订单状态');

            // 获取最近2小时内的待支付订单
            $checkTime = date('Y-m-d H:i:s', time() - 2 * 3600);
            
            $pendingOrders = Db::table('recharge_orders')
                ->where('status', self::STATUS_PENDING)
                ->where('create_time', '>', $checkTime)
                ->field('id,out_trade_no,trade_no,user_id,money')
                ->limit(50) // 限制每次检查的数量
                ->select()
                ->toArray();

            if (empty($pendingOrders)) {
                Log::info('没有需要检查的待支付订单');
                return;
            }

            Log::info('找到 ' . count($pendingOrders) . ' 个待支付订单需要检查');

            $checkedCount = 0;
            $updatedCount = 0;

            foreach ($pendingOrders as $order) {
                try {
                    // 这里可以调用支付平台的查询接口
                    // 由于支付平台接口限制，暂时跳过主动查询
                    // 实际项目中可以根据需要实现
                    
                    $checkedCount++;
                    
                    // 短暂延迟，避免过于频繁的API调用
                    usleep(100000); // 0.1秒

                } catch (Exception $e) {
                    Log::error('检查订单状态失败: ' . $order['out_trade_no'] . ', 错误: ' . $e->getMessage());
                }
            }

            Log::info('订单状态检查完成: 检查 ' . $checkedCount . ' 个，更新 ' . $updatedCount . ' 个');

        } catch (Exception $e) {
            Log::error('检查待支付订单状态异常: ' . $e->getMessage());
        }
    }

    /**
     * 生成充值统计报告（每天执行）
     * 
     * @return void
     */
    public function generateDailyReport(): void
    {
        try {
            Log::info('开始生成充值每日统计报告');

            $yesterday = date('Y-m-d', strtotime('-1 day'));
            $startTime = $yesterday . ' 00:00:00';
            $endTime = $yesterday . ' 23:59:59';

            // 统计昨日充值数据
            $totalOrders = Db::table('recharge_orders')
                ->whereTime('create_time', 'between', [$startTime, $endTime])
                ->count();

            $paidOrders = Db::table('recharge_orders')
                ->where('status', self::STATUS_PAID)
                ->whereTime('create_time', 'between', [$startTime, $endTime])
                ->count();

            $cancelledOrders = Db::table('recharge_orders')
                ->where('status', self::STATUS_CANCELLED)
                ->whereTime('create_time', 'between', [$startTime, $endTime])
                ->count();

            $totalAmount = Db::table('recharge_orders')
                ->where('status', self::STATUS_PAID)
                ->whereTime('create_time', 'between', [$startTime, $endTime])
                ->sum('money');

            $successRate = $totalOrders > 0 ? round($paidOrders / $totalOrders * 100, 2) : 0;

            Log::info("充值日报 [{$yesterday}]: 总订单 {$totalOrders} 个，成功 {$paidOrders} 个，取消 {$cancelledOrders} 个，成功率 {$successRate}%，总金额 {$totalAmount} 元");

        } catch (Exception $e) {
            Log::error('生成充值每日统计报告异常: ' . $e->getMessage());
        }
    }

    /**
     * 清理旧的充值订单数据（每周执行）
     * 
     * @return void
     */
    public function cleanupOldOrders(): void
    {
        try {
            Log::info('开始清理旧的充值订单数据');

            // 清理90天前的已取消订单
            $cleanupTime = date('Y-m-d H:i:s', time() - 90 * 24 * 3600);
            
            $deletedCount = Db::table('recharge_orders')
                ->where('status', self::STATUS_CANCELLED)
                ->where('create_time', '<', $cleanupTime)
                ->delete();

            Log::info('清理旧充值订单完成: 删除 ' . $deletedCount . ' 条记录');

        } catch (Exception $e) {
            Log::error('清理旧充值订单数据异常: ' . $e->getMessage());
        }
    }

    /**
     * 记录订单取消日志
     * 
     * @param array $order 订单信息
     * @param string $reason 取消原因
     * @return void
     */
    private function logOrderCancellation(array $order, string $reason): void
    {
        try {
            Db::table('recharge_logs')->insert([
                'username' => $order['user_id'],
                'currency_type' => $order['currency_type'],
                'operation' => 'cancel',
                'amount' => $order['recharge_amount'],
                'old_balance' => 0,
                'new_balance' => 0,
                'order_no' => $order['out_trade_no'],
                'pay_amount' => $order['money'],
                'reason' => $reason,
                'admin_user' => 'system', // 系统自动操作
                'remark' => '系统自动取消超时订单',
                'ip_address' => '127.0.0.1',
                'create_time' => date('Y-m-d H:i:s')
            ]);
        } catch (Exception $e) {
            Log::error('记录订单取消日志失败: ' . $e->getMessage());
        }
    }

    /**
     * 健康检查（每小时执行）
     * 
     * @return void
     */
    public function healthCheck(): void
    {
        try {
            // 检查待支付订单数量
            $pendingCount = Db::table('recharge_orders')
                ->where('status', self::STATUS_PENDING)
                ->count();

            // 检查今日成功订单数量
            $todayPaidCount = Db::table('recharge_orders')
                ->where('status', self::STATUS_PAID)
                ->whereTime('create_time', 'today')
                ->count();

            // 检查超时订单数量
            $timeoutMinutes = Config::get('purchase.order.timeout_minutes', 30);
            $timeoutTime = date('Y-m-d H:i:s', time() - $timeoutMinutes * 60);
            
            $timeoutCount = Db::table('recharge_orders')
                ->where('status', self::STATUS_PENDING)
                ->where('create_time', '<', $timeoutTime)
                ->count();

            if ($timeoutCount > 10) {
                Log::warning("充值订单健康检查: 发现 {$timeoutCount} 个超时订单，需要关注");
            }

            Log::info("充值订单健康检查: 待支付 {$pendingCount} 个，今日成功 {$todayPaidCount} 个，超时 {$timeoutCount} 个");

        } catch (Exception $e) {
            Log::error('充值订单健康检查异常: ' . $e->getMessage());
        }
    }
}
