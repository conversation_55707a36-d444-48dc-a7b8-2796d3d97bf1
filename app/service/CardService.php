<?php
declare(strict_types=1);

namespace app\service;

use think\facade\Db;
use think\facade\Log;
use app\service\RechargeService;

/**
 * 卡片系统服务类
 * 处理周卡、月卡的购买、领取等功能
 */
class CardService
{
    private $rechargeService;

    public function __construct()
    {
        $this->rechargeService = new RechargeService();
    }

    /**
     * 获取所有可用的卡片配置
     * 
     * @return array
     */
    public function getAvailableCards(): array
    {
        try {
            $cards = Db::table('card_config')
                ->where('status', 1)
                ->order('sort_order', 'asc')
                ->select()
                ->toArray();

            foreach ($cards as &$card) {
                // 解析奖励配置
                $card['instant_reward_parsed'] = json_decode($card['instant_reward'], true) ?: [];
                $card['daily_reward_parsed'] = json_decode($card['daily_reward'], true) ?: [];

                // 格式化奖励描述（保持向后兼容）
                $card['instant_reward_desc'] = $this->formatRewardDescription($card['instant_reward_parsed']);
                $card['daily_reward_desc'] = $this->formatRewardDescription($card['daily_reward_parsed']);

                // 添加详细的奖励信息（包含图标）
                $card['instant_reward_details'] = $this->formatRewardDetails($card['instant_reward_parsed']);
                $card['daily_reward_details'] = $this->formatRewardDetails($card['daily_reward_parsed']);
            }

            return $cards;
        } catch (\Exception $e) {
            Log::error('获取卡片配置失败: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 获取用户的卡片状态
     * 
     * @param string $userId 用户ID
     * @return array
     */
    public function getUserCardStatus(string $userId): array
    {
        try {
            $userCards = Db::table('user_cards')
                ->alias('uc')
                ->join('card_config cc', 'uc.card_id = cc.id')
                ->where('uc.user_id', $userId)
                ->where('uc.status', 1)
                ->where('uc.expire_time', '>', date('Y-m-d H:i:s'))
                ->field('uc.*, cc.name, cc.card_type, cc.daily_reward')
                ->select()
                ->toArray();

            $status = [];
            foreach ($userCards as $card) {
                $today = date('Y-m-d');
                $canClaimToday = $card['last_claim_date'] !== $today;
                
                $status[$card['card_type']] = [
                    'id' => $card['id'],
                    'card_id' => $card['card_id'],
                    'name' => $card['name'],
                    'expire_time' => $card['expire_time'],
                    'total_days' => $card['total_days'],
                    'claimed_days' => $card['claimed_days'],
                    'remaining_days' => $card['total_days'] - $card['claimed_days'],
                    'can_claim_today' => $canClaimToday,
                    'last_claim_date' => $card['last_claim_date'],
                    'daily_reward' => json_decode($card['daily_reward'], true) ?: []
                ];
            }

            return $status;
        } catch (\Exception $e) {
            Log::error('获取用户卡片状态失败: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 购买卡片
     *
     * @param string $userId 用户ID
     * @param int $cardId 卡片ID
     * @param bool $skipDuplicateCheck 是否跳过重复购买检查（支付回调时使用）
     * @return array
     */
    public function purchaseCard(string $userId, int $cardId, bool $skipDuplicateCheck = false): array
    {
        try {
            // 获取卡片配置
            $card = Db::table('card_config')->where('id', $cardId)->where('status', 1)->find();
            if (!$card) {
                return ['success' => false, 'message' => '卡片不存在或已下架'];
            }

            // 检查用户是否已有同类型的有效卡片（除非跳过检查）
            if (!$skipDuplicateCheck) {
                $existingCard = Db::table('user_cards')
                    ->where('user_id', $userId)
                    ->where('card_type', $card['card_type'])
                    ->where('status', 1)
                    ->where('expire_time', '>', date('Y-m-d H:i:s'))
                    ->find();

                if ($existingCard) {
                    return ['success' => false, 'message' => '您已拥有有效的' . $card['name'] . '，无需重复购买'];
                }
            }

            // 开始事务
            Db::startTrans();

            try {
                // 创建用户卡片记录
                $expireTime = date('Y-m-d H:i:s', strtotime('+' . $card['duration_days'] . ' days'));
                $userCardId = Db::table('user_cards')->insertGetId([
                    'user_id' => $userId,
                    'card_id' => $cardId,
                    'card_type' => $card['card_type'],
                    'expire_time' => $expireTime,
                    'total_days' => $card['duration_days'],
                    'claimed_days' => 0,
                    'status' => 1,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);

                // 发放即时奖励
                $instantRewards = json_decode($card['instant_reward'], true) ?: [];
                if (!empty($instantRewards)) {
                    $rewardResult = $this->grantRewards($userId, $instantRewards, '购买' . $card['name'] . '即时奖励');
                    if (!$rewardResult['success']) {
                        throw new \Exception('发放即时奖励失败: ' . $rewardResult['message']);
                    }

                    // 记录即时奖励领取
                    Db::table('card_claim_logs')->insert([
                        'user_id' => $userId,
                        'user_card_id' => $userCardId,
                        'claim_type' => 'instant',
                        'claim_date' => date('Y-m-d'),
                        'reward_data' => $card['instant_reward'],
                        'claim_time' => date('Y-m-d H:i:s')
                    ]);

                    // 标记即时奖励已领取
                    Db::table('user_cards')
                        ->where('id', $userCardId)
                        ->update(['instant_reward_claimed' => 1]);
                }

                Db::commit();

                return [
                    'success' => true, 
                    'message' => $card['name'] . '购买成功！即时奖励已发放',
                    'instant_rewards' => $instantRewards,
                    'user_card_id' => $userCardId
                ];

            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            Log::error('购买卡片失败: ' . $e->getMessage());
            return ['success' => false, 'message' => '购买失败: ' . $e->getMessage()];
        }
    }

    /**
     * 领取每日奖励
     * 
     * @param string $userId 用户ID
     * @param string $cardType 卡片类型
     * @return array
     */
    public function claimDailyReward(string $userId, string $cardType): array
    {
        try {
            // 获取用户的有效卡片
            $userCard = Db::table('user_cards')
                ->alias('uc')
                ->join('card_config cc', 'uc.card_id = cc.id')
                ->where('uc.user_id', $userId)
                ->where('uc.card_type', $cardType)
                ->where('uc.status', 1)
                ->where('uc.expire_time', '>', date('Y-m-d H:i:s'))
                ->field('uc.*, cc.daily_reward, cc.name')
                ->find();

            if (!$userCard) {
                return ['success' => false, 'message' => '您没有有效的' . $cardType . '卡片'];
            }

            $today = date('Y-m-d');
            
            // 检查今天是否已经领取
            if ($userCard['last_claim_date'] === $today) {
                return ['success' => false, 'message' => '今日奖励已领取，请明天再来'];
            }

            // 检查是否还有剩余天数
            if ($userCard['claimed_days'] >= $userCard['total_days']) {
                return ['success' => false, 'message' => '卡片奖励已全部领取完毕'];
            }

            // 开始事务
            Db::startTrans();

            try {
                // 发放每日奖励
                $dailyRewards = json_decode($userCard['daily_reward'], true) ?: [];
                if (!empty($dailyRewards)) {
                    $rewardResult = $this->grantRewards($userId, $dailyRewards, $userCard['name'] . '每日奖励');
                    if (!$rewardResult['success']) {
                        throw new \Exception('发放每日奖励失败: ' . $rewardResult['message']);
                    }
                }

                // 更新用户卡片记录
                Db::table('user_cards')
                    ->where('id', $userCard['id'])
                    ->update([
                        'claimed_days' => $userCard['claimed_days'] + 1,
                        'last_claim_date' => $today,
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);

                // 记录领取日志
                Db::table('card_claim_logs')->insert([
                    'user_id' => $userId,
                    'user_card_id' => $userCard['id'],
                    'claim_type' => 'daily',
                    'claim_date' => $today,
                    'reward_data' => $userCard['daily_reward'],
                    'claim_time' => date('Y-m-d H:i:s')
                ]);

                Db::commit();

                return [
                    'success' => true, 
                    'message' => '每日奖励领取成功！',
                    'rewards' => $dailyRewards,
                    'remaining_days' => $userCard['total_days'] - ($userCard['claimed_days'] + 1)
                ];

            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            Log::error('领取每日奖励失败: ' . $e->getMessage());
            return ['success' => false, 'message' => '领取失败: ' . $e->getMessage()];
        }
    }

    /**
     * 发放奖励
     *
     * @param string $userId 用户ID
     * @param array $rewards 奖励配置
     * @param string $reason 发放原因
     * @return array
     */
    public function grantRewards(string $userId, array $rewards, string $reason): array
    {
        try {
            foreach ($rewards as $reward) {
                if ($reward['type'] === 'currency') {
                    // 发放货币奖励（卡片奖励不赠送积分）
                    $result = $this->grantCurrencyReward($userId, $reward['currency_type'], $reward['amount'], $reason);

                    if (!$result['success']) {
                        return ['success' => false, 'message' => '发放货币奖励失败: ' . $result['message']];
                    }
                } elseif ($reward['type'] === 'item') {
                    // 发放道具奖励
                    $itemResult = $this->sendItem($userId, $reward['item_id'], $reward['number'], $reason);
                    if (!$itemResult['success']) {
                        return ['success' => false, 'message' => '发放道具奖励失败: ' . $itemResult['message']];
                    }
                }
            }

            return ['success' => true, 'message' => '奖励发放成功'];
        } catch (\Exception $e) {
            Log::error('发放奖励失败: ' . $e->getMessage());
            return ['success' => false, 'message' => '发放奖励失败: ' . $e->getMessage()];
        }
    }

    /**
     * 发放货币奖励（不赠送积分）
     *
     * @param string $userId 用户ID
     * @param string $currencyType 货币类型
     * @param int $amount 金额
     * @param string $reason 原因
     * @return array
     */
    private function grantCurrencyReward(string $userId, string $currencyType, int $amount, string $reason): array
    {
        try {
            switch ($currencyType) {
                case 'c_coin':
                    return $this->addCCoinBalance($userId, $amount, $reason);
                case 'point':
                    return $this->addPointBalance($userId, $amount, $reason);
                case 'score':
                    return $this->addScoreBalance($userId, $amount, $reason);
                default:
                    return ['success' => false, 'message' => '不支持的货币类型: ' . $currencyType];
            }
        } catch (\Exception $e) {
            Log::error('发放货币奖励失败: ' . $e->getMessage());
            return ['success' => false, 'message' => '发放货币奖励失败: ' . $e->getMessage()];
        }
    }

    /**
     * 增加C币余额
     *
     * @param string $userId 用户ID
     * @param int $amount 金额
     * @param string $reason 原因
     * @return array
     */
    private function addCCoinBalance(string $userId, int $amount, string $reason): array
    {
        try {
            $numericUserId = is_numeric($userId) ? (int)$userId : (int)preg_replace('/[^0-9]/', '', $userId);

            // 获取当前余额
            $currentBalance = Db::table('c_coin')
                ->where('user_id', $numericUserId)
                ->value('balance');

            if ($currentBalance === null) {
                // 创建新记录
                $result = Db::table('c_coin')->insert([
                    'user_id' => $numericUserId,
                    'balance' => $amount,
                    'game_account' => $userId,
                    'nickname' => $userId
                ]);
                $newBalance = $amount;
            } else {
                // 更新现有记录
                $newBalance = $currentBalance + $amount;
                $result = Db::table('c_coin')
                    ->where('user_id', $numericUserId)
                    ->update(['balance' => $newBalance]);
            }

            if ($result) {
                // 清除用户余额缓存
                $this->clearUserBalanceCache($userId);

                Log::info("C币发放成功 - 用户: {$userId}, 发放数量: {$amount}, 原余额: " . ($currentBalance ?: 0) . ", 新余额: {$newBalance}, 原因: {$reason}");
                return ['success' => true, 'message' => 'C币发放成功'];
            } else {
                return ['success' => false, 'message' => 'C币发放失败'];
            }
        } catch (\Exception $e) {
            Log::error('C币发放失败: ' . $e->getMessage());
            return ['success' => false, 'message' => 'C币发放失败: ' . $e->getMessage()];
        }
    }

    /**
     * 增加泡点余额
     *
     * @param string $userId 用户ID
     * @param int $amount 金额
     * @param string $reason 原因
     * @return array
     */
    private function addPointBalance(string $userId, int $amount, string $reason): array
    {
        try {
            // 找到用户所在的表并更新
            for ($i = 1; $i <= 5; $i++) {
                $user = Db::connect('seal_member')
                    ->table('idtable' . $i)
                    ->where('id', $userId)
                    ->find();

                if ($user) {
                    $currentBalance = $user['point'] ?: 0;
                    $newBalance = $currentBalance + $amount;

                    $result = Db::connect('seal_member')
                        ->table('idtable' . $i)
                        ->where('id', $userId)
                        ->update(['point' => $newBalance]);

                    if ($result) {
                        // 清除用户余额缓存
                        $this->clearUserBalanceCache($userId);

                        Log::info("泡点发放成功 - 用户: {$userId}, 发放数量: {$amount}, 原余额: {$currentBalance}, 新余额: {$newBalance}, 原因: {$reason}");
                        return ['success' => true, 'message' => '泡点发放成功'];
                    } else {
                        return ['success' => false, 'message' => '泡点发放失败'];
                    }
                }
            }

            return ['success' => false, 'message' => '用户不存在'];
        } catch (\Exception $e) {
            Log::error('泡点发放失败: ' . $e->getMessage());
            return ['success' => false, 'message' => '泡点发放失败: ' . $e->getMessage()];
        }
    }

    /**
     * 增加积分余额
     *
     * @param string $userId 用户ID
     * @param int $amount 金额
     * @param string $reason 原因
     * @return array
     */
    private function addScoreBalance(string $userId, int $amount, string $reason): array
    {
        try {
            $currentBalance = Db::connect('seal_web')
                ->table('sealmember')
                ->where('id', $userId)
                ->value('hahapoint');

            $currentBalance = $currentBalance ?: 0;
            $newBalance = $currentBalance + $amount;

            $result = Db::connect('seal_web')
                ->table('sealmember')
                ->where('id', $userId)
                ->update(['hahapoint' => $newBalance]);

            if ($result) {
                // 清除用户余额缓存
                $this->clearUserBalanceCache($userId);

                Log::info("积分发放成功 - 用户: {$userId}, 发放数量: {$amount}, 原余额: {$currentBalance}, 新余额: {$newBalance}, 原因: {$reason}");
                return ['success' => true, 'message' => '积分发放成功'];
            } else {
                return ['success' => false, 'message' => '积分发放失败'];
            }
        } catch (\Exception $e) {
            Log::error('积分发放失败: ' . $e->getMessage());
            return ['success' => false, 'message' => '积分发放失败: ' . $e->getMessage()];
        }
    }

    /**
     * 发送道具
     *
     * @param string $userId 用户ID
     * @param int $itemId 道具ID
     * @param int $number 数量
     * @param string $reason 发送原因
     * @return array
     */
    private function sendItem(string $userId, int $itemId, int $number, string $reason): array
    {
        try {
            // 获取道具信息
            $itemInfo = \app\service\ItemService::getItemInfoFromDatabase($itemId);
            if (!$itemInfo || !isset($itemInfo['name'])) {
                return ['success' => false, 'message' => '道具不存在: ' . $itemId];
            }

            // 使用与管理员发送道具相同的逻辑
            $result = $this->sendItemToGame($userId, $itemId, $number, $reason);

            if ($result['success']) {
                Log::info("发送道具: 用户{$userId}, 道具{$itemId}, 数量{$number}, 原因{$reason}");
            }

            return $result;
        } catch (\Exception $e) {
            Log::error('发送道具失败: ' . $e->getMessage());
            return ['success' => false, 'message' => '发送道具失败: ' . $e->getMessage()];
        }
    }

    /**
     * 发送道具到游戏
     *
     * @param string $userId 用户ID
     * @param int $itemId 道具ID
     * @param int $number 数量
     * @param string $reason 发送原因
     * @return array
     */
    private function sendItemToGame(string $userId, int $itemId, int $number, string $reason): array
    {
        try {
            // 获取远程数据库配置
            $remoteConfig = [
                'type' => 'mysql',
                'hostname' => env('REMOTE_DB_HOST'),
                'hostport' => env('REMOTE_DB_PORT'),
                'database' => env('ITEM_DB_NAME', 'item'),
                'username' => env('REMOTE_DB_USER'),
                'password' => env('REMOTE_DB_PASS'),
                'charset'  => env('REMOTE_DB_CHARSET', 'utf8'),
            ];

            // 连接远程数据库
            $remoteDb = Db::connect($remoteConfig);

            // 将中文原因转换为英文标识
            $bxaid = $this->convertReasonToEnglish($reason);

            // 插入道具到seal_item表
            $insertData = [
                'ItemType' => $itemId,
                'ItemOp1' => $number, // 道具数量
                'ItemOp2' => 0, // 道具属性，默认为0
                'ItemLimit' => 0, // 时间限制，默认为0（永久）
                'OwnerID' => $userId,
                'OwnerDate' => date('Y-m-d H:i:s'),
                'bxaid' => $bxaid // 使用英文标识
            ];

            $uniqueNum = $remoteDb->table('seal_item')->insertGetId($insertData);

            if ($uniqueNum) {
                return [
                    'success' => true,
                    'unique_num' => $uniqueNum,
                    'message' => '道具发送成功'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => '插入道具记录失败'
                ];
            }
        } catch (\Exception $e) {
            Log::error('发送道具到游戏失败: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => '发送道具失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 格式化奖励描述
     *
     * @param array $rewards 奖励配置
     * @return string
     */
    private function formatRewardDescription(array $rewards): string
    {
        $descriptions = [];

        foreach ($rewards as $reward) {
            if ($reward['type'] === 'currency') {
                $currencyName = $this->getCurrencyName($reward['currency_type']);
                $descriptions[] = $reward['amount'] . $currencyName;
            } elseif ($reward['type'] === 'item') {
                $itemName = $this->getItemName($reward['item_id']);
                $descriptions[] = $itemName . ' x' . $reward['number'];
            }
        }

        return implode(' + ', $descriptions);
    }

    /**
     * 格式化奖励详细信息（包含图标）
     *
     * @param array $rewards 奖励配置
     * @return array
     */
    private function formatRewardDetails(array $rewards): array
    {
        $details = [];

        foreach ($rewards as $reward) {
            if ($reward['type'] === 'currency') {
                $currencyName = $this->getCurrencyName($reward['currency_type']);
                $details[] = [
                    'type' => 'currency',
                    'currency_type' => $reward['currency_type'],
                    'amount' => $reward['amount'],
                    'name' => $currencyName,
                    'icon' => $this->getCurrencyIcon($reward['currency_type']),
                    'display' => $reward['amount'] . $currencyName
                ];
            } elseif ($reward['type'] === 'item') {
                $itemInfo = \app\service\ItemService::getItemInfoFromDatabase($reward['item_id']);
                $details[] = [
                    'type' => 'item',
                    'item_id' => $reward['item_id'],
                    'number' => $reward['number'],
                    'name' => $itemInfo['name'],
                    'icon' => $itemInfo['icon'],
                    'explanation' => $itemInfo['explanation'],
                    'display' => $itemInfo['name'] . ' x' . $reward['number']
                ];
            }
        }

        return $details;
    }

    /**
     * 获取货币名称
     *
     * @param string $currencyType 货币类型
     * @return string
     */
    private function getCurrencyName(string $currencyType): string
    {
        $names = [
            'c_coin' => 'C币',
            'point' => '泡点',
            'score' => '积分'
        ];

        return $names[$currencyType] ?? $currencyType;
    }

    /**
     * 获取货币图标
     *
     * @param string $currencyType 货币类型
     * @return string
     */
    private function getCurrencyIcon(string $currencyType): string
    {
        $icons = [
            'c_coin' => 'bi-coin',
            'point' => 'bi-circle-fill',
            'score' => 'bi-star-fill'
        ];

        return $icons[$currencyType] ?? 'bi-currency-exchange';
    }

    /**
     * 将中文原因转换为英文标识
     *
     * @param string $reason 中文原因
     * @return string 英文标识
     */
    private function convertReasonToEnglish(string $reason): string
    {
        $reasonMap = [
            // 周卡相关
            '周卡即时奖励' => 'weekly_instant_reward',
            '周卡每日奖励' => 'weekly_daily_reward',
            '测试周卡即时奖励' => 'test_weekly_instant',
            '测试新周卡即时奖励' => 'test_weekly_instant',

            // 月卡相关
            '月卡即时奖励' => 'monthly_instant_reward',
            '月卡每日奖励' => 'monthly_daily_reward',
            '测试月卡每日奖励' => 'test_monthly_daily',

            // 管理员操作
            '管理员发送' => 'admin_send',

            // 其他测试
            '测试' => 'test',
        ];

        return $reasonMap[$reason] ?? 'card_reward';
    }

    /**
     * 获取道具名称
     *
     * @param int $itemId 道具ID
     * @return string
     */
    private function getItemName(int $itemId): string
    {
        try {
            // 使用ItemService获取道具信息
            $itemInfo = \app\service\ItemService::getItemInfoFromDatabase($itemId);
            return $itemInfo['name'] ?? "道具#{$itemId}";
        } catch (\Exception $e) {
            return "道具#{$itemId}";
        }
    }

    /**
     * 清除用户余额缓存
     *
     * @param string $userId 用户ID
     * @return void
     */
    private function clearUserBalanceCache(string $userId): void
    {
        try {
            // 清除用户余额缓存
            $cacheKey = 'user_balance:' . $userId;
            \app\service\CacheService::delete($cacheKey);

            // 清除泡点缓存
            $coinCacheKey = 'user_balance:coin:' . $userId;
            \app\service\CacheService::delete($coinCacheKey);

            Log::info("CardService: 已清除用户 {$userId} 的余额缓存");
        } catch (\Exception $e) {
            Log::warning("CardService: 清除用户余额缓存失败: " . $e->getMessage());
        }
    }
}
