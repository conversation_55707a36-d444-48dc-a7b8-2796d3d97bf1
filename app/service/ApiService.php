<?php

namespace app\service;

use think\facade\Log;

/**
 * API服务类
 * 统一管理所有外部API调用
 */
class ApiService
{
    /**
     * 获取API基础URL
     * @return string
     */
    public static function getBaseUrl(): string
    {
        $apiConfig = config('api');
        $serverConfig = $apiConfig['server'];
        $port = $serverConfig['port'] != '80' ? ':' . $serverConfig['port'] : '';
        return $serverConfig['protocol'] . '://' . $serverConfig['host'] . $port;
    }
    
    /**
     * 获取完整的API URL
     * @param string $endpoint 端点名称
     * @param array $params URL参数
     * @return string
     */
    public static function getApiUrl(string $endpoint, array $params = []): string
    {
        $apiConfig = config('api');
        $baseUrl = self::getBaseUrl();
        $endpointPath = $apiConfig['endpoints'][$endpoint] ?? '';
        
        $url = $baseUrl . $endpointPath;
        
        if (!empty($params)) {
            $url .= '?' . http_build_query($params);
        }
        
        return $url;
    }
    
    /**
     * 获取物品信息API URL
     * @param int $itemId 物品ID
     * @param array $fields 需要获取的字段
     * @return string
     */
    public static function getItemInfoUrl(int $itemId, array $fields = []): string
    {
        $params = ['item_id' => $itemId];
        if (!empty($fields)) {
            $params['fields'] = implode(',', $fields);
        }
        return self::getApiUrl('item_info', $params);
    }
    
    /**
     * 获取物品图标API URL
     * @return string
     */
    public static function getItemIconUrl(): string
    {
        return self::getApiUrl('item_icon');
    }
    
    /**
     * 获取静态资源基础URL
     * @return string
     */
    public static function getStaticBaseUrl(): string
    {
        $apiConfig = config('api');
        $staticConfig = $apiConfig['static'];
        $port = $staticConfig['port'] != '80' ? ':' . $staticConfig['port'] : '';
        return $staticConfig['protocol'] . '://' . $staticConfig['host'] . $port;
    }
    
    /**
     * 获取完整的静态资源URL
     * @param string $path 相对路径
     * @return string
     */
    public static function getStaticUrl(string $path): string
    {
        $apiConfig = config('api');
        $staticConfig = $apiConfig['static'];
        $baseUrl = self::getStaticBaseUrl();
        
        // 确保路径以/开头
        if (!str_starts_with($path, '/')) {
            $path = '/' . $path;
        }
        
        // 如果路径已经包含base_path，直接拼接
        if (str_starts_with($path, $staticConfig['base_path'])) {
            return $baseUrl . $path;
        }
        
        // 否则添加base_path
        return $baseUrl . $staticConfig['base_path'] . $path;
    }
    
    /**
     * 发送HTTP请求
     * @param string $url 请求URL
     * @param array $options 请求选项
     * @return array|false
     */
    public static function sendRequest(string $url, array $options = [])
    {
        $apiConfig = config('api');
        $requestConfig = $apiConfig['request'];
        
        try {
            $defaultOptions = [
                'http' => [
                    'method' => 'GET',
                    'timeout' => $requestConfig['timeout'],
                    'header' => 'Content-Type: application/json',
                ]
            ];
            
            $options = array_merge_recursive($defaultOptions, $options);
            $context = stream_context_create($options);
            
            $response = file_get_contents($url, false, $context);
            
            if ($response === false) {
                Log::error('API请求失败: ' . $url);
                return false;
            }
            
            $result = json_decode($response, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::error('API响应JSON解析失败: ' . $url . ' - ' . json_last_error_msg());
                return false;
            }
            
            return $result;
            
        } catch (\Exception $e) {
            Log::error('API请求异常: ' . $url . ' - ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 发送POST请求
     * @param string $url 请求URL
     * @param array $data 请求数据
     * @return array|false
     */
    public static function sendPostRequest(string $url, array $data = [])
    {
        $postData = json_encode($data);
        
        $options = [
            'http' => [
                'method' => 'POST',
                'content' => $postData,
                'header' => [
                    'Content-Type: application/json',
                    'Content-Length: ' . strlen($postData)
                ]
            ]
        ];
        
        return self::sendRequest($url, $options);
    }
    
    /**
     * 使用cURL发送POST请求（用于需要更多控制的场景）
     * @param string $url 请求URL
     * @param array $data 请求数据
     * @return array|false
     */
    public static function sendCurlPostRequest(string $url, array $data = [])
    {
        $apiConfig = config('api');
        $requestConfig = $apiConfig['request'];
        
        try {
            $postData = json_encode($data);
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Content-Length: ' . strlen($postData)
            ]);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, $requestConfig['timeout']);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $requestConfig['connect_timeout']);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($httpCode !== 200 || !$response) {
                Log::error('cURL请求失败: ' . $url . ' - HTTP Code: ' . $httpCode);
                return false;
            }
            
            $result = json_decode($response, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::error('cURL响应JSON解析失败: ' . $url . ' - ' . json_last_error_msg());
                return false;
            }
            
            return $result;
            
        } catch (\Exception $e) {
            Log::error('cURL请求异常: ' . $url . ' - ' . $e->getMessage());
            return false;
        }
    }
}