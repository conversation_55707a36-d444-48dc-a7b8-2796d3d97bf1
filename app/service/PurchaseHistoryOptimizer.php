<?php

namespace app\service;

use think\facade\Cache;
use think\facade\Log;
use think\facade\Db;

/**
 * 购买历史查询优化服务
 */
class PurchaseHistoryOptimizer
{
    /**
     * 缓存前缀
     */
    const CACHE_PREFIX = 'purchase_history_';
    
    /**
     * 默认缓存时间（秒）
     */
    const DEFAULT_CACHE_TIME = 300; // 5分钟
    
    /**
     * 批量获取购买历史（带缓存优化）
     * 
     * @param int $userId 用户ID
     * @param int $page 页码
     * @param int $limit 每页数量
     * @param array $filters 筛选条件
     * @return array
     */
    public static function getBatchPurchaseHistory($userId, $page = 1, $limit = 10, $filters = [])
    {
        // 生成缓存键
        $cacheKey = self::generateCacheKey($userId, $page, $limit, $filters);
        
        // 尝试从缓存获取
        $cached = Cache::get($cacheKey);
        if ($cached !== false) {
            Log::info("购买历史缓存命中: {$cacheKey}");
            return $cached;
        }
        
        // 缓存未命中，执行查询
        $startTime = microtime(true);
        
        try {
            $result = self::queryPurchaseHistoryOptimized($userId, $page, $limit, $filters);
            
            // 记录查询时间
            $queryTime = microtime(true) - $startTime;
            Log::info("购买历史查询耗时: {$queryTime}秒");
            
            // 缓存结果
            Cache::set($cacheKey, $result, self::DEFAULT_CACHE_TIME);
            
            return $result;
            
        } catch (\Exception $e) {
            Log::error("购买历史查询失败: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 优化的购买历史查询
     * 
     * @param int $userId
     * @param int $page
     * @param int $limit
     * @param array $filters
     * @return array
     */
    private static function queryPurchaseHistoryOptimized($userId, $page, $limit, $filters)
    {
        $query = Db::name('purchase_orders')
            ->where('user_id', $userId);
        
        // 应用筛选条件
        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
            $query->whereTime('create_time', 'between', [
                $filters['start_date'] . ' 00:00:00',
                $filters['end_date'] . ' 23:59:59'
            ]);
        }

        if (!empty($filters['currency_type'])) {
            // 将前端传递的字符串货币类型转换为数据库中的数字类型
            $currencyTypeMap = [
                'COIN' => '1',   // C币
                'POINT' => '2',  // 泡点
                'SCORE' => '3'   // 积分
            ];
            $dbCurrencyType = $currencyTypeMap[$filters['currency_type']] ?? $filters['currency_type'];
            $query->where('currency_type', $dbCurrencyType);
        }

        if (!empty($filters['item_name'])) {
            $query->where('item_name', 'like', '%' . $filters['item_name'] . '%');
        }
        
        // 获取总数（使用索引优化）
        $total = $query->count();
        
        if ($total == 0) {
            return [
                'list' => [],
                'total' => 0,
                'page' => $page,
                'limit' => $limit,
                'pages' => 0
            ];
        }
        
        // 获取分页数据，只查询必要字段
        $orders = $query->field([
                'id', 'order_no', 'item_id', 'item_name', 'quantity',
                'currency_type', 'unit_price', 'total_price', 'status', 'create_time'
            ])
            ->order('create_time', 'desc')
            ->limit(($page - 1) * $limit, $limit)
            ->select()
            ->toArray();
        
        // 批量获取商品图标
        $itemIds = array_unique(array_filter(array_column($orders, 'item_id')));
        $itemIcons = [];
        
        if (!empty($itemIds)) {
            $itemIcons = self::getBatchItemIcons($itemIds);
        }
        
        // 组装数据 - 格式化订单数据
        $formattedOrders = [];
        foreach ($orders as $order) {
            $formattedOrders[] = [
                'id' => $order['id'],
                'order_no' => $order['order_no'],
                'item_id' => $order['item_id'],
                'item_name' => $order['item_name'] ?? '未知商品',
                'item_icon' => $itemIcons[$order['item_id']] ?? '/static/img/default.png',
                'quantity' => $order['quantity'],
                'currency_type' => $order['currency_type'],
                'currency_text' => self::convertCurrencyText($order['currency_type']),
                'unit_price' => $order['unit_price'],
                'total_price' => $order['total_price'],
                'status' => $order['status'],
                'status_text' => self::getStatusText($order['status']),
                'create_time' => $order['create_time'],
                'create_time_formatted' => date('Y-m-d H:i:s', strtotime($order['create_time']))
            ];
        }
        
        return [
            'list' => $formattedOrders,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ];
    }
    
    /**
     * 批量获取商品图标（带缓存）
     * 
     * @param array $itemIds
     * @return array
     */
    private static function getBatchItemIcons($itemIds)
    {
        if (empty($itemIds)) {
            return [];
        }
        
        // 生成缓存键
        sort($itemIds);
        $cacheKey = self::CACHE_PREFIX . 'icons_' . md5(implode(',', $itemIds));
        
        // 尝试从缓存获取
        $cached = Cache::get($cacheKey);
        if ($cached !== false) {
            return $cached;
        }
        
        try {
            // 使用ItemService的批量查询
            $itemInfos = ItemService::getBatchItemInfoFromDatabase($itemIds);
            
            $icons = [];
            foreach ($itemIds as $itemId) {
                if (isset($itemInfos[$itemId]) && is_array($itemInfos[$itemId])) {
                    $icons[$itemId] = $itemInfos[$itemId]['icon'] ?? '/static/img/default.png';
                } else {
                    $icons[$itemId] = '/static/img/default.png';
                }
            }
            
            // 缓存结果
            Cache::set($cacheKey, $icons, self::DEFAULT_CACHE_TIME);
            
            return $icons;
            
        } catch (\Exception $e) {
            Log::error('批量获取商品图标失败: ' . $e->getMessage());
            
            // 返回默认图标
            $defaultIcons = [];
            foreach ($itemIds as $itemId) {
                $defaultIcons[$itemId] = '/static/img/default.png';
            }
            return $defaultIcons;
        }
    }
    
    /**
     * 生成缓存键
     * 
     * @param int $userId
     * @param int $page
     * @param int $limit
     * @param array $filters
     * @return string
     */
    private static function generateCacheKey($userId, $page, $limit, $filters)
    {
        $filterStr = '';
        if (!empty($filters)) {
            ksort($filters);
            $filterStr = '_' . md5(serialize($filters));
        }
        
        return self::CACHE_PREFIX . "user_{$userId}_page_{$page}_limit_{$limit}{$filterStr}";
    }
    
    /**
     * 清除用户的购买历史缓存
     * 
     * @param int $userId
     */
    public static function clearUserCache($userId)
    {
        $pattern = self::CACHE_PREFIX . "user_{$userId}_*";
        
        // 注意：这里需要根据实际的缓存驱动实现清除逻辑
        // Redis可以使用KEYS命令，但生产环境建议使用SCAN
        try {
            if (Cache::getConfig('default') === 'redis') {
                $redis = Cache::store('redis')->handler();
                $keys = $redis->keys($pattern);
                if (!empty($keys)) {
                    $redis->del($keys);
                }
            }
            
            Log::info("已清除用户 {$userId} 的购买历史缓存");
            
        } catch (\Exception $e) {
            Log::error("清除购买历史缓存失败: " . $e->getMessage());
        }
    }
    
    /**
     * 预热缓存（可以在用户登录时调用）
     * 
     * @param int $userId
     */
    public static function warmupCache($userId)
    {
        try {
            // 预热第一页数据
            self::getBatchPurchaseHistory($userId, 1, 10);
            Log::info("已预热用户 {$userId} 的购买历史缓存");
            
        } catch (\Exception $e) {
            Log::error("预热购买历史缓存失败: " . $e->getMessage());
        }
    }
    
    /**
     * 获取缓存统计信息
     *
     * @return array
     */
    public static function getCacheStats()
    {
        // 这里可以实现缓存命中率统计等功能
        return [
            'cache_prefix' => self::CACHE_PREFIX,
            'default_cache_time' => self::DEFAULT_CACHE_TIME,
            'timestamp' => time()
        ];
    }

    /**
     * 转换货币类型为文本
     *
     * @param string $currencyType 货币类型数字
     * @return string 货币文本
     */
    private static function convertCurrencyText($currencyType): string
    {
        $currencyMap = [
            '1' => '泡点',
            '2' => '积分',
            '3' => 'C币',
            1 => '泡点',
            2 => '积分',
            3 => 'C币'
        ];

        return $currencyMap[$currencyType] ?? '未知货币';
    }

    /**
     * 获取状态文本
     *
     * @param int $status 状态码
     * @return string 状态文本
     */
    private static function getStatusText($status): string
    {
        $statusMap = [
            1 => '待处理',
            2 => '处理中',
            3 => '已完成',
            4 => '失败',
            5 => '已取消',
            6 => '已退款'
        ];

        return $statusMap[$status] ?? '未知状态';
    }
}
