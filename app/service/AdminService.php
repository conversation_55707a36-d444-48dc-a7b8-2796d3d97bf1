<?php

namespace app\service;

use think\facade\Db;
use think\facade\Session;
use think\facade\Log;
use app\service\CacheService;

/**
 * 管理员服务类
 * 负责管理员权限验证和相关业务逻辑
 * 遵循单一职责原则：专门处理管理员权限相关功能
 */
class AdminService
{
    /**
     * 管理员权限缓存时间（秒）
     */
    const ADMIN_CACHE_TTL = 300; // 5分钟

    /**
     * 检查当前用户是否为管理员（带缓存优化）
     *
     * @return bool
     */
    public function isAdmin(): bool
    {
        if (!Session::has('user_info')) {
            Log::info('AdminService: 没有user_info session');
            return false;
        }

        $userInfo = Session::get('user_info');
        Log::info('AdminService: user_info = ' . json_encode($userInfo));

        if (!isset($userInfo['username'])) {
            Log::info('AdminService: user_info中没有username字段');
            return false;
        }

        $username = $userInfo['username'];
        Log::info('AdminService: 检查用户权限 - ' . $username);

        // 生成缓存键
        $cacheKey = 'admin_auth:' . $username;

        // 尝试从缓存获取
        $cachedResult = CacheService::get($cacheKey);
        if ($cachedResult !== null) {
            Log::info('AdminService: 权限检查缓存命中 - ' . ($cachedResult ? 'admin' : 'not admin'));
            return $cachedResult;
        }

        try {
            // 在seal_web数据库的admin表中查询UserName字段
            $adminRecord = Db::connect('seal_web')
                ->table('admin')
                ->where('UserName', $username)
                ->find();

            $isAdmin = !empty($adminRecord);
            Log::info('AdminService: admin查询结果 - ' . ($isAdmin ? 'found' : 'not found'));

            // 缓存结果
            CacheService::set($cacheKey, $isAdmin, self::ADMIN_CACHE_TTL);

            return $isAdmin;
        } catch (\Exception $e) {
            Log::error('管理员权限检查失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 获取管理员信息
     * 
     * @return array|null
     */
    public function getAdminInfo(): ?array
    {
        if (!Session::has('user_info')) {
            return null;
        }
        
        $userInfo = Session::get('user_info');
        
        if (!isset($userInfo['username'])) {
            return null;
        }
        
        $username = $userInfo['username'];
        
        try {
            // 在seal_web数据库的admin表中查询UserName字段
            $adminRecord = Db::connect('seal_web')
                ->table('admin')
                ->where('UserName', $username)
                ->find();
                
            return $adminRecord ?: null;
        } catch (\Exception $e) {
            Log::error('获取管理员信息失败: ' . $e->getMessage());
            return null;
        }
    }
    
    /**
     * 检查用户是否有特定权限
     * 
     * @param string $permission 权限名称
     * @return bool
     */
    public function hasPermission(string $permission): bool
    {
        $adminInfo = $this->getAdminInfo();
        
        if (!$adminInfo) {
            return false;
        }
        
        // 这里可以根据admin表的结构来判断具体权限
        // 假设admin表有一个permissions字段存储权限信息
        // 或者有一个role字段表示角色等级
        
        return true; // 暂时返回true，具体权限逻辑可以后续完善
    }
    
    /**
     * 检查指定用户是否为管理员
     * 
     * @param string $username 用户名
     * @return bool
     */
    public function isUserAdmin(string $username): bool
    {
        try {
            // 在seal_web数据库的admin表中查询UserName字段
            $adminRecord = Db::connect('seal_web')
                ->table('admin')
                ->where('UserName', $username)
                ->find();
                
            return !empty($adminRecord);
        } catch (\Exception $e) {
            Log::error('检查用户管理员权限失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 获取所有管理员列表（仅超级管理员可用）
     * 
     * @return array
     */
    public function getAdminList(): array
    {
        if (!$this->isAdmin()) {
            return [];
        }
        
        try {
            $adminList = Db::connect('seal_web')
                ->table('admin')
                ->select()
                ->toArray();
                
            return $adminList;
        } catch (\Exception $e) {
            Log::error('获取管理员列表失败: ' . $e->getMessage());
            return [];
        }
    }
}