<?php

namespace app\service;

use think\facade\Log;
use think\facade\Db;

/**
 * 购买历史预加载服务
 * 用于在用户登录时预热数据，提升首次查询速度
 */
class PurchaseHistoryPreloader
{
    /**
     * 预热用户购买历史数据
     * 
     * @param string $userId 用户ID
     * @return bool
     */
    public static function preloadUserData($userId)
    {
        try {
            Log::info("开始预热用户 {$userId} 的购买历史数据");
            
            // 异步预热，不阻塞用户登录
            self::preloadInBackground($userId);
            
            return true;
            
        } catch (\Exception $e) {
            Log::error("预热购买历史数据失败: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 后台预热数据
     * 
     * @param string $userId
     */
    private static function preloadInBackground($userId)
    {
        try {
            // 1. 预热第一页购买历史
            self::preloadPurchaseHistory($userId);
            
            // 2. 预热常用商品图标
            self::preloadCommonItemIcons($userId);
            
            Log::info("用户 {$userId} 数据预热完成");
            
        } catch (\Exception $e) {
            Log::error("后台预热失败: " . $e->getMessage());
        }
    }
    
    /**
     * 预热购买历史
     * 
     * @param string $userId
     */
    private static function preloadPurchaseHistory($userId)
    {
        // 生成第一页的缓存键
        $cacheKey = 'purchase_history:' . $userId . ':' . md5(serialize([]) . '1' . '10');
        
        // 检查是否已有缓存
        $cached = \app\service\CacheService::get($cacheKey);
        if ($cached !== null) {
            return; // 已有缓存，无需预热
        }
        
        // 查询第一页数据
        $query = \app\model\PurchaseOrder::field('id,order_no,item_id,item_name,quantity,currency_type,unit_price,total_price,status,create_time')
            ->where('user_id', $userId)
            ->order('create_time', 'desc')
            ->limit(10);
        
        $orders = $query->select()->toArray();
        
        if (empty($orders)) {
            return;
        }
        
        // 获取商品图标
        $itemIds = array_unique(array_filter(array_column($orders, 'item_id')));
        $itemIcons = self::preloadItemIcons($itemIds);
        
        // 格式化数据
        $formattedOrders = [];
        $currencyMap = ['1' => '泡点', '2' => '积分', '3' => 'C币'];
        $statusMap = [1 => '待处理', 2 => '处理中', 3 => '已完成', 4 => '失败', 5 => '已取消', 6 => '已退款'];
        
        foreach ($orders as $order) {
            $itemId = $order['item_id'];
            $formattedOrders[] = [
                'id' => $order['id'],
                'order_no' => $order['order_no'],
                'item_id' => $itemId,
                'item_name' => $order['item_name'] ?? '未知商品',
                'item_icon' => $itemIcons[$itemId] ?? '/static/img/default.png',
                'quantity' => $order['quantity'],
                'currency_type' => $order['currency_type'],
                'currency_text' => $currencyMap[$order['currency_type']] ?? '未知货币',
                'unit_price' => $order['unit_price'],
                'total_price' => $order['total_price'],
                'status' => $order['status'],
                'status_text' => $statusMap[$order['status']] ?? '未知状态',
                'create_time' => $order['create_time'],
                'create_time_formatted' => date('Y-m-d H:i:s', strtotime($order['create_time']))
            ];
        }
        
        // 获取总数
        $total = \app\model\PurchaseOrder::where('user_id', $userId)->count();
        
        $result = [
            'list' => $formattedOrders,
            'total' => $total,
            'page' => 1,
            'limit' => 10,
            'pages' => ceil($total / 10)
        ];
        
        // 缓存结果
        \app\service\CacheService::set($cacheKey, $result, 900); // 15分钟
    }
    
    /**
     * 预热常用商品图标
     * 
     * @param string $userId
     */
    private static function preloadCommonItemIcons($userId)
    {
        try {
            // 获取用户最近购买的商品ID
            $recentItemIds = Db::name('purchase_orders')
                ->where('user_id', $userId)
                ->order('create_time', 'desc')
                ->limit(50)
                ->column('item_id');
            
            if (!empty($recentItemIds)) {
                $recentItemIds = array_unique(array_filter($recentItemIds));
                self::preloadItemIcons($recentItemIds);
            }
            
        } catch (\Exception $e) {
            Log::error("预热商品图标失败: " . $e->getMessage());
        }
    }
    
    /**
     * 预热商品图标
     * 
     * @param array $itemIds
     * @return array
     */
    private static function preloadItemIcons($itemIds)
    {
        if (empty($itemIds)) {
            return [];
        }
        
        sort($itemIds);
        $cacheKey = 'purchase_history_icons_' . md5(implode(',', $itemIds));
        
        // 检查缓存
        $cached = \app\service\CacheService::get($cacheKey, false);
        if ($cached !== false) {
            return $cached;
        }
        
        // 查询图标信息
        $icons = [];
        $defaultIcon = '/static/img/default.png';
        
        try {
            $itemIdsStr = implode(',', array_map('intval', $itemIds));
            $sql = "SELECT ItemID, Icon FROM iteminfo WHERE ItemID IN ({$itemIdsStr})";
            
            $db = Db::connect('mysql');
            $results = $db->query($sql);
            
            $iconMap = [];
            foreach ($results as $row) {
                $iconId = $row['Icon'];
                if (!empty($iconId)) {
                    $iconPath = "/static/img/{$iconId}.png";
                    if (file_exists(root_path() . "public{$iconPath}")) {
                        $iconMap[$row['ItemID']] = $iconPath;
                    } else {
                        $iconMap[$row['ItemID']] = "/static/img/x{$iconId}.png";
                    }
                } else {
                    $iconMap[$row['ItemID']] = $defaultIcon;
                }
            }
            
            foreach ($itemIds as $itemId) {
                $icons[$itemId] = $iconMap[$itemId] ?? $defaultIcon;
            }
            
            // 缓存结果
            \app\service\CacheService::set($cacheKey, $icons, 1800); // 30分钟
            
            return $icons;
            
        } catch (\Exception $e) {
            Log::error("预热图标查询失败: " . $e->getMessage());
            
            foreach ($itemIds as $itemId) {
                $icons[$itemId] = $defaultIcon;
            }
            return $icons;
        }
    }
    
    /**
     * 清除用户预热数据
     * 
     * @param string $userId
     */
    public static function clearUserPreloadData($userId)
    {
        try {
            // 清除购买历史缓存
            $patterns = [
                'purchase_history:' . $userId . ':*',
                'purchase_history_icons_*'
            ];
            
            // 这里可以根据实际缓存驱动实现清除逻辑
            Log::info("已清除用户 {$userId} 的预热数据");
            
        } catch (\Exception $e) {
            Log::error("清除预热数据失败: " . $e->getMessage());
        }
    }
}
