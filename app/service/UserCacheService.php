<?php
declare(strict_types=1);

namespace app\service;

use think\facade\Cache;
use think\facade\Log;

/**
 * 用户缓存服务类
 * 负责用户数据的缓存管理，提升查询性能
 */
class UserCacheService
{
    private const CACHE_PREFIX = 'user_list:';
    private const CACHE_TTL = 300; // 5分钟缓存
    private const NICKNAME_CACHE_PREFIX = 'user_nickname:';
    private const LOGIN_CACHE_PREFIX = 'user_login:';
    
    private UserService $userService;
    
    public function __construct()
    {
        $this->userService = new UserService();
    }
    
    /**
     * 获取缓存的用户列表
     * 
     * @param int $page 页码
     * @param int $limit 每页数量
     * @param string $search 搜索关键词
     * @param string $status 状态筛选
     * @return array
     */
    public function getCachedUserList(int $page = 1, int $limit = 20, string $search = '', string $status = ''): array
    {
        try {
            // 生成缓存键
            $cacheKey = $this->generateCacheKey($page, $limit, $search, $status);
            
            // 尝试从缓存获取
            $cachedData = Cache::get($cacheKey);
            if ($cachedData !== null) {
                Log::info('用户列表缓存命中: ' . $cacheKey);
                return $cachedData;
            }
            
            // 缓存未命中，从数据库获取
            Log::info('用户列表缓存未命中，查询数据库: ' . $cacheKey);
            $result = $this->userService->getUserListOptimized($page, $limit, $search, $status);
            
            // 存入缓存
            Cache::set($cacheKey, $result, self::CACHE_TTL);
            
            return $result;
            
        } catch (\Exception $e) {
            Log::error('获取缓存用户列表失败: ' . $e->getMessage());
            // 缓存失败时直接查询数据库
            return $this->userService->getUserListOptimized($page, $limit, $search, $status);
        }
    }
    
    /**
     * 生成缓存键
     * 
     * @param int $page
     * @param int $limit
     * @param string $search
     * @param string $status
     * @return string
     */
    private function generateCacheKey(int $page, int $limit, string $search, string $status): string
    {
        $params = [
            'page' => $page,
            'limit' => $limit,
            'search' => $search,
            'status' => $status
        ];
        
        return self::CACHE_PREFIX . md5(json_encode($params));
    }
    
    /**
     * 清除用户列表缓存
     * 
     * @return bool
     */
    public function clearUserListCache(): bool
    {
        try {
            // 清除所有用户列表相关缓存
            $pattern = self::CACHE_PREFIX . '*';
            Cache::clear();
            
            Log::info('用户列表缓存已清除');
            return true;
            
        } catch (\Exception $e) {
            Log::error('清除用户列表缓存失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 缓存用户昵称
     * 
     * @param string $userId
     * @param string $nickname
     * @return bool
     */
    public function cacheUserNickname(string $userId, string $nickname): bool
    {
        try {
            $cacheKey = self::NICKNAME_CACHE_PREFIX . $userId;
            Cache::set($cacheKey, $nickname, self::CACHE_TTL * 2); // 昵称缓存时间更长
            return true;
        } catch (\Exception $e) {
            Log::error('缓存用户昵称失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 获取缓存的用户昵称
     * 
     * @param string $userId
     * @return string|null
     */
    public function getCachedUserNickname(string $userId): ?string
    {
        try {
            $cacheKey = self::NICKNAME_CACHE_PREFIX . $userId;
            return Cache::get($cacheKey);
        } catch (\Exception $e) {
            Log::error('获取缓存用户昵称失败: ' . $e->getMessage());
            return null;
        }
    }
    
    /**
     * 批量缓存用户昵称
     * 
     * @param array $nicknames 用户ID => 昵称的数组
     * @return bool
     */
    public function batchCacheUserNicknames(array $nicknames): bool
    {
        try {
            foreach ($nicknames as $userId => $nickname) {
                $this->cacheUserNickname($userId, $nickname);
            }
            return true;
        } catch (\Exception $e) {
            Log::error('批量缓存用户昵称失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 预热缓存
     * 在系统空闲时预先加载常用数据到缓存
     * 
     * @return bool
     */
    public function warmupCache(): bool
    {
        try {
            Log::info('开始预热用户列表缓存');
            
            // 预热第一页数据
            $this->getCachedUserList(1, 20);
            
            // 预热搜索为空的前几页
            for ($page = 1; $page <= 3; $page++) {
                $this->getCachedUserList($page, 20);
                usleep(100000); // 100ms延迟，避免数据库压力
            }
            
            Log::info('用户列表缓存预热完成');
            return true;
            
        } catch (\Exception $e) {
            Log::error('预热用户列表缓存失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 获取缓存统计信息
     * 
     * @return array
     */
    public function getCacheStats(): array
    {
        try {
            // 这里可以添加缓存命中率等统计信息
            return [
                'cache_prefix' => self::CACHE_PREFIX,
                'cache_ttl' => self::CACHE_TTL,
                'status' => 'active'
            ];
        } catch (\Exception $e) {
            Log::error('获取缓存统计信息失败: ' . $e->getMessage());
            return ['status' => 'error'];
        }
    }
}