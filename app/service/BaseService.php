<?php

namespace app\service;

use think\facade\Db;
use think\facade\Log;
use app\service\CacheService;

/**
 * 基础服务类
 * 提供通用的服务层功能，遵循DRY原则
 * 
 * 设计原则：
 * 1. 单一职责原则：专门提供通用服务功能
 * 2. 开闭原则：易于扩展新的通用功能
 * 3. 里氏替换原则：子类可以替换父类
 * 4. 依赖倒置原则：依赖抽象而非具体实现
 */
abstract class BaseService
{
    /**
     * 默认缓存时间（秒）
     */
    protected const DEFAULT_CACHE_TTL = 1800; // 30分钟
    
    /**
     * 列表缓存时间（秒）
     */
    protected const LIST_CACHE_TTL = 600; // 10分钟
    
    /**
     * 详情缓存时间（秒）
     */
    protected const DETAIL_CACHE_TTL = 3600; // 1小时
    
    /**
     * 缓存前缀
     */
    protected string $cachePrefix = '';
    
    /**
     * 主表名
     */
    protected string $tableName = '';
    
    /**
     * 主键字段名
     */
    protected string $primaryKey = 'id';
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->initializeService();
    }
    
    /**
     * 初始化服务（子类可重写）
     */
    protected function initializeService(): void
    {
        // 子类可以重写此方法进行初始化
    }
    
    /**
     * 通用分页查询方法
     * 
     * @param int $page 页码
     * @param int $limit 每页数量
     * @param array $conditions 查询条件
     * @param array $options 查询选项
     * @return array
     */
    protected function getPaginatedList(int $page = 1, int $limit = 20, array $conditions = [], array $options = []): array
    {
        // 生成缓存键
        $cacheKey = $this->buildListCacheKey($page, $limit, $conditions, $options);
        
        // 使用缓存
        return CacheService::getWithFallback(
            $cacheKey,
            function() use ($page, $limit, $conditions, $options) {
                return $this->fetchPaginatedData($page, $limit, $conditions, $options);
            },
            static::LIST_CACHE_TTL,
            $this->getEmptyListResult($page, $limit)
        );
    }
    
    /**
     * 从数据库获取分页数据
     * 
     * @param int $page 页码
     * @param int $limit 每页数量
     * @param array $conditions 查询条件
     * @param array $options 查询选项
     * @return array
     */
    protected function fetchPaginatedData(int $page, int $limit, array $conditions, array $options): array
    {
        try {
            $query = $this->buildQuery($conditions, $options);
            
            // 获取总数
            $total = $query->count();
            
            // 获取数据
            $list = $query->page($page, $limit)->select()->toArray();
            
            // 后处理数据
            $list = $this->postProcessListData($list);
            
            return [
                'list' => $list,
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'total_pages' => ceil($total / $limit)
            ];
        } catch (\Exception $e) {
            Log::error($this->getServiceName() . '分页查询失败: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 构建查询对象
     * 
     * @param array $conditions 查询条件
     * @param array $options 查询选项
     * @return \think\db\Query
     */
    protected function buildQuery(array $conditions = [], array $options = []): \think\db\Query
    {
        $query = Db::table($this->tableName);
        
        // 应用查询条件
        $this->applyConditions($query, $conditions);
        
        // 应用查询选项
        $this->applyOptions($query, $options);
        
        return $query;
    }
    
    /**
     * 应用查询条件
     * 
     * @param \think\db\Query $query 查询对象
     * @param array $conditions 查询条件
     */
    protected function applyConditions(\think\db\Query $query, array $conditions): void
    {
        foreach ($conditions as $field => $value) {
            if ($value === null || $value === '') {
                continue;
            }
            
            if (is_array($value)) {
                // 处理数组条件（如 IN 查询）
                $query->whereIn($field, $value);
            } elseif (strpos($field, '_like') !== false) {
                // 处理模糊查询
                $realField = str_replace('_like', '', $field);
                $query->where($realField, 'like', '%' . $value . '%');
            } else {
                // 普通等值查询
                $query->where($field, $value);
            }
        }
    }
    
    /**
     * 应用查询选项
     * 
     * @param \think\db\Query $query 查询对象
     * @param array $options 查询选项
     */
    protected function applyOptions(\think\db\Query $query, array $options): void
    {
        // 排序
        if (isset($options['order'])) {
            if (is_array($options['order'])) {
                foreach ($options['order'] as $field => $direction) {
                    $query->order($field, $direction);
                }
            } else {
                $query->order($options['order']);
            }
        } else {
            // 默认排序
            $query->order($this->primaryKey, 'desc');
        }
        
        // 字段选择
        if (isset($options['field'])) {
            $query->field($options['field']);
        }
        
        // 关联查询
        if (isset($options['with'])) {
            foreach ($options['with'] as $relation) {
                $query->with($relation);
            }
        }
    }
    
    /**
     * 通用详情查询方法
     * 
     * @param mixed $id 主键值
     * @param array $options 查询选项
     * @return array|null
     */
    protected function getDetail($id, array $options = []): ?array
    {
        // 生成缓存键
        $cacheKey = $this->buildDetailCacheKey($id, $options);
        
        // 使用缓存
        return CacheService::getWithFallback(
            $cacheKey,
            function() use ($id, $options) {
                return $this->fetchDetailData($id, $options);
            },
            static::DETAIL_CACHE_TTL,
            null
        );
    }
    
    /**
     * 从数据库获取详情数据
     * 
     * @param mixed $id 主键值
     * @param array $options 查询选项
     * @return array|null
     */
    protected function fetchDetailData($id, array $options): ?array
    {
        try {
            $query = Db::table($this->tableName)->where($this->primaryKey, $id);
            
            // 应用查询选项
            $this->applyOptions($query, $options);
            
            $data = $query->find();
            
            if ($data) {
                // 后处理数据
                $data = $this->postProcessDetailData($data);
            }
            
            return $data;
        } catch (\Exception $e) {
            Log::error($this->getServiceName() . '详情查询失败: ' . $e->getMessage());
            return null;
        }
    }
    
    /**
     * 通用创建方法
     * 
     * @param array $data 数据
     * @return bool|int
     */
    protected function create(array $data)
    {
        try {
            // 预处理数据
            $data = $this->preProcessCreateData($data);
            
            // 验证数据
            $this->validateCreateData($data);
            
            // 插入数据
            $result = Db::table($this->tableName)->insertGetId($data);
            
            if ($result) {
                // 清除相关缓存
                $this->clearRelatedCache();
                
                // 后处理
                $this->postProcessCreate($result, $data);
            }
            
            return $result;
        } catch (\Exception $e) {
            Log::error($this->getServiceName() . '创建失败: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 通用更新方法
     * 
     * @param mixed $id 主键值
     * @param array $data 数据
     * @return bool
     */
    protected function update($id, array $data): bool
    {
        try {
            // 预处理数据
            $data = $this->preProcessUpdateData($data);
            
            // 验证数据
            $this->validateUpdateData($id, $data);
            
            // 更新数据
            $result = Db::table($this->tableName)
                ->where($this->primaryKey, $id)
                ->update($data);
            
            if ($result !== false) {
                // 清除相关缓存
                $this->clearRelatedCache($id);
                
                // 后处理
                $this->postProcessUpdate($id, $data);
                
                return true;
            }
            
            return false;
        } catch (\Exception $e) {
            Log::error($this->getServiceName() . '更新失败: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 通用删除方法
     * 
     * @param mixed $id 主键值
     * @return bool
     */
    protected function delete($id): bool
    {
        try {
            // 验证删除权限
            $this->validateDelete($id);
            
            // 软删除或硬删除
            if ($this->supportsSoftDelete()) {
                $result = Db::table($this->tableName)
                    ->where($this->primaryKey, $id)
                    ->update(['delete_time' => date('Y-m-d H:i:s')]);
            } else {
                $result = Db::table($this->tableName)
                    ->where($this->primaryKey, $id)
                    ->delete();
            }
            
            if ($result) {
                // 清除相关缓存
                $this->clearRelatedCache($id);
                
                // 后处理
                $this->postProcessDelete($id);
                
                return true;
            }
            
            return false;
        } catch (\Exception $e) {
            Log::error($this->getServiceName() . '删除失败: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 构建列表缓存键
     * 
     * @param int $page 页码
     * @param int $limit 每页数量
     * @param array $conditions 查询条件
     * @param array $options 查询选项
     * @return string
     */
    protected function buildListCacheKey(int $page, int $limit, array $conditions, array $options): string
    {
        $params = [
            'page' => $page,
            'limit' => $limit,
            'conditions' => $conditions,
            'options' => $options
        ];
        return $this->cachePrefix . 'list:' . md5(serialize($params));
    }
    
    /**
     * 构建详情缓存键
     * 
     * @param mixed $id 主键值
     * @param array $options 查询选项
     * @return string
     */
    protected function buildDetailCacheKey($id, array $options): string
    {
        $params = ['id' => $id, 'options' => $options];
        return $this->cachePrefix . 'detail:' . md5(serialize($params));
    }
    
    /**
     * 清除相关缓存
     * 
     * @param mixed $id 主键值（可选）
     */
    protected function clearRelatedCache($id = null): void
    {
        try {
            // 清除列表缓存
            CacheService::deleteByPattern($this->cachePrefix . 'list:*');
            
            // 如果指定了ID，清除详情缓存
            if ($id !== null) {
                CacheService::deleteByPattern($this->cachePrefix . 'detail:*' . $id . '*');
            }
        } catch (\Exception $e) {
            Log::warning($this->getServiceName() . '清除缓存失败: ' . $e->getMessage());
        }
    }
    
    // 以下方法由子类实现或重写
    
    /**
     * 获取服务名称
     * 
     * @return string
     */
    abstract protected function getServiceName(): string;
    
    /**
     * 后处理列表数据
     * 
     * @param array $list 列表数据
     * @return array
     */
    protected function postProcessListData(array $list): array
    {
        return $list;
    }
    
    /**
     * 后处理详情数据
     * 
     * @param array $data 详情数据
     * @return array
     */
    protected function postProcessDetailData(array $data): array
    {
        return $data;
    }
    
    /**
     * 预处理创建数据
     * 
     * @param array $data 原始数据
     * @return array
     */
    protected function preProcessCreateData(array $data): array
    {
        return $data;
    }
    
    /**
     * 预处理更新数据
     * 
     * @param array $data 原始数据
     * @return array
     */
    protected function preProcessUpdateData(array $data): array
    {
        return $data;
    }
    
    /**
     * 验证创建数据
     * 
     * @param array $data 数据
     * @throws \Exception
     */
    protected function validateCreateData(array $data): void
    {
        // 子类可重写此方法进行数据验证
    }
    
    /**
     * 验证更新数据
     * 
     * @param mixed $id 主键值
     * @param array $data 数据
     * @throws \Exception
     */
    protected function validateUpdateData($id, array $data): void
    {
        // 子类可重写此方法进行数据验证
    }
    
    /**
     * 验证删除权限
     * 
     * @param mixed $id 主键值
     * @throws \Exception
     */
    protected function validateDelete($id): void
    {
        // 子类可重写此方法进行删除验证
    }
    
    /**
     * 是否支持软删除
     * 
     * @return bool
     */
    protected function supportsSoftDelete(): bool
    {
        return false;
    }
    
    /**
     * 创建后处理
     * 
     * @param mixed $id 新创建的ID
     * @param array $data 创建的数据
     */
    protected function postProcessCreate($id, array $data): void
    {
        // 子类可重写此方法进行后处理
    }
    
    /**
     * 更新后处理
     * 
     * @param mixed $id 主键值
     * @param array $data 更新的数据
     */
    protected function postProcessUpdate($id, array $data): void
    {
        // 子类可重写此方法进行后处理
    }
    
    /**
     * 删除后处理
     * 
     * @param mixed $id 主键值
     */
    protected function postProcessDelete($id): void
    {
        // 子类可重写此方法进行后处理
    }
    
    /**
     * 获取空列表结果
     * 
     * @param int $page 页码
     * @param int $limit 每页数量
     * @return array
     */
    protected function getEmptyListResult(int $page, int $limit): array
    {
        return [
            'list' => [],
            'total' => 0,
            'page' => $page,
            'limit' => $limit,
            'total_pages' => 0
        ];
    }
}
