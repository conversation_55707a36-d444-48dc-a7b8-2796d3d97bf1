<?php

namespace app\service;

use think\facade\Db;
use think\facade\Log;
use think\facade\Cache;
use app\service\DatabaseService;

/**
 * 订单服务类
 * 负责处理订单相关的业务逻辑
 * 遵循单一职责原则：专门处理订单管理功能
 * 继承BaseService以复用通用功能
 */
class OrderService extends BaseService
{
    /**
     * 订单状态常量
     */
    const STATUS_PENDING = 1;      // 待处理
    const STATUS_PROCESSING = 2;   // 处理中
    const STATUS_COMPLETED = 3;    // 已完成
    const STATUS_FAILED = 4;       // 失败
    const STATUS_CANCELLED = 5;    // 已取消

    /**
     * 数据库服务实例
     * @var DatabaseService
     */
    private $databaseService;

    /**
     * 缓存前缀
     */
    protected string $cachePrefix = 'order:';

    /**
     * 主表名
     */
    protected string $tableName = 'purchase_orders';

    /**
     * 主键字段名
     */
    protected string $primaryKey = 'id';

    /**
     * 缓存过期时间（秒）
     * @var int
     */
    private $cacheExpire = 300; // 5分钟

    /**
     * 构造函数
     */
    public function __construct()
    {
        parent::__construct();
        $this->databaseService = new DatabaseService();
    }

    /**
     * 获取服务名称
     */
    protected function getServiceName(): string
    {
        return '订单服务';
    }
    
    /**
     * 获取订单列表
     * 
     * @param int $page 页码
     * @param int $limit 每页数量
     * @param string $search 搜索关键词
     * @param string $status 状态筛选
     * @param string $dateRange 日期范围
     * @return array
     */
    public function getOrderList(int $page = 1, int $limit = 20, string $search = '', string $status = '', string $dateRange = ''): array
    {
        try {
            // 构建缓存键
            $cacheKey = $this->cachePrefix . 'list:' . md5(serialize([
                'page' => $page,
                'limit' => $limit,
                'search' => $search,
                'status' => $status,
                'dateRange' => $dateRange
            ]));
            
            // 尝试从缓存获取
            $cachedResult = Cache::get($cacheKey);
            if ($cachedResult !== null) {
                return $cachedResult;
            }
            
            // 计算偏移量
            $offset = ($page - 1) * $limit;
            
            // 构建查询条件
            $where = [];
            $whereRaw = [];
            
            // 搜索条件
            if (!empty($search)) {
                $whereRaw[] = "(po.order_no LIKE '%{$search}%' OR po.user_id LIKE '%{$search}%' OR po.item_name LIKE '%{$search}%')";
            }
            
            // 状态筛选
            if (!empty($status)) {
                // 状态字符串到数字的映射
                $statusToNumber = [
                    'pending' => 1,
                    'processing' => 2,
                    'completed' => 3,
                    'failed' => 4,
                    'cancelled' => 5,
                    'refunded' => 6
                ];
                
                // 如果是字符串状态，转换为数字
                if (isset($statusToNumber[$status])) {
                    $where['po.status'] = $statusToNumber[$status];
                } else {
                    // 如果已经是数字，直接使用
                    $where['po.status'] = $status;
                }
            }
            
            // 日期范围筛选
            if (!empty($dateRange)) {
                switch ($dateRange) {
                    case 'today':
                        $whereRaw[] = "DATE(po.create_time) = CURDATE()";
                        break;
                    case 'yesterday':
                        $whereRaw[] = "DATE(po.create_time) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)";
                        break;
                    case 'week':
                        $whereRaw[] = "po.create_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
                        break;
                    case 'month':
                        $whereRaw[] = "po.create_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
                        break;
                }
            }
            
            // 软删除条件
            $where['po.delete_time'] = null;
            
            // 构建查询
            $query = Db::table('purchase_orders')
                ->alias('po')
                ->field([
                    'po.id',
                    'po.order_no',
                    'po.user_id',
                    'po.item_id',
                    'po.item_name',
                    'po.quantity',
                    'po.unit_price',
                    'po.total_price',
                    'po.currency_type',
                    'po.status',
                    'po.unique_num',
                    'po.source',
                    'po.retry_count',
                    'po.error_message',
                    'po.delivery_time',
                    'po.create_time',
                    'po.update_time'
                ])
                ->where($where);
            
            // 添加原生WHERE条件
            foreach ($whereRaw as $condition) {
                $query->whereRaw($condition);
            }
            
            // 获取总数
            $total = $query->count();
            
            // 获取列表数据
            $list = $query->order('po.create_time', 'desc')
                ->limit($offset, $limit)
                ->select()
                ->toArray();
            
            // 批量获取用户信息
            $userIds = array_unique(array_column($list, 'user_id'));
            $userInfoMap = $this->getUserInfoBatch($userIds);
            
            // 格式化数据
            $formattedList = array_map(function($order) use ($userInfoMap) {
                return $this->formatOrderData($order, $userInfoMap);
            }, $list);
            
            $result = [
                'list' => $formattedList,
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'pages' => ceil($total / $limit)
            ];
            
            // 缓存结果
            Cache::set($cacheKey, $result, $this->cacheExpire);
            
            return $result;
            
        } catch (\Exception $e) {
            Log::error('获取订单列表失败: ' . $e->getMessage());
            throw new \Exception('获取订单列表失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取订单详情
     *
     * @param string|int $orderId 订单ID
     * @return array|null
     */
    public function getOrderDetail(string|int $orderId): ?array
    {
        try {
            // 确保orderId是字符串类型
            $orderId = (string)$orderId;

            // 构建缓存键
            $cacheKey = $this->cachePrefix . 'detail:' . $orderId;
            
            // 尝试从缓存获取
            $cachedResult = Cache::get($cacheKey);
            if ($cachedResult !== null) {
                return $cachedResult;
            }
            
            // 从数据库获取订单基本信息
            $order = Db::table('purchase_orders')
                ->alias('po')
                ->leftJoin('iteminfo i', 'po.item_id = i.ItemID')
                ->leftJoin('item_delivery_logs idl', 'po.id = idl.order_id')
                ->field('po.*, i.ItemName as item_description, i.Explanation as item_explanation, idl.delivery_time, idl.error_message')
                ->where('po.id', $orderId)
                ->find();
                
            if (!$order) {
                return null;
            }
            
            // 从远程数据库获取用户信息
            try {
                $userInfo = Db::connect('seal_member')
                    ->table('userinfoex2')
                    ->field('id, char_name as username, char_name as nickname')
                    ->where('id', $order['user_id'])
                    ->find();
                    
                if ($userInfo) {
                    $order['username'] = $this->fixCharacterNameEncoding($userInfo['username']);
                    $order['nickname'] = $this->fixCharacterNameEncoding($userInfo['nickname']);
                    $order['unique_num'] = $userInfo['id'];
                } else {
                    $order['username'] = $order['user_id'];
                    $order['nickname'] = $order['user_id'];
                    $order['unique_num'] = $order['user_id'];
                }
            } catch (\Exception $e) {
                Log::warning('获取用户信息失败: ' . $e->getMessage());
                $order['username'] = $order['user_id'];
                $order['nickname'] = $order['user_id'];
                $order['unique_num'] = $order['user_id'];
            }
            
            // 获取货币类型名称
            $currencyMap = [
                'COIN' => 'C币',
                'POINT' => '泡点',
                'SCORE' => '积分'
            ];
            $order['currency_name'] = $currencyMap[$order['currency_type']] ?? $order['currency_type'];
            
            // 格式化数据
            $formattedOrder = $this->formatOrderDetailData($order);
            
            // 缓存结果
            Cache::set($cacheKey, $formattedOrder, $this->cacheExpire);
            
            return $formattedOrder;
            
        } catch (\Exception $e) {
            Log::error('获取订单详情失败: ' . $e->getMessage());
            throw new \Exception('获取订单详情失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 格式化订单详情数据
     * 
     * @param array $order 原始订单数据
     * @return array
     */
    private function formatOrderDetailData(array $order): array
    {
        // 状态映射
        $statusMap = [
            1 => 'pending',    // 待处理
            2 => 'processing', // 处理中
            3 => 'completed',  // 已完成
            4 => 'failed',     // 失败
            5 => 'cancelled',  // 已取消
            6 => 'refunded'    // 已退款
        ];
        
        // 货币类型映射
        $currencyMap = [
            // 字符串格式
            'COIN' => 'C币',
            'POINT' => '泡点',
            'SCORE' => '积分',
            // 数字格式（兼容性）
            '1' => '泡点',
            '2' => '积分',
            '3' => 'C币',
            1 => '泡点',
            2 => '积分',
            3 => 'C币'
        ];
        
        return [
            'id' => $order['id'],
            'order_no' => $order['order_no'],
            'user_id' => $order['user_id'],
            'username' => $order['username'] ?? $order['user_id'],
            'nickname' => $order['nickname'] ?? $order['user_id'],
            'unique_num' => $order['unique_num'] ?? '',
            'item_id' => $order['item_id'],
            'item_name' => $order['item_name'] ?? '',
            'quantity' => $order['quantity'],
            'unit_price' => $order['unit_price'],
            'total_price' => $order['total_price'],
            'amount' => $order['total_price'], // 兼容前端
            'currency_type' => $order['currency_type'],
            'currency_name' => $order['currency_name'] ?? ($currencyMap[$order['currency_type']] ?? $order['currency_type']),
            'status' => $statusMap[$order['status']] ?? 'unknown',
            'status_code' => $order['status'],
            'source' => $order['source'],
            'retry_count' => $order['retry_count'] ?? 0,
            'error_message' => $order['error_message'],
            'delivery_time' => $order['delivery_time'],
            'create_time' => $order['create_time'],
            'update_time' => $order['update_time']
        ];
    }
    
    /**
     * 获取订单统计数据
     *
     * @param string $dateRange 日期范围
     * @return array
     */
    public function getOrderStats(string $dateRange = ''): array
    {
        try {
            // 构建缓存键
            $cacheKey = $this->cachePrefix . 'stats:' . date('Y-m-d-H');
            
            // 尝试从缓存获取
            $cachedResult = Cache::get($cacheKey);
            if ($cachedResult !== null) {
                return $cachedResult;
            }
            
            // 总订单数
            $totalOrders = Db::table('purchase_orders')
                ->where('delete_time', null)
                ->count();
            
            // 待处理订单数
            $pendingOrders = Db::table('purchase_orders')
                ->where('status', 1)
                ->where('delete_time', null)
                ->count();
            
            // 已完成订单数
            $completedOrders = Db::table('purchase_orders')
                ->where('status', 3)
                ->where('delete_time', null)
                ->count();
            
            // 今日收入（按货币类型分别统计）
            $todayRevenueData = Db::table('purchase_orders')
                ->where('status', 3)
                ->whereRaw('DATE(create_time) = CURDATE()')
                ->where('delete_time', null)
                ->field('currency_type, sum(total_price) as total_amount')
                ->group('currency_type')
                ->select()
                ->toArray();
            
            // 格式化今日收入数据
            $currencyMap = [
                // 字符串格式
                'COIN' => 'C币',
                'POINT' => '泡点', 
                'SCORE' => '积分',
                // 数字格式（兼容性）
                '1' => '泡点',
                '2' => '积分',
                '3' => 'C币',
                1 => '泡点',
                2 => '积分',
                3 => 'C币'
            ];
            
            $todayRevenue = [];
            $totalRevenue = 0;
            foreach ($todayRevenueData as $revenue) {
                $currencyName = $currencyMap[$revenue['currency_type']] ?? $revenue['currency_type'];
                $todayRevenue[] = [
                    'currency_type' => $revenue['currency_type'],
                    'currency_name' => $currencyName,
                    'amount' => $revenue['total_amount']
                ];
                $totalRevenue += $revenue['total_amount'];
            }
            
            // 今日订单数
            $todayOrders = Db::table('purchase_orders')
                ->whereRaw('DATE(create_time) = CURDATE()')
                ->where('delete_time', null)
                ->count();
            
            $result = [
                'total' => $totalOrders,
                'pending' => $pendingOrders,
                'completed' => $completedOrders,
                'todayRevenue' => $todayRevenue,
                'todayRevenueTotal' => $totalRevenue,
                'todayOrders' => $todayOrders
            ];
            
            // 缓存结果（缓存1小时）
            Cache::set($cacheKey, $result, 3600);
            
            return $result;
            
        } catch (\Exception $e) {
            Log::error('获取订单统计失败: ' . $e->getMessage());
            throw new \Exception('获取订单统计失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 更新订单状态
     *
     * @param string|int $orderId 订单ID
     * @param int|string $status 新状态（支持数字或字符串）
     * @param string $errorMessage 错误信息（可选）
     * @return bool
     */
    public function updateOrderStatus(string|int $orderId, $status, string $errorMessage = ''): bool
    {
        try {
            // 确保orderId是字符串类型
            $orderId = (string)$orderId;

            // 状态转换：字符串转数字
            $statusMap = [
                'pending' => 1,    // 待处理
                'processing' => 2, // 处理中
                'completed' => 3,  // 已完成
                'failed' => 4,     // 失败
                'cancelled' => 5,  // 已取消
                'refunded' => 6    // 已退款
            ];

            // 如果是字符串状态，转换为数字
            if (is_string($status) && isset($statusMap[$status])) {
                $numericStatus = $statusMap[$status];
            } elseif (is_numeric($status)) {
                $numericStatus = (int)$status;
            } else {
                Log::error('无效的订单状态: ' . $status);
                return false;
            }

            $updateData = [
                'status' => $numericStatus,
                'update_time' => date('Y-m-d H:i:s')
            ];

            if (!empty($errorMessage)) {
                $updateData['error_message'] = $errorMessage;
            }

            // 如果是完成状态，记录发送时间
            if ($numericStatus == 3) {
                $updateData['delivery_time'] = date('Y-m-d H:i:s');
            }
            
            $result = Db::table('purchase_orders')
                ->where('id', $orderId)
                ->update($updateData);
            
            if ($result) {
                // 清除相关缓存
                $this->clearOrderCache($orderId);
                return true;
            }
            
            return false;
            
        } catch (\Exception $e) {
            Log::error('更新订单状态失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 批量获取用户信息
     * 
     * @param array $userIds 用户ID数组
     * @return array
     */
    private function getUserInfoBatch(array $userIds): array
    {
        if (empty($userIds)) {
            return [];
        }
        
        try {
            $userInfos = Db::connect('seal_member')
                ->table('userinfoex2')
                ->field('id, char_name as username, char_name as nickname')
                ->whereIn('id', $userIds)
                ->select()
                ->toArray();
                
            $userInfoMap = [];
            foreach ($userInfos as $userInfo) {
                // 修复角色名编码
                $userInfo['username'] = $this->fixCharacterNameEncoding($userInfo['username']);
                $userInfo['nickname'] = $this->fixCharacterNameEncoding($userInfo['nickname']);
                $userInfoMap[$userInfo['id']] = $userInfo;
            }
            
            return $userInfoMap;
        } catch (\Exception $e) {
            Log::warning('批量获取用户信息失败: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * 格式化订单数据
     * 
     * @param array $order 原始订单数据
     * @param array $userInfoMap 用户信息映射
     * @return array
     */
    private function formatOrderData(array $order, array $userInfoMap = []): array
    {
        // 状态映射
        $statusMap = [
            1 => 'pending',    // 待处理
            2 => 'processing', // 处理中
            3 => 'completed',  // 已完成
            4 => 'failed',     // 失败
            5 => 'cancelled',  // 已取消
            6 => 'refunded'    // 已退款
        ];
        
        // 货币类型映射
        $currencyMap = [
            // 字符串格式
            'COIN' => 'C币',
            'POINT' => '泡点',
            'SCORE' => '积分',
            // 数字格式（兼容性）
            '1' => '泡点',
            '2' => '积分',
            '3' => 'C币',
            1 => '泡点',
            2 => '积分',
            3 => 'C币'
        ];
        
        // 获取用户信息
        $userInfo = $userInfoMap[$order['user_id']] ?? null;
        $username = $userInfo['username'] ?? $order['user_id'];
        $nickname = $userInfo['nickname'] ?? $order['user_id'];
        
        return [
            'id' => $order['id'],
            'order_no' => $order['order_no'],
            'user_id' => $order['user_id'],
            'username' => $username,
             'nickname' => $nickname,
             'item_id' => $order['item_id'],
            'item_name' => $order['item_name'],
            'quantity' => $order['quantity'],
            'unit_price' => $order['unit_price'],
            'total_price' => $order['total_price'],
            'amount' => $order['total_price'], // 兼容前端
            'currency_type' => $order['currency_type'],
            'currency_name' => $currencyMap[$order['currency_type']] ?? $order['currency_type'],
            'status' => $statusMap[$order['status']] ?? 'unknown',
            'status_code' => $order['status'],
            'unique_num' => $order['unique_num'],
            'source' => $order['source'],
            'retry_count' => $order['retry_count'],
            'error_message' => $order['error_message'],
            'delivery_time' => $order['delivery_time'],
            'create_time' => $order['create_time'],
            'update_time' => $order['update_time']
        ];
    }
    
    /**
     * 清除订单相关缓存
     * 
     * @param string $orderId 订单ID
     * @return void
     */
    private function clearOrderCache(string $orderId = ''): void
    {
        try {
            // 清除订单详情缓存
            if (!empty($orderId)) {
                Cache::delete($this->cachePrefix . 'detail:' . $orderId);
            }
            
            // 清除订单列表缓存（使用通配符删除）
            $cacheKeys = Cache::tag($this->cachePrefix . 'list')->clear();
            
            // 清除统计缓存
            $statsCacheKey = $this->cachePrefix . 'stats:' . date('Y-m-d-H');
            Cache::delete($statsCacheKey);
            
        } catch (\Exception $e) {
            Log::warning('清除订单缓存失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 清除所有订单缓存
     *
     * @return void
     */
    public function clearAllCache(): void
    {
        $this->clearOrderCache();
    }

    /**
     * 补发订单
     *
     * @param string|int $orderId 订单ID
     * @param string $reason 补发原因
     * @return array
     */
    public function resendOrder(string|int $orderId, string $reason = '管理员手动补发'): array
    {
        try {
            // 确保orderId是字符串类型
            $orderId = (string)$orderId;

            // 获取订单详情
            $order = Db::table('purchase_orders')->where('id', $orderId)->find();
            if (!$order) {
                return ['success' => false, 'message' => '订单不存在'];
            }

            // 检查订单状态
            if ($order['status'] != 4) {
                return ['success' => false, 'message' => '只有失败的订单才能补发'];
            }

            Log::info('开始补发订单', [
                'order_id' => $orderId,
                'user_id' => $order['user_id'],
                'item_id' => $order['item_id'],
                'quantity' => $order['quantity'],
                'reason' => $reason
            ]);

            // 获取商品信息 - 从iteminfo表获取物品信息
            $itemInfo = Db::table('iteminfo')->where('ItemID', $order['item_id'])->find();
            if (!$itemInfo) {
                return ['success' => false, 'message' => '物品信息不存在'];
            }

            // 构造兼容的商品信息格式
            $compatibleItemInfo = [
                'item' => $itemInfo['ItemID'],
                'name' => $itemInfo['ItemName'],
                'io' => 1, // 默认值
                'ioo' => 0 // 默认值
            ];

            // 调用发送服务重新发送
            $itemDeliveryService = new \app\service\ItemDeliveryService();
            $deliveryResult = $itemDeliveryService->deliverItemToGame(
                $order['user_id'],
                $compatibleItemInfo,
                $order['quantity'],
                'resend'
            );

            if ($deliveryResult['success']) {
                // 更新订单状态为已完成
                $updateResult = $this->updateOrderStatus($orderId, 3, $reason);

                if ($updateResult) {
                    Log::info('订单补发成功', [
                        'order_id' => $orderId,
                        'delivery_result' => $deliveryResult
                    ]);

                    return [
                        'success' => true,
                        'message' => '订单补发成功',
                        'delivery_result' => $deliveryResult
                    ];
                } else {
                    Log::error('订单补发后状态更新失败', ['order_id' => $orderId]);
                    return ['success' => false, 'message' => '补发成功但状态更新失败'];
                }
            } else {
                Log::error('订单补发失败', [
                    'order_id' => $orderId,
                    'error' => $deliveryResult['message']
                ]);

                // 更新错误信息
                Db::table('purchase_orders')
                    ->where('id', $orderId)
                    ->update([
                        'error_message' => '补发失败: ' . $deliveryResult['message'],
                        'update_time' => date('Y-m-d H:i:s')
                    ]);

                return [
                    'success' => false,
                    'message' => '补发失败: ' . $deliveryResult['message']
                ];
            }

        } catch (\Exception $e) {
            Log::error('补发订单异常: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => '补发订单异常: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 处理退款
     *
     * @param string|int $orderId 订单ID
     * @param float $refundAmount 退款金额
     * @param string $reason 退款原因
     * @return bool
     */
    public function processRefund(string|int $orderId, float $refundAmount, string $reason = ''): bool
    {
        try {
            // 确保orderId是字符串类型
            $orderId = (string)$orderId;

            // 获取订单详情
            $order = Db::table('purchase_orders')->where('id', $orderId)->find();
            if (!$order) {
                Log::error('退款失败：订单不存在', ['order_id' => $orderId]);
                return false;
            }

            // 检查订单状态（只有已完成的订单才能退款）
            if ($order['status'] != 3) {
                Log::error('退款失败：只有已完成的订单才能退款', ['order_id' => $orderId, 'status' => $order['status']]);
                return false;
            }

            // 检查退款金额
            if ($refundAmount <= 0 || $refundAmount > $order['total_price']) {
                Log::error('退款失败：退款金额无效', ['order_id' => $orderId, 'refund_amount' => $refundAmount, 'total_price' => $order['total_price']]);
                return false;
            }

            // 开始事务
            Db::startTrans();

            try {
                // 更新订单状态为已退款
                $updateResult = Db::table('purchase_orders')
                    ->where('id', $orderId)
                    ->update([
                        'status' => 6, // 已退款
                        'error_message' => $reason,
                        'update_time' => date('Y-m-d H:i:s')
                    ]);

                if (!$updateResult) {
                    throw new \Exception('更新订单状态失败');
                }

                // 这里应该调用货币服务来退还用户货币
                // 暂时记录日志，实际实现需要根据具体的货币系统
                Log::info('订单退款处理', [
                    'order_id' => $orderId,
                    'user_id' => $order['user_id'],
                    'currency_type' => $order['currency_type'],
                    'refund_amount' => $refundAmount,
                    'reason' => $reason
                ]);

                // 提交事务
                Db::commit();

                // 清除相关缓存
                $this->clearOrderCache($orderId);

                return true;

            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            Log::error('处理退款失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 批量完成订单
     *
     * @param array $orderIds 订单ID数组
     * @param string $reason 操作原因
     * @return bool
     */
    public function batchCompleteOrders(array $orderIds, string $reason = ''): bool
    {
        try {
            $successCount = 0;
            foreach ($orderIds as $orderId) {
                if ($this->updateOrderStatus($orderId, 3, $reason)) {
                    $successCount++;
                }
            }

            Log::info('批量完成订单', [
                'total' => count($orderIds),
                'success' => $successCount,
                'reason' => $reason
            ]);

            return $successCount > 0;

        } catch (\Exception $e) {
            Log::error('批量完成订单失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 批量取消订单
     *
     * @param array $orderIds 订单ID数组
     * @param string $reason 操作原因
     * @return bool
     */
    public function batchCancelOrders(array $orderIds, string $reason = ''): bool
    {
        try {
            $successCount = 0;
            foreach ($orderIds as $orderId) {
                if ($this->updateOrderStatus($orderId, 5, $reason)) {
                    $successCount++;
                }
            }

            Log::info('批量取消订单', [
                'total' => count($orderIds),
                'success' => $successCount,
                'reason' => $reason
            ]);

            return $successCount > 0;

        } catch (\Exception $e) {
            Log::error('批量取消订单失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 批量导出订单
     *
     * @param array $orderIds 订单ID数组
     * @return string|false 导出文件路径或false
     */
    public function batchExportOrders(array $orderIds)
    {
        try {
            // 获取订单数据
            $orders = Db::table('purchase_orders')
                ->whereIn('id', $orderIds)
                ->select()
                ->toArray();

            if (empty($orders)) {
                return false;
            }

            // 生成CSV文件
            $filename = 'orders_export_' . date('YmdHis') . '.csv';
            $filepath = app()->getRuntimePath() . 'export/' . $filename;

            // 确保目录存在
            $dir = dirname($filepath);
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }

            $file = fopen($filepath, 'w');
            if (!$file) {
                return false;
            }

            // 写入BOM头，解决中文乱码
            fwrite($file, "\xEF\xBB\xBF");

            // 写入表头
            $headers = ['订单号', '用户ID', '商品ID', '商品名称', '数量', '单价', '总价', '货币类型', '状态', '创建时间'];
            fputcsv($file, $headers);

            // 写入数据
            foreach ($orders as $order) {
                $row = [
                    $order['order_no'],
                    $order['user_id'],
                    $order['item_id'],
                    $order['item_name'],
                    $order['quantity'],
                    $order['unit_price'],
                    $order['total_price'],
                    $order['currency_type'],
                    $this->getStatusText($order['status']),
                    $order['create_time']
                ];
                fputcsv($file, $row);
            }

            fclose($file);

            return $filename;

        } catch (\Exception $e) {
            Log::error('批量导出订单失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 导出订单数据
     *
     * @param string $format 导出格式
     * @param array $filters 筛选条件
     * @return string|false 导出文件路径或false
     */
    public function exportOrders(string $format = 'csv', array $filters = [])
    {
        try {
            // 构建查询条件
            $where = ['delete_time' => null];
            $whereRaw = [];

            if (!empty($filters['search'])) {
                $whereRaw[] = "(order_no LIKE '%{$filters['search']}%' OR user_id LIKE '%{$filters['search']}%' OR item_name LIKE '%{$filters['search']}%')";
            }

            if (!empty($filters['status'])) {
                $where['status'] = $filters['status'];
            }

            if (!empty($filters['date_range'])) {
                switch ($filters['date_range']) {
                    case 'today':
                        $whereRaw[] = "DATE(create_time) = CURDATE()";
                        break;
                    case 'yesterday':
                        $whereRaw[] = "DATE(create_time) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)";
                        break;
                    case 'week':
                        $whereRaw[] = "create_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
                        break;
                    case 'month':
                        $whereRaw[] = "create_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
                        break;
                }
            }

            // 获取订单数据
            $query = Db::table('purchase_orders')->where($where);
            foreach ($whereRaw as $condition) {
                $query->whereRaw($condition);
            }

            $orders = $query->order('create_time', 'desc')->select()->toArray();

            if (empty($orders)) {
                return false;
            }

            // 生成文件
            $filename = 'orders_export_' . date('YmdHis') . '.' . $format;
            $filepath = app()->getRuntimePath() . 'export/' . $filename;

            // 确保目录存在
            $dir = dirname($filepath);
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }

            if ($format === 'csv') {
                return $this->exportToCsv($orders, $filepath) ? $filename : false;
            }

            return false;

        } catch (\Exception $e) {
            Log::error('导出订单数据失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 导出为CSV格式
     *
     * @param array $orders 订单数据
     * @param string $filepath 文件路径
     * @return bool
     */
    private function exportToCsv(array $orders, string $filepath): bool
    {
        try {
            $file = fopen($filepath, 'w');
            if (!$file) {
                return false;
            }

            // 写入BOM头，解决中文乱码
            fwrite($file, "\xEF\xBB\xBF");

            // 写入表头
            $headers = ['订单号', '用户ID', '商品ID', '商品名称', '数量', '单价', '总价', '货币类型', '状态', '错误信息', '创建时间', '更新时间'];
            fputcsv($file, $headers);

            // 写入数据
            foreach ($orders as $order) {
                $row = [
                    $order['order_no'],
                    $order['user_id'],
                    $order['item_id'],
                    $order['item_name'],
                    $order['quantity'],
                    $order['unit_price'],
                    $order['total_price'],
                    $order['currency_type'],
                    $this->getStatusText($order['status']),
                    $order['error_message'] ?? '',
                    $order['create_time'],
                    $order['update_time']
                ];
                fputcsv($file, $row);
            }

            fclose($file);
            return true;

        } catch (\Exception $e) {
            Log::error('导出CSV失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取状态文本
     *
     * @param int $status 状态码
     * @return string
     */
    private function getStatusText(int $status): string
    {
        $statusMap = [
            1 => '待处理',
            2 => '处理中',
            3 => '已完成',
            4 => '失败',
            5 => '已取消',
            6 => '已退款'
        ];

        return $statusMap[$status] ?? '未知';
    }

    /**
     * 修复角色名编码问题
     *
     * @param string $originalCharName 原始角色名
     * @return string 修复后的角色名
     */
    private function fixCharacterNameEncoding(string $originalCharName): string
    {
        if (empty($originalCharName)) {
            return $originalCharName;
        }

        // 检查是否已经是有效的UTF-8编码且包含中文字符
        if (mb_check_encoding($originalCharName, 'UTF-8') && preg_match('/[\x{4e00}-\x{9fff}]/u', $originalCharName)) {
            return $originalCharName;
        }

        // 由于数据库连接已经使用GBK字符集，通常不需要额外转换
        // 但如果仍有编码问题，可以尝试以下方法
        $encodingMethods = [
            // 方法1：直接从GBK转换
            'GBK_direct' => function($data) {
                return mb_convert_encoding($data, 'UTF-8', 'GBK');
            },
            // 方法2：使用iconv进行转换
            'iconv_GBK' => function($data) {
                return iconv('GBK', 'UTF-8//IGNORE', $data);
            }
        ];

        foreach ($encodingMethods as $encoding => $method) {
            try {
                $converted = $method($originalCharName);

                // 检查转换结果是否包含中文字符且是有效的UTF-8
                if (!empty($converted) && mb_check_encoding($converted, 'UTF-8') && preg_match('/[\x{4e00}-\x{9fff}]/u', $converted)) {
                    Log::debug('OrderService成功使用' . $encoding . '编码转换角色名', [
                        'original' => $originalCharName,
                        'converted' => $converted
                    ]);
                    return $converted;
                }
            } catch (\Exception $e) {
                Log::debug('OrderService ' . $encoding . '编码转换失败: ' . $e->getMessage());
            }
        }

        // 如果所有转换方法都失败，但是包含可打印字符，保持原样
        if (ctype_print($originalCharName)) {
            return $originalCharName;
        }

        // 最后的备选方案：返回默认值
        Log::warning('OrderService角色名编码转换失败，使用默认值', [
            'original' => $originalCharName,
            'hex' => bin2hex($originalCharName)
        ]);

        return '角色名显示异常';
    }
}