<?php

namespace app\service;

use think\facade\Db;
use think\facade\Log;
use app\model\PurchaseOrder;
use app\service\CacheService;
use Exception;

/**
 * 物品发送服务类
 * 负责处理购买物品后发送到游戏商城的逻辑
 * 
 * 设计原则：
 * 1. 单一职责原则：专门负责物品发送逻辑
 * 2. 开闭原则：可扩展不同类型的物品发送方式
 * 3. 依赖倒置原则：依赖抽象的数据库接口而非具体实现
 */
class ItemDeliveryService
{
    /**
     * 远程游戏数据库连接配置
     */
    private $remoteDbConfig = [];

    /**
     * 本地数据库连接
     */
    private $localDb;

    /**
     * 远程数据库连接
     */
    private $remoteDb;

    /**
     * 构造函数
     * 初始化数据库连接
     */
    public function __construct()
    {
        // 初始化远程数据库配置
        $this->remoteDbConfig = [
            'type' => 'mysql',
            'hostname' => env('REMOTE_DB_HOST'),
            'database' => env('ITEM_DB_NAME'),
            'username' => env('REMOTE_DB_USER'),
            'password' => env('REMOTE_DB_PASS'),
            'hostport' => env('REMOTE_DB_PORT'),
            'charset' => env('REMOTE_DB_CHARSET'),
            'prefix' => '',
        ];
        
        $this->initializeConnections();
    }

    /**
     * 初始化数据库连接
     * 
     * @throws Exception
     */
    private function initializeConnections()
    {
        try {
            // 本地数据库连接
            $this->localDb = Db::connect();
            
            // 远程数据库连接
            $this->remoteDb = Db::connect($this->remoteDbConfig);
            
        } catch (Exception $e) {
            Log::error('数据库连接失败: ' . $e->getMessage());
            throw new Exception('数据库连接失败');
        }
    }

    /**
     * 购买物品并发送到游戏商城
     * 
     * @param string $userId 用户游戏ID
     * @param int $itemId 商品ID
     * @param int $quantity 购买数量
     * @param string $source 发送来源标识
     * @return array 操作结果
     */
    public function purchaseAndDeliverItem(string $userId, int $itemId, int $quantity = 1, string $source = 'shop'): array
    {
        try {
            Log::info("开始购买流程", ['user_id' => $userId, 'item_id' => $itemId, 'quantity' => $quantity, 'source' => $source]);

            // 1. 验证商品信息
            $itemInfo = $this->validateItem($itemId);
            if (!$itemInfo) {
                Log::error("商品验证失败", ['item_id' => $itemId]);
                return $this->createErrorResponse('商品不存在或已下架');
            }
            Log::info("商品验证成功", ['item_id' => $itemInfo['id'], 'item_name' => $itemInfo['name'], 'price' => $itemInfo['price'], 'price_type' => $itemInfo['price_type']]);

            // 2. 验证用户购买限制
            $limitCheck = $this->checkPurchaseLimit($userId, $itemId, $quantity, $itemInfo);
            if (!$limitCheck['success']) {
                Log::error("购买限制检查失败", ['user_id' => $userId, 'item_id' => $itemId, 'message' => $limitCheck['message']]);
                return $this->createErrorResponse($limitCheck['message']);
            }
            Log::info("购买限制检查通过");

            // 3. 验证用户余额
            $totalPrice = $itemInfo['price'] * $quantity;
            $balanceCheck = $this->checkUserBalance($userId, $itemInfo['price_type'], $totalPrice);
            if (!$balanceCheck['success']) {
                Log::error("余额检查失败", ['user_id' => $userId, 'price_type' => $itemInfo['price_type'], 'total_price' => $totalPrice, 'message' => $balanceCheck['message']]);
                return $this->createErrorResponse($balanceCheck['message']);
            }
            Log::info("余额检查通过", ['current_balance' => $balanceCheck['current_balance'], 'required' => $totalPrice]);

            // 4. 开始事务处理
            $this->localDb->startTrans();

            try {
                $purchaseStartTime = microtime(true);

                // 5. 扣除用户余额（传递余额检查结果以避免重复查询）
                $this->deductUserBalance($userId, $itemInfo['price_type'], $itemInfo['price'] * $quantity, $balanceCheck);

                // 6. 发送物品到游戏商城
                $deliveryResult = $this->deliverItemToGame($userId, $itemInfo, $quantity, $source);

                if (!$deliveryResult['success']) {
                    throw new Exception($deliveryResult['message']);
                }

                // 7. 记录购买订单
                $orderNo = $this->recordPurchaseOrder($userId, $itemInfo, $quantity, $deliveryResult['unique_num']);

                // 8. 清除购买历史相关缓存
                $this->clearPurchaseHistoryCache($userId);

                // 9. 提交事务
                $this->localDb->commit();

                $purchaseEndTime = microtime(true);
                $purchaseDuration = round(($purchaseEndTime - $purchaseStartTime) * 1000, 2);

                Log::info("购买流程完成", [
                    'user_id' => $userId,
                    'item_id' => $itemId,
                    'order_no' => $orderNo,
                    'total_duration_ms' => $purchaseDuration,
                    'delivery_duration_ms' => $deliveryResult['duration_ms'] ?? 0
                ]);

                return $this->createSuccessResponse('购买成功，物品已发送到游戏商城', [
                    'order_no' => $orderNo,
                    'unique_num' => $deliveryResult['unique_num'],
                    'item_name' => $itemInfo['name'],
                    'quantity' => $quantity,
                    'performance' => [
                        'total_duration_ms' => $purchaseDuration,
                        'delivery_duration_ms' => $deliveryResult['duration_ms'] ?? 0
                    ]
                ]);

            } catch (Exception $e) {
                $this->localDb->rollback();
                throw $e;
            }

        } catch (Exception $e) {
            Log::error('购买物品失败: ' . $e->getMessage());
            return $this->createErrorResponse('购买失败: ' . $e->getMessage());
        }
    }

    /**
     * 验证商品信息
     * 
     * @param int $itemId 商品ID
     * @return array|false 商品信息或false
     */
    public function validateItem(int $itemId)
    {
        $itemInfo = $this->localDb->table('pointshop')
            ->where('id', $itemId)
            ->where('status', 1)
            ->find();
        
        // 如果商品不存在，直接返回false
        if (!$itemInfo) {
            return false;
        }
        
        return $itemInfo;
    }

    /**
     * 检查购买限制
     * 
     * @param string $userId 用户ID
     * @param int $itemId 商品ID
     * @param int $quantity 购买数量
     * @param array $itemInfo 商品信息
     * @return array 检查结果
     */
    private function checkPurchaseLimit(string $userId, int $itemId, int $quantity, array $itemInfo): array
    {
        // 根据限制类型检查购买记录
        $restrictionType = (int)$itemInfo['restriction'];
        
        // 添加调试日志
        Log::info("购买限制检查 - 用户ID: {$userId}, 商品ID: {$itemId}, 购买数量: {$quantity}, 限制类型: {$restrictionType}, 限制数量: {$itemInfo['limit_quantity']}");
        
        // 如果是不限购策略(0)，完全不限制购买
        if ($restrictionType === 0) {
            Log::info('不限购商品检查通过 - 无任何限制');
            return ['success' => true];
        }
        
        // 对于有限购的商品，检查历史购买记录
        $timeCondition = $this->getTimeConditionByRestriction($restrictionType);
        
        if ($timeCondition) {
            $purchaseCount = $this->localDb->table('purchase_orders')
                ->where('user_id', $userId)
                ->where('item_id', $itemId)
                ->where('status', 3)
                ->where('create_time', '>=', $timeCondition)
                ->sum('quantity');

            Log::info('历史购买记录检查', [
                'purchase_count' => $purchaseCount,
                'new_quantity' => $quantity,
                'total_after_purchase' => $purchaseCount + $quantity,
                'limit' => $itemInfo['limit_quantity']
            ]);

            if ($purchaseCount + $quantity > $itemInfo['limit_quantity']) {
                return ['success' => false, 'message' => '超过购买限制'];
            }
        }

        return ['success' => true];
    }

    /**
     * 根据限制类型获取时间条件
     * 
     * @param int $restrictionType 限制类型
     * @return string|null 时间条件
     */
    private function getTimeConditionByRestriction(int $restrictionType): ?string
    {
        switch ($restrictionType) {
            case 0: // 不限购
                return null;
            case 1: // 账号限购一次
                return null;
            case 2: // 每日一次
                return date('Y-m-d 00:00:00');
            case 3: // 每周一次
                return date('Y-m-d 00:00:00', strtotime('this week'));
            case 4: // 每月一次
                return date('Y-m-01 00:00:00');
            default:
                return null;
        }
    }

    /**
     * 检查用户余额
     * 
     * @param string $userId 用户ID
     * @param string $priceType 货币类型
     * @param int $totalPrice 总价格
     * @return array 检查结果
     */
    public function checkUserBalance(string $userId, string $priceType, int $totalPrice): array
    {
        $balanceField = $this->getCurrencyField($priceType);
        if (!$balanceField) {
            return ['success' => false, 'message' => '不支持的货币类型'];
        }

        $userBalance = 0;
        
        // 根据货币类型选择不同的数据库和表
        if ($balanceField === 'c_coin') {
            // C币从本地数据库的c_coin表获取，需要将user_id转换为数字
            $numericUserId = is_numeric($userId) ? (int)$userId : (int)preg_replace('/[^0-9]/', '', $userId);
            $userBalance = $this->localDb->table('c_coin')
                ->where('user_id', $numericUserId)
                ->value('balance');
        } elseif ($balanceField === 'coin') {
            // 泡点从seal_member数据库的idtable1-5表获取，使用缓存优化
            $cacheKey = 'user_balance:coin:' . $userId;
            $balanceData = \app\service\CacheService::remember($cacheKey, function() use ($userId) {
                $startTime = microtime(true);
                $totalBalance = 0;
                $tableDistribution = [];

                // 使用已配置的seal_member连接，避免重复配置
                for ($i = 1; $i <= 5; $i++) {
                    try {
                        $tablePoint = Db::connect('seal_member')
                            ->table('idtable' . $i)
                            ->where('id', $userId)
                            ->value('point');
                        if ($tablePoint !== null && $tablePoint > 0) {
                            $tableDistribution[$i] = $tablePoint;
                            $totalBalance += $tablePoint;
                        }
                    } catch (\Exception $e) {
                        Log::warning("查询idtable{$i}失败: " . $e->getMessage());
                        continue;
                    }
                }

                $endTime = microtime(true);
                $duration = round(($endTime - $startTime) * 1000, 2);
                Log::info("泡点余额查询完成", ['user_id' => $userId, 'balance' => $totalBalance, 'duration_ms' => $duration]);

                return [
                    'balance' => $totalBalance,
                    'table_distribution' => $tableDistribution
                ];
            }, 300); // 缓存5分钟

            $userBalance = $balanceData['balance'];
        } elseif ($balanceField === 'silver') {
            // 积分从seal_web数据库的sealmember表获取
            $sealWebConfig = [
                'type' => 'mysql',
                'hostname' => env('REMOTE_DB_HOST'),
                'database' => env('SEAL_WEB_DB_NAME'),
                'username' => env('REMOTE_DB_USER'),
                'password' => env('REMOTE_DB_PASS'),
                'hostport' => env('REMOTE_DB_PORT'),
                'charset' => env('REMOTE_DB_CHARSET'),
            ];
            $userBalance = Db::connect($sealWebConfig)
                 ->table('sealmember')
                 ->where('id', $userId)
                 ->value('hahapoint');
        }

        $userBalance = $userBalance ?: 0;
        if ($userBalance < $totalPrice) {
            return ['success' => false, 'message' => '余额不足', 'current_balance' => $userBalance];
        }

        $result = ['success' => true, 'current_balance' => $userBalance];

        // 如果是泡点类型，添加表分布信息以供扣除时使用
        if ($balanceField === 'coin' && isset($balanceData['table_distribution'])) {
            $result['table_distribution'] = $balanceData['table_distribution'];
        }

        return $result;
    }

    /**
     * 扣除用户余额
     * 
     * @param string $userId 用户ID
     * @param string $priceType 货币类型
     * @param int $amount 扣除金额
     * @throws Exception
     */
    private function deductUserBalance(string $userId, string $priceType, int $amount, ?array $balanceCheckResult = null): void
    {
        // 如果金额为0，直接返回成功
        if ($amount <= 0) {
            return;
        }

        $startTime = microtime(true);
        $balanceField = $this->getCurrencyField($priceType);

        // 根据货币类型选择不同的数据库和表
        if ($balanceField === 'c_coin') {
            // C币从本地数据库的c_coin表扣除，需要将user_id转换为数字
            $numericUserId = is_numeric($userId) ? (int)$userId : (int)preg_replace('/[^0-9]/', '', $userId);

            // 先检查用户是否存在
            $currentBalance = $this->localDb->table('c_coin')
                ->where('user_id', $numericUserId)
                ->value('balance');

            if ($currentBalance === null) {
                throw new Exception("用户C币记录不存在，用户ID: {$numericUserId}");
            }

            if ($currentBalance < $amount) {
                throw new Exception("C币余额不足，当前余额: {$currentBalance}，需要扣除: {$amount}");
            }

            // 使用update方法直接更新余额
            $affectedRows = $this->localDb->table('c_coin')
                ->where('user_id', $numericUserId)
                ->update(['balance' => $currentBalance - $amount]);
        } elseif ($balanceField === 'coin') {
            // 泡点从seal_member数据库的idtable1-5表扣除
            // 优化：使用缓存的余额信息，避免重复查询
            $userTables = [];

            // 如果有余额检查结果且包含表分布信息，直接使用
            if ($balanceCheckResult && isset($balanceCheckResult['table_distribution'])) {
                $userTables = $balanceCheckResult['table_distribution'];
            } else {
                // 否则重新查询（兼容旧代码）
                for ($i = 1; $i <= 5; $i++) {
                    $tablePoint = Db::connect('seal_member')
                        ->table('idtable' . $i)
                        ->where('id', $userId)
                        ->value('point');
                    if ($tablePoint !== null && $tablePoint > 0) {
                        $userTables[$i] = $tablePoint;
                    }
                }
            }

            if (empty($userTables)) {
                throw new Exception('用户泡点余额不足');
            }
            
            // 按表顺序逐个扣除泡点
            $remainingAmount = $amount;
            $result = false;
            
            foreach ($userTables as $tableIndex => $tableBalance) {
                if ($remainingAmount <= 0) {
                    break;
                }
                
                $deductFromThisTable = min($remainingAmount, $tableBalance);
                $newTableBalance = $tableBalance - $deductFromThisTable;
                
                $updateResult = Db::connect('seal_member')
                    ->table('idtable' . $tableIndex)
                    ->where('id', $userId)
                    ->update(['point' => $newTableBalance]);
                
                if ($updateResult) {
                    $result = true;
                    $remainingAmount -= $deductFromThisTable;
                } else {
                    throw new Exception('更新idtable' . $tableIndex . '失败');
                }
            }
            
            if ($remainingAmount > 0) {
                throw new Exception('泡点余额不足，无法完成扣除');
            }
        } elseif ($balanceField === 'silver') {
            // 积分从seal_web数据库的sealmember表扣除
            $sealWebConfig = [
                'type' => 'mysql',
                'hostname' => env('REMOTE_DB_HOST'),
                'database' => env('SEAL_WEB_DB_NAME'),
                'username' => env('REMOTE_DB_USER'),
                'password' => env('REMOTE_DB_PASS'),
                'hostport' => env('REMOTE_DB_PORT'),
                'charset' => env('REMOTE_DB_CHARSET'),
            ];
            $result = Db::connect($sealWebConfig)
                ->table('sealmember')
                ->where('id', $userId)
                ->dec('hahapoint', $amount);
        } else {
            throw new Exception('不支持的货币类型');
        }

        // 对于C币，检查是否成功扣除（affectedRows应该是影响的行数）
        if ($balanceField === 'c_coin') {
            if ($affectedRows === false || $affectedRows === 0) {
                throw new Exception('C币扣除失败，可能是用户不存在或余额不足');
            }
        } else {
            // 对于其他货币类型，保持原有逻辑
            if (!$result) {
                throw new Exception('扣除余额失败');
            }
        }
        
        // 清除用户余额缓存
        $cacheKey = 'user_balance:' . $userId;
        CacheService::delete($cacheKey);
    }

    /**
     * 发送物品到游戏商城
     * 
     * @param string $userId 用户游戏ID
     * @param array $itemInfo 商品信息
     * @param int $quantity 数量
     * @param string $source 发送来源
     * @return array 发送结果
     */
    public function deliverItemToGame(string $userId, array $itemInfo, int $quantity, string $source): array
    {
        try {
            $startTime = microtime(true);
            Log::info("开始发送物品到游戏", ['user_id' => $userId, 'item_id' => $itemInfo['item'], 'quantity' => $quantity]);

            // 获取发送模式，默认为批量发送
            $deliveryMode = (int)($itemInfo['delivery_mode'] ?? 1);

            $result = null;
            if ($deliveryMode === 2) {
                // 逐条发送模式
                $result = $this->deliverItemsIndividually($userId, $itemInfo, $quantity, $source);
            } else {
                // 批量发送模式（默认）
                $result = $this->deliverItemsBatch($userId, $itemInfo, $quantity, $source);
            }

            $endTime = microtime(true);
            $duration = round(($endTime - $startTime) * 1000, 2); // 转换为毫秒
            Log::info("物品发送完成", ['duration_ms' => $duration, 'success' => $result['success']]);

            return $result;

        } catch (Exception $e) {
            Log::error('发送物品到游戏失败: ' . $e->getMessage());
            return ['success' => false, 'message' => '发送物品失败'];
        }
    }

    /**
     * 批量发送物品
     * 
     * @param string $userId 用户游戏ID
     * @param array $itemInfo 商品信息
     * @param int $quantity 数量
     * @param string $source 发送来源
     * @return array 发送结果
     */
    private function deliverItemsBatch(string $userId, array $itemInfo, int $quantity, string $source): array
    {
        $startTime = microtime(true);

        $data = [
            'ItemType' => (int)$itemInfo['item'],
            'ItemOp1' => (int)$itemInfo['io'] * $quantity - 1, // 使用商品设置的数量乘以购买数量，然后减1（游戏机制要求）
            'ItemOp2' => (int)($itemInfo['ioo'] ?? 0),
            'ItemLimit' => 0,
            'OwnerID' => $userId,
            'OwnerDate' => date('Y-m-d H:i:s'),
            'bxaid' => $source
        ];

        // 添加重试机制，最多重试3次
        $maxRetries = 3;
        $retryCount = 0;
        $uniqueNum = null;

        while ($retryCount < $maxRetries) {
            try {
                $dbStartTime = microtime(true);
                $uniqueNum = $this->remoteDb->table('seal_item')->insertGetId($data);
                $dbEndTime = microtime(true);
                $dbDuration = round(($dbEndTime - $dbStartTime) * 1000, 2);

                if ($uniqueNum) {
                    Log::info("批量发送物品成功", [
                        'user_id' => $userId,
                        'item_id' => $itemInfo['item'],
                        'quantity' => $quantity,
                        'unique_num' => $uniqueNum,
                        'db_duration_ms' => $dbDuration,
                        'retry_count' => $retryCount
                    ]);
                    break;
                }
            } catch (Exception $e) {
                $retryCount++;
                Log::warning("批量发送物品重试", [
                    'user_id' => $userId,
                    'item_id' => $itemInfo['item'],
                    'retry_count' => $retryCount,
                    'error' => $e->getMessage()
                ]);

                if ($retryCount >= $maxRetries) {
                    throw new Exception('批量发送物品到游戏失败，已重试' . $maxRetries . '次');
                }

                // 重试前等待一小段时间
                usleep(100000); // 100ms
            }
        }

        if (!$uniqueNum) {
            throw new Exception('批量发送物品到游戏失败');
        }

        $endTime = microtime(true);
        $totalDuration = round(($endTime - $startTime) * 1000, 2);

        return [
            'success' => true,
            'unique_num' => $uniqueNum,
            'delivery_mode' => 'batch',
            'duration_ms' => $totalDuration
        ];
    }

    /**
     * 逐条发送物品
     * 
     * @param string $userId 用户游戏ID
     * @param array $itemInfo 商品信息
     * @param int $quantity 数量
     * @param string $source 发送来源
     * @return array 发送结果
     */
    private function deliverItemsIndividually(string $userId, array $itemInfo, int $quantity, string $source): array
    {
        $uniqueNums = [];
        $successCount = 0;
        
        // 逐个发送物品
        for ($i = 0; $i < $quantity; $i++) {
            try {
                $data = [
                    'ItemType' => (int)$itemInfo['item'],
                    'ItemOp1' => (int)$itemInfo['io'] - 1, // 使用商品设置的数量减1（游戏机制要求）
                    'ItemOp2' => (int)($itemInfo['ioo'] ?? 0),
                    'ItemLimit' => 0,
                    'OwnerID' => $userId,
                    'OwnerDate' => date('Y-m-d H:i:s'),
                    'bxaid' => $source
                ];

                $uniqueNum = $this->remoteDb->table('seal_item')->insertGetId($data);
                
                if ($uniqueNum) {
                    $uniqueNums[] = $uniqueNum;
                    $successCount++;
                } else {
                    Log::warning("逐条发送物品失败 - 用户ID: {$userId}, 物品ID: {$itemInfo['item']}, 第" . ($i + 1) . "个");
                }
            } catch (Exception $e) {
                Log::error("逐条发送物品异常 - 用户ID: {$userId}, 物品ID: {$itemInfo['item']}, 第" . ($i + 1) . "个: " . $e->getMessage());
            }
        }
        
        if ($successCount === 0) {
            throw new Exception('逐条发送物品全部失败');
        } elseif ($successCount < $quantity) {
            Log::warning("逐条发送物品部分失败 - 用户ID: {$userId}, 物品ID: {$itemInfo['item']}, 成功: {$successCount}/{$quantity}");
        }
        
        Log::info("逐条发送物品完成 - 用户ID: {$userId}, 物品ID: {$itemInfo['item']}, 成功数量: {$successCount}/{$quantity}");
        
        return [
            'success' => true, 
            'unique_num' => $uniqueNums[0], // 返回第一个唯一编号用于订单记录
            'unique_nums' => $uniqueNums, // 返回所有唯一编号
            'success_count' => $successCount,
            'total_count' => $quantity,
            'delivery_mode' => 'individual'
        ];
    }

    /**
     * 记录购买订单
     * 
     * @param string $userId 用户ID
     * @param array $itemInfo 商品信息
     * @param int $quantity 购买数量
     * @param int $uniqueNum 游戏唯一编号
     * @return string 订单号
     */
    private function recordPurchaseOrder(string $userId, array $itemInfo, int $quantity, int $uniqueNum): string
    {
        $orderNo = 'PO' . date('YmdHis') . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
        
        $orderData = [
            'order_no' => $orderNo,
            'user_id' => $userId,
            'item_id' => $itemInfo['item'], // 修复：使用游戏物品ID而不是数据库主键ID
            'item_name' => $itemInfo['name'],
            'quantity' => $quantity,
            'currency_type' => $itemInfo['price_type'],
            'unit_price' => $itemInfo['price'],
            'total_price' => $itemInfo['price'] * $quantity,
            'unique_num' => $uniqueNum,
            'status' => 3,
            'create_time' => date('Y-m-d H:i:s'),
            'update_time' => date('Y-m-d H:i:s')
        ];

        $this->localDb->table('purchase_orders')->insert($orderData);
        
        return $orderNo;
    }

    /**
     * 根据货币类型获取对应的数据库字段
     * 
     * @param string $priceType 货币类型
     * @return string|null 字段名
     */
    private function getCurrencyField(string $priceType): ?string
    {
        $currencyMap = [
            // 支持字符串格式的货币类型（来自ItemService）
            'coin' => 'coin',      // 泡点
            'silver' => 'silver',  // 积分
            'c_coin' => 'c_coin',  // C币
            // 支持数字格式的货币类型（来自数据库）
            '1' => 'coin',         // 泡点
            '2' => 'silver',       // 积分
            '3' => 'c_coin'        // C币
        ];

        return $currencyMap[$priceType] ?? null;
    }

    /**
     * 创建成功响应
     * 
     * @param string $message 消息
     * @param array $data 数据
     * @return array
     */
    private function createSuccessResponse(string $message, array $data = []): array
    {
        return [
            'success' => true,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 创建错误响应
     * 
     * @param string $message 错误消息
     * @return array
     */
    private function createErrorResponse(string $message): array
    {
        return [
            'success' => false,
            'message' => $message,
            'data' => []
        ];
    }

    /**
     * 清除购买历史相关缓存
     * 
     * @param string $userId 用户ID
     * @return void
     */
    private function clearPurchaseHistoryCache(string $userId): void
    {
        try {
            // 清除购买历史列表缓存（所有可能的筛选条件组合）
            CacheService::clearByPrefix('purchase_history:' . $userId . ':');
            
            // 清除用户统计缓存
            CacheService::delete('user_statistics:' . $userId);
            
            // 清除订单统计缓存
            CacheService::clearByPrefix('order_statistics:' . $userId . ':');
            
            Log::info('已清除用户购买历史缓存: ' . $userId);
            
        } catch (Exception $e) {
            Log::error('清除购买历史缓存失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取用户购买历史
     * 
     * @param string $userId 用户ID
     * @param int $limit 限制数量
     * @return array 购买历史
     */
    public function getUserPurchaseHistory(string $userId, int $limit = 10): array
    {
        try {
            $orders = $this->localDb->table('purchase_orders')
                ->where('user_id', $userId)
                ->order('create_time', 'desc')
                ->limit($limit)
                ->select();

            return $this->createSuccessResponse('获取成功', $orders->toArray());

        } catch (Exception $e) {
            Log::error('获取购买历史失败: ' . $e->getMessage());
            return $this->createErrorResponse('获取购买历史失败');
        }
    }

    /**
     * 检查物品是否已发送到游戏
     * 
     * @param int $uniqueNum 游戏物品唯一编号
     * @return bool
     */
    public function checkItemDeliveryStatus(int $uniqueNum): bool
    {
        try {
            $item = $this->remoteDb->table('seal_item')
                ->where('UniqueNum', $uniqueNum)
                ->find();

            return !empty($item);

        } catch (Exception $e) {
            Log::error('检查物品发送状态失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取订单状态
     * 
     * @param string $orderNo 订单号
     * @param string $userId 用户ID
     * @return array 订单状态信息
     */
    public function getOrderStatus(string $orderNo, string $userId): array
    {
        try {
            // 查询订单信息
            $order = $this->localDb->table('purchase_orders')
                ->where('id', $orderNo)
                ->where('user_id', $userId)
                ->find();

            if (!$order) {
                return $this->createErrorResponse('订单不存在');
            }

            // 检查物品是否已发送到游戏
            $deliveryStatus = false;
            if ($order['unique_num']) {
                $deliveryStatus = $this->checkItemDeliveryStatus($order['unique_num']);
            } else {
                $deliveryStatus = false;
            }

            $orderData = [
                'order_id' => $order['id'],
                'item_name' => $order['item_name'],
                'quantity' => $order['quantity'],
                'total_price' => $order['total_price'],
                'price_type' => $order['currency_type'],
                'status' => $order['status'],
                'delivery_status' => $deliveryStatus ? 'delivered' : 'pending',
                'game_unique_num' => $order['unique_num'],
                'create_time' => $order['create_time'],
                'updated_at' => $order['update_time']
            ];

            return $this->createSuccessResponse('查询成功', $orderData);

        } catch (Exception $e) {
            Log::error('查询订单状态失败: ' . $e->getMessage());
            return $this->createErrorResponse('查询订单状态失败');
        }
    }
}