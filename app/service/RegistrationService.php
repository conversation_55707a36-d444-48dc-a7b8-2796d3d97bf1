<?php

namespace app\service;

use think\facade\Db;
use think\facade\Log;

/**
 * 用户注册服务类
 * 负责处理用户注册相关的业务逻辑
 * 遵循单一职责原则：专门处理注册相关功能
 */
class RegistrationService
{
    /**
     * 用户注册验证器
     */
    private $userValidator;
    
    /**
     * 用户数据仓库
     */
    private $userRepository;
    
    /**
     * 密码加密器
     */
    private $passwordEncoder;
    
    /**
     * 构造函数
     * 依赖注入相关服务
     */
    public function __construct()
    {
        $this->userValidator = new UserRegistrationValidator();
        $this->userRepository = new UserRegistrationRepository();
        $this->passwordEncoder = new IdTablePasswordEncoder();
    }
    
    /**
     * 用户注册
     * @param array $userData 用户注册数据
     * @return array 注册结果
     */
    public function register(array $userData): array
    {
        try {

            // 验证用户名格式是否被禁止
            if ($this->userValidator->isForbiddenUsername($userData['username'])) {
                return $this->createErrorResponse(400, '禁止注册的账号');
            }
            
            // 验证用户名是否已存在
            if ($this->userValidator->isUsernameExists($userData['username'])) {
                return $this->createErrorResponse(400, '用户名已存在');
            }
            
            // 验证邮箱是否已存在
            if ($this->userValidator->isEmailExists($userData['email'])) {
                return $this->createErrorResponse(400, '邮箱已被注册');
            }
            
            // 开始数据库事务
            Db::startTrans();
            
            try {
                // 在事务中再次检查用户名是否存在（防止并发注册）
                if ($this->userValidator->isUsernameExists($userData['username'])) {
                    Db::rollback();
                    return $this->createErrorResponse(400, '用户名已存在');
                }
                
                // 加密密码
                $encryptedPassword = $this->passwordEncoder->encode($userData['password']);
                
                // 准备用户数据
                $userInfo = [
                    'id' => $userData['username'],
                    'passwd' => $encryptedPassword,
                    'email' => $userData['email'],
                    'turid' => '', // 身份证号，默认为空
                    'GroupId' => 0 // 用户组ID，默认为0
                ];
                
                // 在idtable中创建用户记录
                $this->userRepository->createIdTableUser($userInfo);
                
                // 在sealmember表中创建用户记录
                $sealMemberInfo = [
                    'id' => $userData['username'],
                    'Pass' => $userData['password'],
                    'email' => $userData['email'],
                    'sex' => 0, // 性别，默认为0
                    'birday' => '0000-00-00', // 生日，默认值
                    'turid' => '', // 身份证号
                    'CkCode' => 1, // 验证码状态
                    'HorCah' => 0, // 现金，默认为0
                    'Z_Cash' => 0, // Z币，默认为0
                    'Z_Cash_T' => 0, // Z币总额，默认为0
                    'hahapoint' => 0, // 积分，默认为0
                    'viptime' => 0, // VIP时间
                    'vipbag_star' => 0, // VIP背包开始时间
                    'vipbag_end' => 0, // VIP背包结束时间
                    'vipbag_next' => 0, // VIP背包下次时间
                    'vipbag_getdat' => 0, // VIP背包获取日期
                    'get_null' => 0 // 获取空值
                ];
                
                $this->userRepository->createSealMemberUser($sealMemberInfo);
                
                // 在usermsgex表中创建用户扩展信息
                $userMsgExInfo = [
                    'userId' => $userData['username'],
                    'email' => $userData['email'],
                    'gold' => 0, // 金币，默认为0
                    'nickName' => '', // 昵称，默认为空
                    'oneTimeChangePwd' => '', // 一次性修改密码，默认为空
                    'isGiftsReferrerGold' => 'N', // 是否赠送推荐人金币，默认为N
                    'Referrer' => $userData['password'], // 推荐人，默认为明文密码
                    'honor' => 0, // 荣誉值，默认为0
                    'xixi' => $userData['password'], // 嘻嘻字段，默认为明文密码
                    'giftForReferrerTime' => '0000-00-00 00:00:00', // 赠送推荐人时间
                    'question' => '', // 密保问题，默认为空
                    'answer' => '', // 密保答案，默认为空
                    'totalGold' => 0, // 总金币，默认为0
                    'vipLevel' => 0, // VIP等级，默认为0
                    'adminLevel' => 0, // 管理员等级，默认为0
                    'ip' => request()->ip() ?? '', // IP地址
                    'onlineTime' => 0, // 在线时间，默认为0
                    'TotalOnlineTime' => 0, // 总在线时间，默认为0
                    'LotteryCount' => 0, // 抽奖次数，默认为0
                    'referee' => '', // 裁判，默认为空
                    'Receive' => 0, // 接收，默认为0
                    'code' => 99999, // 代码，默认为99999
                    'codetime' => 0 // 代码时间，默认为0
                ];
                
                $this->userRepository->createUserMsgEx($userMsgExInfo);
                
                // 提交事务
                Db::commit();
                
                Log::info('用户注册成功: ' . $userData['username']);
                
                return $this->createSuccessResponse([
                    'username' => $userData['username'],
                    'message' => '注册成功，请登录'
                ]);
                
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                Log::error('注册事务失败: ' . $e->getMessage());
                Log::error('错误堆栈: ' . $e->getTraceAsString());
                throw $e;
            }
            
        } catch (\Exception $e) {
            Log::error('用户注册失败: ' . $e->getMessage());
            
            // 检查是否是主键重复错误
            if (strpos($e->getMessage(), 'Duplicate entry') !== false && strpos($e->getMessage(), 'PRIMARY') !== false) {
                return $this->createErrorResponse(400, '用户名已存在，请选择其他用户名');
            }
            
            // 检查是否是邮箱重复错误
            if (strpos($e->getMessage(), 'Duplicate entry') !== false && strpos($e->getMessage(), 'email') !== false) {
                return $this->createErrorResponse(400, '邮箱已被注册，请使用其他邮箱');
            }
            
            return $this->createErrorResponse(500, '注册失败，请稍后重试');
        }
    }
    
    /**
     * 创建成功响应
     * @param array $data 响应数据
     * @return array
     */
    private function createSuccessResponse(array $data): array
    {
        return [
            'code' => 200,
            'message' => 'success',
            'data' => $data
        ];
    }
    
    /**
     * 创建错误响应
     * @param int $code 错误码
     * @param string $message 错误信息
     * @return array
     */
    private function createErrorResponse(int $code, string $message): array
    {
        return [
            'code' => $code,
            'message' => $message,
            'data' => null
        ];
    }
}

/**
 * 用户注册验证器接口
 * 定义用户注册验证的标准契约
 */
interface UserValidatorInterface
{
    /**
     * 检查用户名格式是否被禁止
     * @param string $username 用户名
     * @return bool
     */
    public function isForbiddenUsername(string $username): bool;
    
    /**
     * 检查用户名是否存在
     * @param string $username 用户名
     * @return bool
     */
    public function isUsernameExists(string $username): bool;
    
    /**
     * 检查邮箱是否存在
     * @param string $email 邮箱
     * @return bool
     */
    public function isEmailExists(string $email): bool;
}

/**
 * 用户注册验证器
 * 实现用户注册时的各种验证逻辑
 */
class UserRegistrationValidator implements UserValidatorInterface
{
    /**
     * 检查用户名格式是否被禁止
     * 禁止所有包含"gm"的用户名注册（管理员专用账号）
     * @param string $username 用户名
     * @return bool
     */
    public function isForbiddenUsername(string $username): bool
    {
        $username = strtolower($username);

        // 禁止所有包含"gm"的用户名（不区分大小写）
        if (strpos($username, 'gm') !== false) {
            return true;
        }

        // 禁止其他管理员相关的用户名
        $forbiddenPatterns = [
            'admin',
            'administrator',
            'root',
            'system',
            'operator',
            'master',
            'owner'
        ];

        foreach ($forbiddenPatterns as $pattern) {
            if (strpos($username, $pattern) !== false) {
                return true;
            }
        }

        return false;
    }
    
    /**
     * 检查用户名是否存在
     * 在idtable1-idtable5表中查找
     * @param string $username 用户名
     * @return bool
     */
    public function isUsernameExists(string $username): bool
    {
        try {
            // 遍历idtable1-idtable5表查找用户
            for ($i = 1; $i <= 5; $i++) {
                $user = Db::connect('seal_member')
                    ->table('idtable' . $i)
                    ->where('id', $username)
                    ->find();
                    
                if ($user) {
                    return true;
                }
            }
            
            return false;
            
        } catch (\Exception $e) {
            Log::error('检查用户名失败: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 检查邮箱是否存在
     * 在idtable1-idtable5表中查找
     * @param string $email 邮箱
     * @return bool
     */
    public function isEmailExists(string $email): bool
    {
        try {
            // 遍历idtable1-idtable5表查找邮箱
            for ($i = 1; $i <= 5; $i++) {
                $user = Db::connect('seal_member')
                    ->table('idtable' . $i)
                    ->where('email', $email)
                    ->find();
                    
                if ($user) {
                    return true;
                }
            }
            
            return false;
            
        } catch (\Exception $e) {
            Log::error('检查邮箱失败: ' . $e->getMessage());
            throw $e;
        }
    }
}

/**
 * 用户注册数据仓库接口
 * 定义用户注册数据访问的标准契约
 */
interface UserRegistrationRepositoryInterface
{
    /**
     * 在idtable中创建用户
     * @param array $userInfo 用户信息
     * @return bool
     */
    public function createIdTableUser(array $userInfo): bool;
    
    /**
     * 在sealmember表中创建用户
     * @param array $userInfo 用户信息
     * @return bool
     */
    public function createSealMemberUser(array $userInfo): bool;
    
    /**
     * 在usermsgex表中创建用户扩展信息
     * @param array $userInfo 用户信息
     * @return bool
     */
    public function createUserMsgEx(array $userInfo): bool;
}

/**
 * 用户注册数据仓库
 * 负责用户注册时的数据库操作
 */
class UserRegistrationRepository implements UserRegistrationRepositoryInterface
{
    /**
     * 在idtable中创建用户
     * 根据用户名首字母分配到对应的表
     * @param array $userInfo 用户信息
     * @return bool
     */
    public function createIdTableUser(array $userInfo): bool
    {
        try {
            // 根据用户名首字母确定表号
            $tableNumber = $this->getTableNumberByUsername($userInfo['id']);
            
            // 在对应的表中插入用户
            $result = Db::connect('seal_member')
                ->table('idtable' . $tableNumber)
                ->insert($userInfo);
                
            return $result !== false;
            
        } catch (\Exception $e) {
            Log::error('创建idtable用户失败: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 根据用户名首字母确定应该插入的表号
     * 分配规则：
     * idtable1: a-d
     * idtable2: e-i  
     * idtable3: j-n
     * idtable4: o-s
     * idtable5: t-z
     * @param string $username 用户名
     * @return int 表号(1-5)
     */
    private function getTableNumberByUsername(string $username): int
    {
        $firstLetter = strtolower(substr($username, 0, 1));
        
        if ($firstLetter >= 'a' && $firstLetter <= 'd') {
            return 1;
        } elseif ($firstLetter >= 'e' && $firstLetter <= 'i') {
            return 2;
        } elseif ($firstLetter >= 'j' && $firstLetter <= 'n') {
            return 3;
        } elseif ($firstLetter >= 'o' && $firstLetter <= 's') {
            return 4;
        } elseif ($firstLetter >= 't' && $firstLetter <= 'z') {
            return 5;
        } else {
            // 如果是数字或特殊字符，默认分配到idtable1
            return 1;
        }
    }
    
    /**
     * 在sealmember表中创建用户
     * @param array $userInfo 用户信息
     * @return bool
     */
    public function createSealMemberUser(array $userInfo): bool
    {
        try {
            $result = Db::connect('seal_web')
                ->table('sealmember')
                ->insert($userInfo);
                
            return $result !== false;
            
        } catch (\Exception $e) {
            Log::error('创建sealmember用户失败: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 在usermsgex表中创建用户扩展信息
     * @param array $userInfo 用户信息
     * @return bool
     */
    public function createUserMsgEx(array $userInfo): bool
    {
        try {
            $result = Db::connect('seal_member')
                ->table('usermsgex')
                ->insert($userInfo);
                
            return $result !== false;
            
        } catch (\Exception $e) {
            Log::error('创建usermsgex用户扩展信息失败: ' . $e->getMessage());
            throw $e;
        }
    }
}

/**
 * 密码加密器接口
 * 定义密码加密的标准契约
 */
interface PasswordEncoderInterface
{
    /**
     * 加密密码
     * @param string $plainPassword 明文密码
     * @return string 加密后的密码
     */
    public function encode(string $plainPassword): string;
}

/**
 * IdTable密码加密器
 * 使用OLD_PASSWORD()函数进行密码加密
 */
class IdTablePasswordEncoder implements PasswordEncoderInterface
{
    /**
     * 使用OLD_PASSWORD()函数加密密码
     * @param string $plainPassword 明文密码
     * @return string 加密后的密码
     */
    public function encode(string $plainPassword): string
    {
        try {
            // 使用数据库的OLD_PASSWORD()函数进行加密
            $result = Db::connect('seal_member')
                ->query('SELECT OLD_PASSWORD(?) as encrypted_password', [$plainPassword]);
                
            return $result[0]['encrypted_password'] ?? '';
            
        } catch (\Exception $e) {
            Log::error('密码加密失败: ' . $e->getMessage());
            throw $e;
        }
    }
}