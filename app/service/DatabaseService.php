<?php

namespace app\service;

use think\facade\Db;
use think\facade\Log;
use app\service\CacheService;

/**
 * 数据库服务类
 * 提供多数据库连接管理和缓存集成
 */
class DatabaseService
{
    /**
     * 本地数据库连接名
     */
    const LOCAL_CONNECTION = 'mysql';
    
    /**
     * 远程数据库连接名
     */
    const REMOTE_GDB0101 = 'gdb0101';
    const REMOTE_ITEM = 'item';
    const REMOTE_SEAL_MEMBER = 'seal_member';
    const REMOTE_SEAL_WEB = 'seal_web';
    const REMOTE_SEALONLINE_ITEM_MALL = 'sealonline_item_mall';
    
    /**
     * 所有远程数据库连接名数组
     */
    const REMOTE_CONNECTIONS = [
        self::REMOTE_GDB0101,
        self::REMOTE_ITEM,
        self::REMOTE_SEAL_MEMBER,
        self::REMOTE_SEAL_WEB,
        self::REMOTE_SEALONLINE_ITEM_MALL
    ];

    /**
     * 从本地数据库查询数据（带缓存）
     * @param string $table 表名
     * @param array $where 查询条件
     * @param string $field 查询字段
     * @param int $cacheTime 缓存时间
     * @return array
     */
    public static function getFromLocal(string $table, array $where = [], string $field = '*', int $cacheTime = 1800): array
    {
        $cacheKey = 'local:' . $table . ':' . md5(serialize($where) . $field);
        
        return CacheService::remember($cacheKey, function() use ($table, $where, $field) {
            try {
                return Db::connect(self::LOCAL_CONNECTION)
                    ->table($table)
                    ->field($field)
                    ->where($where)
                    ->select()
                    ->toArray();
            } catch (\Exception $e) {
                Log::error('本地数据库查询失败: ' . $e->getMessage());
                return [];
            }
        }, $cacheTime);
    }

    /**
     * 从指定远程数据库查询数据（带缓存）
     * @param string $connection 连接名
     * @param string $table 表名
     * @param array $where 查询条件
     * @param string $field 查询字段
     * @param int $cacheTime 缓存时间
     * @return array
     */
    public static function getFromRemote(string $connection, string $table, array $where = [], string $field = '*', int $cacheTime = 3600): array
    {
        $cacheKey = $connection . ':' . $table . ':' . md5(serialize($where) . $field);
        
        return CacheService::remember($cacheKey, function() use ($connection, $table, $where, $field) {
            try {
                return Db::connect($connection)
                    ->table($table)
                    ->field($field)
                    ->where($where)
                    ->select()
                    ->toArray();
            } catch (\Exception $e) {
                Log::error("远程数据库查询失败 [{$connection}]: " . $e->getMessage());
                return [];
            }
        }, $cacheTime);
    }

    /**
     * 查询单条记录（优先本地，失败时查远程）
     * @param string $table 表名
     * @param array $where 查询条件
     * @param string $field 查询字段
     * @param array $remoteConnections 要尝试的远程连接（默认尝试所有）
     * @return array|null
     */
    public static function findWithFallback(string $table, array $where, string $field = '*', array $remoteConnections = []): ?array
    {
        // 先尝试从本地数据库查询
        try {
            $result = Db::connect(self::LOCAL_CONNECTION)
                ->table($table)
                ->field($field)
                ->where($where)
                ->find();
            
            if ($result) {
                return $result;
            }
        } catch (\Exception $e) {
            Log::warning('本地数据库查询失败，尝试远程数据库: ' . $e->getMessage());
        }
        
        // 如果没有指定远程连接，使用所有远程连接
        if (empty($remoteConnections)) {
            $remoteConnections = self::REMOTE_CONNECTIONS;
        }
        
        // 本地查询失败或无结果，尝试远程数据库
        foreach ($remoteConnections as $connection) {
            try {
                $result = Db::connect($connection)
                    ->table($table)
                    ->field($field)
                    ->where($where)
                    ->find();
                
                if ($result) {
                    Log::info("从远程数据库 [{$connection}] 找到数据");
                    return $result;
                }
            } catch (\Exception $e) {
                Log::warning("远程数据库 [{$connection}] 查询失败: " . $e->getMessage());
                continue;
            }
        }
        
        Log::error('所有数据库查询都失败');
        return null;
    }

    /**
     * 向本地数据库插入数据
     * @param string $table 表名
     * @param array $data 数据
     * @return int|false 插入的ID或false
     */
    public static function insertToLocal(string $table, array $data)
    {
        try {
            $result = Db::connect(self::LOCAL_CONNECTION)
                ->table($table)
                ->insertGetId($data);
            
            // 清除相关缓存
            self::clearTableCache($table, 'local');
            
            return $result;
        } catch (\Exception $e) {
            Log::error('本地数据库插入失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 向指定远程数据库插入数据
     * @param string $connection 连接名
     * @param string $table 表名
     * @param array $data 数据
     * @return int|false 插入的ID或false
     */
    public static function insertToRemote(string $connection, string $table, array $data)
    {
        try {
            $result = Db::connect($connection)
                ->table($table)
                ->insertGetId($data);
            
            // 清除相关缓存
            self::clearTableCache($table, $connection);
            
            return $result;
        } catch (\Exception $e) {
            Log::error("远程数据库插入失败 [{$connection}]: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 更新本地数据库数据
     * @param string $table 表名
     * @param array $data 更新数据
     * @param array $where 更新条件
     * @return int|false 影响行数或false
     */
    public static function updateLocal(string $table, array $data, array $where)
    {
        try {
            $result = Db::connect(self::LOCAL_CONNECTION)
                ->table($table)
                ->where($where)
                ->update($data);
            
            // 清除相关缓存
            self::clearTableCache($table, 'local');
            
            return $result;
        } catch (\Exception $e) {
            Log::error('本地数据库更新失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 更新指定远程数据库数据
     * @param string $connection 连接名
     * @param string $table 表名
     * @param array $data 更新数据
     * @param array $where 更新条件
     * @return int|false 影响行数或false
     */
    public static function updateRemote(string $connection, string $table, array $data, array $where)
    {
        try {
            $result = Db::connect($connection)
                ->table($table)
                ->where($where)
                ->update($data);
            
            // 清除相关缓存
            self::clearTableCache($table, $connection);
            
            return $result;
        } catch (\Exception $e) {
            Log::error("远程数据库更新失败 [{$connection}]: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 删除本地数据库数据
     * @param string $table 表名
     * @param array $where 删除条件
     * @return int|false 影响行数或false
     */
    public static function deleteFromLocal(string $table, array $where)
    {
        try {
            $result = Db::connect(self::LOCAL_CONNECTION)
                ->table($table)
                ->where($where)
                ->delete();
            
            // 清除相关缓存
            self::clearTableCache($table, 'local');
            
            return $result;
        } catch (\Exception $e) {
            Log::error('本地数据库删除失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 删除指定远程数据库数据
     * @param string $connection 连接名
     * @param string $table 表名
     * @param array $where 删除条件
     * @return int|false 影响行数或false
     */
    public static function deleteFromRemote(string $connection, string $table, array $where)
    {
        try {
            $result = Db::connect($connection)
                ->table($table)
                ->where($where)
                ->delete();
            
            // 清除相关缓存
            self::clearTableCache($table, $connection);
            
            return $result;
        } catch (\Exception $e) {
            Log::error("远程数据库删除失败 [{$connection}]: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 数据同步（从指定远程数据库同步到本地）
     * @param string $remoteConnection 远程连接名
     * @param string $table 表名
     * @param array $where 同步条件
     * @param string $primaryKey 主键字段名（默认为id）
     * @return bool
     */
    public static function syncFromRemoteToLocal(string $remoteConnection, string $table, array $where = [], string $primaryKey = 'id'): bool
    {
        try {
            // 从远程获取数据
            $remoteData = Db::connect($remoteConnection)
                ->table($table)
                ->where($where)
                ->select()
                ->toArray();
            
            if (empty($remoteData)) {
                Log::info("没有需要同步的数据: {$remoteConnection}.{$table}");
                return true;
            }
            
            // 开启事务
            Db::connect(self::LOCAL_CONNECTION)->startTrans();
            
            $insertCount = 0;
            $updateCount = 0;
            
            foreach ($remoteData as $row) {
                // 检查本地是否已存在
                $exists = Db::connect(self::LOCAL_CONNECTION)
                    ->table($table)
                    ->where($primaryKey, $row[$primaryKey])
                    ->find();
                
                if ($exists) {
                    // 更新
                    Db::connect(self::LOCAL_CONNECTION)
                        ->table($table)
                        ->where($primaryKey, $row[$primaryKey])
                        ->update($row);
                    $updateCount++;
                } else {
                    // 插入
                    Db::connect(self::LOCAL_CONNECTION)
                        ->table($table)
                        ->insert($row);
                    $insertCount++;
                }
            }
            
            // 提交事务
            Db::connect(self::LOCAL_CONNECTION)->commit();
            
            // 清除缓存
            self::clearTableCache($table, 'local');
            
            Log::info("数据同步成功: {$remoteConnection}.{$table} 表插入了 {$insertCount} 条记录，更新了 {$updateCount} 条记录");
            return true;
            
        } catch (\Exception $e) {
            // 回滚事务
            Db::connect(self::LOCAL_CONNECTION)->rollback();
            Log::error("数据同步失败 [{$remoteConnection}.{$table}]: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 清除表相关缓存
     * @param string $table 表名
     * @param string $connection 连接类型
     */
    private static function clearTableCache(string $table, string $connection): void
    {
        CacheService::clearByPrefix($connection . ':' . $table);
    }

    /**
     * 测试数据库连接
     * @param string $connection 连接名
     * @return bool
     */
    public static function testConnection(string $connection): bool
    {
        try {
            Db::connect($connection)->query('SELECT 1');
            return true;
        } catch (\Exception $e) {
            Log::error("数据库连接测试失败 [{$connection}]: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 测试所有远程数据库连接
     * @return array
     */
    public static function testAllRemoteConnections(): array
    {
        $results = [];
        foreach (self::REMOTE_CONNECTIONS as $connection) {
            $results[$connection] = self::testConnection($connection);
        }
        return $results;
    }
    
    /**
     * 获取指定远程数据库的表列表
     * @param string $connection 连接名
     * @return array
     */
    public static function getRemoteTables(string $connection): array
    {
        try {
            $tables = Db::connect($connection)->query('SHOW TABLES');
            return array_column($tables, array_values($tables[0])[0]);
        } catch (\Exception $e) {
            Log::error("获取远程数据库表列表失败 [{$connection}]: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * 批量同步多个远程数据库的数据
     * @param array $syncConfig 同步配置 [['connection' => 'gdb0101', 'table' => 'users', 'where' => []], ...]
     * @return array
     */
    public static function batchSync(array $syncConfig): array
    {
        $results = [];
        foreach ($syncConfig as $config) {
            $connection = $config['connection'];
            $table = $config['table'];
            $where = $config['where'] ?? [];
            $primaryKey = $config['primary_key'] ?? 'id';
            
            $results["{$connection}.{$table}"] = self::syncFromRemoteToLocal($connection, $table, $where, $primaryKey);
        }
        return $results;
    }
}