<?php

namespace app\service;

use think\facade\Cache;
use think\facade\Log;

/**
 * 缓存服务类
 * 提供统一的缓存操作接口，支持Redis缓存
 */
class CacheService
{
    /**
     * 缓存前缀
     */
    const CACHE_PREFIX = 'shop:';
    
    /**
     * 默认缓存时间（秒）
     */
    const DEFAULT_EXPIRE = 3600;
    
    /**
     * 数据库查询缓存时间（秒）
     */
    const DB_CACHE_EXPIRE = 1800;
    
    /**
     * 用户会话缓存时间（秒）
     */
    const USER_CACHE_EXPIRE = 7200;

    /**
     * 缓存统计信息
     */
    private static $stats = [
        'hits' => 0,
        'misses' => 0,
        'sets' => 0,
        'deletes' => 0,
        'errors' => 0
    ];

    /**
     * 设置缓存
     * @param string $key 缓存键
     * @param mixed $value 缓存值
     * @param int $expire 过期时间（秒）
     * @return bool
     */
    public static function set(string $key, $value, int $expire = self::DEFAULT_EXPIRE): bool
    {
        try {
            $cacheKey = self::buildKey($key);
            $result = Cache::set($cacheKey, $value, $expire);
            if ($result) {
                self::$stats['sets']++;
            }
            return $result;
        } catch (\Exception $e) {
            self::$stats['errors']++;
            Log::error('缓存设置失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取缓存
     * @param string $key 缓存键
     * @param mixed $default 默认值
     * @return mixed
     */
    public static function get(string $key, $default = null)
    {
        try {
            $cacheKey = self::buildKey($key);
            $result = Cache::get($cacheKey, $default);

            // 统计缓存命中率
            if ($result !== $default) {
                self::$stats['hits']++;
            } else {
                self::$stats['misses']++;
            }

            return $result;
        } catch (\Exception $e) {
            self::$stats['errors']++;
            Log::error('缓存获取失败: ' . $e->getMessage());
            return $default;
        }
    }

    /**
     * 删除缓存
     * @param string $key 缓存键
     * @return bool
     */
    public static function delete(string $key): bool
    {
        try {
            $cacheKey = self::buildKey($key);
            $result = Cache::delete($cacheKey);
            if ($result) {
                self::$stats['deletes']++;
            }
            return $result;
        } catch (\Exception $e) {
            self::$stats['errors']++;
            Log::error('缓存删除失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 检查缓存是否存在
     * @param string $key 缓存键
     * @return bool
     */
    public static function has(string $key): bool
    {
        try {
            $cacheKey = self::buildKey($key);
            return Cache::has($cacheKey);
        } catch (\Exception $e) {
            Log::error('缓存检查失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 批量获取缓存
     * @param array $keys 缓存键数组
     * @param mixed $default 默认值
     * @return array
     */
    public static function getMultiple(array $keys, $default = null): array
    {
        try {
            $result = [];
            $cacheKeys = [];
            
            // 构建缓存键映射
            foreach ($keys as $key) {
                $cacheKey = self::buildKey($key);
                $cacheKeys[$cacheKey] = $key;
            }
            
            // 批量获取缓存
            foreach ($cacheKeys as $cacheKey => $originalKey) {
                $value = Cache::get($cacheKey, $default);
                $result[$originalKey] = $value;
            }
            
            return $result;
        } catch (\Exception $e) {
            Log::error('批量缓存获取失败: ' . $e->getMessage());
            // 返回默认值数组
            $result = [];
            foreach ($keys as $key) {
                $result[$key] = $default;
            }
            return $result;
        }
    }

    /**
     * 批量设置缓存
     * @param array $values 键值对数组
     * @param int $expire 过期时间（秒）
     * @return bool
     */
    public static function setMultiple(array $values, int $expire = self::DEFAULT_EXPIRE): bool
    {
        try {
            $success = true;
            foreach ($values as $key => $value) {
                $cacheKey = self::buildKey($key);
                if (!Cache::set($cacheKey, $value, $expire)) {
                    $success = false;
                }
            }
            return $success;
        } catch (\Exception $e) {
            Log::error('批量缓存设置失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 批量删除缓存
     * @param array $keys 缓存键数组
     * @return bool
     */
    public static function deleteMultiple(array $keys): bool
    {
        try {
            $success = true;
            foreach ($keys as $key) {
                $cacheKey = self::buildKey($key);
                if (!Cache::delete($cacheKey)) {
                    $success = false;
                }
            }
            return $success;
        } catch (\Exception $e) {
            Log::error('批量缓存删除失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 缓存数据库查询结果
     * @param string $key 缓存键
     * @param callable $callback 查询回调函数
     * @param int $expire 过期时间
     * @return mixed
     */
    public static function remember(string $key, callable $callback, int $expire = self::DB_CACHE_EXPIRE)
    {
        $cacheKey = self::buildKey($key);
        
        try {
            // 先尝试从缓存获取
            $result = Cache::get($cacheKey);
            if ($result !== null) {
                return $result;
            }
            
            // 缓存不存在，执行回调获取数据
            $result = $callback();
            
            // 将结果存入缓存
            if ($result !== null) {
                Cache::set($cacheKey, $result, $expire);
            }
            
            return $result;
        } catch (\Exception $e) {
            Log::error('缓存记忆失败: ' . $e->getMessage());
            // 缓存失败时直接执行回调
            return $callback();
        }
    }

    /**
     * 清除指定前缀的缓存
     * @param string $prefix 前缀
     * @return bool
     */
    public static function clearByPrefix(string $prefix): bool
    {
        try {
            $pattern = self::buildKey($prefix . '*');
            return Cache::clear($pattern);
        } catch (\Exception $e) {
            Log::error('批量清除缓存失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 构建缓存键
     * @param string $key 原始键
     * @return string
     */
    private static function buildKey(string $key): string
    {
        return self::CACHE_PREFIX . $key;
    }

    /**
     * 获取缓存统计信息
     * @return array
     */
    public static function getStats(): array
    {
        try {
            // 计算命中率
            $total = self::$stats['hits'] + self::$stats['misses'];
            $hitRate = $total > 0 ? round((self::$stats['hits'] / $total) * 100, 2) : 0;

            // 基本连接信息
            $stats = [
                'driver' => 'Redis',
                'host' => env('REDIS_HOST', '127.0.0.1'),
                'port' => env('REDIS_PORT', 6379),
                'prefix' => env('CACHE_PREFIX', 'shop:'),
                'connection_status' => 'connected',
                // 新增统计信息
                'hits' => self::$stats['hits'],
                'misses' => self::$stats['misses'],
                'sets' => self::$stats['sets'],
                'deletes' => self::$stats['deletes'],
                'errors' => self::$stats['errors'],
                'hit_rate' => $hitRate . '%',
                'total_requests' => $total
            ];

            // 测试连接是否正常
            $testKey = 'test_connection_' . time();
            if (Cache::set($testKey, 'test', 1)) {
                $stats['test_result'] = 'success';
                Cache::delete($testKey);
            } else {
                $stats['test_result'] = 'failed';
                $stats['connection_status'] = 'failed';
            }

            return $stats;
        } catch (\Exception $e) {
            Log::error('获取缓存统计失败: ' . $e->getMessage());
            return [
                'driver' => 'Redis',
                'connection_status' => 'failed',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 缓存用户数据
     * @param int $userId 用户ID
     * @param array $userData 用户数据
     * @return bool
     */
    public static function setUserCache(int $userId, array $userData): bool
    {
        return self::set('user:' . $userId, $userData, self::USER_CACHE_EXPIRE);
    }

    /**
     * 获取用户缓存
     * @param int $userId 用户ID
     * @return array|null
     */
    public static function getUserCache(int $userId): ?array
    {
        return self::get('user:' . $userId);
    }

    /**
     * 删除用户缓存
     * @param int $userId 用户ID
     * @return bool
     */
    public static function deleteUserCache(int $userId): bool
    {
        return self::delete('user:' . $userId);
    }



    /**
     * 重置缓存统计信息
     */
    public static function resetStats(): void
    {
        self::$stats = [
            'hits' => 0,
            'misses' => 0,
            'sets' => 0,
            'deletes' => 0,
            'errors' => 0
        ];
    }

    /**
     * 批量删除缓存（支持通配符）
     * @param string $pattern 缓存键模式
     * @return int 删除的缓存数量
     */
    public static function deleteByPattern(string $pattern): int
    {
        try {
            $fullPattern = self::buildKey($pattern);
            $keys = Cache::tag('')->keys($fullPattern);
            $count = 0;

            foreach ($keys as $key) {
                if (Cache::delete($key)) {
                    $count++;
                    self::$stats['deletes']++;
                }
            }

            return $count;
        } catch (\Exception $e) {
            self::$stats['errors']++;
            Log::error('批量删除缓存失败: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * 缓存预热 - 预加载常用数据
     * @return array 预热结果
     */
    public static function warmup(): array
    {
        $results = [];

        try {
            // 预热用户列表缓存（第一页）
            $userService = new \app\service\UserService();
            $userList = $userService->getUserListOptimized(1, 20);
            $results['user_list'] = '用户列表缓存预热成功';

            // 预热商品列表缓存（第一页）
            $itemList = \app\service\ItemService::getItems(1, 10);
            $results['item_list'] = '商品列表缓存预热成功';

            // 预热管理员权限缓存（如果有session）
            if (\think\facade\Session::has('user_info')) {
                $adminService = new \app\service\AdminService();
                $adminService->isAdmin();
                $results['admin_auth'] = '管理员权限缓存预热成功';
            }

        } catch (\Exception $e) {
            $results['error'] = '缓存预热失败: ' . $e->getMessage();
            Log::error('缓存预热失败: ' . $e->getMessage());
        }

        return $results;
    }

    /**
     * 缓存降级机制 - 当缓存不可用时的备用方案
     * @param string $key 缓存键
     * @param callable $callback 获取数据的回调函数
     * @param int $expire 缓存时间
     * @param mixed $fallback 降级时的默认值
     * @return mixed
     */
    public static function getWithFallback(string $key, callable $callback, int $expire = self::DEFAULT_EXPIRE, $fallback = null)
    {
        try {
            // 尝试从缓存获取
            $result = self::get($key);
            if ($result !== null) {
                return $result;
            }

            // 缓存未命中，执行回调获取数据
            $data = $callback();

            // 尝试设置缓存
            self::set($key, $data, $expire);

            return $data;
        } catch (\Exception $e) {
            self::$stats['errors']++;
            Log::error('缓存降级处理: ' . $e->getMessage());

            try {
                // 缓存失败，直接执行回调
                return $callback();
            } catch (\Exception $callbackError) {
                Log::error('回调执行失败: ' . $callbackError->getMessage());
                return $fallback;
            }
        }
    }

    /**
     * 智能缓存刷新 - 在缓存即将过期时异步刷新
     * @param string $key 缓存键
     * @param callable $callback 获取数据的回调函数
     * @param int $expire 缓存时间
     * @param int $refreshThreshold 刷新阈值（剩余时间百分比）
     * @return mixed
     */
    public static function smartRefresh(string $key, callable $callback, int $expire = self::DEFAULT_EXPIRE, int $refreshThreshold = 20)
    {
        try {
            $cacheKey = self::buildKey($key);
            $result = Cache::get($cacheKey);

            if ($result !== null) {
                // 检查是否需要异步刷新
                $ttl = Cache::ttl($cacheKey);
                $refreshTime = $expire * ($refreshThreshold / 100);

                if ($ttl > 0 && $ttl < $refreshTime) {
                    // 异步刷新缓存
                    try {
                        $newData = $callback();
                        Cache::set($cacheKey, $newData, $expire);
                        Log::info("缓存异步刷新成功: {$key}");
                    } catch (\Exception $e) {
                        Log::warning("缓存异步刷新失败: {$key} - " . $e->getMessage());
                    }
                }

                self::$stats['hits']++;
                return $result;
            }

            // 缓存未命中，同步获取数据
            self::$stats['misses']++;
            $data = $callback();
            Cache::set($cacheKey, $data, $expire);
            self::$stats['sets']++;

            return $data;
        } catch (\Exception $e) {
            self::$stats['errors']++;
            Log::error('智能缓存刷新失败: ' . $e->getMessage());
            return $callback();
        }
    }
}