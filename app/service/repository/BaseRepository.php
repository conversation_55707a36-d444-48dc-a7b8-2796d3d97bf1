<?php

namespace app\service\repository;

use think\facade\Db;
use think\facade\Log;
use app\service\contract\RepositoryInterface;
use app\service\CacheService;

/**
 * 基础数据仓库类
 * 实现通用的数据访问方法
 * 
 * 设计原则：
 * 1. 单一职责原则：专门负责数据访问
 * 2. 开闭原则：易于扩展新的数据访问方法
 * 3. 里氏替换原则：子类可以替换父类
 * 4. 依赖倒置原则：依赖抽象接口
 */
abstract class BaseRepository implements RepositoryInterface
{
    /**
     * 表名
     */
    protected string $table;
    
    /**
     * 主键字段名
     */
    protected string $primaryKey = 'id';
    
    /**
     * 数据库连接名
     */
    protected string $connection = 'default';
    
    /**
     * 缓存前缀
     */
    protected string $cachePrefix = '';
    
    /**
     * 缓存时间（秒）
     */
    protected int $cacheTime = 1800;
    
    /**
     * 是否启用缓存
     */
    protected bool $enableCache = true;
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->initialize();
    }
    
    /**
     * 初始化方法（子类可重写）
     */
    protected function initialize(): void
    {
        // 子类可以重写此方法进行初始化
    }
    
    /**
     * 根据ID查找记录
     */
    public function findById($id): ?array
    {
        if ($this->enableCache) {
            $cacheKey = $this->buildCacheKey('find', $id);
            return CacheService::getWithFallback(
                $cacheKey,
                function() use ($id) {
                    return $this->queryById($id);
                },
                $this->cacheTime,
                null
            );
        }
        
        return $this->queryById($id);
    }
    
    /**
     * 根据条件查找单条记录
     */
    public function findOne(array $conditions): ?array
    {
        if ($this->enableCache) {
            $cacheKey = $this->buildCacheKey('findOne', $conditions);
            return CacheService::getWithFallback(
                $cacheKey,
                function() use ($conditions) {
                    return $this->queryOne($conditions);
                },
                $this->cacheTime,
                null
            );
        }
        
        return $this->queryOne($conditions);
    }
    
    /**
     * 根据条件查找多条记录
     */
    public function findMany(array $conditions = [], array $options = []): array
    {
        if ($this->enableCache) {
            $cacheKey = $this->buildCacheKey('findMany', [$conditions, $options]);
            return CacheService::getWithFallback(
                $cacheKey,
                function() use ($conditions, $options) {
                    return $this->queryMany($conditions, $options);
                },
                $this->cacheTime,
                []
            );
        }
        
        return $this->queryMany($conditions, $options);
    }
    
    /**
     * 分页查询
     */
    public function paginate(int $page, int $limit, array $conditions = [], array $options = []): array
    {
        if ($this->enableCache) {
            $cacheKey = $this->buildCacheKey('paginate', [$page, $limit, $conditions, $options]);
            return CacheService::getWithFallback(
                $cacheKey,
                function() use ($page, $limit, $conditions, $options) {
                    return $this->queryPaginate($page, $limit, $conditions, $options);
                },
                $this->cacheTime,
                ['list' => [], 'total' => 0, 'page' => $page, 'limit' => $limit]
            );
        }
        
        return $this->queryPaginate($page, $limit, $conditions, $options);
    }
    
    /**
     * 创建记录
     */
    public function create(array $data)
    {
        try {
            $result = $this->getQuery()->insertGetId($data);
            
            if ($result && $this->enableCache) {
                $this->clearCache();
            }
            
            return $result;
        } catch (\Exception $e) {
            Log::error('创建记录失败: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 更新记录
     */
    public function update($id, array $data): bool
    {
        try {
            $result = $this->getQuery()
                ->where($this->primaryKey, $id)
                ->update($data);
            
            if ($result !== false && $this->enableCache) {
                $this->clearCache($id);
            }
            
            return $result !== false;
        } catch (\Exception $e) {
            Log::error('更新记录失败: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 删除记录
     */
    public function delete($id): bool
    {
        try {
            $result = $this->getQuery()
                ->where($this->primaryKey, $id)
                ->delete();
            
            if ($result && $this->enableCache) {
                $this->clearCache($id);
            }
            
            return $result > 0;
        } catch (\Exception $e) {
            Log::error('删除记录失败: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 批量创建记录
     */
    public function batchCreate(array $dataList): bool
    {
        try {
            $result = $this->getQuery()->insertAll($dataList);
            
            if ($result && $this->enableCache) {
                $this->clearCache();
            }
            
            return $result > 0;
        } catch (\Exception $e) {
            Log::error('批量创建记录失败: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 批量更新记录
     */
    public function batchUpdate(array $conditions, array $data): int
    {
        try {
            $query = $this->getQuery();
            $this->applyConditions($query, $conditions);
            $result = $query->update($data);
            
            if ($result && $this->enableCache) {
                $this->clearCache();
            }
            
            return $result;
        } catch (\Exception $e) {
            Log::error('批量更新记录失败: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 批量删除记录
     */
    public function batchDelete(array $conditions): int
    {
        try {
            $query = $this->getQuery();
            $this->applyConditions($query, $conditions);
            $result = $query->delete();
            
            if ($result && $this->enableCache) {
                $this->clearCache();
            }
            
            return $result;
        } catch (\Exception $e) {
            Log::error('批量删除记录失败: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 统计记录数量
     */
    public function count(array $conditions = []): int
    {
        try {
            $query = $this->getQuery();
            $this->applyConditions($query, $conditions);
            return $query->count();
        } catch (\Exception $e) {
            Log::error('统计记录数量失败: ' . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * 检查记录是否存在
     */
    public function exists(array $conditions): bool
    {
        return $this->count($conditions) > 0;
    }
    
    /**
     * 获取最大值
     */
    public function max(string $field, array $conditions = [])
    {
        try {
            $query = $this->getQuery();
            $this->applyConditions($query, $conditions);
            return $query->max($field);
        } catch (\Exception $e) {
            Log::error('获取最大值失败: ' . $e->getMessage());
            return null;
        }
    }
    
    /**
     * 获取最小值
     */
    public function min(string $field, array $conditions = [])
    {
        try {
            $query = $this->getQuery();
            $this->applyConditions($query, $conditions);
            return $query->min($field);
        } catch (\Exception $e) {
            Log::error('获取最小值失败: ' . $e->getMessage());
            return null;
        }
    }
    
    /**
     * 获取平均值
     */
    public function avg(string $field, array $conditions = []): float
    {
        try {
            $query = $this->getQuery();
            $this->applyConditions($query, $conditions);
            return (float)$query->avg($field);
        } catch (\Exception $e) {
            Log::error('获取平均值失败: ' . $e->getMessage());
            return 0.0;
        }
    }
    
    /**
     * 获取总和
     */
    public function sum(string $field, array $conditions = [])
    {
        try {
            $query = $this->getQuery();
            $this->applyConditions($query, $conditions);
            return $query->sum($field);
        } catch (\Exception $e) {
            Log::error('获取总和失败: ' . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * 开始事务
     */
    public function beginTransaction(): void
    {
        Db::connect($this->connection)->startTrans();
    }
    
    /**
     * 提交事务
     */
    public function commit(): void
    {
        Db::connect($this->connection)->commit();
    }
    
    /**
     * 回滚事务
     */
    public function rollback(): void
    {
        Db::connect($this->connection)->rollback();
    }
    
    /**
     * 执行原生SQL查询
     */
    public function query(string $sql, array $bindings = []): array
    {
        try {
            return Db::connect($this->connection)->query($sql, $bindings);
        } catch (\Exception $e) {
            Log::error('执行SQL查询失败: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 执行原生SQL语句
     */
    public function execute(string $sql, array $bindings = []): int
    {
        try {
            return Db::connect($this->connection)->execute($sql, $bindings);
        } catch (\Exception $e) {
            Log::error('执行SQL语句失败: ' . $e->getMessage());
            throw $e;
        }
    }
    
    // 受保护的辅助方法
    
    /**
     * 获取查询对象
     */
    protected function getQuery(): \think\db\Query
    {
        return Db::connect($this->connection)->table($this->table);
    }
    
    /**
     * 根据ID查询
     */
    protected function queryById($id): ?array
    {
        try {
            return $this->getQuery()
                ->where($this->primaryKey, $id)
                ->find();
        } catch (\Exception $e) {
            Log::error('根据ID查询失败: ' . $e->getMessage());
            return null;
        }
    }
    
    /**
     * 根据条件查询单条记录
     */
    protected function queryOne(array $conditions): ?array
    {
        try {
            $query = $this->getQuery();
            $this->applyConditions($query, $conditions);
            return $query->find();
        } catch (\Exception $e) {
            Log::error('查询单条记录失败: ' . $e->getMessage());
            return null;
        }
    }
    
    /**
     * 根据条件查询多条记录
     */
    protected function queryMany(array $conditions, array $options): array
    {
        try {
            $query = $this->getQuery();
            $this->applyConditions($query, $conditions);
            $this->applyOptions($query, $options);
            return $query->select()->toArray();
        } catch (\Exception $e) {
            Log::error('查询多条记录失败: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * 分页查询
     */
    protected function queryPaginate(int $page, int $limit, array $conditions, array $options): array
    {
        try {
            $query = $this->getQuery();
            $this->applyConditions($query, $conditions);
            
            // 获取总数
            $total = $query->count();
            
            // 应用选项并分页
            $this->applyOptions($query, $options);
            $list = $query->page($page, $limit)->select()->toArray();
            
            return [
                'list' => $list,
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'total_pages' => ceil($total / $limit)
            ];
        } catch (\Exception $e) {
            Log::error('分页查询失败: ' . $e->getMessage());
            return [
                'list' => [],
                'total' => 0,
                'page' => $page,
                'limit' => $limit,
                'total_pages' => 0
            ];
        }
    }
    
    /**
     * 应用查询条件
     */
    protected function applyConditions(\think\db\Query $query, array $conditions): void
    {
        foreach ($conditions as $field => $value) {
            if ($value === null || $value === '') {
                continue;
            }
            
            if (is_array($value)) {
                $query->whereIn($field, $value);
            } elseif (strpos($field, '_like') !== false) {
                $realField = str_replace('_like', '', $field);
                $query->where($realField, 'like', '%' . $value . '%');
            } else {
                $query->where($field, $value);
            }
        }
    }
    
    /**
     * 应用查询选项
     */
    protected function applyOptions(\think\db\Query $query, array $options): void
    {
        // 排序
        if (isset($options['order'])) {
            if (is_array($options['order'])) {
                foreach ($options['order'] as $field => $direction) {
                    $query->order($field, $direction);
                }
            } else {
                $query->order($options['order']);
            }
        }
        
        // 字段选择
        if (isset($options['field'])) {
            $query->field($options['field']);
        }
        
        // 分组
        if (isset($options['group'])) {
            $query->group($options['group']);
        }
        
        // HAVING条件
        if (isset($options['having'])) {
            $query->having($options['having']);
        }
    }
    
    /**
     * 构建缓存键
     */
    protected function buildCacheKey(string $method, $params = null): string
    {
        $key = $this->cachePrefix . $this->table . ':' . $method;
        if ($params !== null) {
            $key .= ':' . md5(serialize($params));
        }
        return $key;
    }
    
    /**
     * 清除缓存
     */
    protected function clearCache($id = null): void
    {
        if (!$this->enableCache) {
            return;
        }
        
        try {
            $pattern = $this->cachePrefix . $this->table . ':*';
            CacheService::deleteByPattern($pattern);
        } catch (\Exception $e) {
            Log::warning('清除缓存失败: ' . $e->getMessage());
        }
    }
}
