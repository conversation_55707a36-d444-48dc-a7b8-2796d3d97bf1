<?php

namespace app\service;

/**
 * 商城配置服务类
 * 提供统一的配置访问接口
 */
class ShopConfigService
{
    private static $config = null;

    /**
     * 获取配置
     */
    private static function getConfig()
    {
        if (self::$config === null) {
            self::$config = include config_path() . 'shop_config.php';
        }
        return self::$config;
    }

    /**
     * 获取商品分类名称
     * @param int $categoryId 分类ID
     * @return string
     */
    public static function getCategoryName($categoryId)
    {
        $config = self::getConfig();
        return $config['item_categories'][$categoryId] ?? '其他';
    }

    /**
     * 获取所有商品分类
     * @return array
     */
    public static function getAllCategories()
    {
        $config = self::getConfig();
        return $config['item_categories'] ?? [];
    }

    /**
     * 获取货币类型信息
     * @param int $currencyType 货币类型ID
     * @return array
     */
    public static function getCurrencyInfo($currencyType)
    {
        $config = self::getConfig();
        return $config['currency_types'][$currencyType] ?? [
            'name' => '未知',
            'short_name' => '未知',
            'symbol' => '',
            'color_class' => 'bg-secondary'
        ];
    }

    /**
     * 获取货币类型名称
     * @param int $currencyType 货币类型ID
     * @return string
     */
    public static function getCurrencyName($currencyType)
    {
        $info = self::getCurrencyInfo($currencyType);
        return $info['name'];
    }

    /**
     * 获取货币类型CSS类
     * @param int $currencyType 货币类型ID
     * @return string
     */
    public static function getCurrencyColorClass($currencyType)
    {
        $info = self::getCurrencyInfo($currencyType);
        return $info['color_class'];
    }

    /**
     * 获取所有货币类型
     * @return array
     */
    public static function getAllCurrencyTypes()
    {
        $config = self::getConfig();
        return $config['currency_types'] ?? [];
    }

    /**
     * 获取商品状态信息
     * @param int $status 状态ID
     * @return array
     */
    public static function getItemStatusInfo($status)
    {
        $config = self::getConfig();
        return $config['item_status'][$status] ?? [
            'name' => '未知',
            'color_class' => 'bg-secondary'
        ];
    }

    /**
     * 获取商品状态名称
     * @param int $status 状态ID
     * @return string
     */
    public static function getItemStatusName($status)
    {
        $info = self::getItemStatusInfo($status);
        return $info['name'];
    }

    /**
     * 获取商品状态CSS类
     * @param int $status 状态ID
     * @return string
     */
    public static function getItemStatusColorClass($status)
    {
        $info = self::getItemStatusInfo($status);
        return $info['color_class'];
    }

    /**
     * 获取订单状态信息
     * @param int $status 状态ID
     * @return array
     */
    public static function getOrderStatusInfo($status)
    {
        $config = self::getConfig();
        return $config['order_status'][$status] ?? [
            'name' => '未知',
            'color_class' => 'bg-secondary'
        ];
    }

    /**
     * 获取订单状态名称
     * @param int $status 状态ID
     * @return string
     */
    public static function getOrderStatusName($status)
    {
        $info = self::getOrderStatusInfo($status);
        return $info['name'];
    }

    /**
     * 获取订单状态CSS类
     * @param int $status 状态ID
     * @return string
     */
    public static function getOrderStatusColorClass($status)
    {
        $info = self::getOrderStatusInfo($status);
        return $info['color_class'];
    }

    /**
     * 获取用户状态信息
     * @param int $status 状态ID
     * @return array
     */
    public static function getUserStatusInfo($status)
    {
        $config = self::getConfig();
        return $config['user_status'][$status] ?? [
            'name' => '未知',
            'color_class' => 'bg-secondary'
        ];
    }

    /**
     * 获取购买限制名称
     * @param int $restriction 限制类型ID
     * @return string
     */
    public static function getPurchaseRestrictionName($restriction)
    {
        $config = self::getConfig();
        return $config['purchase_restrictions'][$restriction] ?? '未知';
    }

    /**
     * 获取默认商品图标
     * @return string
     */
    public static function getDefaultItemIcon()
    {
        $config = self::getConfig();
        return $config['icons']['default_item_icon'] ?? '/static/img/default.png';
    }

    /**
     * 获取商品图标API端点
     * @return string
     */
    public static function getItemIconEndpoint()
    {
        $config = self::getConfig();
        return $config['api']['item_icon_endpoint'] ?? '/api/getItemIcon';
    }

    /**
     * 获取分页配置
     * @param string $key 配置键
     * @return mixed
     */
    public static function getPaginationConfig($key = null)
    {
        $config = self::getConfig();
        $pagination = $config['pagination'] ?? [];
        
        if ($key === null) {
            return $pagination;
        }
        
        return $pagination[$key] ?? null;
    }

    /**
     * 获取缓存配置
     * @param string $key 配置键
     * @return mixed
     */
    public static function getCacheConfig($key = null)
    {
        $config = self::getConfig();
        $cache = $config['cache'] ?? [];
        
        if ($key === null) {
            return $cache;
        }
        
        return $cache[$key] ?? null;
    }

    /**
     * 生成前端配置JSON
     * 用于在前端JavaScript中使用配置
     * @return string
     */
    public static function getFrontendConfigJson()
    {
        $config = self::getConfig();
        
        $frontendConfig = [
            'categories' => $config['item_categories'] ?? [],
            'currencies' => [],
            'currency_types' => $config['currency_types'] ?? [], // 添加完整的货币类型配置
            'itemStatus' => [],
            'orderStatus' => [],
            'userStatus' => [],
            'restrictions' => $config['purchase_restrictions'] ?? [],
            'purchase_restrictions' => $config['purchase_restrictions'] ?? [], // 添加限制类型配置
            'api' => $config['api'] ?? [],
            'pagination' => $config['pagination'] ?? []
        ];

        // 处理货币类型
        foreach ($config['currency_types'] ?? [] as $id => $info) {
            $frontendConfig['currencies'][$id] = [
                'name' => $info['name'],
                'shortName' => $info['short_name'] ?? $info['name'],
                'symbol' => $info['symbol'] ?? '',
                'colorClass' => $info['color_class'] ?? 'bg-secondary'
            ];
        }

        // 处理商品状态
        foreach ($config['item_status'] ?? [] as $id => $info) {
            $frontendConfig['itemStatus'][$id] = [
                'name' => $info['name'],
                'colorClass' => $info['color_class']
            ];
        }

        // 处理订单状态
        foreach ($config['order_status'] ?? [] as $id => $info) {
            $frontendConfig['orderStatus'][$id] = [
                'name' => $info['name'],
                'colorClass' => $info['color_class']
            ];
        }

        // 处理用户状态
        foreach ($config['user_status'] ?? [] as $id => $info) {
            $frontendConfig['userStatus'][$id] = [
                'name' => $info['name'],
                'colorClass' => $info['color_class']
            ];
        }

        return json_encode($frontendConfig, JSON_UNESCAPED_UNICODE);
    }

    /**
     * 清除配置缓存
     */
    public static function clearCache()
    {
        self::$config = null;
    }
}
