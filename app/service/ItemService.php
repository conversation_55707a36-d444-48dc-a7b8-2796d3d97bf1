<?php
namespace app\service;

use think\facade\Db;
use think\facade\Log;
use app\service\CacheService;

class ItemService
{
    /**
     * 商品列表缓存时间（秒）
     */
    const ITEMS_CACHE_EXPIRE = 600; // 10分钟
    
    /**
     * 商品信息缓存时间（秒）
     */
    const ITEM_INFO_CACHE_EXPIRE = 1800; // 30分钟
    
    /**
     * 获取商品列表，支持分页、分类、搜索、状态筛选、货币类型筛选和排序
     */
    public static function getItems($page = 1, $limit = 10, $category = '', $search = '', $status = '', $sort = 'default', $currency = '')
    {
        // 生成缓存键，包含所有查询参数
        $cacheKey = self::buildItemsCacheKey($page, $limit, $category, $search, $status, $sort, $currency);

        // 使用缓存记忆模式
        return CacheService::remember($cacheKey, function() use ($page, $limit, $category, $search, $status, $sort, $currency) {
            return self::fetchItemsFromDatabase($page, $limit, $category, $search, $status, $sort, $currency);
        }, self::ITEMS_CACHE_EXPIRE);
    }
    
    /**
     * 从数据库获取商品列表
     */
    private static function fetchItemsFromDatabase($page, $limit, $category, $search, $status = '', $sort = 'default', $currency = '')
    {
        $query = Db::table('pointshop');

        // 处理排序
        switch ($sort) {
            case 'price_asc':
                $query->order('price', 'asc');
                break;
            case 'price_desc':
                $query->order('price', 'desc');
                break;
            case 'name_asc':
                $query->order('name', 'asc');
                break;
            case 'name_desc':
                $query->order('name', 'desc');
                break;
            case 'default':
            default:
                $query->order('id', 'asc');
                break;
        }

        // 状态筛选
        if (!empty($status)) {
            $query->where('status', $status);
        }

        // 货币类型筛选
        if (!empty($currency)) {
            $query->where('price_type', $currency);
        }

        if (!empty($category) && $category !== 'all') {
            $query->where('class', $category);
        }
        if (!empty($search)) {
            $query->where('name', 'like', '%' . $search . '%');
        }
        $total = $query->count();
        $items = $query->page($page, $limit)->select()->toArray();
        
        // 批量获取商品详细信息（解决N+1查询问题）
        $itemIds = array_column($items, 'item');
        $itemInfos = self::getBatchItemInfoFromDatabase($itemIds);

        // 处理每个商品的详细信息
        foreach ($items as &$item) {
            $itemInfo = $itemInfos[$item['item']] ?? [
                'icon' => '/static/img/default.png',
                'name' => '未知物品',
                'explanation' => '暂无说明'
            ];

            $item['icon'] = $itemInfo['icon'];
            if (!empty($itemInfo['name']) && $itemInfo['name'] !== '未知物品') {
                $item['name'] = $itemInfo['name'];
            }
            if (!empty($itemInfo['explanation']) && $itemInfo['explanation'] !== '暂无说明') {
                $item['item_info'] = $itemInfo['explanation'];
            }
            $item['price_type_name'] = self::getPriceTypeName($item['price_type']);
            $item['price_display'] = $item['price'] . ' ' . $item['price_type_name'];
            $item['class_name'] = self::getClassName($item['class']);
            $item['restriction_name'] = self::getRestrictionName($item['restriction']);
        }
        
        return [
            'items' => $items,
            'total' => $total,
            'page' => $page,
            'limit' => $limit
        ];
    }
    
    /**
     * 构建商品列表缓存键
     */
    private static function buildItemsCacheKey($page, $limit, $category, $search, $status = '', $sort = 'default', $currency = '')
    {
        $params = [
            'page' => $page,
            'limit' => $limit,
            'category' => $category ?: 'all',
            'search' => $search ?: 'none',
            'status' => $status ?: 'all',
            'sort' => $sort ?: 'default',
            'currency' => $currency ?: 'all'
        ];
        return 'items:list:' . md5(json_encode($params));
    }

    /**
     * 通过API获取商品完整信息（带缓存）
     */
    public static function getItemInfoFromAPI($itemId)
    {
        // 生成商品信息缓存键
        $cacheKey = 'item:info:' . $itemId;
        
        // 使用缓存记忆模式
        return CacheService::remember($cacheKey, function() use ($itemId) {
            return self::fetchItemInfoFromAPI($itemId);
        }, self::ITEM_INFO_CACHE_EXPIRE);
    }
    
    /**
     * 从API获取商品信息（不使用缓存）
     */
    private static function fetchItemInfoFromAPI($itemId)
    {
        try {
            $apiUrl = ApiService::getItemInfoUrl($itemId);
             $result = ApiService::sendRequest($apiUrl);
             
             if ($result === false) {
                 return [
                     'icon' => '/static/img/default.png',
                     'name' => '未知物品',
                     'explanation' => '暂无说明'
                 ];
             }
             
             if ($result && $result['code'] === 200 && isset($result['data'])) {
                 return [
                     'icon' => $result['data']['icon'] ?? '/static/img/default.png',
                     'name' => $result['data']['name'] ?? '未知物品',
                     'explanation' => $result['data']['explanation'] ?? '暂无说明'
                 ];
            }
            return [
                'icon' => '/static/img/default.png',
                'name' => '未知物品',
                'explanation' => '暂无说明'
            ];
        } catch (\Exception $e) {
            Log::error('获取商品信息API失败: ' . $e->getMessage());
            return [
                'icon' => '/static/img/default.png',
                'name' => '未知物品',
                'explanation' => '暂无说明'
            ];
        }
    }

    /**
     * 批量从本地数据库获取商品信息（解决N+1查询问题，支持缓存）
     * @param array $itemIds 商品ID数组
     * @return array 以商品ID为键的商品信息数组
     */
    public static function getBatchItemInfoFromDatabase(array $itemIds): array
    {
        if (empty($itemIds)) {
            return [];
        }

        // 启用缓存优化性能
        $sortedIds = $itemIds;
        sort($sortedIds);
        $cacheKey = 'item_info_batch_' . md5(implode(',', $sortedIds));

        // 尝试从缓存获取
        $cached = \app\service\CacheService::get($cacheKey, false);
        if ($cached !== false) {
            return $cached;
        }

        try {
            // 使用本地数据库连接进行批量查询（iteminfo表在本地数据库）
            $results = Db::connect('mysql')
                ->table('iteminfo')
                ->field('ItemID, ItemName, Icon, Explanation')
                ->whereIn('ItemID', $itemIds)
                ->select()
                ->toArray();

            $itemInfos = [];
            foreach ($results as $result) {
                // 使用改进的图标路径生成逻辑，支持多种文件格式
                $iconUrl = self::generateIconUrl($result['Icon']);

                $itemInfos[$result['ItemID']] = [
                    'icon' => $iconUrl,
                    'name' => $result['ItemName'] ?? '未知物品',
                    'explanation' => $result['Explanation'] ?? '暂无说明'
                ];
            }

            // 为没有找到信息的商品ID填充默认值
            foreach ($itemIds as $itemId) {
                if (!isset($itemInfos[$itemId])) {
                    $itemInfos[$itemId] = [
                        'icon' => '/static/img/default.png',
                        'name' => '未知物品',
                        'explanation' => '暂无说明'
                    ];
                }
            }

            // 缓存结果（30分钟）
            \app\service\CacheService::set($cacheKey, $itemInfos, 1800);

            return $itemInfos;

        } catch (\Exception $e) {
            Log::error('批量获取商品信息失败: ' . $e->getMessage());
            Log::error('错误详情: ' . $e->getTraceAsString());

            // 发生错误时，为所有商品ID返回默认值
            $defaultInfo = [
                'icon' => '/static/img/default.png',
                'name' => '未知物品',
                'explanation' => '暂无说明'
            ];

            $itemInfos = [];
            foreach ($itemIds as $itemId) {
                $itemInfos[$itemId] = $defaultInfo;
            }

            return $itemInfos;
        }
    }

    /**
     * 从本地数据库获取商品信息（单个查询，保留向后兼容性）
     * @param int $itemId 商品ID
     * @return array
     */
    public static function getItemInfoFromDatabase($itemId)
    {
        $result = self::getBatchItemInfoFromDatabase([$itemId]);
        return $result[$itemId] ?? [
            'icon' => '/static/img/default.png',
            'name' => '未知物品',
            'explanation' => '暂无说明'
        ];
    }

    public static function getPriceTypeName($priceType)
    {
        return \app\service\ShopConfigService::getCurrencyName($priceType);
    }

    public static function getClassName($class)
    {
        return \app\service\ShopConfigService::getCategoryName($class);
    }

    public static function getRestrictionName($restriction)
    {
        $restrictions = [0 => '不限购', 1 => '账号限购', 2 => '每日限购', 3 => '每周限购', 4 => '每月限购'];
        return $restrictions[$restriction] ?? '无限制';
    }

    /**
     * 生成图标URL
     * @param string|null $iconId 图标ID
     * @return string
     */
    public static function generateIconUrl($iconId)
    {
        $defaultIcon = '/static/img/default.png';

        if (empty($iconId)) {
            return $defaultIcon;
        }

        // 图标文件路径基础目录
        $imgDir = root_path() . 'public/static/img/';

        // 尝试多种文件名格式，按优先级查找
        $possibleFiles = [
            $iconId . '.png',        // 直接数字格式: 1.png
            'x' . $iconId . '.png',  // x前缀格式: x1.png
        ];

        foreach ($possibleFiles as $filename) {
            if (file_exists($imgDir . $filename)) {
                return '/static/img/' . $filename;
            }
        }

        return $defaultIcon;
    }

    /**
     * 获取商品详情
     * @param int $itemId 商品ID
     * @return array|null
     */
    public static function getItemDetail($itemId)
    {
        try {
            // 从pointshop表获取商品基本信息
            $item = Db::table('pointshop')->where('id', $itemId)->find();

            if (!$item) {
                return null;
            }

            // 获取商品详细信息
            $itemInfo = self::getItemInfoFromDatabase($item['item']);

            // 合并信息
            $item['icon'] = $itemInfo['icon'];
            $item['item_info'] = $itemInfo['explanation'];
            $item['price_type_name'] = self::getPriceTypeName($item['price_type']);
            $item['class_name'] = self::getClassName($item['class']);
            $item['restriction_name'] = self::getRestrictionName($item['restriction']);

            return $item;
        } catch (\Exception $e) {
            Log::error('获取商品详情失败: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 添加商品
     * @param array $data 商品数据
     * @return int|false 返回新商品ID或false
     */
    public static function addItem($data)
    {
        try {
            // 过滤允许添加的字段
            $allowedFields = ['name', 'item', 'price', 'price_type', 'class', 'status', 'limit_quantity', 'restriction', 'delivery_mode', 'item_info', 'io', 'ioo'];
            $insertData = [];

            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $insertData[$field] = $data[$field];
                }
            }

            // 设置默认值
            $insertData['status'] = $insertData['status'] ?? 1; // 默认上架
            $insertData['delivery_mode'] = $insertData['delivery_mode'] ?? 1; // 默认批量发送
            $insertData['limit_quantity'] = $insertData['limit_quantity'] ?? 1;
            $insertData['restriction'] = $insertData['restriction'] ?? 0;
            $insertData['io'] = $insertData['io'] ?? 1;

            // 字段映射处理
            if (isset($data['category'])) {
                $insertData['class'] = $data['category'];
            }
            if (isset($data['description'])) {
                $insertData['item_info'] = $data['description'];
            }

            $result = Db::table('pointshop')->insertGetId($insertData);

            if ($result) {
                // 清除相关缓存
                self::clearAllItemsCache();
                Log::info('商品添加成功', ['item_id' => $result, 'data' => $insertData]);
                return $result;
            }

            return false;
        } catch (\Exception $e) {
            Log::error('添加商品失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 更新商品信息
     * @param int $itemId 商品ID
     * @param array $data 更新数据
     * @return bool
     */
    public static function updateItem($itemId, $data)
    {
        try {
            // 过滤允许更新的字段
            $allowedFields = ['name', 'price', 'price_type', 'class', 'status', 'limit_quantity', 'restriction', 'delivery_mode', 'item_info'];
            $updateData = [];

            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $updateData[$field] = $data[$field];
                }
            }

            // 字段映射处理
            if (isset($data['category'])) {
                $updateData['class'] = $data['category'];
            }
            if (isset($data['description'])) {
                $updateData['item_info'] = $data['description'];
            }

            if (empty($updateData)) {
                return false;
            }

            $result = Db::table('pointshop')
                ->where('id', $itemId)
                ->update($updateData);

            // 清除相关缓存
            self::clearAllItemsCache();

            return $result !== false;
        } catch (\Exception $e) {
            Log::error('更新商品失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 购买商品业务逻辑
     * @param int $userId 用户ID
     * @param int $itemId 商品ID
     * @param int $quantity 购买数量
     * @param callable $deductCurrency 回调函数，处理货币扣除
     * @return array
     */
    public static function buyItem($userId, $itemId, $quantity, callable $deductCurrency)
    {
        if (empty($itemId)) {
            return [
                'code' => 400,
                'message' => '请选择要购买的商品',
                'data' => null
            ];
        }
        
        try {
            // 获取商品信息
            $item = Db::table('pointshop')
                ->where('id', $itemId)
                ->where('status', 1)
                ->find();
                
            if (!$item) {
                return [
                    'code' => 404,
                    'message' => '商品不存在或已下架',
                    'data' => null
                ];
            }
            
            // 创建物品发送服务实例
            $itemDeliveryService = new \app\service\ItemDeliveryService();
            
            // 检查购买限制（需要先获取商品信息）
            $itemInfo = [
                'limit_quantity' => $item['limit_quantity'] ?? 999,
                'restriction' => $item['restriction'] ?? 0
            ];
            
            // 这里暂时跳过购买限制检查，因为checkPurchaseLimit是私有方法
            // 实际的限制检查会在purchaseAndDeliverItem方法中进行
            
            $totalPrice = $item['price'] * $quantity;
            $operations = [];
            $currencyName = '';
            $currencyType = '';
            
            switch ($item['price_type']) {
                case 1:
                    $operations[] = ['type' => 'coin', 'amount' => $totalPrice];
                    $currencyName = '泡点';
                    $currencyType = 'coin';
                    break;
                case 2:
                    $operations[] = ['type' => 'silver', 'amount' => $totalPrice];
                    $currencyName = '积分';
                    $currencyType = 'silver';
                    break;
                case 3:
                    $operations[] = ['type' => 'c_coin', 'amount' => $totalPrice];
                    $currencyName = 'C币';
                    $currencyType = 'c_coin';
                    break;
                default:
                    return [
                        'code' => 400,
                        'message' => '无效的货币类型',
                        'data' => null
                    ];
            }
            
            // 使用物品发送系统购买并发送物品（包含货币扣除）
            $orderResult = $itemDeliveryService->purchaseAndDeliverItem(
                $userId,
                $itemId, // 使用商城商品ID
                $quantity,
                'shop'
            );
            
            if (!$orderResult['success']) {
                return [
                    'code' => 500,
                    'message' => '购买失败：' . $orderResult['message'],
                    'data' => null
                ];
            }
            
            // 物品发送系统已经完成了货币扣除，不需要再次扣除
            $deductResult = ['code' => 200, 'message' => '货币扣除成功', 'data' => null];
            
            Log::info('商品购买成功', [
                'user_id' => $userId,
                'item_id' => $itemId,
                'quantity' => $quantity,
                'total_price' => $totalPrice,
                'currency_type' => $currencyType,
                'order_no' => $orderResult['data']['order_no'] ?? null
            ]);
            
            return [
                'code' => 200,
                'message' => '购买成功，物品已发送到游戏中',
                'data' => [
                    'order_no' => $orderResult['data']['order_no'] ?? null,
                    'item' => $item,
                    'quantity' => $quantity,
                    'total_price' => $totalPrice,
                    'currency_type' => $currencyName,
                    'deduct_details' => $deductResult['data'] ?? null,
                    'delivery_status' => 'completed',
                    'unique_num' => $orderResult['data']['unique_num'] ?? null,
                    'item_name' => $orderResult['data']['item_name'] ?? $item['name']
                ]
            ];
            
        } catch (\Exception $e) {
            return [
                'code' => 500,
                'message' => '购买失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 清除商品列表缓存
     * @param string $category 可选，指定分类
     * @param string $search 可选，指定搜索关键词
     * @return bool
     */
    public static function clearItemsCache($category = '', $search = '')
    {
        try {
            // 如果指定了具体参数，只清除对应的缓存
            if (!empty($category) || !empty($search)) {
                // 清除指定条件的缓存（需要遍历可能的分页）
                for ($page = 1; $page <= 10; $page++) { // 假设最多10页
                    for ($limit = 10; $limit <= 20; $limit += 10) { // 常用的分页大小
                        $cacheKey = self::buildItemsCacheKey($page, $limit, $category, $search);
                        CacheService::delete($cacheKey);
                    }
                }
            } else {
                // 清除所有商品列表缓存
                CacheService::clearByPrefix('items:list:');
            }
            
            Log::info('商品列表缓存已清除', ['category' => $category, 'search' => $search]);
            return true;
        } catch (\Exception $e) {
            Log::error('清除商品列表缓存失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 清除指定商品的信息缓存
     * @param int $itemId 商品ID
     * @return bool
     */
    public static function clearItemInfoCache($itemId)
    {
        try {
            $cacheKey = 'item:info:' . $itemId;
            $result = CacheService::delete($cacheKey);
            Log::info('商品信息缓存已清除', ['item_id' => $itemId]);
            return $result;
        } catch (\Exception $e) {
            Log::error('清除商品信息缓存失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 清除所有商品相关缓存
     * @return bool
     */
    public static function clearAllItemsCache()
    {
        try {
            // 清除商品列表缓存
            CacheService::clearByPrefix('items:list:');
            // 清除商品信息缓存
            CacheService::clearByPrefix('item:info:');
            
            Log::info('所有商品缓存已清除');
            return true;
        } catch (\Exception $e) {
            Log::error('清除所有商品缓存失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 预热商品缓存
     * @param int $maxPages 预热的最大页数
     * @return bool
     */
    public static function warmupItemsCache($maxPages = 5)
    {
        try {
            Log::info('开始预热商品缓存');
            
            // 预热主要的商品列表页面
            for ($page = 1; $page <= $maxPages; $page++) {
                // 预热全部分类
                self::getItems($page, 10, '', '');
                
                // 预热各个分类
                for ($category = 1; $category <= 8; $category++) {
                    self::getItems($page, 10, (string)$category, '');
                }
            }
            
            Log::info('商品缓存预热完成', ['pages' => $maxPages]);
            return true;
        } catch (\Exception $e) {
            Log::error('商品缓存预热失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 更新商品状态
     *
     * @param int $itemId 商品ID
     * @param int $status 状态 (1=上架, 2=下架)
     * @return bool
     */
    public static function updateItemStatus($itemId, $status)
    {
        try {
            // 更新数据库中的商品状态
            $result = Db::table('pointshop')
                ->where('id', $itemId)
                ->update(['status' => $status]);

            if ($result !== false) {
                // 清除相关缓存
                self::clearAllItemsCache();
                Log::info('商品状态更新成功', ['item_id' => $itemId, 'status' => $status]);
                return true;
            }

            return false;
        } catch (\Exception $e) {
            Log::error('更新商品状态失败: ' . $e->getMessage(), ['item_id' => $itemId, 'status' => $status]);
            return false;
        }
    }

    /**
     * 删除商品
     *
     * @param int $itemId 商品ID
     * @return bool
     */
    public static function deleteItem($itemId)
    {
        try {
            // 删除数据库中的商品
            $result = Db::table('pointshop')
                ->where('id', $itemId)
                ->delete();

            if ($result !== false) {
                // 清除相关缓存
                self::clearAllItemsCache();
                Log::info('商品删除成功', ['item_id' => $itemId]);
                return true;
            }

            return false;
        } catch (\Exception $e) {
            Log::error('删除商品失败: ' . $e->getMessage(), ['item_id' => $itemId]);
            return false;
        }
    }
}