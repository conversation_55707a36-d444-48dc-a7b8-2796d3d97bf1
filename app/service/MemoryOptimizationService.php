<?php

namespace app\service;

use think\facade\Log;
use think\facade\Cache;

/**
 * 内存优化服务
 * 监控和优化应用程序内存使用
 */
class MemoryOptimizationService
{
    /**
     * 内存配置
     */
    private static $config = [
        'memory_limit_warning' => 80, // 内存使用警告阈值（%）
        'memory_limit_critical' => 90, // 内存使用严重阈值（%）
        'gc_threshold' => 70, // 垃圾回收触发阈值（%）
        'monitoring_interval' => 60, // 监控间隔（秒）
    ];

    /**
     * 内存统计
     */
    private static $stats = [
        'peak_memory' => 0,
        'current_memory' => 0,
        'gc_collections' => 0,
        'memory_warnings' => 0,
        'memory_optimizations' => 0,
    ];

    /**
     * 内存快照
     */
    private static $snapshots = [];

    /**
     * 开始内存监控
     * 
     * @param string $operation 操作名称
     * @return string 监控ID
     */
    public static function startMonitoring(string $operation): string
    {
        $monitorId = uniqid('mem_', true);
        
        self::$snapshots[$monitorId] = [
            'operation' => $operation,
            'start_memory' => memory_get_usage(true),
            'start_peak' => memory_get_peak_usage(true),
            'start_time' => microtime(true),
            'allocations' => [],
        ];
        
        return $monitorId;
    }

    /**
     * 结束内存监控
     * 
     * @param string $monitorId 监控ID
     * @return array 内存报告
     */
    public static function endMonitoring(string $monitorId): array
    {
        if (!isset(self::$snapshots[$monitorId])) {
            return [];
        }

        $snapshot = self::$snapshots[$monitorId];
        $endMemory = memory_get_usage(true);
        $endPeak = memory_get_peak_usage(true);
        $endTime = microtime(true);

        $report = [
            'operation' => $snapshot['operation'],
            'duration' => round($endTime - $snapshot['start_time'], 4),
            'memory_start' => $snapshot['start_memory'],
            'memory_end' => $endMemory,
            'memory_used' => $endMemory - $snapshot['start_memory'],
            'memory_peak' => $endPeak,
            'memory_peak_diff' => $endPeak - $snapshot['start_peak'],
            'memory_start_mb' => round($snapshot['start_memory'] / 1024 / 1024, 2),
            'memory_end_mb' => round($endMemory / 1024 / 1024, 2),
            'memory_used_mb' => round(($endMemory - $snapshot['start_memory']) / 1024 / 1024, 2),
            'memory_peak_mb' => round($endPeak / 1024 / 1024, 2),
            'allocations' => $snapshot['allocations'],
        ];

        // 更新统计
        self::$stats['current_memory'] = $endMemory;
        self::$stats['peak_memory'] = max(self::$stats['peak_memory'], $endPeak);

        // 检查内存使用情况
        self::checkMemoryUsage($report);

        // 清理快照
        unset(self::$snapshots[$monitorId]);

        return $report;
    }

    /**
     * 记录内存分配
     * 
     * @param string $monitorId 监控ID
     * @param string $description 描述
     * @param int $size 分配大小
     */
    public static function recordAllocation(string $monitorId, string $description, int $size = null): void
    {
        if (!isset(self::$snapshots[$monitorId])) {
            return;
        }

        $currentMemory = memory_get_usage(true);
        $allocation = [
            'description' => $description,
            'memory_before' => $currentMemory,
            'size' => $size,
            'timestamp' => microtime(true),
        ];

        self::$snapshots[$monitorId]['allocations'][] = $allocation;
    }

    /**
     * 检查内存使用情况
     * 
     * @param array $report 内存报告
     */
    private static function checkMemoryUsage(array $report): void
    {
        $memoryLimit = self::getMemoryLimit();
        if ($memoryLimit <= 0) {
            return;
        }

        $currentUsage = $report['memory_end'];
        $usagePercent = ($currentUsage / $memoryLimit) * 100;

        if ($usagePercent >= self::$config['memory_limit_critical']) {
            self::$stats['memory_warnings']++;
            Log::error('内存使用严重警告', [
                'operation' => $report['operation'],
                'memory_usage_mb' => $report['memory_end_mb'],
                'memory_limit_mb' => round($memoryLimit / 1024 / 1024, 2),
                'usage_percent' => round($usagePercent, 2)
            ]);
            
            // 触发紧急垃圾回收
            self::forceGarbageCollection();
            
        } elseif ($usagePercent >= self::$config['memory_limit_warning']) {
            self::$stats['memory_warnings']++;
            Log::warning('内存使用警告', [
                'operation' => $report['operation'],
                'memory_usage_mb' => $report['memory_end_mb'],
                'usage_percent' => round($usagePercent, 2)
            ]);
        }

        if ($usagePercent >= self::$config['gc_threshold']) {
            self::optimizeMemory();
        }
    }

    /**
     * 优化内存使用
     */
    public static function optimizeMemory(): void
    {
        try {
            self::$stats['memory_optimizations']++;
            
            // 1. 强制垃圾回收
            $beforeGC = memory_get_usage(true);
            self::forceGarbageCollection();
            $afterGC = memory_get_usage(true);
            
            // 2. 清理过期缓存
            self::clearExpiredCache();
            
            // 3. 清理临时变量
            self::clearTemporaryData();
            
            $memoryFreed = $beforeGC - $afterGC;
            
            Log::info('内存优化完成', [
                'memory_before_mb' => round($beforeGC / 1024 / 1024, 2),
                'memory_after_mb' => round($afterGC / 1024 / 1024, 2),
                'memory_freed_mb' => round($memoryFreed / 1024 / 1024, 2)
            ]);
            
        } catch (\Exception $e) {
            Log::error('内存优化失败: ' . $e->getMessage());
        }
    }

    /**
     * 强制垃圾回收
     */
    public static function forceGarbageCollection(): void
    {
        if (function_exists('gc_collect_cycles')) {
            $collected = gc_collect_cycles();
            self::$stats['gc_collections']++;
            
            Log::debug('垃圾回收执行', [
                'cycles_collected' => $collected,
                'memory_after_mb' => round(memory_get_usage(true) / 1024 / 1024, 2)
            ]);
        }
    }

    /**
     * 清理过期缓存
     */
    private static function clearExpiredCache(): void
    {
        try {
            // 清理应用缓存中的过期项
            Cache::clear();
        } catch (\Exception $e) {
            Log::error('清理过期缓存失败: ' . $e->getMessage());
        }
    }

    /**
     * 清理临时数据
     */
    private static function clearTemporaryData(): void
    {
        // 清理快照数据（保留最近的10个）
        if (count(self::$snapshots) > 10) {
            $keys = array_keys(self::$snapshots);
            $toRemove = array_slice($keys, 0, -10);
            foreach ($toRemove as $key) {
                unset(self::$snapshots[$key]);
            }
        }
    }

    /**
     * 获取内存限制
     * 
     * @return int 内存限制（字节）
     */
    private static function getMemoryLimit(): int
    {
        $memoryLimit = ini_get('memory_limit');
        
        if ($memoryLimit == -1) {
            return 0; // 无限制
        }
        
        // 转换为字节
        $value = (int)$memoryLimit;
        $unit = strtolower(substr($memoryLimit, -1));
        
        switch ($unit) {
            case 'g':
                $value *= 1024 * 1024 * 1024;
                break;
            case 'm':
                $value *= 1024 * 1024;
                break;
            case 'k':
                $value *= 1024;
                break;
        }
        
        return $value;
    }

    /**
     * 获取内存统计信息
     * 
     * @return array
     */
    public static function getStats(): array
    {
        $currentMemory = memory_get_usage(true);
        $peakMemory = memory_get_peak_usage(true);
        $memoryLimit = self::getMemoryLimit();
        
        $stats = self::$stats;
        $stats['current_memory'] = $currentMemory;
        $stats['peak_memory'] = max($stats['peak_memory'], $peakMemory);
        $stats['memory_limit'] = $memoryLimit;
        
        // 计算百分比
        if ($memoryLimit > 0) {
            $stats['current_usage_percent'] = round(($currentMemory / $memoryLimit) * 100, 2);
            $stats['peak_usage_percent'] = round(($stats['peak_memory'] / $memoryLimit) * 100, 2);
        } else {
            $stats['current_usage_percent'] = 0;
            $stats['peak_usage_percent'] = 0;
        }
        
        // 格式化为MB
        $stats['current_memory_mb'] = round($currentMemory / 1024 / 1024, 2);
        $stats['peak_memory_mb'] = round($stats['peak_memory'] / 1024 / 1024, 2);
        $stats['memory_limit_mb'] = $memoryLimit > 0 ? round($memoryLimit / 1024 / 1024, 2) : 'unlimited';
        
        return $stats;
    }

    /**
     * 获取内存使用详情
     * 
     * @return array
     */
    public static function getMemoryDetails(): array
    {
        return [
            'current_usage' => memory_get_usage(false),
            'current_usage_real' => memory_get_usage(true),
            'peak_usage' => memory_get_peak_usage(false),
            'peak_usage_real' => memory_get_peak_usage(true),
            'memory_limit' => self::getMemoryLimit(),
            'active_snapshots' => count(self::$snapshots),
            'gc_status' => function_exists('gc_status') ? gc_status() : null,
        ];
    }

    /**
     * 重置统计信息
     */
    public static function resetStats(): void
    {
        self::$stats = [
            'peak_memory' => 0,
            'current_memory' => 0,
            'gc_collections' => 0,
            'memory_warnings' => 0,
            'memory_optimizations' => 0,
        ];
        
        self::$snapshots = [];
    }
}
