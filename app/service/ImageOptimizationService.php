<?php

namespace app\service;

use think\facade\Log;
use think\facade\Cache;

/**
 * 图片优化服务
 * 提供图片懒加载、CDN缓存、压缩等功能
 */
class ImageOptimizationService
{
    /**
     * 图片配置
     */
    private static $config = [
        'base_url' => '/static/images/',
        'default_image' => '/static/images/default.png',
        'cache_ttl' => 86400, // 24小时
        'supported_formats' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
        'max_size' => 2 * 1024 * 1024, // 2MB
    ];

    /**
     * 图片缓存
     */
    private static $imageCache = [];

    /**
     * 获取优化的图片URL
     * 
     * @param string $imagePath 图片路径
     * @param array $options 优化选项
     * @return string
     */
    public static function getOptimizedImageUrl(string $imagePath, array $options = []): string
    {
        try {
            // 检查缓存
            $cacheKey = 'image_url:' . md5($imagePath . serialize($options));
            $cached = Cache::get($cacheKey);
            if ($cached !== null) {
                return $cached;
            }

            // 处理图片路径
            $processedUrl = self::processImagePath($imagePath, $options);
            
            // 缓存结果
            Cache::set($cacheKey, $processedUrl, self::$config['cache_ttl']);
            
            return $processedUrl;
            
        } catch (\Exception $e) {
            Log::error('图片URL优化失败: ' . $e->getMessage(), [
                'image_path' => $imagePath,
                'options' => $options
            ]);
            return self::$config['default_image'];
        }
    }

    /**
     * 处理图片路径
     * 
     * @param string $imagePath 原始图片路径
     * @param array $options 选项
     * @return string
     */
    private static function processImagePath(string $imagePath, array $options): string
    {
        // 如果是完整URL，直接返回
        if (filter_var($imagePath, FILTER_VALIDATE_URL)) {
            return $imagePath;
        }

        // 处理相对路径
        if (!str_starts_with($imagePath, '/')) {
            $imagePath = self::$config['base_url'] . $imagePath;
        }

        // 检查文件是否存在
        $fullPath = public_path() . $imagePath;
        if (!file_exists($fullPath)) {
            Log::warning('图片文件不存在', [
                'image_path' => $imagePath,
                'full_path' => $fullPath
            ]);
            return self::$config['default_image'];
        }

        // 应用优化选项
        if (!empty($options)) {
            $imagePath = self::applyImageOptions($imagePath, $options);
        }

        return $imagePath;
    }

    /**
     * 应用图片优化选项
     * 
     * @param string $imagePath 图片路径
     * @param array $options 选项
     * @return string
     */
    private static function applyImageOptions(string $imagePath, array $options): string
    {
        $params = [];

        // 尺寸调整
        if (isset($options['width'])) {
            $params['w'] = (int)$options['width'];
        }
        if (isset($options['height'])) {
            $params['h'] = (int)$options['height'];
        }

        // 质量设置
        if (isset($options['quality'])) {
            $params['q'] = max(1, min(100, (int)$options['quality']));
        }

        // 格式转换
        if (isset($options['format']) && in_array($options['format'], self::$config['supported_formats'])) {
            $params['f'] = $options['format'];
        }

        // 如果有参数，添加到URL
        if (!empty($params)) {
            $imagePath .= '?' . http_build_query($params);
        }

        return $imagePath;
    }

    /**
     * 生成懒加载图片HTML
     * 
     * @param string $imagePath 图片路径
     * @param array $attributes HTML属性
     * @param array $options 优化选项
     * @return string
     */
    public static function generateLazyImage(string $imagePath, array $attributes = [], array $options = []): string
    {
        $imageUrl = self::getOptimizedImageUrl($imagePath, $options);
        
        // 默认属性
        $defaultAttributes = [
            'loading' => 'lazy',
            'decoding' => 'async',
            'alt' => '',
            'class' => 'lazy-image'
        ];

        $attributes = array_merge($defaultAttributes, $attributes);
        
        // 构建HTML
        $html = '<img';
        foreach ($attributes as $key => $value) {
            $html .= ' ' . $key . '="' . htmlspecialchars($value) . '"';
        }
        $html .= ' data-src="' . htmlspecialchars($imageUrl) . '"';
        $html .= ' src="data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 1 1\'%3E%3C/svg%3E"';
        $html .= '>';

        return $html;
    }

    /**
     * 批量优化图片URL
     * 
     * @param array $imagePaths 图片路径数组
     * @param array $options 优化选项
     * @return array
     */
    public static function batchOptimizeImages(array $imagePaths, array $options = []): array
    {
        $results = [];
        
        foreach ($imagePaths as $key => $imagePath) {
            $results[$key] = self::getOptimizedImageUrl($imagePath, $options);
        }
        
        return $results;
    }

    /**
     * 预加载关键图片
     * 
     * @param array $imagePaths 关键图片路径
     * @return string 预加载HTML
     */
    public static function generatePreloadLinks(array $imagePaths): string
    {
        $html = '';
        
        foreach ($imagePaths as $imagePath) {
            $imageUrl = self::getOptimizedImageUrl($imagePath);
            $html .= '<link rel="preload" as="image" href="' . htmlspecialchars($imageUrl) . '">' . "\n";
        }
        
        return $html;
    }

    /**
     * 获取图片信息
     * 
     * @param string $imagePath 图片路径
     * @return array
     */
    public static function getImageInfo(string $imagePath): array
    {
        try {
            $fullPath = public_path() . $imagePath;
            
            if (!file_exists($fullPath)) {
                return [
                    'exists' => false,
                    'error' => 'File not found'
                ];
            }

            $info = getimagesize($fullPath);
            $fileSize = filesize($fullPath);
            
            return [
                'exists' => true,
                'width' => $info[0] ?? 0,
                'height' => $info[1] ?? 0,
                'type' => $info[2] ?? 0,
                'mime' => $info['mime'] ?? '',
                'size' => $fileSize,
                'size_formatted' => self::formatFileSize($fileSize),
                'path' => $imagePath,
                'url' => self::getOptimizedImageUrl($imagePath)
            ];
            
        } catch (\Exception $e) {
            Log::error('获取图片信息失败: ' . $e->getMessage(), [
                'image_path' => $imagePath
            ]);
            
            return [
                'exists' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 格式化文件大小
     * 
     * @param int $size 文件大小（字节）
     * @return string
     */
    private static function formatFileSize(int $size): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $unitIndex = 0;
        
        while ($size >= 1024 && $unitIndex < count($units) - 1) {
            $size /= 1024;
            $unitIndex++;
        }
        
        return round($size, 2) . ' ' . $units[$unitIndex];
    }

    /**
     * 清理图片缓存
     * 
     * @param string $pattern 缓存键模式
     */
    public static function clearImageCache(string $pattern = 'image_url:*'): void
    {
        try {
            Cache::clear();
            Log::info('图片缓存清理完成', ['pattern' => $pattern]);
        } catch (\Exception $e) {
            Log::error('图片缓存清理失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取图片缓存统计
     * 
     * @return array
     */
    public static function getCacheStats(): array
    {
        try {
            return [
                'cache_size' => count(self::$imageCache),
                'memory_usage' => memory_get_usage(true),
                'cache_ttl' => self::$config['cache_ttl'],
                'supported_formats' => self::$config['supported_formats']
            ];
        } catch (\Exception $e) {
            Log::error('获取图片缓存统计失败: ' . $e->getMessage());
            return [];
        }
    }
}
