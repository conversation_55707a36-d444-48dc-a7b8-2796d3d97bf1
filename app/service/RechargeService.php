<?php

namespace app\service;

use think\facade\Db;
use think\facade\Log;
use think\facade\Session;

/**
 * 充值服务类
 * 负责处理C币、泡点、积分的手动充值功能
 * 遵循单一职责原则：专门处理充值相关业务逻辑
 */
class RechargeService
{
    /**
     * 支持的货币类型
     */
    const CURRENCY_C_COIN = 'c_coin';
    const CURRENCY_POINT = 'point';
    const CURRENCY_SCORE = 'score';
    
    /**
     * 货币类型映射
     */
    const CURRENCY_NAMES = [
        self::CURRENCY_C_COIN => 'C币',
        self::CURRENCY_POINT => '泡点',
        self::CURRENCY_SCORE => '积分'
    ];
    
    /**
     * 操作类型
     */
    const OPERATION_ADD = 'add';
    const OPERATION_SUBTRACT = 'subtract';
    const OPERATION_SET = 'set';
    
    /**
     * 缓存服务实例
     * @var CacheService
     */
    private $cacheService;
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->cacheService = new CacheService();
    }
    
    /**
     * 获取支付设置
     * 
     * @return array
     */
    private function getPaymentSettings(): array
    {
        try {
            $configFile = app()->getRootPath() . 'config/payment_settings.php';
            
            if (file_exists($configFile)) {
                return include $configFile;
            } else {
                // 默认设置
                return [
                    'ccoin_bonus_rate' => 10,
                    'coin_bonus_rate' => 5
                ];
            }
        } catch (\Exception $e) {
            Log::error('获取支付设置失败: ' . $e->getMessage());
            return [
                'ccoin_bonus_rate' => 10,
                'coin_bonus_rate' => 5
            ];
        }
    }
    
    /**
     * 计算积分赠送数量
     * 
     * @param string $currencyType 货币类型
     * @param int $amount 充值金额
     * @return int 赠送的积分数量
     */
    private function calculateBonusScore(string $currencyType, int $amount): int
    {
        $settings = $this->getPaymentSettings();
        
        switch ($currencyType) {
            case self::CURRENCY_C_COIN:
                $bonusRate = $settings['ccoin_bonus_rate'] ?? 10;
                return (int)($amount * $bonusRate / 100);
            case self::CURRENCY_POINT:
                $bonusRate = $settings['coin_bonus_rate'] ?? 5;
                return (int)($amount * $bonusRate / 100);
            default:
                return 0;
        }
    }
    
    /**
     * 赠送积分
     * 
     * @param string $username 用户名
     * @param int $bonusAmount 赠送数量
     * @return bool 是否成功
     */
    private function giveScoreBonus(string $username, int $bonusAmount): bool
    {
        if ($bonusAmount <= 0) {
            return true; // 没有赠送，视为成功
        }
        
        try {
            // 获取当前积分余额
            $currentScore = $this->getCurrentBalance($username, self::CURRENCY_SCORE);
            if ($currentScore === false) {
                $currentScore = 0; // 如果获取失败，默认为0
            }
            
            // 计算新的积分余额
            $newScore = $currentScore + $bonusAmount;
            
            // 更新积分余额
            $result = $this->updateScoreBalance($username, $newScore);
            
            if ($result['success']) {
                Log::info("积分赠送成功 - 用户: {$username}, 赠送数量: {$bonusAmount}, 原余额: {$currentScore}, 新余额: {$newScore}");
                return true;
            } else {
                Log::error("积分赠送失败 - 用户: {$username}, 赠送数量: {$bonusAmount}, 错误: " . ($result['message'] ?? '未知错误'));
                return false;
            }
        } catch (\Exception $e) {
            Log::error("积分赠送异常 - 用户: {$username}, 赠送数量: {$bonusAmount}, 错误: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 执行充值操作
     * 
     * @param string $username 用户名
     * @param string $currencyType 货币类型
     * @param string $operation 操作类型
     * @param int $amount 金额
     * @param string $reason 充值原因
     * @param string $adminUser 操作管理员
     * @return array 操作结果
     */
    public function recharge(string $username, string $currencyType, string $operation, int $amount, string $reason = '', string $adminUser = ''): array
    {
        try {
            // 参数验证
            $validationResult = $this->validateRechargeParams($username, $currencyType, $operation, $amount);
            if (!$validationResult['success']) {
                return $validationResult;
            }
            
            // 检查用户是否存在
            $userExists = $this->checkUserExists($username);
            if (!$userExists) {
                return [
                    'success' => false,
                    'message' => '用户不存在',
                    'data' => null
                ];
            }
            
            // 获取当前余额
            $currentBalance = $this->getCurrentBalance($username, $currencyType);
            if ($currentBalance === false) {
                return [
                    'success' => false,
                    'message' => '获取用户当前余额失败',
                    'data' => null
                ];
            }
            
            // 计算新余额
            $newBalance = $this->calculateNewBalance($currentBalance, $operation, $amount);
            if ($newBalance < 0) {
                return [
                    'success' => false,
                    'message' => '余额不足，无法执行扣除操作',
                    'data' => null
                ];
            }
            
            // 执行余额更新
            $updateResult = $this->updateBalance($username, $currencyType, $newBalance);
            if (!$updateResult['success']) {
                return $updateResult;
            }
            
            // 处理积分赠送（仅在增加操作时）
            $bonusScoreAmount = 0;
            if ($operation === self::OPERATION_ADD && in_array($currencyType, [self::CURRENCY_C_COIN, self::CURRENCY_POINT])) {
                $bonusScoreAmount = $this->calculateBonusScore($currencyType, $amount);
                if ($bonusScoreAmount > 0) {
                    $bonusResult = $this->giveScoreBonus($username, $bonusScoreAmount);
                    if (!$bonusResult) {
                        Log::warning("积分赠送失败，但主要充值操作已成功 - 用户: {$username}, 货币: {$currencyType}, 金额: {$amount}, 应赠送积分: {$bonusScoreAmount}");
                    }
                }
            }
            
            // 记录充值日志
            $this->logRechargeOperation($username, $currencyType, $operation, $amount, $currentBalance, $newBalance, $reason, $adminUser, $bonusScoreAmount);
            
            // 清除用户余额缓存
            $this->clearUserBalanceCache($username);
            
            return [
                'success' => true,
                'message' => $this->getOperationSuccessMessage($currencyType, $operation, $amount),
                'bonus_score_amount' => $bonusScoreAmount,
                'data' => [
                    'username' => $username,
                    'currency_type' => $currencyType,
                    'currency_name' => self::CURRENCY_NAMES[$currencyType],
                    'operation' => $operation,
                    'amount' => $amount,
                    'old_balance' => $currentBalance,
                    'new_balance' => $newBalance,
                    'change_amount' => $newBalance - $currentBalance,
                    'bonus_score_amount' => $bonusScoreAmount
                ]
            ];
            
        } catch (\Exception $e) {
            Log::error('充值操作失败: ' . $e->getMessage(), [
                'username' => $username,
                'currency_type' => $currencyType,
                'operation' => $operation,
                'amount' => $amount,
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return [
                'success' => false,
                'message' => '充值操作失败: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 验证充值参数
     * 
     * @param string $username 用户名
     * @param string $currencyType 货币类型
     * @param string $operation 操作类型
     * @param int $amount 金额
     * @return array 验证结果
     */
    private function validateRechargeParams(string $username, string $currencyType, string $operation, int $amount): array
    {
        // 验证用户名
        if (empty($username)) {
            return ['success' => false, 'message' => '用户名不能为空'];
        }
        
        // 验证货币类型
        if (!in_array($currencyType, [self::CURRENCY_C_COIN, self::CURRENCY_POINT, self::CURRENCY_SCORE])) {
            return ['success' => false, 'message' => '不支持的货币类型'];
        }
        
        // 验证操作类型
        if (!in_array($operation, [self::OPERATION_ADD, self::OPERATION_SUBTRACT, self::OPERATION_SET])) {
            return ['success' => false, 'message' => '不支持的操作类型'];
        }
        
        // 验证金额
        if ($amount < 0) {
            return ['success' => false, 'message' => '金额不能为负数'];
        }
        
        if ($operation === self::OPERATION_SET && $amount < 0) {
            return ['success' => false, 'message' => '设置余额不能为负数'];
        }
        
        return ['success' => true];
    }
    
    /**
     * 检查用户是否存在
     * 
     * @param string $username 用户名
     * @return bool 用户是否存在
     */
    private function checkUserExists(string $username): bool
    {
        try {
            // 检查seal_member数据库的idtable1-5表
            for ($i = 1; $i <= 5; $i++) {
                $user = Db::connect('seal_member')
                    ->table('idtable' . $i)
                    ->where('id', $username)
                    ->find();
                if ($user) {
                    return true;
                }
            }
            return false;
        } catch (\Exception $e) {
            Log::error('检查用户存在性失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 获取用户当前余额
     * 
     * @param string $username 用户名
     * @param string $currencyType 货币类型
     * @return int|false 当前余额或false表示失败
     */
    private function getCurrentBalance(string $username, string $currencyType)
    {
        try {
            switch ($currencyType) {
                case self::CURRENCY_C_COIN:
                    return $this->getCCoinBalance($username);
                case self::CURRENCY_POINT:
                    return $this->getPointBalance($username);
                case self::CURRENCY_SCORE:
                    return $this->getScoreBalance($username);
                default:
                    return false;
            }
        } catch (\Exception $e) {
            Log::error('获取用户余额失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 获取C币余额
     * 
     * @param string $username 用户名
     * @return int C币余额
     */
    private function getCCoinBalance(string $username): int
    {
        $numericUserId = is_numeric($username) ? (int)$username : (int)preg_replace('/[^0-9]/', '', $username);
        $balance = Db::table('c_coin')
            ->where('user_id', $numericUserId)
            ->value('balance');
        return $balance ?: 0;
    }
    
    /**
     * 获取泡点余额
     * 
     * @param string $username 用户名
     * @return int 泡点余额
     */
    private function getPointBalance(string $username): int
    {
        $totalBalance = 0;
        for ($i = 1; $i <= 5; $i++) {
            $balance = Db::connect('seal_member')
                ->table('idtable' . $i)
                ->where('id', $username)
                ->value('point');
            $totalBalance += $balance ?: 0;
        }
        return $totalBalance;
    }
    
    /**
     * 获取积分余额
     * 
     * @param string $username 用户名
     * @return int 积分余额
     */
    private function getScoreBalance(string $username): int
    {
        // 直接使用用户名查询积分余额
        $balance = Db::connect('seal_web')
            ->table('sealmember')
            ->where('id', $username)
            ->value('hahapoint');
        return $balance ?: 0;
    }
    
    /**
     * 计算新余额
     * 
     * @param int $currentBalance 当前余额
     * @param string $operation 操作类型
     * @param int $amount 金额
     * @return int 新余额
     */
    private function calculateNewBalance(int $currentBalance, string $operation, int $amount): int
    {
        switch ($operation) {
            case self::OPERATION_ADD:
                return $currentBalance + $amount;
            case self::OPERATION_SUBTRACT:
                return $currentBalance - $amount;
            case self::OPERATION_SET:
                return $amount;
            default:
                return $currentBalance;
        }
    }
    
    /**
     * 更新用户余额
     * 
     * @param string $username 用户名
     * @param string $currencyType 货币类型
     * @param int $newBalance 新余额
     * @return array 更新结果
     */
    private function updateBalance(string $username, string $currencyType, int $newBalance): array
    {
        try {
            switch ($currencyType) {
                case self::CURRENCY_C_COIN:
                    return $this->updateCCoinBalance($username, $newBalance);
                case self::CURRENCY_POINT:
                    return $this->updatePointBalance($username, $newBalance);
                case self::CURRENCY_SCORE:
                    return $this->updateScoreBalance($username, $newBalance);
                default:
                    return ['success' => false, 'message' => '不支持的货币类型'];
            }
        } catch (\Exception $e) {
            Log::error('更新用户余额失败: ' . $e->getMessage());
            return ['success' => false, 'message' => '更新余额失败: ' . $e->getMessage()];
        }
    }
    
    /**
     * 更新C币余额
     * 
     * @param string $username 用户名
     * @param int $newBalance 新余额
     * @return array 更新结果
     */
    private function updateCCoinBalance(string $username, int $newBalance): array
    {
        $numericUserId = is_numeric($username) ? (int)$username : (int)preg_replace('/[^0-9]/', '', $username);
        
        // 检查记录是否存在
        $exists = Db::table('c_coin')
            ->where('user_id', $numericUserId)
            ->find();
        
        if ($exists) {
            // 更新现有记录
            $result = Db::table('c_coin')
                ->where('user_id', $numericUserId)
                ->update(['balance' => $newBalance]);
        } else {
            // 创建新记录
            $result = Db::table('c_coin')
                ->insert([
                    'user_id' => $numericUserId,
                    'balance' => $newBalance,
                    'game_account' => $username,
                    'nickname' => $username
                ]);
        }
        
        return $result ? ['success' => true] : ['success' => false, 'message' => 'C币余额更新失败'];
    }
    
    /**
     * 更新泡点余额
     * 
     * @param string $username 用户名
     * @param int $newBalance 新余额
     * @return array 更新结果
     */
    private function updatePointBalance(string $username, int $newBalance): array
    {
        // 找到用户所在的表并更新
        for ($i = 1; $i <= 5; $i++) {
            $user = Db::connect('seal_member')
                ->table('idtable' . $i)
                ->where('id', $username)
                ->find();
            
            if ($user) {
                $result = Db::connect('seal_member')
                    ->table('idtable' . $i)
                    ->where('id', $username)
                    ->update(['point' => $newBalance]);
                
                return $result ? ['success' => true] : ['success' => false, 'message' => '泡点余额更新失败'];
            }
        }
        
        return ['success' => false, 'message' => '未找到用户记录'];
    }
    
    /**
     * 更新积分余额
     * 
     * @param string $username 用户名
     * @param int $newBalance 新余额
     * @return array 更新结果
     */
    private function updateScoreBalance(string $username, int $newBalance): array
    {
        // 直接使用用户名更新积分余额
        $result = Db::connect('seal_web')
            ->table('sealmember')
            ->where('id', $username)
            ->update(['hahapoint' => $newBalance]);
        
        return $result ? ['success' => true] : ['success' => false, 'message' => '积分余额更新失败'];
    }
    
    /**
     * 记录充值操作日志
     * 
     * @param string $username 用户名
     * @param string $currencyType 货币类型
     * @param string $operation 操作类型
     * @param int $amount 金额
     * @param int $oldBalance 原余额
     * @param int $newBalance 新余额
     * @param string $reason 充值原因
     * @param string $adminUser 操作管理员
     */
    private function logRechargeOperation(string $username, string $currencyType, string $operation, int $amount, int $oldBalance, int $newBalance, string $reason, string $adminUser, int $bonusScoreAmount = 0): void
    {
        try {
            // 记录到数据库日志表
            Db::table('recharge_logs')->insert([
                'username' => $username,
                'currency_type' => $currencyType,
                'operation' => $operation,
                'amount' => $amount,
                'old_balance' => $oldBalance,
                'new_balance' => $newBalance,
                'reason' => $reason,
                'admin_user' => $adminUser,
                'bonus_score_amount' => $bonusScoreAmount,
                'create_time' => date('Y-m-d H:i:s'),
                'ip_address' => request()->ip()
            ]);
        } catch (\Exception $e) {
            // 如果表不存在，只记录到文件日志
            Log::info('充值操作记录', [
                'username' => $username,
                'currency_type' => $currencyType,
                'currency_name' => self::CURRENCY_NAMES[$currencyType],
                'operation' => $operation,
                'amount' => $amount,
                'old_balance' => $oldBalance,
                'new_balance' => $newBalance,
                'change_amount' => $newBalance - $oldBalance,
                'bonus_score_amount' => $bonusScoreAmount,
                'reason' => $reason,
                'admin_user' => $adminUser,
                'ip_address' => request()->ip()
            ]);
        }
    }
    
    /**
     * 清除用户余额缓存
     * 
     * @param string $username 用户名
     */
    private function clearUserBalanceCache(string $username): void
    {
        try {
            $cacheKey = 'user_balance:' . $username;
            $this->cacheService->delete($cacheKey);
        } catch (\Exception $e) {
            Log::warning('清除用户余额缓存失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取操作成功消息
     * 
     * @param string $currencyType 货币类型
     * @param string $operation 操作类型
     * @param int $amount 金额
     * @return string 成功消息
     */
    private function getOperationSuccessMessage(string $currencyType, string $operation, int $amount): string
    {
        $currencyName = self::CURRENCY_NAMES[$currencyType];
        
        switch ($operation) {
            case self::OPERATION_ADD:
                return "成功为用户充值 {$amount} {$currencyName}";
            case self::OPERATION_SUBTRACT:
                return "成功为用户扣除 {$amount} {$currencyName}";
            case self::OPERATION_SET:
                return "成功将用户{$currencyName}余额设置为 {$amount}";
            default:
                return "操作完成";
        }
    }
    
    /**
     * 获取支持的货币类型列表
     * 
     * @return array 货币类型列表
     */
    public static function getSupportedCurrencies(): array
    {
        return [
            ['value' => self::CURRENCY_C_COIN, 'name' => self::CURRENCY_NAMES[self::CURRENCY_C_COIN]],
            ['value' => self::CURRENCY_POINT, 'name' => self::CURRENCY_NAMES[self::CURRENCY_POINT]],
            ['value' => self::CURRENCY_SCORE, 'name' => self::CURRENCY_NAMES[self::CURRENCY_SCORE]]
        ];
    }
    
    /**
     * 获取支持的操作类型列表
     *
     * @return array 操作类型列表
     */
    public static function getSupportedOperations(): array
    {
        return [
            ['value' => self::OPERATION_ADD, 'name' => '增加'],
            ['value' => self::OPERATION_SUBTRACT, 'name' => '扣除'],
            ['value' => self::OPERATION_SET, 'name' => '设置']
        ];
    }

    /**
     * 获取用户余额信息
     *
     * @param string $username 用户名
     * @return array|false 用户余额信息或false表示失败
     */
    public function getUserBalance(string $username)
    {
        try {
            if (!$this->checkUserExists($username)) {
                return false;
            }

            $balances = [];

            // 获取C币余额
            $cCoinBalance = $this->getCCoinBalance($username);
            if ($cCoinBalance !== false) {
                $balances[self::CURRENCY_C_COIN] = $cCoinBalance;
            }

            // 获取泡点余额
            $pointBalance = $this->getPointBalance($username);
            if ($pointBalance !== false) {
                $balances[self::CURRENCY_POINT] = $pointBalance;
            }

            // 获取积分余额
            $scoreBalance = $this->getScoreBalance($username);
            if ($scoreBalance !== false) {
                $balances[self::CURRENCY_SCORE] = $scoreBalance;
            }

            return [
                'username' => $username,
                'balances' => $balances
            ];
        } catch (\Exception $e) {
            Log::error('获取用户余额失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取充值记录
     *
     * @param int $page 页码
     * @param int $limit 每页数量
     * @param string $username 用户名筛选
     * @param string $currencyType 货币类型筛选
     * @param string $dateRange 日期范围筛选
     * @return array 充值记录列表
     */
    public function getRechargeHistory(int $page = 1, int $limit = 20, string $username = '', string $currencyType = '', string $dateRange = ''): array
    {
        try {
            $query = Db::table('recharge_logs');

            // 添加筛选条件
            if (!empty($username)) {
                $query->where('username', 'like', '%' . $username . '%');
            }

            if (!empty($currencyType)) {
                $query->where('currency_type', $currencyType);
            }

            if (!empty($dateRange)) {
                // 解析日期范围，格式：2024-01-01 to 2024-01-31
                $dates = explode(' to ', $dateRange);
                if (count($dates) === 2) {
                    $query->whereBetween('create_time', [$dates[0] . ' 00:00:00', $dates[1] . ' 23:59:59']);
                }
            }

            // 获取总数
            $total = $query->count();

            // 获取分页数据
            $list = $query->order('create_time', 'desc')
                ->limit(($page - 1) * $limit, $limit)
                ->select()
                ->toArray();

            // 格式化数据
            foreach ($list as &$item) {
                $item['currency_name'] = self::CURRENCY_NAMES[$item['currency_type']] ?? $item['currency_type'];
                $item['operation_name'] = $this->getOperationName($item['operation']);
            }

            return [
                'list' => $list,
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'pages' => ceil($total / $limit)
            ];
        } catch (\Exception $e) {
            Log::error('获取充值记录失败: ' . $e->getMessage());
            return [
                'list' => [],
                'total' => 0,
                'page' => $page,
                'limit' => $limit,
                'pages' => 0
            ];
        }
    }

    /**
     * 获取操作类型名称
     *
     * @param string $operation 操作类型
     * @return string 操作名称
     */
    private function getOperationName(string $operation): string
    {
        $names = [
            self::OPERATION_ADD => '增加',
            self::OPERATION_SUBTRACT => '扣除',
            self::OPERATION_SET => '设置'
        ];
        return $names[$operation] ?? $operation;
    }
}