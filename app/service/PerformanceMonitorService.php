<?php

namespace app\service;

use think\facade\Log;
use think\facade\Cache;

/**
 * 性能监控服务
 * 监控API响应时间、数据库查询性能、内存使用等
 */
class PerformanceMonitorService
{
    /**
     * 性能阈值配置
     */
    private static $thresholds = [
        'api_response_time' => 2.0,      // API响应时间阈值（秒）
        'db_query_time' => 1.0,          // 数据库查询时间阈值（秒）
        'memory_usage' => 128 * 1024 * 1024, // 内存使用阈值（128MB）
        'cache_hit_rate' => 80,          // 缓存命中率阈值（%）
    ];

    /**
     * 性能数据收集
     */
    private static $metrics = [];

    /**
     * 开始性能监控
     * 
     * @param string $operation 操作名称
     * @return string 监控ID
     */
    public static function start(string $operation): string
    {
        $monitorId = uniqid('perf_', true);
        
        self::$metrics[$monitorId] = [
            'operation' => $operation,
            'start_time' => microtime(true),
            'start_memory' => memory_get_usage(true),
            'start_peak_memory' => memory_get_peak_usage(true),
            'db_queries' => 0,
            'cache_operations' => 0,
        ];
        
        return $monitorId;
    }

    /**
     * 结束性能监控
     * 
     * @param string $monitorId 监控ID
     * @param array $additionalData 额外数据
     * @return array 性能报告
     */
    public static function end(string $monitorId, array $additionalData = []): array
    {
        if (!isset(self::$metrics[$monitorId])) {
            return [];
        }

        $metric = self::$metrics[$monitorId];
        $endTime = microtime(true);
        $endMemory = memory_get_usage(true);
        $endPeakMemory = memory_get_peak_usage(true);

        $report = [
            'operation' => $metric['operation'],
            'duration' => round($endTime - $metric['start_time'], 4),
            'memory_used' => $endMemory - $metric['start_memory'],
            'peak_memory' => $endPeakMemory - $metric['start_peak_memory'],
            'memory_usage_mb' => round($endMemory / 1024 / 1024, 2),
            'peak_memory_mb' => round($endPeakMemory / 1024 / 1024, 2),
            'timestamp' => date('Y-m-d H:i:s'),
        ];

        // 合并额外数据
        $report = array_merge($report, $additionalData);

        // 检查性能阈值
        $issues = self::checkThresholds($report);
        if (!empty($issues)) {
            $report['performance_issues'] = $issues;
            self::logPerformanceIssue($report);
        }

        // 记录性能数据
        self::recordMetrics($report);

        // 清理监控数据
        unset(self::$metrics[$monitorId]);

        return $report;
    }

    /**
     * 记录数据库查询性能
     * 
     * @param string $monitorId 监控ID
     * @param float $queryTime 查询时间
     * @param string $sql SQL语句
     */
    public static function recordDbQuery(string $monitorId, float $queryTime, string $sql = ''): void
    {
        if (isset(self::$metrics[$monitorId])) {
            self::$metrics[$monitorId]['db_queries']++;
            
            if (!isset(self::$metrics[$monitorId]['db_query_times'])) {
                self::$metrics[$monitorId]['db_query_times'] = [];
            }
            
            self::$metrics[$monitorId]['db_query_times'][] = [
                'time' => $queryTime,
                'sql' => substr($sql, 0, 100) // 只记录前100个字符
            ];
        }
    }

    /**
     * 记录缓存操作
     * 
     * @param string $monitorId 监控ID
     * @param string $operation 操作类型（get/set/delete）
     * @param bool $hit 是否命中
     */
    public static function recordCacheOperation(string $monitorId, string $operation, bool $hit = false): void
    {
        if (isset(self::$metrics[$monitorId])) {
            self::$metrics[$monitorId]['cache_operations']++;
            
            if (!isset(self::$metrics[$monitorId]['cache_stats'])) {
                self::$metrics[$monitorId]['cache_stats'] = [
                    'hits' => 0,
                    'misses' => 0,
                    'operations' => []
                ];
            }
            
            if ($operation === 'get') {
                if ($hit) {
                    self::$metrics[$monitorId]['cache_stats']['hits']++;
                } else {
                    self::$metrics[$monitorId]['cache_stats']['misses']++;
                }
            }
            
            self::$metrics[$monitorId]['cache_stats']['operations'][] = $operation;
        }
    }

    /**
     * 检查性能阈值
     * 
     * @param array $report 性能报告
     * @return array 性能问题列表
     */
    private static function checkThresholds(array $report): array
    {
        $issues = [];

        // 检查响应时间
        if ($report['duration'] > self::$thresholds['api_response_time']) {
            $issues[] = "API响应时间过长: {$report['duration']}s (阈值: " . self::$thresholds['api_response_time'] . "s)";
        }

        // 检查内存使用
        if ($report['memory_usage_mb'] > (self::$thresholds['memory_usage'] / 1024 / 1024)) {
            $issues[] = "内存使用过高: {$report['memory_usage_mb']}MB (阈值: " . (self::$thresholds['memory_usage'] / 1024 / 1024) . "MB)";
        }

        // 检查数据库查询时间
        if (isset($report['db_query_times'])) {
            foreach ($report['db_query_times'] as $query) {
                if ($query['time'] > self::$thresholds['db_query_time']) {
                    $issues[] = "数据库查询时间过长: {$query['time']}s (SQL: {$query['sql']})";
                }
            }
        }

        // 检查缓存命中率
        if (isset($report['cache_stats'])) {
            $total = $report['cache_stats']['hits'] + $report['cache_stats']['misses'];
            if ($total > 0) {
                $hitRate = ($report['cache_stats']['hits'] / $total) * 100;
                if ($hitRate < self::$thresholds['cache_hit_rate']) {
                    $issues[] = "缓存命中率过低: {$hitRate}% (阈值: " . self::$thresholds['cache_hit_rate'] . "%)";
                }
            }
        }

        return $issues;
    }

    /**
     * 记录性能指标
     * 
     * @param array $report 性能报告
     */
    private static function recordMetrics(array $report): void
    {
        try {
            // 记录到缓存中，用于统计分析
            $cacheKey = 'performance_metrics:' . date('Y-m-d-H');
            $metrics = Cache::get($cacheKey, []);
            $metrics[] = $report;
            
            // 只保留最近100条记录
            if (count($metrics) > 100) {
                $metrics = array_slice($metrics, -100);
            }
            
            Cache::set($cacheKey, $metrics, 3600); // 缓存1小时
            
        } catch (\Exception $e) {
            Log::error('性能指标记录失败: ' . $e->getMessage());
        }
    }

    /**
     * 记录性能问题
     * 
     * @param array $report 性能报告
     */
    private static function logPerformanceIssue(array $report): void
    {
        Log::warning('性能问题检测', [
            'operation' => $report['operation'],
            'duration' => $report['duration'],
            'memory_mb' => $report['memory_usage_mb'],
            'issues' => $report['performance_issues']
        ]);
    }

    /**
     * 获取性能统计
     * 
     * @param string $period 时间段（hour/day）
     * @return array
     */
    public static function getStats(string $period = 'hour'): array
    {
        try {
            $cacheKey = 'performance_metrics:' . date('Y-m-d-H');
            $metrics = Cache::get($cacheKey, []);
            
            if (empty($metrics)) {
                return [
                    'total_requests' => 0,
                    'avg_response_time' => 0,
                    'avg_memory_usage' => 0,
                    'slow_requests' => 0,
                    'operations' => []
                ];
            }
            
            $stats = [
                'total_requests' => count($metrics),
                'avg_response_time' => 0,
                'avg_memory_usage' => 0,
                'slow_requests' => 0,
                'operations' => []
            ];
            
            $totalTime = 0;
            $totalMemory = 0;
            
            foreach ($metrics as $metric) {
                $totalTime += $metric['duration'];
                $totalMemory += $metric['memory_usage_mb'];
                
                if ($metric['duration'] > self::$thresholds['api_response_time']) {
                    $stats['slow_requests']++;
                }
                
                $operation = $metric['operation'];
                if (!isset($stats['operations'][$operation])) {
                    $stats['operations'][$operation] = [
                        'count' => 0,
                        'avg_time' => 0,
                        'total_time' => 0
                    ];
                }
                
                $stats['operations'][$operation]['count']++;
                $stats['operations'][$operation]['total_time'] += $metric['duration'];
                $stats['operations'][$operation]['avg_time'] = 
                    $stats['operations'][$operation]['total_time'] / $stats['operations'][$operation]['count'];
            }
            
            $stats['avg_response_time'] = round($totalTime / count($metrics), 4);
            $stats['avg_memory_usage'] = round($totalMemory / count($metrics), 2);
            
            return $stats;
            
        } catch (\Exception $e) {
            Log::error('获取性能统计失败: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 重置性能统计数据
     */
    public static function resetStats(): void
    {
        try {
            // 清理所有性能数据
            Cache::tag('performance')->clear();

            // 重置内存统计
            self::$stats = [
                'total_requests' => 0,
                'total_response_time' => 0,
                'total_memory_usage' => 0,
                'slow_requests' => 0,
                'operations' => []
            ];

            Log::info('性能统计数据已重置');

        } catch (\Exception $e) {
            Log::error('性能统计数据重置失败: ' . $e->getMessage());
        }
    }

    /**
     * 清理过期的性能数据
     */
    public static function cleanup(): void
    {
        try {
            // 清理超过24小时的性能数据
            $pattern = 'performance_metrics:*';
            $keys = Cache::tag('performance')->clear();

            Log::info('性能监控数据清理完成');

        } catch (\Exception $e) {
            Log::error('性能监控数据清理失败: ' . $e->getMessage());
        }
    }
}
