<?php
declare(strict_types=1);

namespace app\service;

use think\facade\Db;
use think\facade\Log;
use app\service\DatabasePoolService;
use app\service\PerformanceMonitorService;

/**
 * IdTable服务类
 * 负责处理idtable1-idtable5表的查询优化
 * 根据账号首字母智能路由到对应表，减少查询次数
 * 
 * 表分配规则：
 * idtable1: a-d
 * idtable2: e-i  
 * idtable3: j-n
 * idtable4: o-s
 * idtable5: t-z
 * 数字和特殊字符默认分配到idtable1
 */
class IdTableService
{
    /**
     * 表分配映射（基于实际数据库查询结果）
     */
    private const TABLE_MAPPING = [
        1 => ['a', 'b', 'c', 'd'],
        2 => ['e', 'f', 'g', 'h', 'i'],
        3 => ['j', 'k', 'l', 'm', 'n'],
        4 => ['o', 'p', 'q', 'r', 's'],
        5 => ['t', 'u', 'v', 'w', 'x', 'y', 'z']  // u分配到第五个表
    ];
    
    /**
     * 缓存前缀
     */
    private const CACHE_PREFIX = 'idtable:';
    
    /**
     * 缓存时间（秒）
     */
    private const CACHE_TTL = 1800; // 30分钟
    
    /**
     * 根据用户名获取对应的表号
     *
     * @param string $username 用户名
     * @return int 表号(1-5)
     */
    public function getTableNumberByUsername(string $username): int
    {
        $firstLetter = strtolower(substr($username, 0, 1));

        foreach (self::TABLE_MAPPING as $tableNum => $letters) {
            if (in_array($firstLetter, $letters)) {
                return $tableNum;
            }
        }

        // 数字或其他特殊字符默认分配到表1
        return 1;
    }
    
    /**
     * 根据首字母范围获取表号列表
     * 
     * @param string $startLetter 起始字母
     * @param string $endLetter 结束字母
     * @return array 表号列表
     */
    public function getTableNumbersByLetterRange(string $startLetter, string $endLetter): array
    {
        $startLetter = strtolower($startLetter);
        $endLetter = strtolower($endLetter);
        $tables = [];
        
        foreach (self::TABLE_MAPPING as $tableNum => $letters) {
            foreach ($letters as $letter) {
                if ($letter >= $startLetter && $letter <= $endLetter) {
                    $tables[] = $tableNum;
                    break; // 找到一个匹配就跳出内层循环
                }
            }
        }
        
        return array_unique($tables);
    }
    
    /**
     * 获取单个用户的泡点余额（优化版本）
     * 
     * @param string $username 用户名
     * @return int 泡点余额
     */
    public function getUserPoint(string $username): int
    {
        $monitorId = PerformanceMonitorService::start("getUserPoint:{$username}");

        try {
            // 使用缓存
            $cacheKey = self::CACHE_PREFIX . 'point:' . $username;
            $cachedPoint = CacheService::get($cacheKey);
            if ($cachedPoint !== null) {
                PerformanceMonitorService::recordCacheOperation($monitorId, 'get', true);
                PerformanceMonitorService::end($monitorId, ['cache_hit' => true]);
                return (int)$cachedPoint;
            }

            PerformanceMonitorService::recordCacheOperation($monitorId, 'get', false);

            // 根据用户名直接定位到对应表
            $tableNum = $this->getTableNumberByUsername($username);

            // 使用数据库连接池
            $result = DatabasePoolService::query(
                'seal_member',
                function($db) use ($tableNum, $username) {
                    return $db->table('idtable' . $tableNum)
                        ->where('id', $username)
                        ->value('point');
                },
                $cacheKey,
                self::CACHE_TTL
            );

            $point = (int)($result ?: 0);

            PerformanceMonitorService::end($monitorId, [
                'cache_hit' => false,
                'table_number' => $tableNum,
                'point_value' => $point
            ]);

            return $point;

        } catch (\Exception $e) {
            PerformanceMonitorService::end($monitorId, ['error' => $e->getMessage()]);
            Log::error("获取用户 {$username} 泡点失败: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * 批量获取用户泡点余额（优化版本）
     * 
     * @param array $usernames 用户名列表
     * @return array 用户名 => 泡点余额的映射
     */
    public function getBatchUserPoints(array $usernames): array
    {
        if (empty($usernames)) {
            return [];
        }
        
        try {
            $results = [];
            $uncachedUsers = [];
            
            // 先从缓存获取
            foreach ($usernames as $username) {
                $cacheKey = self::CACHE_PREFIX . 'point:' . $username;
                $cachedPoint = CacheService::get($cacheKey);
                if ($cachedPoint !== null) {
                    $results[$username] = (int)$cachedPoint;
                } else {
                    $uncachedUsers[] = $username;
                }
            }
            
            // 对未缓存的用户按表分组查询
            if (!empty($uncachedUsers)) {
                $usersByTable = $this->groupUsersByTable($uncachedUsers);
                
                foreach ($usersByTable as $tableNum => $users) {
                    $points = Db::connect('seal_member')
                        ->table('idtable' . $tableNum)
                        ->whereIn('id', $users)
                        ->where('id', 'not like', 'gm%')
                        ->column('point', 'id');
                    
                    foreach ($users as $username) {
                        $point = $points[$username] ?? 0;
                        $results[$username] = $point;
                        
                        // 缓存结果
                        $cacheKey = self::CACHE_PREFIX . 'point:' . $username;
                        CacheService::set($cacheKey, $point, self::CACHE_TTL);
                    }
                }
            }
            
            return $results;
            
        } catch (\Exception $e) {
            Log::error('批量获取用户泡点失败: ' . $e->getMessage());
            return array_fill_keys($usernames, 0);
        }
    }
    
    /**
     * 获取所有表的泡点总和（优化版本）
     * 
     * @param bool $excludeGM 是否排除GM账号
     * @return int 泡点总和
     */
    public function getTotalPoints(bool $excludeGM = true): int
    {
        try {
            // 使用缓存
            $cacheKey = self::CACHE_PREFIX . 'total_points:' . ($excludeGM ? 'exclude_gm' : 'include_gm');
            $cachedTotal = CacheService::get($cacheKey);
            if ($cachedTotal !== null) {
                return (int)$cachedTotal;
            }
            
            $totalPoints = 0;
            $sealMemberConfig = $this->getSealMemberConfig();
            
            // 并行查询所有表
            $queries = [];
            for ($i = 1; $i <= 5; $i++) {
                $query = Db::connect($sealMemberConfig)
                    ->table('idtable' . $i);
                
                if ($excludeGM) {
                    $query->where('id', 'not like', 'gm%');
                }
                
                $points = (int)($query->sum('point') ?: 0);
                $totalPoints += $points;
                
                Log::info("idtable{$i} 泡点统计", [
                    'table' => "idtable{$i}",
                    'points' => $points
                ]);
            }
            
            // 缓存结果
            CacheService::set($cacheKey, $totalPoints, self::CACHE_TTL);
            
            Log::info('泡点总计', [
                'total_points' => $totalPoints,
                'exclude_gm' => $excludeGM
            ]);
            
            return (int)$totalPoints;
            
        } catch (\Exception $e) {
            Log::error('获取泡点总和失败: ' . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * 获取用户总数（优化版本）
     * 
     * @param bool $excludeGM 是否排除GM账号
     * @return int 用户总数
     */
    public function getTotalUserCount(bool $excludeGM = true): int
    {
        try {
            // 使用缓存
            $cacheKey = self::CACHE_PREFIX . 'total_users:' . ($excludeGM ? 'exclude_gm' : 'include_gm');
            $cachedCount = CacheService::get($cacheKey);
            if ($cachedCount !== null) {
                return (int)$cachedCount;
            }
            
            $totalUsers = 0;
            $sealMemberConfig = $this->getSealMemberConfig();
            
            for ($i = 1; $i <= 5; $i++) {
                try {
                    $query = Db::connect($sealMemberConfig)
                        ->table('idtable' . $i);
                    
                    if ($excludeGM) {
                        $query->where('id', 'not like', 'gm%');
                    }
                    
                    $count = $query->count();
                    $totalUsers += $count;
                    
                    Log::info("idtable{$i} 用户统计", [
                        'table' => "idtable{$i}",
                        'count' => $count
                    ]);
                } catch (\Exception $e) {
                    Log::warning("获取idtable{$i}用户数失败: " . $e->getMessage());
                }
            }
            
            // 缓存结果
            CacheService::set($cacheKey, $totalUsers, self::CACHE_TTL);
            
            return $totalUsers;
            
        } catch (\Exception $e) {
            Log::error('获取用户总数失败: ' . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * 将用户名列表按表分组
     * 
     * @param array $usernames 用户名列表
     * @return array 表号 => 用户名列表的映射
     */
    private function groupUsersByTable(array $usernames): array
    {
        $usersByTable = [];
        
        foreach ($usernames as $username) {
            $tableNum = $this->getTableNumberByUsername($username);
            if (!isset($usersByTable[$tableNum])) {
                $usersByTable[$tableNum] = [];
            }
            $usersByTable[$tableNum][] = $username;
        }
        
        return $usersByTable;
    }
    
    /**
     * 获取seal_member数据库配置
     * 
     * @return array
     */
    private function getSealMemberConfig(): array
    {
        return [
            'type' => env('DB_TYPE', 'mysql'),
            'hostname' => env('REMOTE_DB_HOST', ''),
            'database' => env('SEAL_MEMBER_DB_NAME', ''),
            'username' => env('REMOTE_DB_USER', ''),
            'password' => env('REMOTE_DB_PASS', ''),
            'hostport' => env('REMOTE_DB_PORT', ''),
            'charset' => env('REMOTE_DB_CHARSET', 'utf8'),
        ];
    }
    
    /**
     * 清除用户相关缓存
     * 
     * @param string|null $username 用户名，为null时清除所有缓存
     * @return bool
     */
    public function clearCache(?string $username = null): bool
    {
        try {
            if ($username) {
                // 清除指定用户的缓存
                $cacheKey = self::CACHE_PREFIX . 'point:' . $username;
                return CacheService::delete($cacheKey);
            } else {
                // 清除所有相关缓存
                return CacheService::clearByPrefix(self::CACHE_PREFIX);
            }
        } catch (\Exception $e) {
            Log::error('清除IdTable缓存失败: ' . $e->getMessage());
            return false;
        }
    }
}
