<?php
declare(strict_types=1);

namespace app\service;

use think\facade\Db;
use think\facade\Log;

/**
 * 用户服务类
 * 负责用户相关的业务逻辑处理
 * 继承BaseService以复用通用功能
 */
class UserService extends BaseService
{
    /**
     * 缓存前缀
     */
    protected string $cachePrefix = 'user:';

    /**
     * 主表名（用户数据分布在多个表中）
     */
    protected string $tableName = 'idtable1'; // 默认表，实际使用时会动态切换

    /**
     * 主键字段名
     */
    protected string $primaryKey = 'username';

    /**
     * 初始化服务
     */
    protected function initializeService(): void
    {
        // 用户服务特定的初始化逻辑
    }

    /**
     * 获取服务名称
     */
    protected function getServiceName(): string
    {
        return '用户服务';
    }
    /**
     * 获取用户列表（分页）
     * 
     * @param int $page 页码
     * @param int $limit 每页数量
     * @param string $search 搜索关键词
     * @param string $status 状态筛选
     * @return array
     */
    public function getUserList(int $page = 1, int $limit = 20, string $search = '', string $status = ''): array
    {
        try {
            $offset = ($page - 1) * $limit;
            $users = [];
            $totalCount = 0;
            
            // 从idtable1-idtable5表中获取用户数据
            for ($i = 1; $i <= 5; $i++) {
                $query = Db::connect('seal_member')
                    ->table('idtable' . $i)
                    ->field('id, passwd, point, reg_date, delete_flag');
                
                // 添加搜索条件
                if (!empty($search)) {
                    $query->where('id', 'like', '%' . $search . '%');
                }
                
                // 添加状态筛选条件
                if ($status !== '') {
                    if ($status == '1') {
                        // 正常用户：delete_flag = 0
                        $query->where('delete_flag', 0);
                    } elseif ($status == '0') {
                        // 禁用用户：delete_flag = 1
                        $query->where('delete_flag', 1);
                    }
                }
                
                $tableUsers = $query->select();
                
                foreach ($tableUsers as $user) {
                    // 获取用户昵称（如果表不存在则跳过）
                    $nickname = null;
                    try {
                        $nickname = $this->getUserNickname($user['id']);
                    } catch (\Exception $e) {
                        // 忽略nickname表不存在的错误
                    }
                    
                    // 获取最后登录时间（如果表不存在则跳过）
                    $lastLogin = null;
                    try {
                        $lastLogin = $this->getLastLoginTime($user['id']);
                    } catch (\Exception $e) {
                        // 忽略admin表不存在的错误
                    }
                    
                    // 根据delete_flag字段确定用户状态：0=激活，1=未激活（封禁）
                    $userStatus = ($user['delete_flag'] ?? 0) == 0 ? 1 : 0;
                    
                    $users[] = [
                        'id' => $user['id'],
                        'username' => $user['id'],
                        'nickname' => $nickname,
                        'register_time' => $user['reg_date'] ?? null,
                        'last_login' => $lastLogin,
                        'status' => $userStatus, // 根据delete_flag字段确定状态
                        'point' => $user['point'] ?? 0
                    ];
                }
            }
            
            // 状态筛选已在数据库查询阶段处理
            
            $totalCount = count($users);
            
            // 应用分页
            $users = array_slice($users, $offset, $limit);
            
            return [
                'list' => array_values($users),
                'total' => $totalCount,
                'page' => $page,
                'limit' => $limit,
                'total_pages' => ceil($totalCount / $limit)
            ];
            
        } catch (\Exception $e) {
            // 如果是表不存在的错误，返回空结果而不是抛出异常
            if (strpos($e->getMessage(), "doesn't exist") !== false || strpos($e->getMessage(), 'Table') !== false) {
                Log::warning('数据库表不存在，返回空结果: ' . $e->getMessage());
                return [
                    'list' => [],
                    'total' => 0,
                    'page' => $page,
                    'limit' => $limit
                ];
            }
            Log::error('获取用户列表失败: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 获取用户昵称
     * 
     * @param string $userId 用户ID
     * @return string|null
     */
    private function getUserNickname(string $userId): ?string
    {
        try {
            $nickname = Db::table('user_nickname')
                ->where('user_id', $userId)
                ->value('nickname');
                
            return $nickname;
        } catch (\Exception $e) {
            // 如果user_nickname表不存在，返回null而不是抛出异常
            if (strpos($e->getMessage(), "doesn't exist") !== false) {
                return null;
            }
            Log::error('获取用户昵称失败: ' . $e->getMessage());
            return null;
        }
    }
    
    /**
     * 获取用户最后登录时间
     * 
     * @param string $userId 用户ID
     * @return string|null
     */
    private function getLastLoginTime(string $userId): ?string
    {
        try {
            // 从admin表获取最后登录时间（如果是管理员）
            $adminLogin = Db::connect('seal_web')
                ->table('admin')
                ->where('UserName', $userId)
                ->value('Date');
                
            if ($adminLogin) {
                return $adminLogin;
            }
            
            // 可以添加其他登录记录表的查询
            return null;
            
        } catch (\Exception $e) {
            // 如果admin表不存在，返回null而不是抛出异常
            if (strpos($e->getMessage(), "doesn't exist") !== false) {
                return null;
            }
            Log::error('获取用户最后登录时间失败: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 构建WHERE条件（避免重复代码）
     *
     * @param string $search 搜索关键词
     * @param string $status 状态筛选
     * @param string $role 角色筛选
     * @return array
     */
    private function buildWhereConditions(string $search = '', string $status = '', string $role = ''): array
    {
        $whereConditions = [];

        if (!empty($search)) {
            // 使用参数化查询防止SQL注入
            $search = addslashes($search);
            $whereConditions[] = "id LIKE '%{$search}%'";
        }

        // 添加状态筛选条件
        if ($status !== '') {
            if ($status == '1') {
                // 正常用户：delete_flag = 0
                $whereConditions[] = "delete_flag = 0";
            } elseif ($status == '0') {
                // 禁用用户：delete_flag = 1
                $whereConditions[] = "delete_flag = 1";
            }
        }

        // 添加角色筛选条件
        if (!empty($role)) {
            if ($role === 'admin') {
                // 管理员：从配置文件获取管理员列表
                $adminUsers = $this->getAdminUsernames();
                if (!empty($adminUsers)) {
                    $adminList = "'" . implode("','", array_map('addslashes', $adminUsers)) . "'";
                    $whereConditions[] = "id IN ({$adminList})";
                } else {
                    // 如果没有管理员，返回空结果
                    $whereConditions[] = "1 = 0";
                }
            } elseif ($role === 'user') {
                // 普通用户：排除管理员
                $adminUsers = $this->getAdminUsernames();
                if (!empty($adminUsers)) {
                    $adminList = "'" . implode("','", array_map('addslashes', $adminUsers)) . "'";
                    $whereConditions[] = "id NOT IN ({$adminList})";
                }
            }
        }

        return $whereConditions;
    }

    /**
     * 获取管理员用户名列表
     *
     * @return array
     */
    private function getAdminUsernames(): array
    {
        try {
            $configPath = app()->getConfigPath() . 'admin_users.php';
            if (file_exists($configPath)) {
                $admins = include $configPath;
                return array_keys($admins);
            }
            return [];
        } catch (\Exception $e) {
            Log::error('获取管理员列表失败: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 优化版本：使用批量查询减少数据库连接（支持缓存）
     *
     * @param int $page 页码
     * @param int $limit 每页数量
     * @param string $search 搜索关键词
     * @param string $status 状态筛选
     * @param string $role 角色筛选
     * @return array
     */
    public function getUserListOptimized(int $page = 1, int $limit = 20, string $search = '', string $status = '', string $role = ''): array
    {
        // 生成缓存键
        $cacheKey = 'user_list_' . md5($page . '_' . $limit . '_' . $search . '_' . $status . '_' . $role);

        // 尝试从缓存获取
        $cached = \app\service\CacheService::get($cacheKey, false);
        if ($cached !== false) {
            return $cached;
        }

        try {
            $offset = ($page - 1) * $limit;
            $users = [];

            // 构建WHERE条件（避免重复代码）
            $whereConditions = $this->buildWhereConditions($search, $status, $role);
            $whereClause = !empty($whereConditions) ? " WHERE " . implode(' AND ', $whereConditions) : "";

            // 使用UNION查询一次性获取所有表的数据
            $sql = "";
            for ($i = 1; $i <= 5; $i++) {
                if ($i > 1) $sql .= " UNION ALL ";
                $sql .= "SELECT id, passwd, point, reg_date, delete_flag, {$i} as table_num FROM idtable{$i}" . $whereClause;
            }

            // 添加排序和分页
            $sql .= " ORDER BY id LIMIT {$limit} OFFSET {$offset}";

            $userResults = Db::connect('seal_member')->query($sql);
            
            // 批量获取昵称
            $userIds = array_column($userResults, 'id');
            $nicknames = [];
            if (!empty($userIds)) {
                try {
                    $nicknameResults = Db::table('user_nickname')
                        ->whereIn('user_id', $userIds)
                        ->column('nickname', 'user_id');
                    $nicknames = $nicknameResults;
                } catch (\Exception $e) {
                    Log::warning('获取昵称失败，表可能不存在: ' . $e->getMessage());
                    $nicknames = [];
                }
            }

            // 批量获取登录时间
            $loginTimes = [];
            if (!empty($userIds)) {
                try {
                    $loginResults = Db::connect('seal_web')
                        ->table('admin')
                        ->whereIn('UserName', $userIds)
                        ->column('Date', 'UserName');
                    $loginTimes = $loginResults;
                } catch (\Exception $e) {
                    Log::warning('获取登录时间失败，表可能不存在: ' . $e->getMessage());
                    $loginTimes = [];
                }
            }
            
            // 获取管理员列表用于角色判断
            $adminUsers = $this->getAdminUsernames();

            // 组装结果
            foreach ($userResults as $user) {
                $users[] = [
                    'id' => $user['id'],
                    'username' => $user['id'],
                    'nickname' => $nicknames[$user['id']] ?? null,
                    'register_time' => $user['reg_date'] ?? null,
                    'last_login' => $loginTimes[$user['id']] ?? null,
                    'status' => $user['delete_flag'] == 0 ? 1 : 0, // delete_flag=0为正常，delete_flag=1为禁用
                    'point' => $user['point'] ?? 0,
                    'role' => in_array($user['id'], $adminUsers) ? 'admin' : 'user', // 判断是否为管理员
                    'reg_date' => $user['reg_date'] ?? null // 添加reg_date字段供前端使用
                ];
            }
            
            // 获取总数（使用相同的WHERE条件）
            $countSql = "";
            for ($i = 1; $i <= 5; $i++) {
                if ($i > 1) $countSql .= " UNION ALL ";
                $countSql .= "SELECT COUNT(*) as count FROM idtable{$i}" . $whereClause;
            }
            $countSql = "SELECT SUM(count) as total FROM ({$countSql}) as counts";
            $totalResult = Db::connect('seal_member')->query($countSql);
            $totalCount = $totalResult[0]['total'] ?? 0;
            
            $result = [
                'list' => $users,
                'total' => (int)$totalCount,
                'page' => $page,
                'limit' => $limit,
                'total_pages' => ceil($totalCount / $limit)
            ];

            // 缓存结果（5分钟）
            \app\service\CacheService::set($cacheKey, $result, 300);

            return $result;
            
        } catch (\Exception $e) {
            Log::error('获取用户列表失败: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 通过用户名获取用户详情（别名方法，保持兼容性）
     *
     * @param string $username 用户名
     * @return array|null
     */
    public function getUserDetailByUsername(string $username): ?array
    {
        return $this->getUserDetail($username);
    }

    /**
     * 通过用户名获取基本用户信息
     *
     * @param string $username 用户名
     * @return array|null
     */
    public function getUserByUsername(string $username): ?array
    {
        try {
            // 从idtable1-idtable5表中查找用户
            for ($i = 1; $i <= 5; $i++) {
                $user = Db::connect('seal_member')
                    ->table('idtable' . $i)
                    ->field('id, passwd, point, reg_date, delete_flag')
                    ->where('id', $username)
                    ->find();

                if ($user) {
                    return [
                        'id' => $user['id'],
                        'username' => $user['id'],
                        'point' => $user['point'] ?? 0,
                        'register_time' => $user['reg_date'],
                        'status' => $user['delete_flag'] == 0 ? 1 : 0,
                        'table_index' => $i
                    ];
                }
            }

            return null;
        } catch (\Exception $e) {
            Log::error('通过用户名获取用户信息失败: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 获取用户详情
     *
     * @param string $username 用户名
     * @return array|null
     */
    public function getUserDetail(string $username): ?array
    {
        try {
            $userInfo = null;
            $tableIndex = null;

            // 从idtable1-idtable5表中查找用户（获取更多字段）
            for ($i = 1; $i <= 5; $i++) {
                $user = Db::connect('seal_member')
                    ->table('idtable' . $i)
                    ->field('id, passwd, email, turid, point, reg_date, GroupId, Mac, enter_ip')
                    ->where('id', $username)
                    ->find();

                if ($user) {
                    $userInfo = $user;
                    $tableIndex = $i;
                    break;
                }
            }

            if (!$userInfo) {
                return null;
            }

            // 获取用户货币余额
            $currencyBalances = $this->getUserCurrencyBalances($username);
            
            // 获取sealmember表中的详细信息
            $sealMemberInfo = null;
            try {
                $sealMemberInfo = Db::connect('seal_web')
                    ->table('sealmember')
                    ->field('Pass, email, sex, birday, turid, HorCah, Z_Cash, Z_Cash_T, hahapoint, viptime')
                    ->where('id', $username)
                    ->find();
            } catch (\Exception $e) {
                Log::warning('获取sealmember信息失败: ' . $e->getMessage());
            }
            
            // 获取用户昵称
            $nickname = null;
            try {
                $nickname = $this->getUserNickname($username);
            } catch (\Exception $e) {
                // 忽略nickname表不存在的错误
            }
            
            // 获取最后登录时间和登录次数（从admin表）
            $lastLogin = null;
            $loginCount = 0;
            try {
                $adminInfo = Db::connect('seal_web')
                    ->table('admin')
                    ->field('last_login_time, login_count')
                    ->where('username', $username)
                    ->find();
                if ($adminInfo) {
                    $lastLogin = $adminInfo['last_login_time'];
                    $loginCount = (int)($adminInfo['login_count'] ?? 0);
                }
            } catch (\Exception $e) {
                $loginCount = 0;
            }
            
            // 从idtable中获取MAC地址和最后登录IP
            $lastLoginIp = $userInfo['enter_ip'] ?? null;
            $macAddress = $userInfo['Mac'] ?? null;
            
            // 获取角色名列表
            $characterNames = [];
            try {
                // 获取数据库连接并强制设置字符集
                $connection = Db::connect('gdb0101');

                // 强制设置连接字符集为GBK
                $connection->execute("SET NAMES gbk");
                $connection->execute("SET CHARACTER_SET_CLIENT = gbk");
                $connection->execute("SET CHARACTER_SET_RESULTS = gbk");
                $connection->execute("SET CHARACTER_SET_CONNECTION = gbk");

                // 使用原始SQL查询以更好地控制编码
                $sql = "SELECT char_name, HEX(char_name) as hex_data FROM pc WHERE user_id = ? ORDER BY char_name";
                $characters = $connection->query($sql, [$username]);

                Log::info('角色查询结果', [
                    'username' => $username,
                    'count' => count($characters)
                ]);



                foreach ($characters as $character) {
                    $originalCharName = $character['char_name'];
                    $hexData = $character['hex_data'] ?? '';

                    Log::debug('处理角色名', [
                        'original' => $originalCharName,
                        'hex' => bin2hex($originalCharName),
                        'db_hex' => $hexData,
                        'length' => strlen($originalCharName),
                        'username' => $username,
                        'raw_bytes' => array_map('ord', str_split($originalCharName))
                    ]);

                    $charName = $originalCharName;

                    // 如果PHP接收到的包含问号，但数据库十六进制数据是正确的，从十六进制重建
                    if (strpos($originalCharName, '?') !== false && !empty($hexData) && strlen($hexData) >= 4) {
                        try {
                            // 从十六进制重建原始字节
                            $rawBytes = hex2bin($hexData);
                            if ($rawBytes !== false) {
                                // 尝试从GBK转换到UTF-8
                                $converted = iconv('GBK', 'UTF-8//IGNORE', $rawBytes);
                                if (!empty($converted) && mb_check_encoding($converted, 'UTF-8')) {
                                    $charName = $converted;
                                    Log::debug('从十六进制重建角色名成功', [
                                        'hex_data' => $hexData,
                                        'raw_bytes' => bin2hex($rawBytes),
                                        'converted' => $converted
                                    ]);
                                }
                            }
                        } catch (\Exception $e) {
                            Log::debug('从十六进制重建角色名失败: ' . $e->getMessage());
                        }
                    }
                    // 如果包含特殊字符，尝试转换
                    else if (preg_match('/[ÀÁÂÃÄÅÆÇÈÉÊËÌÍÎÏÐÑÒÓÔÕÖØÙÚÛÜÝÞßàáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ]/', $originalCharName)) {
                        try {
                            $converted = iconv('GBK', 'UTF-8//IGNORE', $originalCharName);
                            if (!empty($converted) && mb_check_encoding($converted, 'UTF-8') && preg_match('/[\x{4e00}-\x{9fff}]/u', $converted)) {
                                $charName = $converted;
                                Log::debug('成功转换角色名', [
                                    'original' => $originalCharName,
                                    'converted' => $converted
                                ]);
                            }
                        } catch (\Exception $e) {
                            Log::debug('角色名转换失败: ' . $e->getMessage());
                        }
                    }

                    $characterNames[] = $charName;
                }
            } catch (\Exception $e) {
                Log::warning('获取角色名失败: ' . $e->getMessage());
            }
            
            // 获取用户状态（从本地数据库）
            $status = 1; // 默认正常状态
            $banReason = null;
            try {
                $userStatus = Db::table('user_status')
                    ->where('username', $username)
                    ->find();
                if ($userStatus) {
                    $status = (int)$userStatus['status'];
                    $banReason = $userStatus['reason'] ?? null;
                }
            } catch (\Exception $e) {
                // 如果表不存在，使用默认状态
            }
            
            // 获取购买统计
            $purchaseCount = 0;
            $totalSpent = 0;
            try {
                $orderStats = Db::table('purchase_orders')
                    ->where('username', $username)
                    ->where('status', 'completed')
                    ->field('COUNT(*) as count, SUM(total_price) as total')
                    ->find();
                if ($orderStats) {
                    $purchaseCount = (int)$orderStats['count'];
                    $totalSpent = (float)$orderStats['total'];
                }
            } catch (\Exception $e) {
                // 忽略错误
            }
            
            // 获取C币余额（从c_coin表）
            $cCoinBalance = 0;
            try {
                $cCoinData = Db::table('c_coin')
                    ->where('game_account', $username)
                    ->value('balance');
                $cCoinBalance = $cCoinData ?: 0;
            } catch (\Exception $e) {
                // 如果c_coin表不存在或查询失败，使用默认值0
                Log::warning('获取C币余额失败: ' . $e->getMessage());
            }
            
            return [
                // 基本信息
                'id' => $userInfo['id'],
                'username' => $this->fixCharacterNameEncoding($userInfo['id']), // 修复用户名编码
                'password' => $userInfo['passwd'], // 加密后的密码
                'email' => $userInfo['email'] ?: ($sealMemberInfo['email'] ?? '未设置'),
                'nickname' => $nickname ?: '未设置',

                // 身份信息
                'turid' => $userInfo['turid'] ?: ($sealMemberInfo['turid'] ?? '未设置'), // 身份证号
                'sex' => $sealMemberInfo['sex'] ?? 0, // 性别
                'birthday' => $sealMemberInfo['birday'] ?? '0000-00-00', // 生日

                // 网络信息
                'last_login_ip' => $lastLoginIp ?: '未知',
                'mac_address' => $macAddress ?: '未知',

                // 游戏货币（只保留三种：泡点、积分、C币）
                'point' => (int)$userInfo['point'], // 泡点
                'haha_point' => (int)($sealMemberInfo['hahapoint'] ?? 0), // 积分
                'c_cash' => (int)$cCoinBalance, // C币（从c_coin表获取）

                // 货币余额（用于前端显示）
                'currency_balances' => $currencyBalances,

                // 时间信息
                'register_time' => $userInfo['reg_date'],
                'last_login' => $lastLogin,
                'vip_time' => (int)($sealMemberInfo['viptime'] ?? 0), // VIP时间

                // 状态信息
                'status' => $status,
                'ban_reason' => $banReason,
                'group_id' => (int)$userInfo['GroupId'], // 用户组ID
                'table_index' => $tableIndex,

                // 角色信息
                'character_names' => $characterNames,
                'character_count' => count($characterNames),

                // 统计信息
                'login_count' => $loginCount ?? 0,
                'purchase_count' => $purchaseCount,
                'total_spent' => $totalSpent
            ];
            
        } catch (\Exception $e) {
            Log::error('获取用户详情失败: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 获取用户货币余额
     *
     * @param string $username 用户名
     * @return array
     */
    private function getUserCurrencyBalances(string $username): array
    {
        try {
            // 获取泡点余额（从idtable1-5表）
            $coinBalance = 0;
            for ($i = 1; $i <= 5; $i++) {
                $point = Db::connect('seal_member')
                    ->table('idtable' . $i)
                    ->where('id', $username)
                    ->value('point');
                if ($point !== null) {
                    $coinBalance = (int)$point;
                    break;
                }
            }

            // 获取积分余额（从seal_web数据库的sealmember表）
            $silverBalance = 0;
            try {
                $silverBalance = Db::connect('seal_web')
                    ->table('sealmember')
                    ->where('id', $username)
                    ->value('hahapoint');
                $silverBalance = (int)($silverBalance ?: 0);
            } catch (\Exception $e) {
                Log::warning('获取积分余额失败: ' . $e->getMessage());
            }

            // 获取C币余额（从c_coin表）
            $cCoinBalance = 0;
            try {
                $numericUserId = is_numeric($username) ? (int)$username : (int)preg_replace('/[^0-9]/', '', $username);
                $cCoinData = Db::table('c_coin')
                    ->where('user_id', $numericUserId)
                    ->value('balance');
                $cCoinBalance = (int)($cCoinData ?: 0);
            } catch (\Exception $e) {
                Log::warning('获取C币余额失败: ' . $e->getMessage());
            }

            return [
                'coin' => $coinBalance,      // 泡点
                'silver' => $silverBalance,  // 积分
                'c_coin' => $cCoinBalance    // C币
            ];

        } catch (\Exception $e) {
            Log::error('获取用户货币余额失败: ' . $e->getMessage());
            return [
                'coin' => 0,
                'silver' => 0,
                'c_coin' => 0
            ];
        }
    }

    /**
     * 更新用户昵称
     *
     * @param string $username 用户名
     * @param string $nickname 新昵称
     * @return bool
     */
    public function updateUserNickname(string $username, string $nickname): bool
    {
        try {
            // 检查用户是否存在
            $userExists = false;
            for ($i = 1; $i <= 5; $i++) {
                $user = Db::connect('seal_member')
                    ->table('idtable' . $i)
                    ->where('id', $username)
                    ->find();
                if ($user) {
                    $userExists = true;
                    break;
                }
            }
            
            if (!$userExists) {
                return false;
            }
            
            // 更新或插入昵称
            $existingNickname = Db::table('user_nickname')
                ->where('user_id', $username)
                ->find();
            
            if ($existingNickname) {
                // 更新现有昵称
                $result = Db::table('user_nickname')
                    ->where('user_id', $username)
                    ->update([
                        'nickname' => $nickname,
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
            } else {
                // 插入新昵称
                $result = Db::table('user_nickname')
                    ->insert([
                        'user_id' => $username,
                        'nickname' => $nickname,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
            }
            
            return $result !== false;
            
        } catch (\Exception $e) {
            Log::error('更新用户昵称失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 更新用户状态
     *
     * @param string $userId 用户ID（可以是用户名）
     * @param int $status 状态（0=禁用，1=启用）
     * @param string $reason 操作原因
     * @return bool
     */
    public function updateUserStatus(string $userId, int $status, string $reason = ''): bool
    {
        try {
            // 检查用户是否存在并更新delete_flag字段
            $userFound = false;
            $result = false;

            for ($i = 1; $i <= 5; $i++) {
                $user = Db::connect('seal_member')
                    ->table('idtable' . $i)
                    ->where('id', $userId)
                    ->find();

                if ($user) {
                    $userFound = true;
                    // delete_flag: 0=正常，1=禁用
                    $deleteFlag = $status == 1 ? 0 : 1;

                    $result = Db::connect('seal_member')
                        ->table('idtable' . $i)
                        ->where('id', $userId)
                        ->update(['delete_flag' => $deleteFlag]);

                    // 即使update返回0（没有变化），也认为是成功的
                    $result = true;
                    break;
                }
            }

            if (!$userFound) {
                return false;
            }

            // 记录状态变更到user_status表
            try {
                $existingStatus = Db::table('user_status')
                    ->where('username', $userId)
                    ->find();

                if ($existingStatus) {
                    // 更新现有记录
                    Db::table('user_status')
                        ->where('username', $userId)
                        ->update([
                            'status' => $status,
                            'reason' => $reason,
                            'update_time' => date('Y-m-d H:i:s'),
                            'ban_time' => $status == 0 ? date('Y-m-d H:i:s') : null,
                            'unban_time' => $status == 1 ? date('Y-m-d H:i:s') : null
                        ]);
                } else {
                    // 插入新记录
                    Db::table('user_status')
                        ->insert([
                            'username' => $userId,
                            'status' => $status,
                            'reason' => $reason,
                            'create_time' => date('Y-m-d H:i:s'),
                            'update_time' => date('Y-m-d H:i:s'),
                            'ban_time' => $status == 0 ? date('Y-m-d H:i:s') : null,
                            'unban_time' => $status == 1 ? date('Y-m-d H:i:s') : null
                        ]);
                }
            } catch (\Exception $e) {
                // 如果user_status表不存在，忽略错误
                Log::warning('更新user_status表失败，表可能不存在: ' . $e->getMessage());
            }

            return $result;

        } catch (\Exception $e) {
            Log::error('更新用户状态失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 封禁用户
     * 
     * @param string $username 用户名
     * @return bool
     */
    public function banUser(string $username): bool
    {
        try {
            // 检查用户是否存在并更新delete_flag字段
            $userFound = false;
            $result = false;
            
            for ($i = 1; $i <= 5; $i++) {
                $user = Db::connect('seal_member')
                    ->table('idtable' . $i)
                    ->where('id', $username)
                    ->find();
                    
                if ($user) {
                    $userFound = true;
                    // 设置delete_flag为1表示未激活（封禁）
                    $result = Db::connect('seal_member')
                        ->table('idtable' . $i)
                        ->where('id', $username)
                        ->update(['delete_flag' => 1]);
                    // 如果用户已经是封禁状态，update返回0，但这不是错误
                    $result = true;
                    break;
                }
            }
            
            if (!$userFound) {
                return false;
            }
            
            // 同时记录封禁信息到user_status表（用于记录时间）
            $existingStatus = Db::table('user_status')
                ->where('username', $username)
                ->find();
            
            if ($existingStatus) {
                // 更新现有状态
                Db::table('user_status')
                    ->where('username', $username)
                    ->update([
                        'status' => 0,
                        'ban_time' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
            } else {
                // 插入新状态记录
                Db::table('user_status')
                    ->insert([
                        'username' => $username,
                        'status' => 0,
                        'ban_time' => date('Y-m-d H:i:s'),
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
            }
            
            return $result !== false;
            
        } catch (\Exception $e) {
            Log::error('封禁用户失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 解封用户
     * 
     * @param string $username 用户名
     * @return bool
     */
    public function unbanUser(string $username): bool
    {
        try {
            // 检查用户是否存在并更新delete_flag字段
            $userFound = false;
            $result = false;
            
            for ($i = 1; $i <= 5; $i++) {
                $user = Db::connect('seal_member')
                    ->table('idtable' . $i)
                    ->where('id', $username)
                    ->find();
                    
                if ($user) {
                    $userFound = true;
                    // 设置delete_flag为0表示激活（解封）
                    $result = Db::connect('seal_member')
                        ->table('idtable' . $i)
                        ->where('id', $username)
                        ->update(['delete_flag' => 0]);
                    // 如果用户已经是激活状态，update返回0，但这不是错误
                    $result = true;
                    break;
                }
            }
            
            if (!$userFound) {
                return false;
            }
            
            // 同时更新user_status表记录解封信息
            $existingStatus = Db::table('user_status')
                ->where('username', $username)
                ->find();
            
            if ($existingStatus) {
                // 更新现有状态
                Db::table('user_status')
                    ->where('username', $username)
                    ->update([
                        'status' => 1,
                        'ban_time' => null,
                        'unban_time' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
            } else {
                // 插入新状态记录（正常状态）
                Db::table('user_status')
                    ->insert([
                        'username' => $username,
                        'status' => 1,
                        'unban_time' => date('Y-m-d H:i:s'),
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
            }
            
            return $result !== false;
            
        } catch (\Exception $e) {
            Log::error('解封用户失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 检查用户状态表是否存在，不存在则创建
     */
    private function ensureUserStatusTable(): void
    {
        try {
            // 检查表是否存在
            $tableExists = Db::query("SHOW TABLES LIKE 'user_status'");
            
            if (empty($tableExists)) {
                // 创建用户状态表
                $sql = "
                    CREATE TABLE `user_status` (
                        `id` int(11) NOT NULL AUTO_INCREMENT,
                        `username` varchar(50) NOT NULL,
                        `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '用户状态：1正常，0禁用',
                        `ban_reason` varchar(255) DEFAULT NULL COMMENT '封禁原因',
                        `ban_time` datetime DEFAULT NULL COMMENT '封禁时间',
                        `unban_time` datetime DEFAULT NULL COMMENT '解封时间',
                        `created_at` datetime NOT NULL,
                        `updated_at` datetime NOT NULL,
                        PRIMARY KEY (`id`),
                        UNIQUE KEY `username` (`username`)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户状态表';
                ";
                
                Db::execute($sql);
                Log::info('用户状态表创建成功');
            }
        } catch (\Exception $e) {
            Log::error('检查/创建用户状态表失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取总用户数
     *
     * @param bool $excludeGM 是否排除GM账号
     * @return int
     */
    public function getTotalUserCount(bool $excludeGM = true): int
    {
        try {
            $totalCount = 0;

            // 从idtable1-idtable5表中统计用户数
            for ($i = 1; $i <= 5; $i++) {
                $query = Db::connect('seal_member')
                    ->table('idtable' . $i);

                if ($excludeGM) {
                    $query->where('id', 'not like', 'gm%');
                }

                $count = $query->count();
                $totalCount += $count;
            }

            return $totalCount;
        } catch (\Exception $e) {
            Log::error('获取总用户数失败: ' . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * 获取激活用户数
     *
     * @param bool $excludeGM 是否排除GM账号
     * @return int
     */
    public function getActiveUserCount(bool $excludeGM = true): int
    {
        try {
            $activeCount = 0;

            // 从idtable1-idtable5表中统计激活用户数（delete_flag = 0）
            for ($i = 1; $i <= 5; $i++) {
                $query = Db::connect('seal_member')
                    ->table('idtable' . $i)
                    ->where('delete_flag', 0);

                if ($excludeGM) {
                    $query->where('id', 'not like', 'gm%');
                }

                $count = $query->count();
                $activeCount += $count;
            }

            return $activeCount;
        } catch (\Exception $e) {
            Log::error('获取激活用户数失败: ' . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * 获取封禁用户数
     *
     * @param bool $excludeGM 是否排除GM账号
     * @return int
     */
    public function getBannedUserCount(bool $excludeGM = true): int
    {
        try {
            $bannedCount = 0;

            // 从idtable1-idtable5表中统计封禁用户数（delete_flag = 1）
            for ($i = 1; $i <= 5; $i++) {
                $query = Db::connect('seal_member')
                    ->table('idtable' . $i)
                    ->where('delete_flag', 1);

                if ($excludeGM) {
                    $query->where('id', 'not like', 'gm%');
                }

                $count = $query->count();
                $bannedCount += $count;
            }

            return $bannedCount;
        } catch (\Exception $e) {
            Log::error('获取封禁用户数失败: ' . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * 获取用户状态信息
     * 
     * @param string $username 用户名
     * @return array|null
     */
    public function getUserStatus(string $username): ?array
    {
        try {
            // 检查用户是否存在并获取状态
            for ($i = 1; $i <= 5; $i++) {
                $user = Db::connect('seal_member')
                    ->table('idtable' . $i)
                    ->field('id, delete_flag')
                    ->where('id', $username)
                    ->find();
                    
                if ($user) {
                    // 根据delete_flag字段确定用户状态：0=激活，1=封禁
                    $status = ($user['delete_flag'] ?? 0) == 0 ? 1 : 0;
                    
                    // 获取封禁详情（如果有）
                    $banInfo = null;
                    if ($status == 0) {
                        $banInfo = Db::table('user_status')
                            ->where('username', $username)
                            ->find();
                    }
                    
                    return [
                        'username' => $username,
                        'status' => $status,
                        'ban_time' => $banInfo['ban_time'] ?? null,
                        'unban_time' => $banInfo['unban_time'] ?? null
                    ];
                }
            }
            
            return null; // 用户不存在
        } catch (\Exception $e) {
            Log::error('获取用户状态失败: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 获取用户状态统计
     *
     * @return array
     */
    public function getUserStats(): array
    {
        try {
            $stats = [
                'total_users' => 0,
                'active_users' => 0,
                'banned_users' => 0,
                'online_users' => 0
            ];

            // 统计总用户数和封禁用户数（排除GM账号，与仪表盘保持一致）
            for ($i = 1; $i <= 5; $i++) {
                try {
                    // 总用户数（排除GM账号）
                    $totalCount = Db::connect('seal_member')
                        ->table('idtable' . $i)
                        ->where('id', 'not like', 'gm%')
                        ->count();
                    $stats['total_users'] += $totalCount;

                    // 正常用户数（delete_flag = 0，排除GM账号）
                    $activeCount = Db::connect('seal_member')
                        ->table('idtable' . $i)
                        ->where('delete_flag', 0)
                        ->where('id', 'not like', 'gm%')
                        ->count();
                    $stats['active_users'] += $activeCount;

                    // 封禁用户数（delete_flag = 1，排除GM账号）
                    $bannedCount = Db::connect('seal_member')
                        ->table('idtable' . $i)
                        ->where('delete_flag', 1)
                        ->where('id', 'not like', 'gm%')
                        ->count();
                    $stats['banned_users'] += $bannedCount;

                } catch (\Exception $e) {
                    // 如果某个表不存在，跳过
                    Log::warning("统计用户数据时表 idtable{$i} 不存在: " . $e->getMessage());
                }
            }

            // 今日操作数（封禁+解封）
            try {
                $todayStart = date('Y-m-d 00:00:00');
                $todayEnd = date('Y-m-d 23:59:59');

                $todayOperations = Db::table('user_status')
                    ->where('updated_at', '>=', $todayStart)
                    ->where('updated_at', '<=', $todayEnd)
                    ->count();

                $stats['today_operations'] = $todayOperations;
            } catch (\Exception $e) {
                $stats['today_operations'] = 0;
            }

            return $stats;

        } catch (\Exception $e) {
            Log::error('获取用户统计失败: ' . $e->getMessage());
            return [
                'total_users' => 0,
                'active_users' => 0,
                'banned_users' => 0,
                'online_users' => 0
            ];
        }
    }

    /**
     * 获取在线用户列表
     *
     * @param int $page 页码
     * @param int $limit 每页数量
     * @param string $search 搜索用户名
     * @return array
     */
    public function getOnlineUsers(int $page = 1, int $limit = 20, string $search = ''): array
    {
        // 由于没有实际的在线状态表，这里返回最近活跃的用户
        // 可以根据实际需求修改为真实的在线用户查询
        return $this->getUserList($page, $limit, $search, '1'); // 返回正常状态的用户
    }

    /**
     * 获取封禁记录列表
     *
     * @param int $page 页码
     * @param int $limit 每页数量
     * @param string $search 搜索用户名
     * @param string $status 状态筛选 (all|active|banned)
     * @param string $dateRange 日期范围筛选
     * @return array
     */
    public function getBanRecords(int $page = 1, int $limit = 20, string $search = '', string $status = '', string $dateRange = ''): array
    {
        try {
            $offset = ($page - 1) * $limit;

            // 构建查询条件
            $query = Db::table('user_status')
                ->field('id, username, status, ban_time, unban_time, created_at, updated_at')
                ->order('updated_at', 'desc');

            // 添加搜索条件
            if (!empty($search)) {
                $query->where('username', 'like', '%' . $search . '%');
            }

            // 添加状态筛选
            if ($status !== '' && $status !== 'all') {
                if ($status === 'active' || $status === '1') {
                    $query->where('status', 1);
                } elseif ($status === 'banned' || $status === '0') {
                    $query->where('status', 0);
                }
            }

            // 添加日期范围筛选
            if (!empty($dateRange)) {
                $dates = explode(' - ', $dateRange);
                if (count($dates) === 2) {
                    $startDate = trim($dates[0]) . ' 00:00:00';
                    $endDate = trim($dates[1]) . ' 23:59:59';
                    $query->whereBetween('updated_at', [$startDate, $endDate]);
                }
            }

            // 获取总数
            $totalCount = $query->count();

            // 获取分页数据
            $records = $query->limit($offset, $limit)->select();

            // 格式化数据
            $formattedRecords = [];
            foreach ($records as $record) {
                $formattedRecords[] = [
                    'id' => $record['id'],
                    'username' => $record['username'],
                    'status' => $record['status'],
                    'status_text' => $record['status'] == 1 ? '正常' : '封禁',
                    'status_badge' => $record['status'] == 1 ? 'success' : 'danger',
                    'ban_time' => $record['ban_time'],
                    'unban_time' => $record['unban_time'],
                    'created_at' => $record['created_at'],
                    'updated_at' => $record['updated_at'],
                    'last_action' => $record['status'] == 1 ?
                        ($record['unban_time'] ? '解封于 ' . $record['unban_time'] : '创建于 ' . $record['created_at']) :
                        ($record['ban_time'] ? '封禁于 ' . $record['ban_time'] : '创建于 ' . $record['created_at'])
                ];
            }

            return [
                'list' => $formattedRecords,
                'total' => $totalCount,
                'page' => $page,
                'limit' => $limit,
                'total_pages' => ceil($totalCount / $limit)
            ];

        } catch (\Exception $e) {
            Log::error('获取封禁记录失败: ' . $e->getMessage());
            return [
                'list' => [],
                'total' => 0,
                'page' => $page,
                'limit' => $limit,
                'total_pages' => 0
            ];
        }
    }

    /**
     * 获取激活记录列表
     *
     * @param int $page 页码
     * @param int $limit 每页数量
     * @param string $search 搜索用户名
     * @param string $status 状态筛选 (all|active|banned)
     * @return array
     */
    public function getActivationRecords(int $page = 1, int $limit = 20, string $search = '', string $status = 'all'): array
    {
        try {
            $offset = ($page - 1) * $limit;

            // 构建查询条件
            $query = Db::table('user_status')
                ->field('id, username, status, ban_time, unban_time, created_at, updated_at')
                ->order('updated_at', 'desc');

            // 添加搜索条件
            if (!empty($search)) {
                $query->where('username', 'like', '%' . $search . '%');
            }

            // 添加状态筛选
            if ($status !== 'all') {
                if ($status === 'active') {
                    $query->where('status', 1);
                } elseif ($status === 'banned') {
                    $query->where('status', 0);
                }
            }

            // 获取总数
            $totalCount = $query->count();

            // 获取分页数据
            $records = $query->limit($offset, $limit)->select();

            // 格式化数据
            $formattedRecords = [];
            foreach ($records as $record) {
                $formattedRecords[] = [
                    'id' => $record['id'],
                    'username' => $record['username'],
                    'status' => $record['status'],
                    'status_text' => $record['status'] == 1 ? '激活' : '封禁',
                    'ban_time' => $record['ban_time'],
                    'unban_time' => $record['unban_time'],
                    'created_at' => $record['created_at'],
                    'updated_at' => $record['updated_at'],
                    'last_action' => $record['status'] == 1 ?
                        ($record['unban_time'] ? '激活于 ' . $record['unban_time'] : '创建于 ' . $record['created_at']) :
                        ($record['ban_time'] ? '封禁于 ' . $record['ban_time'] : '创建于 ' . $record['created_at'])
                ];
            }
            
            return [
                'records' => $formattedRecords,
                'total' => $totalCount,
                'page' => $page,
                'limit' => $limit,
                'pages' => ceil($totalCount / $limit)
            ];
            
        } catch (\Exception $e) {
            Log::error('获取激活记录失败: ' . $e->getMessage());
            return [
                'records' => [],
                'total' => 0,
                'page' => $page,
                'limit' => $limit,
                'pages' => 0
            ];
        }
    }

    /**
     * 获取激活记录统计信息
     * 
     * @return array
     */
    public function getActivationStats(): array
    {
        try {
            $totalRecords = Db::table('user_status')->count();
            $activeRecords = Db::table('user_status')->where('status', 1)->count();
            $bannedRecords = Db::table('user_status')->where('status', 0)->count();
            
            // 今日激活数
            $todayActivated = Db::table('user_status')
                ->where('status', 1)
                ->where('unban_time', '>=', date('Y-m-d 00:00:00'))
                ->where('unban_time', '<=', date('Y-m-d 23:59:59'))
                ->count();
            
            // 今日封禁数
            $todayBanned = Db::table('user_status')
                ->where('status', 0)
                ->where('ban_time', '>=', date('Y-m-d 00:00:00'))
                ->where('ban_time', '<=', date('Y-m-d 23:59:59'))
                ->count();
            
            return [
                'total_records' => $totalRecords,
                'active_records' => $activeRecords,
                'banned_records' => $bannedRecords,
                'today_activated' => $todayActivated,
                'today_banned' => $todayBanned,
                'active_rate' => $totalRecords > 0 ? round(($activeRecords / $totalRecords) * 100, 2) : 0
            ];
            
        } catch (\Exception $e) {
            Log::error('获取激活记录统计失败: ' . $e->getMessage());
            return [
                'total_records' => 0,
                'active_records' => 0,
                'banned_records' => 0,
                'today_activated' => 0,
                'today_banned' => 0,
                'active_rate' => 0
            ];
        }
    }

    /**
     * 修复角色名编码问题
     *
     * @param string $originalCharName 原始角色名
     * @return string 修复后的角色名
     */
    private function fixCharacterNameEncoding(string $originalCharName): string
    {
        if (empty($originalCharName)) {
            return $originalCharName;
        }

        // 记录原始数据用于调试
        $hexData = bin2hex($originalCharName);
        Log::debug('处理角色名编码 - 新版本', [
            'original' => $originalCharName,
            'hex' => $hexData,
            'length' => strlen($originalCharName),
            'version' => 'v2.0'
        ]);

        // 检查是否已经是有效的UTF-8编码且包含中文字符
        if (mb_check_encoding($originalCharName, 'UTF-8') && preg_match('/[\x{4e00}-\x{9fff}]/u', $originalCharName)) {
            Log::debug('角色名已经是有效的UTF-8编码: ' . $originalCharName);
            return $originalCharName;
        }

        // 尝试多种编码转换方法
        $encodingMethods = [
            // 方法1：直接从GBK转换
            'GBK_direct' => function($data) {
                return mb_convert_encoding($data, 'UTF-8', 'GBK');
            },
            // 方法2：从GB2312转换
            'GB2312_direct' => function($data) {
                return mb_convert_encoding($data, 'UTF-8', 'GB2312');
            },
            // 方法3：从CP936转换
            'CP936_direct' => function($data) {
                return mb_convert_encoding($data, 'UTF-8', 'CP936');
            },
            // 方法4：假设是Latin-1编码的GBK数据，先转回二进制再转UTF-8
            'Latin1_to_GBK' => function($data) {
                // 将Latin-1字符转回原始字节
                $rawBytes = '';
                for ($i = 0; $i < strlen($data); $i++) {
                    $rawBytes .= chr(ord($data[$i]));
                }
                return mb_convert_encoding($rawBytes, 'UTF-8', 'GBK');
            },
            // 方法5：使用iconv进行转换
            'iconv_GBK' => function($data) {
                return iconv('GBK', 'UTF-8//IGNORE', $data);
            },
            // 方法6：使用iconv从GB2312转换
            'iconv_GB2312' => function($data) {
                return iconv('GB2312', 'UTF-8//IGNORE', $data);
            }
        ];

        foreach ($encodingMethods as $encoding => $method) {
            try {
                $converted = $method($originalCharName);

                // 检查转换结果是否包含中文字符且是有效的UTF-8
                if (!empty($converted) && mb_check_encoding($converted, 'UTF-8') && preg_match('/[\x{4e00}-\x{9fff}]/u', $converted)) {
                    Log::debug('成功使用' . $encoding . '编码转换角色名', [
                        'original' => $originalCharName,
                        'hex' => $hexData,
                        'converted' => $converted
                    ]);
                    return $converted;
                }
            } catch (\Exception $e) {
                Log::debug($encoding . '编码转换失败: ' . $e->getMessage());
            }
        }

        // 特殊处理：检查是否包含疑似乱码字符
        $suspiciousChars = ['²', '³', '¿', 'À', 'Á', 'Â', 'Ã', 'Ä', 'Å', 'Æ', 'Ç', 'È', 'É', 'Ê', 'Ë', 'Ì', 'Í', 'Î', 'Ï', 'Ð', 'Ñ', 'Ò', 'Ó', 'Ô', 'Õ', 'Ö', 'Ø', 'Ù', 'Ú', 'Û', 'Ü', 'Ý', 'Þ', 'ß', 'à', 'á', 'â', 'ã', 'ä', 'å', 'æ', 'ç', 'è', 'é', 'ê', 'ë', 'ì', 'í', 'î', 'ï', 'ð', 'ñ', 'ò', 'ó', 'ô', 'õ', 'ö', 'ø', 'ù', 'ú', 'û', 'ü', 'ý', 'þ', 'ÿ'];

        $hasSuspiciousChars = false;
        foreach ($suspiciousChars as $char) {
            if (strpos($originalCharName, $char) !== false) {
                $hasSuspiciousChars = true;
                Log::debug('发现疑似乱码字符: ' . $char . ' 在字符串: ' . $originalCharName);
                break;
            }
        }

        // 额外检查：如果字符串看起来像乱码（包含特定模式）
        if (!$hasSuspiciousChars) {
            // 检查是否包含连续的问号或特殊字符模式
            if (preg_match('/\?{2,}/', $originalCharName) ||
                preg_match('/[^\x20-\x7E\x{4e00}-\x{9fff}]/u', $originalCharName)) {
                $hasSuspiciousChars = true;
                Log::debug('通过模式匹配发现疑似乱码: ' . $originalCharName);
            }
        }

        if ($hasSuspiciousChars) {
            Log::debug('开始处理疑似乱码字符', [
                'original' => $originalCharName,
                'hex' => $hexData
            ]);

            // 尝试多种转换方法
            $conversionMethods = [
                'iconv_GBK' => function($data) {
                    return iconv('GBK', 'UTF-8//IGNORE', $data);
                },
                'mb_convert_GBK' => function($data) {
                    return mb_convert_encoding($data, 'UTF-8', 'GBK');
                },
                'iconv_GB2312' => function($data) {
                    return iconv('GB2312', 'UTF-8//IGNORE', $data);
                },
                'mb_convert_GB2312' => function($data) {
                    return mb_convert_encoding($data, 'UTF-8', 'GB2312');
                }
            ];

            foreach ($conversionMethods as $methodName => $method) {
                try {
                    $fixed = $method($originalCharName);
                    Log::debug('尝试转换方法: ' . $methodName, [
                        'result' => $fixed,
                        'result_hex' => bin2hex($fixed),
                        'is_utf8' => mb_check_encoding($fixed, 'UTF-8'),
                        'has_chinese' => preg_match('/[\x{4e00}-\x{9fff}]/u', $fixed)
                    ]);

                    if (!empty($fixed) && mb_check_encoding($fixed, 'UTF-8')) {
                        // 检查转换后是否包含中文字符
                        if (preg_match('/[\x{4e00}-\x{9fff}]/u', $fixed)) {
                            Log::debug('成功使用' . $methodName . '修复角色名', [
                                'original' => $originalCharName,
                                'hex' => $hexData,
                                'fixed' => $fixed
                            ]);
                            return $fixed;
                        }
                    }
                } catch (\Exception $e) {
                    Log::debug($methodName . '转换失败: ' . $e->getMessage());
                }
            }
        }

        // 如果所有转换方法都失败，但是包含可打印字符且不包含疑似乱码字符，保持原样
        if (ctype_print($originalCharName) && !$hasSuspiciousChars) {
            Log::debug('保持原始角色名（可打印字符）- 新版本: ' . $originalCharName);
            return $originalCharName;
        }

        // 最后的备选方案：返回默认值
        Log::warning('角色名编码转换失败，使用默认值', [
            'original' => $originalCharName,
            'hex' => $hexData
        ]);

        return '角色名显示异常';
    }

    /**
     * 批量封禁用户
     *
     * @param array $userIds 用户ID列表
     * @param string $reason 封禁原因
     * @return bool
     */
    public function batchBanUsers(array $userIds, string $reason = ''): bool
    {
        try {
            $successCount = 0;

            foreach ($userIds as $userId) {
                if ($this->banUser($userId)) {
                    $successCount++;
                }
            }

            return $successCount > 0;
        } catch (\Exception $e) {
            Log::error('批量封禁用户失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 批量解封用户
     *
     * @param array $userIds 用户ID列表
     * @param string $reason 解封原因
     * @return bool
     */
    public function batchUnbanUsers(array $userIds, string $reason = ''): bool
    {
        try {
            $successCount = 0;

            foreach ($userIds as $userId) {
                if ($this->unbanUser($userId)) {
                    $successCount++;
                }
            }

            return $successCount > 0;
        } catch (\Exception $e) {
            Log::error('批量解封用户失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 批量踢出用户（暂时实现为封禁）
     *
     * @param array $userIds 用户ID列表
     * @param string $reason 踢出原因
     * @return bool
     */
    public function batchKickUsers(array $userIds, string $reason = ''): bool
    {
        // 暂时实现为封禁，可以根据实际需求修改
        return $this->batchBanUsers($userIds, $reason);
    }

    /**
     * 清除用户缓存
     *
     * @param string $username 用户名
     * @return bool
     */
    public function clearUserCache(string $username): bool
    {
        try {
            // 清除用户相关的缓存
            $cacheKeys = [
                'user_detail:' . $username,
                'user_balance:' . $username,
                'user_status:' . $username
            ];

            foreach ($cacheKeys as $key) {
                \app\service\CacheService::delete($key);
            }

            return true;
        } catch (\Exception $e) {
            Log::error('清除用户缓存失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 预热用户缓存
     *
     * @return bool
     */
    public function warmupUserCache(): bool
    {
        try {
            // 预热常用的用户数据缓存
            // 这里可以根据实际需求实现
            return true;
        } catch (\Exception $e) {
            Log::error('预热用户缓存失败: ' . $e->getMessage());
            return false;
        }
    }
}