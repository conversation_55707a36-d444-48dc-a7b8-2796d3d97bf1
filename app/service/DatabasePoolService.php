<?php

namespace app\service;

use think\facade\Db;
use think\facade\Log;
use think\facade\Cache;

/**
 * 数据库连接池服务
 * 优化数据库连接管理，减少连接开销，提升并发性能
 */
class DatabasePoolService
{
    /**
     * 连接池配置
     */
    private static $poolConfig = [
        // 最大连接数
        'max_connections' => 20,
        // 最小连接数
        'min_connections' => 5,
        // 连接超时时间（秒）
        'connection_timeout' => 30,
        // 空闲连接超时时间（秒）
        'idle_timeout' => 300,
        // 连接检查间隔（秒）
        'check_interval' => 60,
    ];

    /**
     * 连接池状态
     */
    private static $poolStats = [
        'total_connections' => 0,
        'active_connections' => 0,
        'idle_connections' => 0,
        'failed_connections' => 0,
        'connection_requests' => 0,
        'cache_hits' => 0,
    ];

    /**
     * 连接缓存
     */
    private static $connectionCache = [];

    /**
     * 获取优化的数据库连接
     *
     * @param string $connection 连接名称
     * @return \think\db\Query
     */
    public static function getConnection(string $connection = 'mysql')
    {
        self::$poolStats['connection_requests']++;
        
        try {
            // 检查连接缓存
            if (isset(self::$connectionCache[$connection])) {
                self::$poolStats['cache_hits']++;
                return self::$connectionCache[$connection];
            }

            // 创建新连接
            $db = Db::connect($connection);
            
            // 缓存连接
            self::$connectionCache[$connection] = $db;
            self::$poolStats['total_connections']++;
            
            Log::info("数据库连接池: 创建新连接", [
                'connection' => $connection,
                'total_connections' => self::$poolStats['total_connections']
            ]);
            
            return $db;
            
        } catch (\Exception $e) {
            self::$poolStats['failed_connections']++;
            Log::error("数据库连接池: 连接失败", [
                'connection' => $connection,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 执行优化的数据库查询
     * 
     * @param string $connection 连接名称
     * @param callable $callback 查询回调
     * @param string $cacheKey 缓存键
     * @param int $cacheTime 缓存时间
     * @return mixed
     */
    public static function query(string $connection, callable $callback, string $cacheKey = null, int $cacheTime = 300)
    {
        // 如果有缓存键，先尝试从缓存获取
        if ($cacheKey) {
            $cached = CacheService::get($cacheKey);
            if ($cached !== null) {
                return $cached;
            }
        }

        try {
            $db = self::getConnection($connection);
            $result = $callback($db);
            
            // 缓存结果
            if ($cacheKey && $result !== null) {
                CacheService::set($cacheKey, $result, $cacheTime);
            }
            
            return $result;
            
        } catch (\Exception $e) {
            Log::error("数据库连接池: 查询失败", [
                'connection' => $connection,
                'cache_key' => $cacheKey,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 批量查询优化
     * 
     * @param array $queries 查询配置数组
     * @return array
     */
    public static function batchQuery(array $queries): array
    {
        $results = [];
        $connections = [];
        
        try {
            // 预先建立连接
            foreach ($queries as $key => $query) {
                $connection = $query['connection'] ?? 'mysql';
                if (!isset($connections[$connection])) {
                    $connections[$connection] = self::getConnection($connection);
                }
            }
            
            // 执行查询
            foreach ($queries as $key => $query) {
                $connection = $query['connection'] ?? 'mysql';
                $callback = $query['callback'];
                $cacheKey = $query['cache_key'] ?? null;
                $cacheTime = $query['cache_time'] ?? 300;
                
                // 检查缓存
                if ($cacheKey) {
                    $cached = CacheService::get($cacheKey);
                    if ($cached !== null) {
                        $results[$key] = $cached;
                        continue;
                    }
                }
                
                // 执行查询
                $result = $callback($connections[$connection]);
                $results[$key] = $result;
                
                // 缓存结果
                if ($cacheKey && $result !== null) {
                    CacheService::set($cacheKey, $result, $cacheTime);
                }
            }
            
            return $results;
            
        } catch (\Exception $e) {
            Log::error("数据库连接池: 批量查询失败", [
                'error' => $e->getMessage(),
                'queries_count' => count($queries)
            ]);
            throw $e;
        }
    }

    /**
     * 获取连接池统计信息
     * 
     * @return array
     */
    public static function getStats(): array
    {
        return array_merge(self::$poolStats, [
            'cache_hit_rate' => self::$poolStats['connection_requests'] > 0 
                ? round((self::$poolStats['cache_hits'] / self::$poolStats['connection_requests']) * 100, 2) 
                : 0,
            'failure_rate' => self::$poolStats['connection_requests'] > 0 
                ? round((self::$poolStats['failed_connections'] / self::$poolStats['connection_requests']) * 100, 2) 
                : 0,
            'cached_connections' => count(self::$connectionCache),
        ]);
    }

    /**
     * 清理空闲连接
     */
    public static function cleanup(): void
    {
        try {
            // 清理连接缓存
            self::$connectionCache = [];
            
            Log::info("数据库连接池: 清理完成", [
                'cleaned_connections' => self::$poolStats['total_connections']
            ]);
            
            // 重置统计
            self::$poolStats['total_connections'] = 0;
            self::$poolStats['active_connections'] = 0;
            self::$poolStats['idle_connections'] = 0;
            
        } catch (\Exception $e) {
            Log::error("数据库连接池: 清理失败", [
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 健康检查
     * 
     * @return array
     */
    public static function healthCheck(): array
    {
        $health = [
            'status' => 'healthy',
            'connections' => [],
            'issues' => []
        ];

        $connections = ['mysql', 'item', 'seal_member', 'gdb0101'];
        
        foreach ($connections as $connection) {
            try {
                $db = self::getConnection($connection);
                $result = $db->query('SELECT 1 as test');
                
                $health['connections'][$connection] = [
                    'status' => 'connected',
                    'response_time' => microtime(true)
                ];
                
            } catch (\Exception $e) {
                $health['connections'][$connection] = [
                    'status' => 'failed',
                    'error' => $e->getMessage()
                ];
                $health['issues'][] = "连接 {$connection} 失败: " . $e->getMessage();
                $health['status'] = 'degraded';
            }
        }

        return $health;
    }

    /**
     * 重置连接池
     */
    public static function reset(): void
    {
        self::cleanup();
        self::$poolStats = [
            'total_connections' => 0,
            'active_connections' => 0,
            'idle_connections' => 0,
            'failed_connections' => 0,
            'connection_requests' => 0,
            'cache_hits' => 0,
        ];
        
        Log::info("数据库连接池: 已重置");
    }
}
