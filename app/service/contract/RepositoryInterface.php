<?php

namespace app\service\contract;

/**
 * 数据仓库接口
 * 定义通用的数据访问方法
 * 
 * 设计原则：
 * 1. 接口隔离原则：定义最小化的接口
 * 2. 依赖倒置原则：高层模块依赖抽象而非具体实现
 * 3. 开闭原则：对扩展开放，对修改封闭
 */
interface RepositoryInterface
{
    /**
     * 根据ID查找记录
     * 
     * @param mixed $id 主键值
     * @return array|null
     */
    public function findById($id): ?array;
    
    /**
     * 根据条件查找单条记录
     * 
     * @param array $conditions 查询条件
     * @return array|null
     */
    public function findOne(array $conditions): ?array;
    
    /**
     * 根据条件查找多条记录
     * 
     * @param array $conditions 查询条件
     * @param array $options 查询选项
     * @return array
     */
    public function findMany(array $conditions = [], array $options = []): array;
    
    /**
     * 分页查询
     * 
     * @param int $page 页码
     * @param int $limit 每页数量
     * @param array $conditions 查询条件
     * @param array $options 查询选项
     * @return array
     */
    public function paginate(int $page, int $limit, array $conditions = [], array $options = []): array;
    
    /**
     * 创建记录
     * 
     * @param array $data 数据
     * @return mixed 新记录的ID
     */
    public function create(array $data);
    
    /**
     * 更新记录
     * 
     * @param mixed $id 主键值
     * @param array $data 数据
     * @return bool
     */
    public function update($id, array $data): bool;
    
    /**
     * 删除记录
     * 
     * @param mixed $id 主键值
     * @return bool
     */
    public function delete($id): bool;
    
    /**
     * 批量创建记录
     * 
     * @param array $dataList 数据列表
     * @return bool
     */
    public function batchCreate(array $dataList): bool;
    
    /**
     * 批量更新记录
     * 
     * @param array $conditions 更新条件
     * @param array $data 更新数据
     * @return int 影响的行数
     */
    public function batchUpdate(array $conditions, array $data): int;
    
    /**
     * 批量删除记录
     * 
     * @param array $conditions 删除条件
     * @return int 影响的行数
     */
    public function batchDelete(array $conditions): int;
    
    /**
     * 统计记录数量
     * 
     * @param array $conditions 查询条件
     * @return int
     */
    public function count(array $conditions = []): int;
    
    /**
     * 检查记录是否存在
     * 
     * @param array $conditions 查询条件
     * @return bool
     */
    public function exists(array $conditions): bool;
    
    /**
     * 获取最大值
     * 
     * @param string $field 字段名
     * @param array $conditions 查询条件
     * @return mixed
     */
    public function max(string $field, array $conditions = []);
    
    /**
     * 获取最小值
     * 
     * @param string $field 字段名
     * @param array $conditions 查询条件
     * @return mixed
     */
    public function min(string $field, array $conditions = []);
    
    /**
     * 获取平均值
     * 
     * @param string $field 字段名
     * @param array $conditions 查询条件
     * @return float
     */
    public function avg(string $field, array $conditions = []): float;
    
    /**
     * 获取总和
     * 
     * @param string $field 字段名
     * @param array $conditions 查询条件
     * @return mixed
     */
    public function sum(string $field, array $conditions = []);
    
    /**
     * 开始事务
     * 
     * @return void
     */
    public function beginTransaction(): void;
    
    /**
     * 提交事务
     * 
     * @return void
     */
    public function commit(): void;
    
    /**
     * 回滚事务
     * 
     * @return void
     */
    public function rollback(): void;
    
    /**
     * 执行原生SQL查询
     * 
     * @param string $sql SQL语句
     * @param array $bindings 参数绑定
     * @return array
     */
    public function query(string $sql, array $bindings = []): array;
    
    /**
     * 执行原生SQL语句
     * 
     * @param string $sql SQL语句
     * @param array $bindings 参数绑定
     * @return int 影响的行数
     */
    public function execute(string $sql, array $bindings = []): int;
}
