<?php

namespace app\service;

use think\facade\Db;
use think\facade\Log;

/**
 * 认证服务类
 * 负责处理用户身份验证相关的业务逻辑
 * 遵循单一职责原则：专门处理认证相关功能
 */
class AuthenticationService
{
    /**
     * 密码验证器接口
     * 定义密码验证的标准接口，支持不同的验证策略
     */
    private $passwordValidator;
    
    /**
     * 用户数据访问对象
     * 负责从数据库获取用户信息
     */
    private $userRepository;
    
    /**
     * 构造函数
     * 依赖注入密码验证器和用户仓库
     */
    public function __construct()
    {
        $this->passwordValidator = new IdTablePasswordValidator();
        $this->userRepository = new IdTableUserRepository();
    }
    
    /**
     * 用户认证
     * @param string $username 用户名
     * @param string $password 密码
     * @return array 认证结果
     */
    public function authenticate(string $username, string $password): array
    {
        try {
            // 参数验证
            if (empty($username) || empty($password)) {
                return $this->createErrorResponse(400, '用户名和密码不能为空');
            }
            
            // 获取用户信息
            $user = $this->userRepository->findUserById($username);
            
            if (!$user) {
                return $this->createErrorResponse(401, '用户不存在');
            }
            
            // 验证密码
            if (!$this->passwordValidator->verify($password, $user['passwd'])) {
                return $this->createErrorResponse(401, '密码错误');
            }
            
            // 认证成功
            return $this->createSuccessResponse([
                'user_id' => $user['id'],
                'username' => $user['id']
            ]);
            
        } catch (\Exception $e) {
            Log::error('用户认证失败: ' . $e->getMessage());
            return $this->createErrorResponse(500, '认证服务异常: ' . $e->getMessage());
        }
    }
    
    /**
     * 创建成功响应
     * @param array $data 响应数据
     * @return array
     */
    private function createSuccessResponse(array $data): array
    {
        return [
            'code' => 200,
            'message' => '认证成功',
            'data' => $data
        ];
    }
    
    /**
     * 创建错误响应
     * @param int $code 错误码
     * @param string $message 错误信息
     * @return array
     */
    private function createErrorResponse(int $code, string $message): array
    {
        return [
            'code' => $code,
            'message' => $message,
            'data' => null
        ];
    }
}

/**
 * 密码验证器接口
 * 定义密码验证的标准契约
 */
interface PasswordValidatorInterface
{
    /**
     * 验证密码
     * @param string $plainPassword 明文密码
     * @param string $hashedPassword 加密后的密码
     * @return bool
     */
    public function verify(string $plainPassword, string $hashedPassword): bool;
}

/**
 * IdTable密码验证器
 * 实现基于OLD_PASSWORD()函数的密码验证
 * 遵循开闭原则：对扩展开放，对修改关闭
 */
class IdTablePasswordValidator implements PasswordValidatorInterface
{
    /**
     * 验证密码
     * 使用OLD_PASSWORD()函数进行密码验证
     * @param string $plainPassword 明文密码
     * @param string $hashedPassword 数据库中的加密密码
     * @return bool
     */
    public function verify(string $plainPassword, string $hashedPassword): bool
    {
        try {
            // 使用OLD_PASSWORD()函数加密输入的密码
            $encryptedPassword = $this->oldPassword($plainPassword);
            
            // 比较加密后的密码
            return $encryptedPassword === $hashedPassword;
            
        } catch (\Exception $e) {
            Log::error('密码验证失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * OLD_PASSWORD()函数实现
     * 模拟MySQL的OLD_PASSWORD()函数
     * @param string $password 明文密码
     * @return string 加密后的密码
     */
    private function oldPassword(string $password): string
    {
        try {
            // 使用数据库的OLD_PASSWORD()函数进行加密
            $result = Db::connect('seal_member')
                ->query('SELECT OLD_PASSWORD(?) as encrypted_password', [$password]);
                
            if (!empty($result) && isset($result[0]['encrypted_password'])) {
                return $result[0]['encrypted_password'];
            }
            
            return '';
        } catch (\Exception $e) {
            Log::error('OLD_PASSWORD加密失败: ' . $e->getMessage());
            return '';
        }
    }
}

/**
 * 用户仓库接口
 * 定义用户数据访问的标准契约
 */
interface UserRepositoryInterface
{
    /**
     * 根据ID查找用户
     * @param string $userId 用户ID
     * @return array|null
     */
    public function findUserById(string $userId): ?array;
}

/**
 * IdTable用户仓库
 * 负责从idtable1-idtable5表中获取用户信息
 * 遵循单一职责原则：专门负责用户数据访问
 */
class IdTableUserRepository implements UserRepositoryInterface
{
    /**
     * 从idtable1-idtable5表中查找用户
     * @param string $userId 用户ID
     * @return array|null
     */
    public function findUserById(string $userId): ?array
    {
        try {
            // 遍历idtable1-idtable5表查找用户
            for ($i = 1; $i <= 5; $i++) {
                $user = Db::connect('seal_member')
                    ->table('idtable' . $i)
                    ->where('id', $userId)
                    ->field('id, passwd')
                    ->find();
                    
                if ($user) {
                    return $user;
                }
            }
            
            return null;
            
        } catch (\Exception $e) {
            Log::error('查找用户失败: ' . $e->getMessage());
            throw $e;
        }
    }
}