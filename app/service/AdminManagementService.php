<?php

namespace app\service;

use think\facade\Db;
use think\facade\Log;
use app\service\CacheService;

/**
 * 管理员管理服务类
 * 负责管理后台的业务逻辑处理
 * 
 * 设计原则：
 * 1. 单一职责原则：专门处理管理后台相关业务
 * 2. 开闭原则：易于扩展新的管理功能
 * 3. 依赖倒置原则：依赖抽象接口而非具体实现
 */
class AdminManagementService
{
    /**
     * IdTable服务实例
     */
    private IdTableService $idTableService;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->idTableService = new IdTableService();
    }

    /**
     * 统计数据缓存时间（秒）
     */
    const STATS_CACHE_TTL = 1800; // 30分钟（延长缓存时间）

    /**
     * 统计数据快速缓存时间（秒）
     */
    const STATS_FAST_CACHE_TTL = 60; // 1分钟（用于快速更新）
    
    /**
     * 商品数据缓存时间（秒）
     */
    const ITEM_CACHE_TTL = 600; // 10分钟
    
    /**
     * 获取管理后台统计数据
     *
     * @param bool $useCache 是否使用缓存
     * @return array
     */
    public function getAdminStats(bool $useCache = true): array
    {
        if (!$useCache) {
            return $this->calculateStats();
        }

        return CacheService::getWithFallback(
            'admin_stats',
            function() {
                return $this->calculateStats();
            },
            self::STATS_CACHE_TTL,
            $this->getDefaultStats()
        );
    }

    /**
     * 获取快速统计数据（用于渐进式加载）
     *
     * @return array
     */
    public function getFastStats(): array
    {
        // 先尝试获取缓存的完整数据
        $cachedStats = CacheService::get('admin_stats');
        if ($cachedStats !== null) {
            return $cachedStats;
        }

        // 如果没有缓存，返回快速统计数据
        return CacheService::getWithFallback(
            'admin_stats_fast',
            function() {
                return $this->calculateFastStats();
            },
            self::STATS_FAST_CACHE_TTL,
            $this->getDefaultStats()
        );
    }

    /**
     * 计算快速统计数据（只包含本地数据库查询）
     *
     * @return array
     */
    private function calculateFastStats(): array
    {
        try {
            // 只查询本地数据库的快速统计
            $orderStats = $this->getOrderStats();
            $totalItems = $this->getTotalItemCount();
            $totalCCoin = Db::name('c_coin')
                ->where('game_account', 'not like', 'gm%')
                ->sum('balance') ?: 0;

            return [
                'total_users' => 0, // 占位符，后续异步更新
                'total_orders' => $orderStats['total'],
                'today_orders' => $orderStats['today'],
                'yesterday_orders' => $orderStats['yesterday'],
                'total_items' => $totalItems,
                'total_ccoin' => $totalCCoin,
                'total_bubble_point' => 0, // 占位符，后续异步更新
                'total_haha_point' => 0, // 占位符，后续异步更新
                'is_partial' => true // 标记为部分数据
            ];
        } catch (\Exception $e) {
            Log::error('计算快速统计数据失败: ' . $e->getMessage());
            return $this->getDefaultStats();
        }
    }
    
    /**
     * 计算统计数据（并行优化版本）
     *
     * @return array
     */
    private function calculateStats(): array
    {
        try {
            // 使用并行计算来提高性能
            $results = $this->calculateStatsParallel();

            return [
                'total_users' => $results['total_users'] ?? 0,
                'total_orders' => $results['order_stats']['total'] ?? 0,
                'today_orders' => $results['order_stats']['today'] ?? 0,
                'yesterday_orders' => $results['order_stats']['yesterday'] ?? 0,
                'total_items' => $results['total_items'] ?? 0,
                'total_ccoin' => $results['currency_stats']['ccoin'] ?? 0,
                'total_bubble_point' => $results['currency_stats']['bubble_point'] ?? 0,
                'total_haha_point' => $results['currency_stats']['haha_point'] ?? 0,
                'online_users' => $results['online_users'] ?? 0,
                'today_sales' => $results['today_sales'] ?? 0
            ];
        } catch (\Exception $e) {
            Log::error('计算统计数据失败: ' . $e->getMessage());
            return $this->getDefaultStats();
        }
    }

    /**
     * 并行计算统计数据
     *
     * @return array
     */
    private function calculateStatsParallel(): array
    {
        $results = [];
        $errors = [];

        // 定义所有统计任务
        $tasks = [
            'total_users' => function() { return $this->getTotalUserCount(); },
            'order_stats' => function() { return $this->getOrderStats(); },
            'total_items' => function() { return $this->getTotalItemCount(); },
            'currency_stats' => function() { return $this->getCurrencyStats(); },
            'online_users' => function() { return $this->getOnlineUserCount(); },
            'today_sales' => function() { return $this->getTodaySales(); }
        ];

        // 并行执行所有任务
        foreach ($tasks as $taskName => $task) {
            try {
                $startTime = microtime(true);
                $results[$taskName] = $task();
                $endTime = microtime(true);
                Log::info("统计任务 {$taskName} 完成", [
                    'duration' => round(($endTime - $startTime) * 1000, 2) . 'ms'
                ]);
            } catch (\Exception $e) {
                $errors[$taskName] = $e->getMessage();
                Log::error("统计任务 {$taskName} 失败: " . $e->getMessage());

                // 设置默认值
                switch ($taskName) {
                    case 'total_users':
                        $results[$taskName] = 0;
                        break;
                    case 'order_stats':
                        $results[$taskName] = ['total' => 0, 'today' => 0, 'yesterday' => 0];
                        break;
                    case 'total_items':
                        $results[$taskName] = 0;
                        break;
                    case 'currency_stats':
                        $results[$taskName] = ['ccoin' => 0, 'bubble_point' => 0, 'haha_point' => 0];
                        break;
                    case 'online_users':
                        $results[$taskName] = 0;
                        break;
                    case 'today_sales':
                        $results[$taskName] = 0;
                        break;
                }
            }
        }

        if (!empty($errors)) {
            Log::warning('部分统计任务失败', $errors);
        }

        return $results;
    }
    
    /**
     * 获取总用户数（使用优化的IdTableService）
     *
     * @return int
     */
    private function getTotalUserCount(): int
    {
        return $this->idTableService->getTotalUserCount(true);
    }
    
    /**
     * 获取订单统计
     * 
     * @return array
     */
    private function getOrderStats(): array
    {
        try {
            // 使用原生SQL查询，确保准确性
            $today = date('Y-m-d');
            $yesterday = date('Y-m-d', strtotime('-1 day'));

            // 总订单数
            $total = Db::name('purchase_orders')
                ->where('delete_time', null)
                ->count();

            // 今日订单数
            $todayCount = Db::query("SELECT COUNT(*) as count FROM purchase_orders WHERE delete_time IS NULL AND DATE(create_time) = ?", [$today]);
            $todayOrders = $todayCount[0]['count'] ?? 0;

            // 昨日订单数
            $yesterdayCount = Db::query("SELECT COUNT(*) as count FROM purchase_orders WHERE delete_time IS NULL AND DATE(create_time) = ?", [$yesterday]);
            $yesterdayOrders = $yesterdayCount[0]['count'] ?? 0;

            Log::info('订单统计查询结果', [
                'today' => $today,
                'yesterday' => $yesterday,
                'total' => $total,
                'today_orders' => $todayOrders,
                'yesterday_orders' => $yesterdayOrders
            ]);

            return [
                'total' => $total,
                'today' => $todayOrders,
                'yesterday' => $yesterdayOrders
            ];
        } catch (\Exception $e) {
            Log::error('获取订单统计失败: ' . $e->getMessage());
            return ['total' => 0, 'today' => 0, 'yesterday' => 0];
        }
    }
    
    /**
     * 获取商品总数
     * 
     * @return int
     */
    private function getTotalItemCount(): int
    {
        try {
            return Db::name('pointshop')->count();
        } catch (\Exception $e) {
            Log::error('获取商品总数失败: ' . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * 获取货币统计（优化版本）
     *
     * @return array
     */
    private function getCurrencyStats(): array
    {
        try {
            $results = [];
            $startTime = microtime(true);

            // 并行执行货币统计查询
            $currencyTasks = [
                'ccoin' => function() {
                    return Db::name('c_coin')
                        ->where('game_account', 'not like', 'gm%')
                        ->sum('balance') ?: 0;
                },
                'bubble_point' => function() {
                    return $this->getBubblePointTotal();
                },
                'haha_point' => function() {
                    return $this->getHahaPointTotal();
                }
            ];

            foreach ($currencyTasks as $currency => $task) {
                try {
                    $taskStart = microtime(true);
                    $results[$currency] = $task();
                    $taskEnd = microtime(true);
                    Log::info("货币统计 {$currency} 完成", [
                        'duration' => round(($taskEnd - $taskStart) * 1000, 2) . 'ms',
                        'value' => $results[$currency]
                    ]);
                } catch (\Exception $e) {
                    Log::error("货币统计 {$currency} 失败: " . $e->getMessage());
                    $results[$currency] = 0;
                }
            }

            $endTime = microtime(true);
            Log::info('货币统计总耗时', [
                'duration' => round(($endTime - $startTime) * 1000, 2) . 'ms'
            ]);

            return $results;
        } catch (\Exception $e) {
            Log::error('获取货币统计失败: ' . $e->getMessage());
            return ['ccoin' => 0, 'bubble_point' => 0, 'haha_point' => 0];
        }
    }

    /**
     * 获取泡点总数（使用优化的IdTableService）
     *
     * @return int
     */
    private function getBubblePointTotal(): int
    {
        return $this->idTableService->getTotalPoints(true);
    }

    /**
     * 获取积分总数（优化版本）
     *
     * @return int
     */
    private function getHahaPointTotal(): int
    {
        try {
            $sealWebConfig = $this->getSealWebConfig();
            return Db::connect($sealWebConfig)
                ->table('sealmember')
                ->where('id', 'not like', 'gm%')
                ->sum('hahapoint') ?: 0;
        } catch (\Exception $e) {
            Log::warning('获取积分统计失败: ' . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * 处理商品数据格式
     * 
     * @param array $items 原始商品数据
     * @return array 格式化后的商品数据
     */
    public function formatItemsForAdmin(array $items): array
    {
        $formattedItems = [];

        foreach ($items as $item) {
            $formattedItems[] = [
                'id' => $item['id'],
                'item' => $item['item'],  // 保留原始item字段
                'item_id' => $item['item'],
                'name' => $item['name'],
                'class' => $item['class'],  // 保留原始class字段用于前端配置
                'category' => $item['class'],  // 兼容字段
                'class_name' => $item['class_name'] ?? \app\service\ShopConfigService::getCategoryName($item['class']),
                'price' => $item['price'],  // 保留原始数字价格
                'price_display' => $item['price_display'] ?? $item['price'],
                'price_type' => $item['price_type'],  // 保留原始price_type数字
                'price_type_name' => $item['price_type_name'] ?? \app\service\ShopConfigService::getCurrencyName($item['price_type']),
                'currency_type' => $item['price_type'],  // 兼容字段
                'stock' => $item['limit_quantity'] ?? 999,
                'status' => $item['status'],
                'create_time' => date('Y-m-d H:i:s'),
                'icon' => $item['icon'] ?? '/static/img/default.png',
                'description' => $item['item_info'] ?? '暂无描述',
                'restriction' => $item['restriction_name'] ?? '无限制',
                'delivery_mode' => $item['delivery_mode'] ?? 1,
            ];
        }
        
        return $formattedItems;
    }

    /**
     * 异步更新统计数据
     *
     * @return bool
     */
    public function updateStatsAsync(): bool
    {
        try {
            // 在后台异步计算完整统计数据
            $stats = $this->calculateStats();

            // 更新缓存
            CacheService::set('admin_stats', $stats, self::STATS_CACHE_TTL);

            Log::info('异步更新统计数据完成', $stats);
            return true;
        } catch (\Exception $e) {
            Log::error('异步更新统计数据失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 预热统计数据缓存
     *
     * @return bool
     */
    public function warmupStatsCache(): bool
    {
        try {
            // 先设置快速统计数据
            $fastStats = $this->calculateFastStats();
            CacheService::set('admin_stats_fast', $fastStats, self::STATS_FAST_CACHE_TTL);

            // 然后异步计算完整统计数据
            $this->updateStatsAsync();

            return true;
        } catch (\Exception $e) {
            Log::error('预热统计数据缓存失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 更新商品信息
     * 
     * @param array $data 商品数据
     * @return bool
     */
    public function updateItem(array $data): bool
    {
        try {
            // 使用验证助手类验证数据
            \app\common\ValidationHelper::validateItemData($data);

            // 使用验证助手类清理数据
            $updateData = \app\common\ValidationHelper::sanitizeData($data, [
                'name' => 'string',
                'price' => 'float',
                'price_type' => 'int',
                'class' => 'string',
                'limit_quantity' => 'int',
                'restriction' => 'int',
                'status' => 'int',
                'item_info' => 'string',
                'delivery_mode' => 'int'
            ]);

            // 移除ID字段，避免更新主键
            unset($updateData['id']);

            $result = Db::table('pointshop')
                ->where('id', $data['id'])
                ->update($updateData);

            // 使用缓存助手类清除相关缓存
            \app\common\CacheHelper::deleteByPattern('items:*');

            return $result > 0;
        } catch (\Exception $e) {
            Log::error('更新商品信息失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 批量处理用户操作
     * 
     * @param array $usernames 用户名列表
     * @param string $operation 操作类型
     * @param UserService $userService 用户服务
     * @return array
     */
    public function batchProcessUsers(array $usernames, string $operation, UserService $userService): array
    {
        $results = [];
        
        foreach ($usernames as $username) {
            $result = [
                'username' => $username,
                'success' => false,
                'message' => ''
            ];
            
            try {
                switch ($operation) {
                    case 'activate':
                        $success = $userService->unbanUser($username);
                        $result['success'] = $success;
                        $result['message'] = $success ? '激活成功' : '激活失败';
                        break;
                        
                    case 'ban':
                        $success = $userService->banUser($username);
                        $result['success'] = $success;
                        $result['message'] = $success ? '封禁成功' : '封禁失败';
                        break;
                        
                    case 'check':
                        $userInfo = $userService->getUserStatus($username);
                        $result['success'] = true;
                        $result['message'] = $userInfo ? 
                            ($userInfo['status'] == 1 ? '用户状态：激活' : '用户状态：封禁') : 
                            '用户不存在';
                        break;
                        
                    default:
                        $result['message'] = '不支持的操作类型';
                }
            } catch (\Exception $e) {
                $result['message'] = '操作失败: ' . $e->getMessage();
            }
            
            $results[] = $result;
        }
        
        return $results;
    }
    
    /**
     * 获取默认统计数据
     * 
     * @return array
     */
    private function getDefaultStats(): array
    {
        return [
            'total_users' => 0,
            'total_orders' => 0,
            'today_orders' => 0,
            'yesterday_orders' => 0,
            'total_items' => 0,
            'total_ccoin' => 0,
            'total_bubble_point' => 0,
            'total_haha_point' => 0,
            'online_users' => 0,
            'today_sales' => 0
        ];
    }
    
    /**
     * 获取seal_member数据库配置
     * 
     * @return array
     */
    private function getSealMemberConfig(): array
    {
        return [
            'type' => 'mysql',
            'hostname' => env('REMOTE_DB_HOST'),
            'database' => env('SEAL_MEMBER_DB_NAME'),
            'username' => env('REMOTE_DB_USER'),
            'password' => env('REMOTE_DB_PASS'),
            'hostport' => env('REMOTE_DB_PORT'),
            'charset' => env('REMOTE_DB_CHARSET'),
        ];
    }
    
    /**
     * 获取seal_web数据库配置
     * 
     * @return array
     */
    private function getSealWebConfig(): array
    {
        return [
            'type' => env('DB_TYPE', 'mysql'),
            'hostname' => env('REMOTE_DB_HOST', ''),
            'database' => env('SEAL_WEB_DB_NAME', ''),
            'username' => env('REMOTE_DB_USER', ''),
            'password' => env('REMOTE_DB_PASS', ''),
            'hostport' => env('REMOTE_DB_PORT', ''),
            'charset' => env('REMOTE_DB_CHARSET', 'utf8'),
        ];
    }
    
    /**
     * 清除商品缓存
     */
    private function clearItemCache(): void
    {
        try {
            CacheService::deleteByPattern('items:*');
            CacheService::deleteByPattern('item_info:*');
        } catch (\Exception $e) {
            Log::warning('清除商品缓存失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取在线用户数
     *
     * @return int
     */
    private function getOnlineUserCount(): int
    {
        try {
            // 从gdb0101数据库的pc表中获取在线用户数
            // play_flag = 0 表示离线，非0表示在线
            $onlineCount = Db::connect('gdb0101')
                ->table('pc')
                ->where('play_flag', '<>', 0)
                ->count();

            return (int)$onlineCount;

        } catch (\Exception $e) {
            Log::error('获取在线用户数失败: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * 获取今日销售额
     *
     * @return int
     */
    private function getTodaySales(): int
    {
        try {
            // 计算今日已完成订单的总销售额
            $today = date('Y-m-d');
            $salesResult = Db::query("SELECT SUM(total_price) as total FROM purchase_orders WHERE status = 3 AND delete_time IS NULL AND DATE(create_time) = ?", [$today]);
            $todaySales = $salesResult[0]['total'] ?? 0;

            return (int)$todaySales;

        } catch (\Exception $e) {
            Log::error('获取今日销售额失败: ' . $e->getMessage());
            return 0;
        }
    }
}
