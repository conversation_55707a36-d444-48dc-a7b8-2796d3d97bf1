<?php

namespace app\service;

use think\facade\Db;
use think\facade\Log;
use app\service\PerformanceMonitorService;

/**
 * 事务优化服务
 * 优化数据库事务处理，确保数据一致性和性能
 */
class TransactionOptimizationService
{
    /**
     * 事务配置
     */
    private static $config = [
        'max_retry_count' => 3,
        'retry_delay' => 100, // 毫秒
        'deadlock_retry_delay' => 200, // 毫秒
        'transaction_timeout' => 30, // 秒
    ];

    /**
     * 事务统计
     */
    private static $stats = [
        'total_transactions' => 0,
        'successful_transactions' => 0,
        'failed_transactions' => 0,
        'retried_transactions' => 0,
        'deadlock_retries' => 0,
    ];

    /**
     * 执行优化的事务
     * 
     * @param callable $callback 事务回调函数
     * @param string $connection 数据库连接
     * @param array $options 选项
     * @return mixed
     * @throws \Exception
     */
    public static function executeTransaction(callable $callback, string $connection = 'mysql', array $options = [])
    {
        $monitorId = PerformanceMonitorService::start('transaction_execution');
        $retryCount = 0;
        $maxRetries = $options['max_retries'] ?? self::$config['max_retry_count'];
        
        self::$stats['total_transactions']++;

        while ($retryCount <= $maxRetries) {
            try {
                $result = Db::connect($connection)->transaction(function() use ($callback, $monitorId) {
                    // 设置事务超时
                    Db::execute('SET SESSION innodb_lock_wait_timeout = ' . self::$config['transaction_timeout']);
                    
                    // 执行事务回调
                    $result = $callback();
                    
                    // 记录事务执行信息
                    PerformanceMonitorService::recordDbQuery($monitorId, 0, 'TRANSACTION_COMMIT');
                    
                    return $result;
                });

                self::$stats['successful_transactions']++;
                PerformanceMonitorService::end($monitorId, [
                    'transaction_result' => 'success',
                    'retry_count' => $retryCount
                ]);

                return $result;

            } catch (\Exception $e) {
                $retryCount++;
                
                // 检查是否是死锁错误
                if (self::isDeadlockError($e)) {
                    self::$stats['deadlock_retries']++;
                    
                    if ($retryCount <= $maxRetries) {
                        Log::warning('检测到死锁，准备重试', [
                            'retry_count' => $retryCount,
                            'max_retries' => $maxRetries,
                            'error' => $e->getMessage()
                        ]);
                        
                        // 死锁重试延迟
                        usleep(self::$config['deadlock_retry_delay'] * 1000 * $retryCount);
                        continue;
                    }
                }
                
                // 检查是否是可重试的错误
                if (self::isRetryableError($e) && $retryCount <= $maxRetries) {
                    self::$stats['retried_transactions']++;
                    
                    Log::warning('事务执行失败，准备重试', [
                        'retry_count' => $retryCount,
                        'max_retries' => $maxRetries,
                        'error' => $e->getMessage()
                    ]);
                    
                    // 普通重试延迟
                    usleep(self::$config['retry_delay'] * 1000 * $retryCount);
                    continue;
                }
                
                // 不可重试的错误或超过最大重试次数
                self::$stats['failed_transactions']++;
                
                PerformanceMonitorService::end($monitorId, [
                    'transaction_result' => 'failed',
                    'retry_count' => $retryCount,
                    'error' => $e->getMessage()
                ]);
                
                Log::error('事务执行最终失败', [
                    'retry_count' => $retryCount,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                
                throw $e;
            }
        }
    }

    /**
     * 批量事务处理
     * 
     * @param array $transactions 事务数组
     * @param string $connection 数据库连接
     * @return array
     */
    public static function batchTransactions(array $transactions, string $connection = 'mysql'): array
    {
        $monitorId = PerformanceMonitorService::start('batch_transactions');
        $results = [];
        $errors = [];

        try {
            foreach ($transactions as $key => $transaction) {
                try {
                    $results[$key] = self::executeTransaction(
                        $transaction['callback'],
                        $transaction['connection'] ?? $connection,
                        $transaction['options'] ?? []
                    );
                } catch (\Exception $e) {
                    $errors[$key] = $e->getMessage();
                    
                    // 根据配置决定是否继续执行其他事务
                    if ($transaction['stop_on_error'] ?? false) {
                        break;
                    }
                }
            }

            PerformanceMonitorService::end($monitorId, [
                'total_transactions' => count($transactions),
                'successful_transactions' => count($results),
                'failed_transactions' => count($errors)
            ]);

            return [
                'results' => $results,
                'errors' => $errors,
                'success_count' => count($results),
                'error_count' => count($errors)
            ];

        } catch (\Exception $e) {
            PerformanceMonitorService::end($monitorId, ['error' => $e->getMessage()]);
            throw $e;
        }
    }

    /**
     * 购买流程专用事务
     * 
     * @param string $userId 用户ID
     * @param int $itemId 商品ID
     * @param int $quantity 数量
     * @param string $currencyType 货币类型
     * @param int $totalCost 总费用
     * @return array
     */
    public static function executePurchaseTransaction(
        string $userId, 
        int $itemId, 
        int $quantity, 
        string $currencyType, 
        int $totalCost
    ): array {
        return self::executeTransaction(function() use ($userId, $itemId, $quantity, $currencyType, $totalCost) {
            // 1. 锁定用户余额记录
            $userBalance = Db::table('users')
                ->where('user_id', $userId)
                ->lock(true)
                ->find();

            if (!$userBalance) {
                throw new \Exception('用户不存在');
            }

            // 2. 检查余额
            $currentBalance = $userBalance[$currencyType] ?? 0;
            if ($currentBalance < $totalCost) {
                throw new \Exception('余额不足');
            }

            // 3. 扣除余额
            $newBalance = $currentBalance - $totalCost;
            Db::table('users')
                ->where('user_id', $userId)
                ->update([
                    $currencyType => $newBalance,
                    'update_time' => date('Y-m-d H:i:s')
                ]);

            // 4. 创建订单记录
            $orderNo = 'PO' . date('YmdHis') . mt_rand(1000, 9999);
            $orderId = Db::table('purchase_orders')->insertGetId([
                'order_no' => $orderNo,
                'user_id' => $userId,
                'item_id' => $itemId,
                'quantity' => $quantity,
                'currency_type' => $currencyType,
                'unit_price' => $totalCost / $quantity,
                'total_amount' => $totalCost,
                'status' => 1, // 待处理
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ]);

            // 5. 记录余额变动
            Db::table('balance_logs')->insert([
                'user_id' => $userId,
                'currency_type' => $currencyType,
                'amount' => -$totalCost,
                'balance_before' => $currentBalance,
                'balance_after' => $newBalance,
                'type' => 'purchase',
                'related_id' => $orderId,
                'description' => "购买商品 #{$itemId} x{$quantity}",
                'create_time' => date('Y-m-d H:i:s')
            ]);

            return [
                'order_id' => $orderId,
                'order_no' => $orderNo,
                'balance_before' => $currentBalance,
                'balance_after' => $newBalance,
                'amount_deducted' => $totalCost
            ];
        });
    }

    /**
     * 检查是否是死锁错误
     * 
     * @param \Exception $e 异常
     * @return bool
     */
    private static function isDeadlockError(\Exception $e): bool
    {
        $message = $e->getMessage();
        return strpos($message, 'Deadlock found') !== false ||
               strpos($message, 'Lock wait timeout') !== false ||
               $e->getCode() == 1213 || // ER_LOCK_DEADLOCK
               $e->getCode() == 1205;   // ER_LOCK_WAIT_TIMEOUT
    }

    /**
     * 检查是否是可重试的错误
     * 
     * @param \Exception $e 异常
     * @return bool
     */
    private static function isRetryableError(\Exception $e): bool
    {
        $retryableCodes = [
            1213, // ER_LOCK_DEADLOCK
            1205, // ER_LOCK_WAIT_TIMEOUT
            2006, // CR_SERVER_GONE_ERROR
            2013, // CR_SERVER_LOST
        ];

        return in_array($e->getCode(), $retryableCodes) ||
               strpos($e->getMessage(), 'server has gone away') !== false ||
               strpos($e->getMessage(), 'Lost connection') !== false;
    }

    /**
     * 获取事务统计信息
     * 
     * @return array
     */
    public static function getStats(): array
    {
        $stats = self::$stats;
        
        if ($stats['total_transactions'] > 0) {
            $stats['success_rate'] = round(
                ($stats['successful_transactions'] / $stats['total_transactions']) * 100, 
                2
            );
            $stats['retry_rate'] = round(
                ($stats['retried_transactions'] / $stats['total_transactions']) * 100, 
                2
            );
        } else {
            $stats['success_rate'] = 0;
            $stats['retry_rate'] = 0;
        }

        return $stats;
    }

    /**
     * 重置统计信息
     */
    public static function resetStats(): void
    {
        self::$stats = [
            'total_transactions' => 0,
            'successful_transactions' => 0,
            'failed_transactions' => 0,
            'retried_transactions' => 0,
            'deadlock_retries' => 0,
        ];
    }
}
