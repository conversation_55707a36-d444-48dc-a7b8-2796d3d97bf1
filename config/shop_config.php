<?php
/**
 * 商城全局配置文件
 * 用于统一管理商品分类、货币类型等配置信息
 */

return array (
  'item_categories' => 
  array (
    1 => '高级时装',
    2 => '玛可时装',
    3 => '辅助道具',
    4 => '宠物',
    5 => '坐骑',
    6 => '料理',
    7 => '积分',
    8 => '其他',
  ),
  'currency_types' => 
  array (
    1 => 
    array (
      'name' => '泡点',
      'short_name' => '泡点',
      'symbol' => '',
      'color_class' => 'bg-primary',
    ),
    2 => 
    array (
      'name' => '积分',
      'short_name' => '积分',
      'symbol' => '',
      'color_class' => 'bg-warning',
    ),
    3 => 
    array (
      'name' => 'C币',
      'short_name' => 'C币',
      'symbol' => '',
      'color_class' => 'bg-success',
    ),
  ),
  'item_status' => 
  array (
    1 => 
    array (
      'name' => '上架',
      'color_class' => 'bg-success',
    ),
    2 => 
    array (
      'name' => '下架',
      'color_class' => 'bg-secondary',
    ),
  ),
  'order_status' => 
  array (
    1 => 
    array (
      'name' => '待处理',
      'color_class' => 'bg-warning',
    ),
    2 => 
    array (
      'name' => '处理中',
      'color_class' => 'bg-info',
    ),
    3 => 
    array (
      'name' => '已完成',
      'color_class' => 'bg-success',
    ),
    4 => 
    array (
      'name' => '失败',
      'color_class' => 'bg-danger',
    ),
    5 => 
    array (
      'name' => '已取消',
      'color_class' => 'bg-secondary',
    ),
  ),
  'user_status' => 
  array (
    0 => 
    array (
      'name' => '禁用',
      'color_class' => 'bg-danger',
    ),
    1 => 
    array (
      'name' => '正常',
      'color_class' => 'bg-success',
    ),
  ),
  'purchase_restrictions' => 
  array (
    0 => '不限购',
    1 => '账号限购',
    2 => '每日限购',
    3 => '每周限购',
    4 => '每月限购',
  ),
  'icons' => 
  array (
    'default_item_icon' => '/static/img/default.png',
    'icon_base_path' => '/static/img/',
    'icon_formats' => 
    array (
      0 => '{id}.png',
      1 => 'x{id}.png',
    ),
  ),
  'api' => 
  array (
    'item_icon_endpoint' => '/api/getItemIcon',
    'item_info_endpoint' => '/api/getItemInfo',
  ),
  'pagination' => 
  array (
    'default_page_size' => 10,
    'max_page_size' => 100,
    'page_sizes' => 
    array (
      0 => 10,
      1 => 20,
      2 => 50,
      3 => 100,
    ),
  ),
  'cache' => 
  array (
    'item_list_ttl' => 600,
    'item_info_ttl' => 1800,
    'stats_ttl' => 300,
  ),
);
