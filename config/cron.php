<?php
/**
 * 定时任务配置文件
 * 配置系统中所有的定时任务
 */

return [
    // 定时任务列表
    'tasks' => [
        // 购买订单相关任务
        'item_delivery' => [
            'class' => 'app\cron\ItemDeliveryCron',
            'methods' => [
                'processPendingOrders' => '*/1 * * * *',    // 每分钟执行
                'cleanupTimeoutOrders' => '0 */1 * * *',    // 每小时执行
                'generateDailyReport' => '0 1 * * *',       // 每天凌晨1点执行
                'cleanupOldData' => '0 2 * * 0',           // 每周日凌晨2点执行
                'healthCheck' => '*/10 * * * *',           // 每10分钟执行
            ]
        ],
        
        // 充值订单相关任务
        'recharge_order' => [
            'class' => 'app\cron\RechargeOrderCron',
            'methods' => [
                'cleanupTimeoutOrders' => '*/5 * * * *',    // 每5分钟执行
                'checkPendingOrderStatus' => '*/10 * * * *', // 每10分钟执行
                'generateDailyReport' => '0 2 * * *',       // 每天凌晨2点执行
                'cleanupOldOrders' => '0 3 * * 0',         // 每周日凌晨3点执行
                'healthCheck' => '0 */1 * * *',            // 每小时执行
            ]
        ],
        
        // 系统维护任务
        'system_maintenance' => [
            'class' => 'app\cron\SystemMaintenanceCron',
            'methods' => [
                'clearCache' => '0 4 * * *',               // 每天凌晨4点清理缓存
                'cleanupLogs' => '0 5 * * 0',              // 每周日凌晨5点清理日志
                'backupDatabase' => '0 6 * * *',           // 每天凌晨6点备份数据库
            ]
        ]
    ],
    
    // 任务执行配置
    'execution' => [
        // 最大执行时间（秒）
        'max_execution_time' => 300,
        
        // 内存限制（MB）
        'memory_limit' => 256,
        
        // 是否记录执行日志
        'log_execution' => true,
        
        // 日志级别
        'log_level' => 'info',
        
        // 错误重试次数
        'retry_count' => 3,
        
        // 重试间隔（秒）
        'retry_interval' => 60,
    ],
    
    // 监控配置
    'monitoring' => [
        // 是否启用监控
        'enabled' => true,
        
        // 监控间隔（分钟）
        'check_interval' => 5,
        
        // 任务超时阈值（分钟）
        'timeout_threshold' => 30,
        
        // 失败率阈值（百分比）
        'failure_threshold' => 10,
        
        // 通知方式
        'notification' => [
            'email' => false,
            'log' => true,
            'webhook' => false,
        ]
    ]
];
