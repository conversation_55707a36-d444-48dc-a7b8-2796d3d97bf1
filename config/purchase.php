<?php
/**
 * 购买系统配置文件
 * 包含物品发送系统的所有配置参数
 */

return [
    // 远程游戏数据库配置
    'remote_database' => [
        'hostname' => env('REMOTE_DB_HOST'),
        'database' => env('REMOTE_DB_NAME'),
        'username' => env('REMOTE_DB_USER'),
        'password' => env('REMOTE_DB_PASS'),
        'hostport' => env('REMOTE_DB_PORT'),
        'charset'  => env('REMOTE_DB_CHARSET'),
        'prefix'   => '',
        'debug'    => env('APP_DEBUG', false),
        'deploy'   => 0,
        'rw_separate' => false,
        'master_num' => 1,
        'slave_no' => '',
        'fields_strict' => true,
        'break_reconnect' => false,
        'auto_timestamp' => false,
        'datetime_format' => 'Y-m-d H:i:s',
        'sql_explain' => false,
        'query_cache' => false,
    ],

    // 物品发送配置
    'delivery' => [
        // 最大重试次数
        'max_retry_count' => 3,
        
        // 重试间隔（秒）
        'retry_interval' => 30,
        
        // 批量处理数量
        'batch_size' => 50,
        
        // 发送超时时间（秒）
        'timeout' => 30,
        
        // 是否启用异步发送
        'async_delivery' => true,
        
        // 发送队列名称
        'queue_name' => 'item_delivery',
        
        // 是否记录详细日志
        'detailed_log' => true,
    ],

    // 订单配置
    'order' => [
        // 订单号前缀
        'order_prefix' => 'PO',
        
        // 订单超时时间（分钟）
        'timeout_minutes' => 30,
        
        // 是否允许重复购买
        'allow_duplicate' => false,
        
        // 单次最大购买数量
        'max_quantity_per_order' => 999,
        
        // 单用户每日最大订单数
        'max_orders_per_day' => 100,
    ],

    // 货币类型配置
    'currency' => [
        'types' => [
            'COIN' => [
                'name' => 'C币',
                'field' => 'c_coin',
                'table' => 'user_currency',
                'min_amount' => 1,
                'max_amount' => 999999999,
            ],
            'POINT' => [
                'name' => '泡点',
                'field' => 'point',
                'table' => 'user_currency',
                'min_amount' => 1,
                'max_amount' => 999999999,
            ],
            'SCORE' => [
                'name' => '积分',
                'field' => 'score',
                'table' => 'user_currency',
                'min_amount' => 1,
                'max_amount' => 999999999,
            ],
        ],
    ],

    // 限制类型配置
    'restrictions' => [
        0 => [
            'name' => '不限购',
            'description' => '无购买限制',
        ],
        1 => [
            'name' => '账号限购一次',
            'description' => '每个账号只能购买一次',
        ],
        2 => [
            'name' => '每日限购一次',
            'description' => '每天只能购买一次',
        ],
        3 => [
            'name' => '每周限购一次',
            'description' => '每周只能购买一次',
        ],
        4 => [
            'name' => '每月限购一次',
            'description' => '每月只能购买一次',
        ],
    ],

    // 物品类型映射
    'item_mapping' => [
        // pointshop表字段 => seal_item表字段
        'item' => 'ItemType',
        'io' => 'ItemOp1',
        'ioo' => 'ItemOp2',
    ],

    // 安全配置
    'security' => [
        // 是否启用IP白名单
        'enable_ip_whitelist' => env('PURCHASE_ENABLE_IP_WHITELIST', false),

        // IP白名单（从环境变量读取，逗号分隔）
        'ip_whitelist' => array_filter(explode(',', env('PURCHASE_IP_WHITELIST'))),

        // 是否启用签名验证
        'enable_signature' => env('PURCHASE_ENABLE_SIGNATURE'),

        // 签名密钥
        'signature_key' => env('PURCHASE_SIGNATURE_KEY'),

        // 请求频率限制（每分钟）
        'rate_limit' => env('PURCHASE_RATE_LIMIT'),
    ],

    // 通知配置
    'notification' => [
        // 是否启用邮件通知
        'enable_email' => false,
        
        // 管理员邮箱
        'admin_email' => env('ADMIN_EMAIL'),
        
        // 是否启用短信通知
        'enable_sms' => false,
        
        // 是否启用微信通知
        'enable_wechat' => false,
    ],

    // 缓存配置
    'cache' => [
        // 商品信息缓存时间（秒）
        'item_cache_time' => 3600,
        
        // 用户余额缓存时间（秒）
        'balance_cache_time' => 300,
        
        // 限制检查缓存时间（秒）
        'restriction_cache_time' => 600,
    ],

    // 日志配置
    'log' => [
        // 是否启用操作日志
        'enable_operation_log' => true,
        
        // 是否启用错误日志
        'enable_error_log' => true,
        
        // 日志保留天数
        'log_retention_days' => 30,
        
        // 日志级别
        'log_level' => 'info',
    ],

    // 监控配置
    'monitor' => [
        // 是否启用性能监控
        'enable_performance' => true,
        
        // 是否启用错误监控
        'enable_error_monitor' => true,
        
        // 监控报告间隔（分钟）
        'report_interval' => 60,
    ],


];