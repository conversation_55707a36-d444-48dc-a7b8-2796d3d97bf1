<?php
// 中间件配置
return [
    // 别名或分组
    'alias'    => [
        'auth' => \app\middleware\AuthMiddleware::class,  // 认证中间件别名
        'validation' => \app\middleware\RequestValidationMiddleware::class,  // 请求验证中间件
        'rate_limit' => \app\middleware\RateLimitMiddleware::class,  // 限流中间件
        'performance' => \app\middleware\PerformanceMonitor::class,  // 性能监控中间件
    ],
    // 优先级设置，此数组中的中间件会按照数组中的顺序优先执行
    'priority' => [
        \think\middleware\SessionInit::class,
        \app\middleware\PerformanceMonitor::class,  // 性能监控优先执行
        \app\middleware\RequestValidationMiddleware::class,  // 请求验证优先执行
        \app\middleware\RateLimitMiddleware::class,  // 限流中间件
    ],
];
