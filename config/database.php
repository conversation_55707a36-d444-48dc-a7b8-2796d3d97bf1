<?php

return [
    // 默认使用的数据库连接配置
    'default'         => env('DB_DRIVER', 'mysql'),

    // 自定义时间查询规则
    'time_query_rule' => [],

    // 自动写入时间戳字段
    // true为自动识别类型 false关闭
    // 字符串则明确指定时间字段类型 支持 int timestamp datetime date
    'auto_timestamp'  => true,

    // 时间字段取出后的默认时间格式
    'datetime_format' => 'Y-m-d H:i:s',

    // 时间字段配置 配置格式：create_time,update_time
    'datetime_field'  => '',

    // 数据库连接配置信息
    'connections'     => [
        // 本地数据库连接
        'mysql' => [
            // 数据库类型
            'type'            => env('DB_TYPE', 'mysql'),
            // 服务器地址
            'hostname'        => env('DB_HOST', ''),
            // 数据库名
            'database'        => env('DB_NAME', ''),
            // 用户名
            'username'        => env('DB_USER', ''),
            // 密码
            'password'        => env('DB_PASS', ''),
            // 端口
            'hostport'        => env('DB_PORT', ''),
            // 数据库连接参数
            'params'          => [
                // 启用持久连接
                \PDO::ATTR_PERSISTENT => true,
                // 设置连接超时
                \PDO::ATTR_TIMEOUT => 30,
                // 启用预处理语句模拟
                \PDO::ATTR_EMULATE_PREPARES => false,
                // 设置错误模式
                \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
                // 设置默认获取模式
                \PDO::ATTR_DEFAULT_FETCH_MODE => \PDO::FETCH_ASSOC,
            ],
            // 数据库编码
            'charset'         => env('DB_CHARSET', 'utf8'),
            // 数据库表前缀
            'prefix'          => env('DB_PREFIX', ''),

            // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
            'deploy'          => 0,
            // 数据库读写是否分离 主从式有效
            'rw_separate'     => false,
            // 读写分离后 主服务器数量
            'master_num'      => 1,
            // 指定从服务器序号
            'slave_no'        => '',
            // 是否严格检查字段是否存在
            'fields_strict'   => true,
            // 是否需要断线重连
            'break_reconnect' => false,
            // 监听SQL
            'trigger_sql'     => env('APP_DEBUG', true),
            // 开启字段缓存
            'fields_cache'    => true,
        ],

        // 本地数据库连接（无前缀）
        'mysql_no_prefix' => [
            // 数据库类型
            'type'            => env('DB_TYPE', 'mysql'),
            // 服务器地址
            'hostname'        => env('DB_HOST', ''),
            // 数据库名
            'database'        => env('DB_NAME', ''),
            // 用户名
            'username'        => env('DB_USER', ''),
            // 密码
            'password'        => env('DB_PASS', ''),
            // 端口
            'hostport'        => env('DB_PORT', ''),
            // 数据库连接参数
            'params'          => [
                // 启用持久连接
                \PDO::ATTR_PERSISTENT => true,
                // 设置连接超时
                \PDO::ATTR_TIMEOUT => 30,
                // 启用预处理语句模拟
                \PDO::ATTR_EMULATE_PREPARES => false,
                // 设置错误模式
                \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
                // 设置默认获取模式
                \PDO::ATTR_DEFAULT_FETCH_MODE => \PDO::FETCH_ASSOC,
            ],
            // 数据库编码
            'charset'         => env('DB_CHARSET', 'utf8'),
            // 数据库表前缀（无前缀）
            'prefix'          => '',

            // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
            'deploy'          => 0,
            // 数据库读写是否分离 主从式有效
            'rw_separate'     => false,
            // 读写分离后 主服务器数量
            'master_num'      => 1,
            // 指定从服务器序号
            'slave_no'        => '',
            // 是否严格检查字段是否存在
            'fields_strict'   => true,
            // 是否需要断线重连
            'break_reconnect' => false,
            // 监听SQL
            'trigger_sql'     => env('APP_DEBUG', true),
            // 开启字段缓存
            'fields_cache'    => true,
        ],

        // 远程数据库连接配置 - gdb0101
        'gdb0101' => [
            'type'            => env('DB_TYPE', 'mysql'),
            'hostname'        => env('REMOTE_DB_HOST', ''),
            'database'        => env('GDB0101_DB_NAME', ''),
            'username'        => env('REMOTE_DB_USER', ''),
            'password'        => env('REMOTE_DB_PASS', ''),
            'hostport'        => env('REMOTE_DB_PORT', ''),
            'charset'         => env('REMOTE_DB_CHARSET', 'gbk'),
            'prefix'          => env('DB_PREFIX', ''),
            'debug'           => env('APP_DEBUG', true),
            'deploy'          => 0,
            'rw_separate'     => false,
            'master_num'      => 1,
            'slave_no'        => '',
            'fields_strict'   => true,
            'break_reconnect' => false,
            // 数据库连接参数
            'params'          => [
                // 设置连接字符集为GBK以匹配数据库实际编码
                \PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES gbk",
                // 设置错误模式
                \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
                // 设置默认获取模式
                \PDO::ATTR_DEFAULT_FETCH_MODE => \PDO::FETCH_ASSOC,
            ],
        ],
        
        // 远程数据库连接配置 - item
        'item' => [
            'type'            => env('DB_TYPE', 'mysql'),
            'hostname'        => env('REMOTE_DB_HOST', ''),
            'database'        => env('ITEM_DB_NAME', ''),
            'username'        => env('REMOTE_DB_USER', ''),
            'password'        => env('REMOTE_DB_PASS', ''),
            'hostport'        => env('REMOTE_DB_PORT', ''),
            'charset'         => env('REMOTE_DB_CHARSET', 'utf8'),
            'prefix'          => env('DB_PREFIX', ''),
            'debug'           => env('APP_DEBUG', true),
            'deploy'          => 0,
            'rw_separate'     => false,
            'master_num'      => 1,
            'slave_no'        => '',
            'fields_strict'   => true,
            'break_reconnect' => false,
            // 启用字段缓存以避免重复的SHOW FULL COLUMNS查询
            'fields_cache'    => true,
            // 数据库连接参数
            'params'          => [
                // 启用持久连接
                \PDO::ATTR_PERSISTENT => true,
                // 设置连接超时
                \PDO::ATTR_TIMEOUT => 10,
                // 启用预处理语句模拟
                \PDO::ATTR_EMULATE_PREPARES => false,
                // 设置错误模式
                \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
                // 设置默认获取模式
                \PDO::ATTR_DEFAULT_FETCH_MODE => \PDO::FETCH_ASSOC,
            ],
        ],
        
        // 远程数据库连接配置 - seal_member
        'seal_member' => [
            'type'            => env('DB_TYPE', 'mysql'),
            'hostname'        => env('REMOTE_DB_HOST', ''),
            'database'        => env('SEAL_MEMBER_DB_NAME', ''),
            'username'        => env('REMOTE_DB_USER', ''),
            'password'        => env('REMOTE_DB_PASS', ''),
            'hostport'        => env('REMOTE_DB_PORT', ''),
            'charset'         => env('REMOTE_DB_CHARSET', 'utf8'),
            'prefix'          => env('DB_PREFIX', ''),
            'debug'           => env('APP_DEBUG', true),
            'deploy'          => 0,
            'rw_separate'     => false,
            'master_num'      => 1,
            'slave_no'        => '',
            'fields_strict'   => true,
            'break_reconnect' => false,
            // 启用字段缓存以避免重复的SHOW FULL COLUMNS查询
            'fields_cache'    => true,
            // 数据库连接参数
            'params'          => [
                // 启用持久连接
                \PDO::ATTR_PERSISTENT => true,
                // 设置连接超时
                \PDO::ATTR_TIMEOUT => 10,
                // 启用预处理语句模拟
                \PDO::ATTR_EMULATE_PREPARES => false,
                // 设置错误模式
                \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
                // 设置默认获取模式
                \PDO::ATTR_DEFAULT_FETCH_MODE => \PDO::FETCH_ASSOC,
            ],
        ],
        
        // 远程数据库连接配置 - seal_web
        'seal_web' => [
            'type'            => env('DB_TYPE', 'mysql'),
            'hostname'        => env('REMOTE_DB_HOST', ''),
            'database'        => env('SEAL_WEB_DB_NAME', ''),
            'username'        => env('REMOTE_DB_USER', ''),
            'password'        => env('REMOTE_DB_PASS', ''),
            'hostport'        => env('REMOTE_DB_PORT', ''),
            'charset'         => env('REMOTE_DB_CHARSET', 'utf8'),
            'prefix'          => env('DB_PREFIX', ''),
            'debug'           => env('APP_DEBUG', true),
            'deploy'          => 0,
            'rw_separate'     => false,
            'master_num'      => 1,
            'slave_no'        => '',
            'fields_strict'   => true,
            'break_reconnect' => false,
        ],
        
        // 远程数据库连接配置 - sealonline_item_mall
        'sealonline_item_mall' => [
            'type'            => env('DB_TYPE', 'mysql'),
            'hostname'        => env('REMOTE_DB_HOST', ''),
            'database'        => env('SEALONLINE_ITEM_MALL_DB_NAME', ''),
            'username'        => env('REMOTE_DB_USER', ''),
            'password'        => env('REMOTE_DB_PASS', ''),
            'hostport'        => env('REMOTE_DB_PORT', ''),
            'charset'         => env('REMOTE_DB_CHARSET', 'utf8'),
            'prefix'          => env('DB_PREFIX', ''),
            'debug'           => env('APP_DEBUG', true),
            'deploy'          => 0,
            'rw_separate'     => false,
            'master_num'      => 1,
            'slave_no'        => '',
            'fields_strict'   => true,
            'break_reconnect' => false,
        ],

        // 更多的数据库配置信息
    ],
];
