<?php
/**
 * API配置文件
 * 管理所有外部API调用的配置
 */

return [
    // API服务器基础配置
    'server' => [
        'protocol' => env('API_SERVER_PROTOCOL', 'http'),
        'host' => env('API_SERVER_HOST', '127.0.0.1'),
        'port' => env('API_SERVER_PORT', '80'),
    ],
    
    // 静态资源配置
    'static' => [
        'protocol' => env('STATIC_SERVER_PROTOCOL', 'http'),
        'host' => env('STATIC_SERVER_HOST', env('API_SERVER_HOST', '127.0.0.1')),
        'port' => env('STATIC_SERVER_PORT', env('API_SERVER_PORT', '80')),
        'base_path' => env('STATIC_BASE_PATH', '/static'),
    ],
    
    // API端点配置
    'endpoints' => [
        // 获取物品图标API
        'item_icon' => '/get_item_icon.php',
        // 获取物品信息API
        'item_info' => '/api/getItemInfo',
    ],
    
    // API调用配置
    'request' => [
        // 超时时间（秒）
        'timeout' => 10,
        // 连接超时时间（秒）
        'connect_timeout' => 5,
        // 重试次数
        'retry_count' => 2,
        // 重试间隔（秒）
        'retry_interval' => 1,
    ],
    
    // 缓存配置
    'cache' => [
        // 成功响应缓存时间（秒）
        'success_ttl' => 1800, // 30分钟
        // 失败响应缓存时间（秒）
        'failure_ttl' => 300,  // 5分钟
        // 缓存键前缀
        'prefix' => 'api:',
    ],
];