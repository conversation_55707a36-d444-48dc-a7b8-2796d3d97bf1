# 商城系统定时任务配置示例
# 请根据实际情况修改路径和时间间隔

# 每5分钟清理超时的充值订单
0,5,10,15,20,25,30,35,40,45,50,55 * * * * /usr/bin/php /www/wwwroot/shop-new/cron.php cleanup_timeout_orders >> /www/wwwroot/shop-new/runtime/log/cron.log 2>&1

# 每10分钟检查待支付订单状态
0,10,20,30,40,50 * * * * /usr/bin/php /www/wwwroot/shop-new/cron.php check_pending_orders >> /www/wwwroot/shop-new/runtime/log/cron.log 2>&1

# 每小时进行健康检查
0 */1 * * * /usr/bin/php /www/wwwroot/shop-new/cron.php health_check >> /www/wwwroot/shop-new/runtime/log/cron.log 2>&1

# 每天凌晨2点生成日报
0 2 * * * /usr/bin/php /www/wwwroot/shop-new/cron.php generate_daily_report >> /www/wwwroot/shop-new/runtime/log/cron.log 2>&1

# 每周日凌晨3点清理旧订单数据
0 3 * * 0 /usr/bin/php /www/wwwroot/shop-new/cron.php cleanup_old_orders >> /www/wwwroot/shop-new/runtime/log/cron.log 2>&1

# 安装说明：
# 1. 复制此文件为 crontab.conf
# 2. 修改路径为实际的项目路径
# 3. 执行命令：crontab crontab.conf
# 4. 查看已安装的定时任务：crontab -l
# 5. 编辑定时任务：crontab -e

# 时间格式说明：
# 分钟(0-59) 小时(0-23) 日期(1-31) 月份(1-12) 星期(0-7，0和7都表示星期日)

# 常用时间表达式：
# 0,5,10,15,20,25,30,35,40,45,50,55 * * * *    每5分钟执行一次
# 0 * * * *      每小时执行一次
# 0 2 * * *      每天凌晨2点执行
# 0 2 * * 0      每周日凌晨2点执行
# 0 2 1 * *      每月1号凌晨2点执行
