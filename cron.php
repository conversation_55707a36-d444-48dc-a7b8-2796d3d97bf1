<?php
/**
 * 定时任务执行器
 * 用于执行系统的定时任务
 * 
 * 使用方法：
 * 1. 通过crontab设置定时执行：
 *    # 每5分钟清理超时订单
 *    0,5,10,15,20,25,30,35,40,45,50,55 * * * * /usr/bin/php /www/wwwroot/shop-new/cron.php cleanup_timeout_orders
 *
 * 2. 手动执行特定任务：
 *    php cron.php cleanup_timeout_orders
 *
 * 3. 执行所有任务：
 *    php cron.php all
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 定义应用根目录
define('ROOT_PATH', __DIR__);

// 引入ThinkPHP框架
require_once ROOT_PATH . '/vendor/autoload.php';

use think\facade\Db;
use think\facade\Log;
use think\facade\Config;
use app\cron\RechargeOrderCron;

// 初始化应用
$app = new think\App();
$app->initialize();

/**
 * 定时任务配置
 */
$cronTasks = [
    'cleanup_timeout_orders' => [
        'class' => RechargeOrderCron::class,
        'method' => 'cleanupTimeoutOrders',
        'description' => '清理超时的充值订单'
    ],
    'check_pending_orders' => [
        'class' => RechargeOrderCron::class,
        'method' => 'checkPendingOrderStatus',
        'description' => '检查待支付订单状态'
    ],
    'generate_daily_report' => [
        'class' => RechargeOrderCron::class,
        'method' => 'generateDailyReport',
        'description' => '生成充值每日统计报告'
    ],
    'cleanup_old_orders' => [
        'class' => RechargeOrderCron::class,
        'method' => 'cleanupOldOrders',
        'description' => '清理旧的充值订单数据'
    ],
    'health_check' => [
        'class' => RechargeOrderCron::class,
        'method' => 'healthCheck',
        'description' => '充值订单健康检查'
    ]
];

/**
 * 执行定时任务
 * 
 * @param string $taskName 任务名称
 * @return void
 */
function executeCronTask($taskName) {
    global $cronTasks;
    
    if (!isset($cronTasks[$taskName])) {
        echo "错误: 未找到任务 '{$taskName}'\n";
        echo "可用任务:\n";
        foreach ($cronTasks as $name => $config) {
            echo "  {$name} - {$config['description']}\n";
        }
        return;
    }
    
    $task = $cronTasks[$taskName];
    $startTime = microtime(true);
    
    echo "[" . date('Y-m-d H:i:s') . "] 开始执行任务: {$taskName}\n";
    echo "描述: {$task['description']}\n";
    
    try {
        // 创建任务实例
        $taskInstance = new $task['class']();
        
        // 执行任务方法
        $method = $task['method'];
        if (!method_exists($taskInstance, $method)) {
            throw new Exception("方法 {$method} 不存在");
        }
        
        $taskInstance->$method();
        
        $endTime = microtime(true);
        $executionTime = round($endTime - $startTime, 2);
        
        echo "[" . date('Y-m-d H:i:s') . "] 任务执行完成: {$taskName}\n";
        echo "执行时间: {$executionTime} 秒\n";
        echo "状态: 成功\n\n";
        
        // 记录执行日志
        Log::info("定时任务执行成功: {$taskName}", [
            'task' => $taskName,
            'execution_time' => $executionTime,
            'status' => 'success'
        ]);
        
    } catch (Exception $e) {
        $endTime = microtime(true);
        $executionTime = round($endTime - $startTime, 2);
        
        echo "[" . date('Y-m-d H:i:s') . "] 任务执行失败: {$taskName}\n";
        echo "错误信息: " . $e->getMessage() . "\n";
        echo "执行时间: {$executionTime} 秒\n";
        echo "状态: 失败\n\n";
        
        // 记录错误日志
        Log::error("定时任务执行失败: {$taskName}", [
            'task' => $taskName,
            'error' => $e->getMessage(),
            'execution_time' => $executionTime,
            'status' => 'failed'
        ]);
    }
}

/**
 * 执行所有任务
 * 
 * @return void
 */
function executeAllTasks() {
    global $cronTasks;
    
    echo "=== 开始执行所有定时任务 ===\n";
    echo "时间: " . date('Y-m-d H:i:s') . "\n";
    echo "任务数量: " . count($cronTasks) . "\n\n";
    
    foreach ($cronTasks as $taskName => $config) {
        executeCronTask($taskName);
    }
    
    echo "=== 所有定时任务执行完成 ===\n";
}

/**
 * 显示帮助信息
 * 
 * @return void
 */
function showHelp() {
    global $cronTasks;
    
    echo "定时任务执行器\n";
    echo "使用方法: php cron.php [任务名称]\n\n";
    echo "可用任务:\n";
    foreach ($cronTasks as $name => $config) {
        echo "  {$name}\n";
        echo "    描述: {$config['description']}\n";
        echo "    类: {$config['class']}\n";
        echo "    方法: {$config['method']}\n\n";
    }
    echo "特殊命令:\n";
    echo "  all - 执行所有任务\n";
    echo "  help - 显示此帮助信息\n\n";
    echo "示例:\n";
    echo "  php cron.php cleanup_timeout_orders\n";
    echo "  php cron.php all\n";
}

/**
 * 检查系统状态
 * 
 * @return void
 */
function checkSystemStatus() {
    echo "=== 系统状态检查 ===\n";
    echo "时间: " . date('Y-m-d H:i:s') . "\n";
    echo "PHP版本: " . PHP_VERSION . "\n";
    echo "内存使用: " . round(memory_get_usage() / 1024 / 1024, 2) . " MB\n";
    echo "内存峰值: " . round(memory_get_peak_usage() / 1024 / 1024, 2) . " MB\n";
    
    try {
        // 检查数据库连接
        $db = Db::connect();
        $result = $db->query('SELECT 1');
        echo "数据库连接: 正常\n";
        
        // 检查待支付订单数量
        $pendingCount = Db::table('recharge_orders')
            ->where('status', 0)
            ->count();
        echo "待支付订单: {$pendingCount} 个\n";
        
        // 检查超时订单数量
        $timeoutMinutes = Config::get('purchase.order.timeout_minutes', 30);
        $timeoutTime = date('Y-m-d H:i:s', time() - $timeoutMinutes * 60);
        
        $timeoutCount = Db::table('recharge_orders')
            ->where('status', 0)
            ->where('create_time', '<', $timeoutTime)
            ->count();
        echo "超时订单: {$timeoutCount} 个\n";
        
    } catch (Exception $e) {
        echo "数据库连接: 失败 - " . $e->getMessage() . "\n";
    }
    
    echo "===================\n\n";
}

// 主程序入口
if ($argc < 2) {
    showHelp();
    exit(1);
}

$command = $argv[1];

switch ($command) {
    case 'all':
        checkSystemStatus();
        executeAllTasks();
        break;
        
    case 'help':
    case '--help':
    case '-h':
        showHelp();
        break;
        
    case 'status':
        checkSystemStatus();
        break;
        
    default:
        checkSystemStatus();
        executeCronTask($command);
        break;
}

echo "程序执行完成\n";
