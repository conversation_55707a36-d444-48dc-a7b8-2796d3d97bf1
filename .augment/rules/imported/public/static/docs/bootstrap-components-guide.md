---
type: "manual"
---

# Bootstrap 通用组件使用指南

基于 Bootstrap 5.3 的通用下拉框和分页组件，提供统一的样式和交互体验。

## 快速开始

### 1. 引入文件

```html
<!-- Bootstrap CSS -->
<link href="/static/css/bootstrap.min.css" rel="stylesheet">
<link href="/static/css/bootstrap-icons.min.css" rel="stylesheet">

<!-- 自定义组件 CSS -->
<link href="/static/css/bootstrap-components.css" rel="stylesheet">

<!-- Bootstrap JS -->
<script src="/static/js/bootstrap.bundle.min.js"></script>

<!-- 自定义组件 JS -->
<script src="/static/js/bootstrap-components.js"></script>
```

### 2. 基本使用

#### 下拉框组件

```html
<!-- 自动初始化 -->
<select class="custom-dropdown-init">
    <option value="">请选择...</option>
    <option value="option1">选项1</option>
    <option value="option2">选项2</option>
</select>

<!-- 手动初始化 -->
<select id="my-dropdown">
    <option value="">请选择...</option>
    <option value="option1">选项1</option>
</select>

<script>
const dropdown = new BootstrapComponents.CustomDropdown(
    document.getElementById('my-dropdown'),
    {
        searchable: true,
        placeholder: '请选择选项...'
    }
);
</script>
```

#### 分页组件

```html
<div id="pagination-container"></div>

<script>
const pagination = BootstrapComponents.createPagination('#pagination-container', {
    currentPage: 1,
    totalPages: 10,
    totalItems: 100,
    itemsPerPage: 10
});
</script>
```

## 下拉框组件详细配置

### 配置选项

| 选项 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `searchable` | Boolean | `false` | 是否可搜索 |
| `placeholder` | String | `'请选择...'` | 占位符文本 |
| `emptyText` | String | `'无选项'` | 无选项时显示的文本 |
| `maxHeight` | String | `'200px'` | 下拉菜单最大高度 |
| `size` | String | `''` | 尺寸：`'sm'`, `'lg'` 或空字符串 |
| `value` | String | `null` | 预选值 |

### 方法

| 方法 | 参数 | 返回值 | 描述 |
|------|------|--------|------|
| `setValue(value)` | String | - | 设置选中值 |
| `getValue()` | - | String | 获取当前选中值 |
| `getText()` | - | String | 获取当前选中文本 |
| `open()` | - | - | 打开下拉菜单 |
| `close()` | - | - | 关闭下拉菜单 |
| `destroy()` | - | - | 销毁组件 |

### 事件

| 事件 | 描述 |
|------|------|
| `change` | 选择改变时触发 |
| `dropdown:open` | 下拉菜单打开时触发 |
| `dropdown:close` | 下拉菜单关闭时触发 |

### 样式类

| 类名 | 描述 |
|------|------|
| `.custom-dropdown` | 基础容器类 |
| `.dropdown-sm` | 小尺寸样式 |
| `.dropdown-lg` | 大尺寸样式 |
| `.searchable` | 可搜索样式 |

## 分页组件详细配置

### 配置选项

| 选项 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `currentPage` | Number | `1` | 当前页码 |
| `totalPages` | Number | `1` | 总页数 |
| `totalItems` | Number | `0` | 总条目数 |
| `itemsPerPage` | Number | `10` | 每页显示数量 |
| `maxVisiblePages` | Number | `5` | 最大可见页码数 |
| `showInfo` | Boolean | `true` | 是否显示信息栏 |
| `showSizeSelector` | Boolean | `true` | 是否显示每页数量选择器 |
| `sizeOptions` | Array | `[10, 20, 50, 100]` | 每页数量选项 |
| `size` | String | `''` | 尺寸：`'sm'`, `'lg'` 或空字符串 |
| `style` | String | `''` | 样式：`'simple'`, `'rounded'` 或空字符串 |
| `prevText` | String | `'上一页'` | 上一页按钮文本 |
| `nextText` | String | `'下一页'` | 下一页按钮文本 |
| `infoText` | String | `'显示 {start} 到 {end} 条，共 {total} 条'` | 信息文本模板 |

### 方法

| 方法 | 参数 | 返回值 | 描述 |
|------|------|--------|------|
| `goToPage(page)` | Number | - | 跳转到指定页 |
| `changePageSize(size)` | Number | - | 改变每页显示数量 |
| `update(options)` | Object | - | 更新配置 |
| `getCurrentPage()` | - | Number | 获取当前页码 |
| `getItemsPerPage()` | - | Number | 获取每页显示数量 |
| `addJumpToPage()` | - | - | 添加跳转功能 |

### 事件

| 事件 | 描述 | 事件数据 |
|------|------|----------|
| `pagination:change` | 页码改变时触发 | `{page, itemsPerPage}` |
| `pagination:sizeChange` | 每页数量改变时触发 | `{page, itemsPerPage}` |

### 样式类

| 类名 | 描述 |
|------|------|
| `.custom-pagination` | 基础分页类 |
| `.pagination-sm` | 小尺寸分页 |
| `.pagination-lg` | 大尺寸分页 |
| `.pagination-simple` | 简洁样式分页 |
| `.pagination-rounded` | 圆形样式分页 |

## 高级用法

### 1. 可搜索下拉框

```javascript
const searchableDropdown = new BootstrapComponents.CustomDropdown(element, {
    searchable: true,
    placeholder: '搜索并选择...',
    emptyText: '未找到匹配项'
});
```

### 2. 带跳转功能的分页

```javascript
const pagination = BootstrapComponents.createPagination('#container', {
    currentPage: 1,
    totalPages: 20,
    totalItems: 200
});

// 添加跳转功能
pagination.addJumpToPage();
```

### 3. 事件监听

```javascript
// 下拉框事件
document.getElementById('my-select').addEventListener('change', function(e) {
    console.log('选择了:', e.target.value);
});

// 分页事件
document.getElementById('pagination').addEventListener('pagination:change', function(e) {
    console.log('跳转到第', e.detail.page, '页');
    // 在这里加载新数据
    loadData(e.detail.page, e.detail.itemsPerPage);
});
```

### 4. 动态更新

```javascript
// 更新分页数据
pagination.update({
    totalPages: 15,
    totalItems: 150,
    currentPage: 1
});

// 设置下拉框值
dropdown.setValue('new-value');
```

## 样式自定义

### CSS 变量

```css
:root {
    --bs-primary: #0d6efd;
    --bs-secondary: #6c757d;
    --bs-border-color: #dee2e6;
    --bs-border-radius: 0.375rem;
}
```

### 自定义样式

```css
/* 自定义下拉框样式 */
.custom-dropdown.my-theme .dropdown-toggle {
    border-color: #28a745;
    background-color: #f8f9fa;
}

.custom-dropdown.my-theme .dropdown-toggle:focus {
    border-color: #28a745;
    box-shadow: 0 0 0 0.25rem rgba(40, 167, 69, 0.25);
}

/* 自定义分页样式 */
.custom-pagination.my-theme .page-link {
    color: #28a745;
    border-color: #28a745;
}

.custom-pagination.my-theme .page-item.active .page-link {
    background-color: #28a745;
    border-color: #28a745;
}
```

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- IE 11（部分功能）

## 注意事项

1. 确保 Bootstrap 5.3 已正确加载
2. 组件依赖现代浏览器的 ES6+ 特性
3. 在动态内容中使用时，需要手动初始化组件
4. 移动端使用时建议测试触摸交互
5. 大量数据时建议使用虚拟滚动优化性能

## 常见问题

### Q: 下拉框在模态框中显示异常？
A: 设置模态框的 z-index 或使用 `appendTo` 选项。

### Q: 分页组件如何与后端 API 集成？
A: 监听 `pagination:change` 事件，在事件处理函数中调用 API。

### Q: 如何实现多选下拉框？
A: 在 select 元素上添加 `multiple` 属性，组件会自动适配。

### Q: 样式与现有 CSS 冲突怎么办？
A: 使用更具体的选择器或 `!important` 声明覆盖样式。
