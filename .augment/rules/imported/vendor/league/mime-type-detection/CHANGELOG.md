---
type: "manual"
---

# Changelog

## 1.16.0 - 2025-09-21

- Updated lookup
- Prepped for 8.4 implicit nullable deprecation

## 1.15.0 - 2024-01-28

- Updated lookup

## 1.14.0 - 2022-10-17

### Updated

- Updated lookup

## 1.13.0 - 2023-08-05

### Added

- A reverse lookup mechanism to fetch one or all extensions for a given mimetype

## 1.12.0 - 2023-08-03

### Updated

- Updated lookup

## 1.11.0 - 2023-04-17

### Updated

- Updated lookup

## 1.10.0 - 2022-04-11

### Fixed

- Added Flysystem v1 inconclusive mime-types and made it configurable as a constructor parameter.

## 1.9.0 - 2021-11-21

### Updated

- Updated lookup

## 1.8.0 - 2021-09-25

### Added

- Added the decorator `OverridingExtensionToMimeTypeMap` which allows you to override values.

## 1.7.0 - 2021-01-18

### Added

- Added a `bufferSampleSize` parameter to the `FinfoMimeTypeDetector` class that allows you to send a reduced content sample which costs less memory.

## 1.6.0 - 2021-01-18

### Changes

- Updated generated mime-type map
