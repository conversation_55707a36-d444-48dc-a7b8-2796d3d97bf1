{extend name="layout/base"}

{block name="title"}设置昵称 - 游戏商城{/block}

{block name="head"}
<link rel="stylesheet" href="/static/css/auth.css">
<style>
.nickname-container {
    max-width: 500px;
    margin: 0 auto;
    padding: 2rem 1rem;
}

.nickname-header {
    text-align: center;
    margin-bottom: 2rem;
}

.nickname-title {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.nickname-subtitle {
    color: #6c757d;
    font-size: 1rem;
}

.nickname-form {
    background: #fff;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
}

.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.btn-primary {
    background: linear-gradient(135deg, #0d6efd 0%, #0b5ed7 100%);
    border: none;
    border-radius: 8px;
    padding: 0.75rem 2rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(13, 110, 253, 0.3);
}

.nickname-tips {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1.5rem;
    border-left: 4px solid #0d6efd;
}

.nickname-tips h6 {
    color: #0d6efd;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.nickname-tips ul {
    margin: 0;
    padding-left: 1.2rem;
}

.nickname-tips li {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.alert {
    border-radius: 8px;
    border: none;
    padding: 1rem;
    margin-bottom: 1rem;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

@media (max-width: 576px) {
    .nickname-container {
        padding: 1rem 0.5rem;
    }
    
    .nickname-form {
        padding: 1.5rem;
    }
}
</style>
{/block}

{block name="content"}
<div class="nickname-container">
    <div class="nickname-header">
        <h1 class="nickname-title">
            <i class="bi bi-person-badge me-2"></i>设置昵称
        </h1>
        <p class="nickname-subtitle">为了更好的游戏体验，请设置您的游戏昵称</p>
    </div>

    <div class="nickname-form">
        <div id="error-message" class="alert alert-danger d-none" role="alert">
            <i class="bi bi-exclamation-triangle-fill me-2"></i>
            <span class="error-text"></span>
        </div>

        <div id="success-message" class="alert alert-success d-none" role="alert">
            <i class="bi bi-check-circle-fill me-2"></i>
            <span class="success-text"></span>
        </div>

        <div class="nickname-tips">
            <h6><i class="bi bi-info-circle me-1"></i>昵称设置规则</h6>
            <ul>
                <li>昵称长度不能超过20个字符</li>
                <li>昵称不能为空</li>
                <li>昵称必须是唯一的，不能与其他玩家重复</li>
            </ul>
        </div>

        <form id="nickname-form" class="needs-validation" novalidate>
            <div class="mb-3">
                <label for="nickname" class="form-label">
                    <i class="bi bi-person-fill me-1"></i>商城昵称
                </label>
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="bi bi-person-badge"></i>
                    </span>
                    <input type="text" id="nickname" name="nickname" class="form-control"
                           placeholder="请输入您的游戏昵称" maxlength="20" required>
                    <div class="invalid-feedback">
                        请输入有效的游戏昵称
                    </div>
                </div>
                <div class="form-text">
                    <small class="text-muted">
                        <span id="char-count">0</span>/20 字符
                    </small>
                </div>
            </div>

            <div class="d-grid mb-3">
                <button type="submit" id="nickname-btn" class="btn btn-primary btn-lg">
                    <span id="btn-text">
                        <i class="bi bi-check-lg me-2"></i>设置昵称
                    </span>
                    <span id="btn-loading" class="d-none">
                        <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                        设置中...
                    </span>
                </button>
            </div>
        </form>

        <div class="text-center">
            <small class="text-muted">
                <i class="bi bi-shield-check me-1"></i>
                设置昵称后将自动跳转到商城页面
            </small>
        </div>
    </div>
</div>
{/block}

{block name="scripts"}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('nickname-form');
    const nicknameInput = document.getElementById('nickname');
    const charCount = document.getElementById('char-count');
    const submitBtn = document.getElementById('nickname-btn');
    const btnText = document.getElementById('btn-text');
    const btnLoading = document.getElementById('btn-loading');
    const errorMessage = document.getElementById('error-message');
    const successMessage = document.getElementById('success-message');

    // 字符计数
    nicknameInput.addEventListener('input', function() {
        const length = this.value.length;
        charCount.textContent = length;
        
        if (length > 20) {
            charCount.style.color = '#dc3545';
        } else if (length > 15) {
            charCount.style.color = '#ffc107';
        } else {
            charCount.style.color = '#6c757d';
        }
    });

    // 表单提交
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const nickname = nicknameInput.value.trim();
        
        // 客户端验证
        if (!nickname) {
            showError('昵称不能为空');
            return;
        }
        
        if (nickname.length > 20) {
            showError('昵称长度不能超过20个字符');
            return;
        }
        
        // 显示加载状态
        setLoading(true);
        hideMessages();
        
        // 提交数据
        fetch('/auth/doSetNickname', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'nickname=' + encodeURIComponent(nickname)
        })
        .then(response => response.json())
        .then(data => {
            setLoading(false);
            
            if (data.code === 200) {
                showSuccess(data.message);
                // 延迟跳转
                setTimeout(() => {
                    window.location.href = data.data.redirect || '/shop';
                }, 1500);
            } else {
                showError(data.message || '设置昵称失败');
            }
        })
        .catch(error => {
            setLoading(false);
            console.error('设置昵称出错:', error);
            showError('网络错误，请稍后重试');
        });
    });

    // 显示错误消息
    function showError(message) {
        errorMessage.querySelector('.error-text').textContent = message;
        errorMessage.classList.remove('d-none');
        successMessage.classList.add('d-none');
    }

    // 显示成功消息
    function showSuccess(message) {
        successMessage.querySelector('.success-text').textContent = message;
        successMessage.classList.remove('d-none');
        errorMessage.classList.add('d-none');
    }

    // 隐藏所有消息
    function hideMessages() {
        errorMessage.classList.add('d-none');
        successMessage.classList.add('d-none');
    }

    // 设置加载状态
    function setLoading(loading) {
        if (loading) {
            submitBtn.disabled = true;
            btnText.classList.add('d-none');
            btnLoading.classList.remove('d-none');
        } else {
            submitBtn.disabled = false;
            btnText.classList.remove('d-none');
            btnLoading.classList.add('d-none');
        }
    }

    // 自动聚焦到昵称输入框
    nicknameInput.focus();
});
</script>
{/block}
