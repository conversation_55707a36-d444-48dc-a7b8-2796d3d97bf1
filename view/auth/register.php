<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户注册 - 游戏商城</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #6f42c1;
            --primary-dark: #5a2d91;
            --secondary-color: #6c757d;
            --success-color: #198754;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #0dcaf0;
            --light-color: #f8f9fa;
            --dark-color: #212529;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .register-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .register-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            max-width: 450px;
            width: 100%;
        }

        .register-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .register-header h1 {
            margin: 0;
            font-size: 1.8rem;
            font-weight: 600;
        }

        .register-header p {
            margin: 0.5rem 0 0 0;
            opacity: 0.9;
            font-size: 0.95rem;
        }

        .register-body {
            padding: 2rem;
        }

        .form-floating {
            margin-bottom: 1rem;
        }

        .form-floating .form-control {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 1rem 0.75rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-floating .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(111, 66, 193, 0.25);
        }

        .form-floating .form-control.is-valid {
            border-color: var(--success-color);
            box-shadow: 0 0 0 0.2rem rgba(25, 135, 84, 0.25);
        }

        .form-floating .form-control.is-invalid {
            border-color: var(--danger-color);
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
        }

        .form-floating label {
            color: var(--secondary-color);
            font-weight: 500;
        }

        .btn-register {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            border: none;
            border-radius: 12px;
            padding: 0.875rem 2rem;
            font-size: 1.1rem;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
            margin-top: 1rem;
        }

        .btn-register:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(111, 66, 193, 0.3);
            color: white;
        }

        .btn-register:disabled {
            opacity: 0.6;
            transform: none;
            box-shadow: none;
        }

        .login-link {
            text-align: center;
            margin-top: 1.5rem;
            padding-top: 1.5rem;
            border-top: 1px solid #e9ecef;
        }

        .login-link a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .login-link a:hover {
            color: var(--primary-dark);
        }

        .alert {
            border-radius: 12px;
            border: none;
            margin-bottom: 1rem;
        }

        .loading-spinner {
            display: none;
            margin-right: 0.5rem;
        }

        .password-strength {
            margin-top: 0.5rem;
            font-size: 0.875rem;
        }

        .strength-bar {
            height: 4px;
            border-radius: 2px;
            background: #e9ecef;
            margin-top: 0.25rem;
            overflow: hidden;
        }

        .strength-fill {
            height: 100%;
            transition: all 0.3s ease;
            border-radius: 2px;
        }

        .strength-weak { background: var(--danger-color); width: 25%; }
        .strength-fair { background: var(--warning-color); width: 50%; }
        .strength-good { background: var(--info-color); width: 75%; }
        .strength-strong { background: var(--success-color); width: 100%; }

        .username-feedback {
            margin-top: 0.25rem;
            font-size: 0.875rem;
            min-height: 1.2rem;
        }

        .username-feedback.checking {
            color: var(--info-color);
        }

        .username-feedback.available {
            color: var(--success-color);
        }

        .username-feedback.unavailable {
            color: var(--danger-color);
        }

        .username-feedback .text-muted {
            color: #6c757d !important;
            font-size: 0.8rem;
        }

        /* 响应式设计 */
        @media (max-width: 576px) {
            .register-container {
                padding: 10px;
            }
            
            .register-header {
                padding: 1.5rem;
            }
            
            .register-header h1 {
                font-size: 1.5rem;
            }
            
            .register-body {
                padding: 1.5rem;
            }
        }

        /* 动画效果 */
        .register-card {
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .form-floating {
            animation: fadeIn 0.6s ease-out;
            animation-fill-mode: both;
        }

        .form-floating:nth-child(1) { animation-delay: 0.1s; }
        .form-floating:nth-child(2) { animation-delay: 0.2s; }
        .form-floating:nth-child(3) { animation-delay: 0.3s; }
        .form-floating:nth-child(4) { animation-delay: 0.4s; }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="register-card">
            <div class="register-header">
                <h1><i class="bi bi-person-plus-fill me-2"></i>用户注册</h1>
                <p>创建您的游戏账户，开始精彩旅程</p>
            </div>
            
            <div class="register-body">
                <div id="alertContainer"></div>
                
                <form id="registerForm">
                    <div class="form-floating">
                        <input type="text" class="form-control" id="username" name="username" placeholder="用户名" required
                               title="用户名只能包含字母和数字，长度3-16位，不能包含gm等管理员专用词汇">
                        <label for="username"><i class="bi bi-person me-2"></i>用户名</label>
                        <div class="username-feedback" id="usernameFeedback">
                            <small class="text-muted">用户名只能包含字母和数字，长度3-16位</small>
                        </div>
                    </div>
                    
                    <div class="form-floating">
                        <input type="email" class="form-control" id="email" name="email" placeholder="邮箱地址" required>
                        <label for="email"><i class="bi bi-envelope me-2"></i>邮箱地址</label>
                    </div>
                    
                    <div class="form-floating">
                        <input type="password" class="form-control" id="password" name="password" placeholder="密码" required>
                        <label for="password"><i class="bi bi-lock me-2"></i>密码</label>
                        <div class="password-strength">
                            <div class="strength-text">密码强度: <span id="strengthText">请输入密码</span></div>
                            <div class="strength-bar">
                                <div class="strength-fill" id="strengthFill"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-floating">
                        <input type="password" class="form-control" id="confirmPassword" name="confirmPassword" placeholder="确认密码" required>
                        <label for="confirmPassword"><i class="bi bi-lock-fill me-2"></i>确认密码</label>
                    </div>
                    
                    <button type="submit" class="btn btn-register" id="registerBtn">
                        <span class="loading-spinner spinner-border spinner-border-sm" role="status"></span>
                        <i class="bi bi-person-plus me-2"></i>立即注册
                    </button>
                </form>
                
                <div class="login-link">
                    <p class="mb-0">已有账户？ <a href="/login">立即登录</a></p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 注册表单处理
        document.getElementById('registerForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = Object.fromEntries(formData);
            
            // 表单验证
            if (!validateForm(data)) {
                return;
            }
            
            const registerBtn = document.getElementById('registerBtn');
            const spinner = registerBtn.querySelector('.loading-spinner');
            
            try {
                // 显示加载状态
                registerBtn.disabled = true;
                spinner.style.display = 'inline-block';
                
                const response = await fetch('/auth/doRegister', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (result.code === 200) {
                    showAlert('success', '注册成功！正在跳转到登录页面...');
                    setTimeout(() => {
                        window.location.href = '/login';
                    }, 2000);
                } else {
                    showAlert('danger', result.message || '注册失败，请重试');
                }
                
            } catch (error) {
                console.error('注册请求失败:', error);
                showAlert('danger', '网络错误，请检查网络连接后重试');
            } finally {
                // 恢复按钮状态
                registerBtn.disabled = false;
                spinner.style.display = 'none';
            }
        });
        
        // 表单验证
        function validateForm(data) {
            clearAlerts();
            
            // 用户名验证
            if (!data.username || data.username.length < 3) {
                showAlert('warning', '用户名至少需要3个字符');
                return false;
            }

            if (data.username.length > 16) {
                showAlert('warning', '用户名不能超过16个字符');
                return false;
            }

            if (!/^[a-zA-Z0-9]+$/.test(data.username)) {
                showAlert('warning', '用户名只能包含字母和数字');
                return false;
            }

            // 检查是否包含禁用词
            if (isForbiddenUsername(data.username)) {
                showAlert('warning', '该用户名为管理员专用，无法注册');
                return false;
            }
            
            // 邮箱验证
            if (!data.email || !isValidEmail(data.email)) {
                showAlert('warning', '请输入有效的邮箱地址');
                return false;
            }
            
            // 密码验证
            if (!data.password || data.password.length < 6) {
                showAlert('warning', '密码至少需要6个字符');
                return false;
            }
            
            // 确认密码验证
            if (data.password !== data.confirmPassword) {
                showAlert('warning', '两次输入的密码不一致');
                return false;
            }
            
            return true;
        }
        
        // 邮箱格式验证
        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        // 检查是否为禁用的用户名
        function isForbiddenUsername(username) {
            const lowerUsername = username.toLowerCase();

            // 禁止所有包含"gm"的用户名
            if (lowerUsername.includes('gm')) {
                return true;
            }

            // 禁止其他管理员相关的用户名
            const forbiddenPatterns = [
                'admin',
                'administrator',
                'root',
                'system',
                'operator',
                'master',
                'owner'
            ];

            return forbiddenPatterns.some(pattern => lowerUsername.includes(pattern));
        }
        
        // 显示提示信息
        function showAlert(type, message) {
            const alertContainer = document.getElementById('alertContainer');
            const alert = document.createElement('div');
            alert.className = `alert alert-${type} alert-dismissible fade show`;
            alert.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            alertContainer.appendChild(alert);
            
            // 自动消失
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.remove();
                }
            }, 5000);
        }
        
        // 清除所有提示
        function clearAlerts() {
            document.getElementById('alertContainer').innerHTML = '';
        }
        
        // 密码强度检测
        document.getElementById('password').addEventListener('input', function() {
            const password = this.value;
            const strength = calculatePasswordStrength(password);
            updatePasswordStrength(strength);
        });
        
        function calculatePasswordStrength(password) {
            if (!password) return { level: 0, text: '请输入密码' };
            
            let score = 0;
            
            // 长度检查
            if (password.length >= 6) score += 1;
            if (password.length >= 8) score += 1;
            
            // 复杂度检查
            if (/[a-z]/.test(password)) score += 1;
            if (/[A-Z]/.test(password)) score += 1;
            if (/[0-9]/.test(password)) score += 1;
            if (/[^a-zA-Z0-9]/.test(password)) score += 1;
            
            if (score <= 2) return { level: 1, text: '弱' };
            if (score <= 3) return { level: 2, text: '一般' };
            if (score <= 4) return { level: 3, text: '良好' };
            return { level: 4, text: '强' };
        }
        
        function updatePasswordStrength(strength) {
            const strengthText = document.getElementById('strengthText');
            const strengthFill = document.getElementById('strengthFill');
            
            strengthText.textContent = strength.text;
            
            // 移除所有强度类
            strengthFill.className = 'strength-fill';
            
            // 添加对应的强度类
            switch (strength.level) {
                case 1:
                    strengthFill.classList.add('strength-weak');
                    break;
                case 2:
                    strengthFill.classList.add('strength-fair');
                    break;
                case 3:
                    strengthFill.classList.add('strength-good');
                    break;
                case 4:
                    strengthFill.classList.add('strength-strong');
                    break;
            }
        }
        
        // 确认密码实时验证
        document.getElementById('confirmPassword').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPassword = this.value;

            if (confirmPassword && password !== confirmPassword) {
                this.setCustomValidity('密码不一致');
                this.classList.add('is-invalid');
            } else {
                this.setCustomValidity('');
                this.classList.remove('is-invalid');
            }
        });

        // 用户名实时验证
        let usernameCheckTimeout;
        document.getElementById('username').addEventListener('input', function() {
            const username = this.value.trim();
            const feedback = document.getElementById('usernameFeedback');

            // 清除之前的定时器
            if (usernameCheckTimeout) {
                clearTimeout(usernameCheckTimeout);
            }

            // 重置样式
            this.classList.remove('is-valid', 'is-invalid');
            feedback.className = 'username-feedback';

            // 如果用户开始输入，隐藏默认提示
            if (username.length > 0) {
                feedback.innerHTML = '';
            } else {
                feedback.innerHTML = '<small class="text-muted">用户名只能包含字母和数字，长度3-16位</small>';
            }

            // 基本格式验证
            if (username.length >= 3 && username.length <= 16 && /^[a-zA-Z0-9]+$/.test(username)) {
                // 检查是否为禁用用户名
                if (isForbiddenUsername(username)) {
                    this.classList.add('is-invalid');
                    feedback.className = 'username-feedback unavailable';
                    feedback.textContent = '✗ 该用户名为管理员专用，无法注册';
                } else {
                    feedback.className = 'username-feedback checking';
                    feedback.textContent = '检查用户名可用性...';

                    // 延迟检查用户名是否存在
                    usernameCheckTimeout = setTimeout(async () => {
                        await checkUsernameAvailability(username);
                    }, 500);
                }
            } else if (username.length > 0) {
                feedback.className = 'username-feedback unavailable';
                if (username.length < 3) {
                    feedback.textContent = '用户名至少需要3个字符';
                } else if (username.length > 16) {
                    feedback.textContent = '用户名不能超过16个字符';
                } else {
                    feedback.textContent = '用户名只能包含字母和数字';
                }
            }
        });

        // 检查用户名是否可用
        async function checkUsernameAvailability(username) {
            const usernameInput = document.getElementById('username');
            const feedback = document.getElementById('usernameFeedback');

            try {
                const response = await fetch('/auth/checkUsername', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({ username: username })
                });

                const result = await response.json();

                if (result.code === 200) {
                    if (result.data.available) {
                        usernameInput.classList.remove('is-invalid');
                        usernameInput.classList.add('is-valid');
                        usernameInput.setCustomValidity('');
                        feedback.className = 'username-feedback available';
                        feedback.textContent = '✓ 用户名可用';
                    } else {
                        usernameInput.classList.remove('is-valid');
                        usernameInput.classList.add('is-invalid');
                        feedback.className = 'username-feedback unavailable';

                        if (result.data.reason === 'exists') {
                            feedback.textContent = '✗ 用户名已存在';
                            usernameInput.setCustomValidity('用户名已存在');
                        } else if (result.data.reason === 'forbidden') {
                            feedback.textContent = '✗ 禁止注册的用户名';
                            usernameInput.setCustomValidity('禁止注册的用户名');
                        } else {
                            feedback.textContent = '✗ 用户名不可用';
                            usernameInput.setCustomValidity('用户名不可用');
                        }
                    }
                } else {
                    feedback.className = 'username-feedback unavailable';
                    feedback.textContent = '✗ ' + (result.message || '检查失败');
                }
            } catch (error) {
                // 检查失败时显示提示
                feedback.className = 'username-feedback';
                feedback.textContent = '网络错误，无法检查用户名';
                console.log('用户名检查失败:', error);
            }
        }
    </script>
</body>
</html>
