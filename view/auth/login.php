{extend name="layout/base"}

{block name="title"}游戏商城 - 登录{/block}

{block name="head"}
<link rel="stylesheet" href="/static/css/login.css">
{/block}

{block name="content"}
<div class="login-container">
    <div class="login-header">
        <h1 class="login-title">
            <i class="bi bi-controller me-2"></i>游戏商城
        </h1>
        <p class="login-subtitle">请登录您的游戏账号</p>
    </div>

    <div id="error-message" class="alert alert-danger d-none" role="alert">
        <i class="bi bi-exclamation-triangle-fill me-2"></i>
        <span class="error-text"></span>
    </div>

    <div id="success-message" class="alert alert-success d-none" role="alert">
        <i class="bi bi-check-circle-fill me-2"></i>
        <span class="success-text"></span>
    </div>

    <form id="login-form" class="needs-validation" novalidate>
        <div class="mb-3">
            <label for="username" class="form-label">
                <i class="bi bi-person-fill me-1"></i>游戏账号
            </label>
            <div class="input-group">
                <span class="input-group-text">
                    <i class="bi bi-person"></i>
                </span>
                <input type="text" id="username" name="username" class="form-control"
                       placeholder="请输入您的游戏账号" required>
                <div class="invalid-feedback">
                    请输入有效的游戏账号
                </div>
            </div>
        </div>

        <div class="mb-4">
            <label for="password" class="form-label">
                <i class="bi bi-lock-fill me-1"></i>密码
            </label>
            <div class="input-group">
                <span class="input-group-text">
                    <i class="bi bi-lock"></i>
                </span>
                <input type="password" id="password" name="password" class="form-control"
                       placeholder="请输入密码" required>
                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                    <i class="bi bi-eye" id="toggleIcon"></i>
                </button>
                <div class="invalid-feedback">
                    请输入密码
                </div>
            </div>
        </div>

        <div class="d-grid mb-3">
            <button type="submit" id="login-btn" class="btn btn-primary btn-lg">
                <span id="btn-text">
                    <i class="bi bi-box-arrow-in-right me-2"></i>登录
                </span>
                <span id="btn-loading" class="d-none">
                    <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                    登录中...
                </span>
            </button>
        </div>
    </form>

    <div class="text-center mb-3">
        <p class="text-muted mb-0">还没有账号？<a href="/register" class="text-decoration-none">立即注册</a></p>
    </div>

    <div class="text-center border-top pt-3">
        <small class="text-muted">
            <i class="bi bi-shield-check me-1"></i>
            © 2024 游戏商城系统 | 安全登录
        </small>
    </div>
</div>
{/block}

{block name="scripts"}
<script src="/static/js/login.js"></script>
{/block}