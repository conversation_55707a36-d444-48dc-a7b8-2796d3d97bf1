<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>缓存管理</title>

    <!-- Bootstrap CSS -->
    <link href="/static/css/bootstrap.min.css" rel="stylesheet">
    <link href="/static/css/bootstrap-icons.min.css" rel="stylesheet">

    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background-color: #fafafa;
        }
        .section h2 {
            color: #555;
            margin-top: 0;
            margin-bottom: 15px;
        }
        .btn-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 15px;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background-color: #0056b3;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .btn-danger:hover {
            background-color: #c82333;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-success:hover {
            background-color: #218838;
        }
        .btn-info {
            background-color: #17a2b8;
            color: white;
        }
        .btn-info:hover {
            background-color: #138496;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        .alert {
            padding: 12px;
            margin-bottom: 20px;
            border: 1px solid transparent;
            border-radius: 4px;
            display: none;
        }
        .alert-success {
            color: #155724;
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .alert-danger {
            color: #721c24;
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #e0e0e0;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        .loading {
            display: none;
            text-align: center;
            color: #666;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>缓存管理中心</h1>
        
        <div id="alertContainer"></div>
        
        <!-- 缓存统计 -->
        <div class="section">
            <h2>缓存统计信息</h2>
            <button class="btn btn-info" onclick="loadCacheStats()">刷新统计</button>
            <div class="loading" id="statsLoading">正在加载统计信息...</div>
            <div class="stats-grid" id="statsContainer">
                <!-- 统计信息将在这里显示 -->
            </div>
        </div>
        
        <!-- 商品列表缓存管理 -->
        <div class="section">
            <h2>商品列表缓存管理</h2>
            <div class="btn-group">
                <button class="btn btn-danger" onclick="clearAllItemsCache()">清除所有商品缓存</button>
                <button class="btn btn-success" onclick="warmupCache()">预热缓存</button>
            </div>
            
            <div class="form-group">
                <label for="category">按分类清除缓存：</label>
                <select class="form-control" id="category" style="width: 200px; display: inline-block;">
                    <option value="">全部分类</option>
                    <option value="1">武器</option>
                    <option value="2">防具</option>
                    <option value="3">饰品</option>
                    <option value="4">消耗品</option>
                    <option value="5">材料</option>
                    <option value="6">其他</option>
                </select>
                <button class="btn btn-primary" onclick="clearItemsCache()">清除指定分类缓存</button>
            </div>
            
            <div class="form-group">
                <label for="search">按搜索关键词清除缓存：</label>
                <input type="text" class="form-control" id="search" placeholder="输入搜索关键词" style="width: 200px; display: inline-block;">
                <button class="btn btn-primary" onclick="clearItemsCache()">清除搜索缓存</button>
            </div>
        </div>
        
        <!-- 单个商品缓存管理 -->
        <div class="section">
            <h2>单个商品缓存管理</h2>
            <div class="form-group">
                <label for="itemId">商品ID：</label>
                <input type="number" class="form-control" id="itemId" placeholder="输入商品ID" style="width: 200px; display: inline-block;">
                <button class="btn btn-danger" onclick="clearItemInfoCache()">清除商品信息缓存</button>
            </div>
        </div>
        
        <!-- 缓存预热设置 -->
        <div class="section">
            <h2>缓存预热设置</h2>
            <div class="form-group">
                <label for="maxPages">预热页数（最多10页）：</label>
                <input type="number" class="form-control" id="maxPages" value="5" min="1" max="10" style="width: 200px; display: inline-block;">
                <button class="btn btn-success" onclick="warmupCacheWithPages()">开始预热</button>
            </div>
            <p style="color: #666; font-size: 12px;">注意：预热操作会消耗一定的服务器资源，建议在访问量较低时进行。</p>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="/static/js/bootstrap.bundle.min.js"></script>

    <script>
        // 显示提示信息
        function showAlert(message, type = 'success') {
            const alertContainer = document.getElementById('alertContainer');
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.textContent = message;
            alertDiv.style.display = 'block';
            
            alertContainer.innerHTML = '';
            alertContainer.appendChild(alertDiv);
            
            // 3秒后自动隐藏
            setTimeout(() => {
                alertDiv.style.display = 'none';
            }, 3000);
        }
        
        // 发送AJAX请求
        function sendRequest(url, data = {}) {
            return fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 0 || data.success === true) {
                    showAlert(data.message, 'success');
                } else {
                    showAlert(data.message, 'danger');
                }
                return data;
            })
            .catch(error => {
                showAlert('操作失败：' + error.message, 'danger');
                throw error;
            });
        }
        
        // 清除所有商品缓存
        function clearAllItemsCache() {
            if (confirm('确定要清除所有商品缓存吗？')) {
                sendRequest('/cache_manager/clearAllItemsCache');
            }
        }
        
        // 清除商品列表缓存
        function clearItemsCache() {
            const category = document.getElementById('category').value;
            const search = document.getElementById('search').value;
            
            sendRequest('/cache_manager/clearItemsCache', {
                category: category,
                search: search
            });
        }
        
        // 清除单个商品信息缓存
        function clearItemInfoCache() {
            const itemId = document.getElementById('itemId').value;
            
            if (!itemId) {
                showAlert('请输入商品ID', 'danger');
                return;
            }
            
            sendRequest('/cache_manager/clearItemInfoCache', {
                item_id: parseInt(itemId)
            });
        }
        
        // 预热缓存（默认5页）
        function warmupCache() {
            if (confirm('确定要开始预热缓存吗？这可能需要一些时间。')) {
                sendRequest('/cache_manager/warmupItemsCache');
            }
        }
        
        // 预热缓存（指定页数）
        function warmupCacheWithPages() {
            const maxPages = document.getElementById('maxPages').value;
            
            if (!maxPages || maxPages < 1 || maxPages > 10) {
                showAlert('请输入有效的页数（1-10）', 'danger');
                return;
            }
            
            if (confirm(`确定要预热前${maxPages}页的缓存吗？这可能需要一些时间。`)) {
                sendRequest('/cache_manager/warmupItemsCache', {
                    max_pages: parseInt(maxPages)
                });
            }
        }
        
        // 加载缓存统计信息
        function loadCacheStats() {
            const loading = document.getElementById('statsLoading');
            const container = document.getElementById('statsContainer');
            
            loading.style.display = 'block';
            container.innerHTML = '';
            
            fetch('/cache_manager/getCacheStats')
                .then(response => response.json())
                .then(data => {
                    loading.style.display = 'none';
                    
                    if ((data.code === 0 || data.success === true) && data.data) {
                        displayStats(data.data);
                    } else {
                        container.innerHTML = '<p style="text-align: center; color: #666;">暂无统计数据</p>';
                    }
                })
                .catch(error => {
                    loading.style.display = 'none';
                    container.innerHTML = '<p style="text-align: center; color: #dc3545;">加载失败</p>';
                });
        }
        
        // 显示统计信息
        function displayStats(stats) {
            const container = document.getElementById('statsContainer');
            let html = '';
            
            // 显示各种统计信息
            for (const [key, value] of Object.entries(stats)) {
                html += `
                    <div class="stat-card">
                        <div class="stat-value">${value}</div>
                        <div class="stat-label">${getStatLabel(key)}</div>
                    </div>
                `;
            }
            
            container.innerHTML = html;
        }
        
        // 获取统计项的中文标签
        function getStatLabel(key) {
            const labels = {
                'total_keys': '总键数',
                'memory_usage': '内存使用',
                'hits': '命中次数',
                'misses': '未命中次数',
                'hit_rate': '命中率',
                'connected_clients': '连接客户端',
                'used_memory_human': '已用内存',
                'keyspace_hits': '键空间命中',
                'keyspace_misses': '键空间未命中'
            };
            
            return labels[key] || key;
        }
        
        // 页面加载完成后自动加载统计信息
        document.addEventListener('DOMContentLoaded', function() {
            loadCacheStats();
        });
    </script>
</body>
</html>