<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{block name="title"}管理面板{/block} - 游戏商城</title>
    
    <!-- 基础CSS -->
    <link href="/static/css/bootstrap.min.css" rel="stylesheet">
    <link href="/static/css/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/sidebar.css" rel="stylesheet">
    <link href="/static/css/dual-ui.css" rel="stylesheet">
    
    <!-- 页面特定CSS -->
    {block name="page_css"}{/block}
    
    <!-- 通用管理面板样式 -->
    <style>
        .main-content {
            background-color: #f8f9fa;
            min-height: 100vh;
            margin-left: 280px;
            transition: margin-left 0.3s ease;
            padding-top: 60px;
        }

        /* 响应式适配 */
        @media (max-width: 991.98px) {
            .main-content {
                margin-left: 0 !important;
                padding-top: 80px !important;
            }
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-2px);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
        }
        
        .btn-success {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border: none;
            border-radius: 8px;
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            border: none;
            border-radius: 8px;
        }
        
        .form-control, .form-select {
            border-radius: 8px;
            border: 1px solid #e0e0e0;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        .table {
            border-radius: 10px;
            overflow: hidden;
        }
        
        .table thead th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
        }
    </style>
</head>
<body>
    <!-- 侧边栏 -->
    {include file="admin/layout/sidebar" /}

    <!-- 主内容区域 -->
    <main class="main-content">
        <div class="container-fluid">
            <!-- 页面标题 -->
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">{block name="page_title"}管理面板{/block}</h1>
            </div>

            <!-- 页面内容 -->
            {block name="content"}{/block}
        </div>
    </main>
    
    <!-- 基础JS -->
    <script src="/static/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/sidebar.js"></script>
    
    <!-- 页面特定JS -->
    {block name="page_js"}{/block}
</body>
</html>
