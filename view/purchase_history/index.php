<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>购买历史 - 商城</title>
    <link href="/static/css/bootstrap.min.css" rel="stylesheet">
    <link href="/static/css/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/purchase-history.css" rel="stylesheet">
    <link href="/static/css/modal-fix.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            <a class="navbar-brand" href="/shop">
                <i class="bi bi-shop"></i> 商城
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="bi bi-person-circle"></i> <?php echo $user_nickname ?? '用户'; ?>
                </span>
                <a class="nav-link" href="/shop">
                    <i class="bi bi-arrow-left"></i> 返回商城
                </a>
                <a class="nav-link" href="/auth/logout">
                    <i class="bi bi-box-arrow-right"></i> 退出
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col">
                <h2><i class="bi bi-clock-history"></i> 购买历史</h2>
                <p class="text-muted">查看您的所有购买记录和统计信息</p>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="stats-card">
            <div class="row">
                <div class="col-md-2 stats-item">
                    <div class="stats-number" id="totalOrders">-</div>
                    <div class="stats-label">总订单数</div>
                </div>
                <div class="col-md-2 stats-item">
                    <div class="stats-number" id="completedOrders">-</div>
                    <div class="stats-label">已完成</div>
                </div>
                <div class="col-md-2 stats-item">
                    <div class="stats-number" id="failedOrders">-</div>
                    <div class="stats-label">失败订单</div>
                </div>
                <div class="col-md-2 stats-item">
                    <div class="stats-number text-success" id="pointAmount">-</div>
                    <div class="stats-label">泡点消费</div>
                </div>
                <div class="col-md-2 stats-item">
                    <div class="stats-number text-warning" id="scoreAmount">-</div>
                    <div class="stats-label">积分消费</div>
                </div>
                <div class="col-md-2 stats-item">
                    <div class="stats-number text-info" id="coinAmount">-</div>
                    <div class="stats-label">C币消费</div>
                </div>
            </div>
        </div>

        <!-- 筛选器 -->
        <div class="filter-card">
            <div class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">物品名称</label>
                    <input type="text" class="form-control" id="itemNameFilter" placeholder="输入物品名称搜索...">
                </div>
                <div class="col-md-2">
                    <label class="form-label">订单状态</label>
                    <select class="form-select" id="statusFilter">
                        <option value="">全部状态</option>
                        <option value="1">待处理</option>
                        <option value="2">处理中</option>
                        <option value="3">已完成</option>
                        <option value="4">失败</option>
                        <option value="5">已取消</option>
                        <option value="6">已退款</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">货币类型</label>
                    <select class="form-select" id="currencyFilter">
                            <option value="">全部货币</option>
                            <option value="COIN">C币</option>
                            <option value="POINT">泡点</option>
                            <option value="SCORE">积分</option>
                        </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">开始日期</label>
                    <input type="date" class="form-control" id="startDate">
                </div>
                <div class="col-md-2">
                    <label class="form-label">结束日期</label>
                    <input type="date" class="form-control" id="endDate">
                </div>
                <div class="col-md-1">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button class="btn btn-primary" onclick="applyFilters()">
                            <i class="bi bi-search"></i> 筛选
                        </button>
                    </div>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col">
                    <button class="btn btn-export" onclick="exportHistory('csv')">
                        <i class="bi bi-download"></i> 导出CSV
                    </button>
                    <button class="btn btn-success ms-2" onclick="refreshPurchaseHistory()">
                        <i class="bi bi-arrow-clockwise"></i> 刷新记录
                    </button>
                    <button class="btn btn-outline-secondary ms-2" onclick="resetFilters()">
                        <i class="bi bi-arrow-clockwise"></i> 重置
                    </button>
                </div>
            </div>
        </div>

        <!-- 顶部分页 -->
        <div id="topPagination" class="top-pagination" style="display: none;">
            <div class="d-flex justify-content-between align-items-center mb-3 p-3 bg-light rounded">
                <div class="d-flex align-items-center">
                    <span class="me-3 text-muted">共 <span id="topTotalRecords">0</span> 条记录</span>
                    <label class="form-label me-2 mb-0">每页显示:</label>
                    <select class="form-select" id="topPageSizeFilter" style="width: auto;">
                        <option value="10">10条</option>
                        <option value="20" selected>20条</option>
                        <option value="50">50条</option>
                        <option value="100">100条</option>
                    </select>
                </div>
                <nav>
                    <ul class="pagination mb-0" id="topPaginationList">
                        <!-- 分页按钮将通过JavaScript动态生成 -->
                    </ul>
                </nav>
            </div>
        </div>

        <!-- 订单列表 -->
        <div id="orderList">
            <div class="loading">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2">正在加载购买历史...</p>
            </div>
        </div>

        <!-- 分页 -->
        <div id="pagination" style="display: none;">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <label class="form-label me-2 mb-0">每页显示:</label>
                    <select class="form-select" id="pageSizeFilter" style="width: auto;">
                        <option value="10">10条</option>
                        <option value="20">20条</option>
                        <option value="50">50条</option>
                        <option value="100">100条</option>
                    </select>
                </div>
                <nav>
                    <ul class="pagination mb-0">
                        <!-- 分页按钮将通过JavaScript动态生成 -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!-- 订单详情模态框 -->
    <div class="modal fade" id="orderDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">订单详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="orderDetailContent">
                    <!-- 订单详情内容将通过JavaScript动态加载 -->
                </div>
            </div>
        </div>
    </div>

    <script src="/static/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/purchase-history.js"></script>
</body>
</html>