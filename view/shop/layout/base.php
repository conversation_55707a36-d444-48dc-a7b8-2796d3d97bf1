<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{block name="title"}游戏商城{/block}</title>
    <meta name="description" content="{block name="description"}游戏商城 - 发现精彩游戏道具，提升您的游戏体验{/block}">
    <meta name="keywords" content="{block name="keywords"}游戏商城,游戏道具,游戏装备,在线购买{/block}">
    
    <!-- Bootstrap CSS -->
    <link href="/static/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="/static/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">

    <!-- 基础样式 -->
    <link rel="stylesheet" href="/static/css/base.css">
    <link rel="stylesheet" href="/static/css/components.css">
    
    <!-- 页面特定样式 -->
    {block name="css"}{/block}
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    
    <!-- 预加载关键资源 -->
    <link rel="preload" href="/static/css/shop.css" as="style">
    <link rel="preload" href="/static/js/shop.js" as="script">
</head>
<body class="shop-body">
    <!-- 页面加载指示器 -->
    <div id="page-loader" class="page-loader">
        <div class="loader-spinner"></div>
        <p>正在加载...</p>
    </div>
    
    <!-- 主要内容区域 -->
    <div id="app" class="app-container">
        {block name="content"}{/block}
    </div>
    
    <!-- 全局通知容器 -->
    <div id="global-notifications" class="notification-container">
        <!-- 通知消息将通过JavaScript动态添加 -->
    </div>
    
    <!-- 全局模态框容器 -->
    <div id="global-modal-container">
        <!-- 模态框将通过JavaScript动态添加 -->
    </div>
    
    <!-- 返回顶部按钮 -->
    <button id="back-to-top" class="back-to-top" style="display: none;" title="返回顶部">
        ↑
    </button>
    
    <!-- Bootstrap JS -->
    <script src="/static/js/bootstrap.bundle.min.js"></script>

    <!-- 基础JavaScript库 -->
    <script src="/static/js/utils.js"></script>
    <script src="/static/js/components.js"></script>
    <script src="/static/js/modal.js"></script>
    
    <!-- 页面特定脚本 -->
    {block name="script"}{/block}
    
    <!-- 全局初始化脚本 -->
    <script>
        // 全局配置
        window.APP_CONFIG = {
            baseUrl: '/',
            apiUrl: '/api',
            debug: false,
            user: {
                id: '{$user_info.id ?? ""}',
                nickname: '{$user_nickname ?? ""}',
                isAdmin: {$user_info.is_admin ?? false}
            }
        };
        
        // 页面加载完成处理
        document.addEventListener('DOMContentLoaded', function() {
            // 隐藏页面加载指示器
            const pageLoader = document.getElementById('page-loader');
            if (pageLoader) {
                pageLoader.style.display = 'none';
            }
            
            // 显示主要内容
            const appContainer = document.getElementById('app');
            if (appContainer) {
                appContainer.style.opacity = '1';
            }
            
            // 初始化全局组件
            initGlobalComponents();
            
            // 初始化页面特定组件
            if (typeof initPageComponents === 'function') {
                initPageComponents();
            }
        });
        
        // 初始化全局组件
        function initGlobalComponents() {
            // 初始化返回顶部按钮
            initBackToTop();
            
            // 初始化全局通知系统
            initNotificationSystem();
            
            // 初始化全局模态框系统
            initModalSystem();
            
            // 初始化全局错误处理
            initGlobalErrorHandler();
        }
        
        // 返回顶部功能
        function initBackToTop() {
            const backToTopBtn = document.getElementById('back-to-top');
            if (!backToTopBtn) return;
            
            // 监听滚动事件
            window.addEventListener('scroll', function() {
                if (window.pageYOffset > 300) {
                    backToTopBtn.style.display = 'block';
                } else {
                    backToTopBtn.style.display = 'none';
                }
            });
            
            // 点击返回顶部
            backToTopBtn.addEventListener('click', function() {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        }
        
        // 通知系统
        function initNotificationSystem() {
            window.showNotification = function(message, type = 'info', duration = 3000) {
                const container = document.getElementById('global-notifications');
                if (!container) return;
                
                const notification = document.createElement('div');
                notification.className = `notification notification-${type}`;
                notification.innerHTML = `
                    <div class="notification-content">
                        <span class="notification-message">${message}</span>
                        <button class="notification-close" onclick="this.parentElement.parentElement.remove()">&times;</button>
                    </div>
                `;
                
                container.appendChild(notification);
                
                // 自动移除
                if (duration > 0) {
                    setTimeout(() => {
                        if (notification.parentElement) {
                            notification.remove();
                        }
                    }, duration);
                }
            };
            



        }
        
        // Bootstrap模态框系统
        function initModalSystem() {
            // 创建Bootstrap模态框的工具函数
            window.showModal = function(content, options = {}) {
                const container = document.getElementById('global-modal-container');
                if (!container) return;

                // 生成唯一ID
                const modalId = 'modal-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);

                // 确定模态框大小类
                let sizeClass = '';
                if (options.size === 'large') sizeClass = 'modal-lg';
                else if (options.size === 'xl') sizeClass = 'modal-xl';
                else if (options.size === 'sm') sizeClass = 'modal-sm';

                // 创建Bootstrap模态框HTML
                const modalHtml = `
                    <div class="modal fade" id="${modalId}" tabindex="-1" aria-labelledby="${modalId}Label" aria-hidden="true">
                        <div class="modal-dialog ${sizeClass} ${options.centered ? 'modal-dialog-centered' : ''} ${options.scrollable ? 'modal-dialog-scrollable' : ''}">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="${modalId}Label">${options.title || '提示'}</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                                </div>
                                <div class="modal-body">
                                    ${content}
                                </div>
                                ${options.footer ? `<div class="modal-footer">${options.footer}</div>` : ''}
                            </div>
                        </div>
                    </div>
                `;

                // 添加到容器
                container.insertAdjacentHTML('beforeend', modalHtml);

                // 获取模态框元素
                const modalElement = document.getElementById(modalId);

                // 创建Bootstrap模态框实例
                const modal = new bootstrap.Modal(modalElement, {
                    backdrop: options.backdrop !== false,
                    keyboard: options.keyboard !== false,
                    focus: options.focus !== false
                });

                // 显示模态框
                modal.show();

                // 监听模态框隐藏事件，自动清理DOM
                modalElement.addEventListener('hidden.bs.modal', function() {
                    modalElement.remove();
                });

                return {
                    element: modalElement,
                    instance: modal,
                    hide: () => modal.hide(),
                    show: () => modal.show()
                };
            };

            // 关闭模态框的工具函数
            window.closeModal = function(modal) {
                if (modal && modal.instance) {
                    modal.instance.hide();
                } else if (modal && modal.hide) {
                    modal.hide();
                } else if (modal && modal.remove) {
                    // 兼容旧的自定义模态框
                    modal.remove();
                }
            };

            // 显示确认对话框
            window.showConfirm = function(message, options = {}) {
                const footer = `
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">${options.cancelText || '取消'}</button>
                    <button type="button" class="btn btn-primary" id="confirm-btn">${options.confirmText || '确认'}</button>
                `;

                const modal = window.showModal(message, {
                    title: options.title || '确认',
                    footer: footer,
                    size: options.size || '',
                    centered: true
                });

                // 绑定确认按钮事件
                const confirmBtn = modal.element.querySelector('#confirm-btn');
                if (confirmBtn && options.onConfirm) {
                    confirmBtn.addEventListener('click', function() {
                        options.onConfirm();
                        modal.hide();
                    });
                }

                return modal;
            };

            // 显示警告对话框
            window.showAlert = function(message, options = {}) {
                const footer = `
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">${options.buttonText || '确定'}</button>
                `;

                return window.showModal(message, {
                    title: options.title || '提示',
                    footer: footer,
                    size: options.size || '',
                    centered: true
                });
            };
        }
        
        // 全局错误处理
        function initGlobalErrorHandler() {
            window.addEventListener('error', function(e) {
                console.error('全局错误:', e.error);
                if (window.APP_CONFIG.debug) {
                    showError('页面发生错误，请查看控制台获取详细信息');
                }
            });
            
            window.addEventListener('unhandledrejection', function(e) {
                console.error('未处理的Promise拒绝:', e.reason);
                if (window.APP_CONFIG.debug) {
                    showError('异步操作失败，请查看控制台获取详细信息');
                }
            });
        }
        
        // 全局工具函数
        window.utils = {
            // 格式化日期
            formatDate: function(date, format = 'YYYY-MM-DD HH:mm:ss') {
                const d = new Date(date);
                const year = d.getFullYear();
                const month = String(d.getMonth() + 1).padStart(2, '0');
                const day = String(d.getDate()).padStart(2, '0');
                const hours = String(d.getHours()).padStart(2, '0');
                const minutes = String(d.getMinutes()).padStart(2, '0');
                const seconds = String(d.getSeconds()).padStart(2, '0');
                
                return format
                    .replace('YYYY', year)
                    .replace('MM', month)
                    .replace('DD', day)
                    .replace('HH', hours)
                    .replace('mm', minutes)
                    .replace('ss', seconds);
            },
            
            // 格式化数字
            formatNumber: function(num, decimals = 0) {
                return Number(num).toLocaleString('zh-CN', {
                    minimumFractionDigits: decimals,
                    maximumFractionDigits: decimals
                });
            },
            
            // 防抖函数
            debounce: function(func, wait) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            },
            
            // 节流函数
            throttle: function(func, limit) {
                let inThrottle;
                return function() {
                    const args = arguments;
                    const context = this;
                    if (!inThrottle) {
                        func.apply(context, args);
                        inThrottle = true;
                        setTimeout(() => inThrottle = false, limit);
                    }
                };
            }
        };
    </script>
</body>
</html>
