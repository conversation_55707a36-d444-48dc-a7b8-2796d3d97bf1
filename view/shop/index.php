{extend name="shop/layout/base" /}

{block name="title"}{$general_settings.site_name|default='游戏商城'} - 主页{/block}

{block name="css"}
    <link rel="stylesheet" href="/static/css/shop.css">
    <!-- 通用组件样式 -->
    <link rel="stylesheet" href="/static/css/bootstrap-components.css">
{/block}

{block name="content"}
    <!-- 桌面端导航 -->
    <header class="desktop-header d-none d-lg-block">
        <div class="desktop-nav-container">
            <div class="nav-left">
                <div class="logo" id="site-logo">
                    <i class="bi bi-shop"></i>
                    <span>{$general_settings.site_name|default='游戏商城'}</span>
                </div>
                <nav class="desktop-nav">
                    <ul class="desktop-nav-menu">
                        <li><a href="/shop" class="nav-link active">
                            <i class="bi bi-house"></i>
                            <span>首页</span>
                        </a></li>
                        <li><a href="#" onclick="refreshItems().catch(error => console.error('刷新失败:', error))" class="nav-link">
                            <i class="bi bi-arrow-clockwise"></i>
                            <span>刷新商品</span>
                        </a></li>
                        <li><a href="#" onclick="showPurchaseHistory()" class="nav-link">
                            <i class="bi bi-clock-history"></i>
                            <span>购买历史</span>
                        </a></li>
                        <li><a href="#" onclick="showRechargeHistory()" class="nav-link">
                            <i class="bi bi-receipt"></i>
                            <span>充值记录</span>
                        </a></li>
                        <!-- 管理面板入口，仅管理员可见 -->
                        <li id="admin-menu-desktop" style="display: none;">
                            <a href="/admin" class="nav-link admin-link">
                                <i class="bi bi-gear"></i>
                                <span>管理面板</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
            <div class="nav-right">
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="bi bi-person-circle"></i>
                    </div>
                    <div class="user-details">
                        <span class="user-name">{$user_nickname|default='用户'}</span>
                        <span class="user-role">玩家</span>
                    </div>
                    <div class="user-actions">
                        <a href="/auth/logout" class="logout-btn">
                            <i class="bi bi-box-arrow-right"></i>
                            <span>退出</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 手机端导航 -->
    <header class="mobile-header d-lg-none">
        <div class="mobile-nav-container">
            <div class="mobile-nav-top">
                <div class="mobile-logo">
                    <i class="bi bi-shop"></i>
                    <span>{$general_settings.site_name|default='游戏商城'}</span>
                </div>
                <div class="mobile-user">
                    <span class="mobile-user-name">{$user_nickname|default='用户'}</span>
                    <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                        <i class="bi bi-list"></i>
                    </button>
                </div>
            </div>

            <!-- 手机端菜单遮罩 -->
            <div class="mobile-menu-overlay" id="mobile-menu-overlay"></div>

            <!-- 手机端菜单 -->
            <div class="mobile-menu" id="mobile-menu">
                <div class="mobile-menu-content">
                    <div class="mobile-nav-items">
                        <a href="/shop" class="mobile-nav-item active">
                            <i class="bi bi-house"></i>
                            <span>首页</span>
                        </a>
                        <a href="#" onclick="refreshItems().catch(error => console.error('刷新失败:', error))" class="mobile-nav-item">
                            <i class="bi bi-arrow-clockwise"></i>
                            <span>刷新商品</span>
                        </a>
                        <a href="#" onclick="showPurchaseHistory()" class="mobile-nav-item">
                            <i class="bi bi-clock-history"></i>
                            <span>购买历史</span>
                        </a>
                        <a href="#" onclick="showRechargeHistory()" class="mobile-nav-item">
                            <i class="bi bi-receipt"></i>
                            <span>充值记录</span>
                        </a>
                        <!-- 管理面板入口，仅管理员可见 -->
                        <a href="/admin" class="mobile-nav-item admin-link" id="admin-menu-mobile" style="display: none;">
                            <i class="bi bi-gear"></i>
                            <span>管理面板</span>
                        </a>
                    </div>
                    <div class="mobile-menu-footer">
                        <a href="/auth/logout" class="mobile-logout-btn">
                            <i class="bi bi-box-arrow-right"></i>
                            <span>退出登录</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>
    
    <!-- 主要内容 -->
    <main class="main-container">
        <!-- 顶部信息区域 -->
        <section class="top-info-section">
            <!-- 桌面端欢迎区域 -->
            <div class="welcome-area d-none d-md-block">
                <p class="welcome-subtitle" id="welcome-subtitle">{$general_settings.site_description|default='发现精彩游戏道具，提升您的游戏体验'}</p>
            </div>

            <!-- 手机端紧凑标题 -->
            <div class="mobile-header-content d-md-none">
                <p class="mobile-subtitle">{$general_settings.site_description|default='选择您需要的道具'}</p>
            </div>

            <!-- 余额显示区域 -->
            <div class="balance-section">
                <!-- 桌面端余额显示 -->
                <div class="card d-none d-md-block mb-4 shadow-sm">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="card-title mb-0 fw-bold text-muted">我的余额</h6>
                            <div class="balance-actions">
                                {if !isset($payment_settings.show_recharge_button) || $payment_settings.show_recharge_button}
                                <button type="button" class="btn btn-primary btn-sm me-2" onclick="showRechargeModal()" title="充值">
                                    <i class="bi bi-credit-card me-1"></i>充值
                                </button>
                                {/if}
                                <button type="button" class="btn btn-success btn-sm me-2" onclick="showCardModal()" title="周卡月卡">
                                    <i class="bi bi-calendar-check me-1"></i>特惠卡
                                </button>
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="refreshBalance()" title="刷新余额">
                                    <i class="bi bi-arrow-clockwise"></i>
                                </button>
                            </div>
                        </div>
                        <div class="row g-3">
                            <div class="col-md-4">
                                <div class="d-flex align-items-center p-3 bg-light rounded">
                                    <div class="me-3">
                                        <i class="bi bi-coin fs-2 text-warning"></i>
                                    </div>
                                    <div>
                                        <div class="text-muted small">泡点</div>
                                        <div id="shop-coin-balance" class="fw-bold fs-5">加载中...</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="d-flex align-items-center p-3 bg-light rounded">
                                    <div class="me-3">
                                        <i class="bi bi-star-fill fs-2 text-info"></i>
                                    </div>
                                    <div>
                                        <div class="text-muted small">积分</div>
                                        <div id="shop-silver-balance" class="fw-bold fs-5">加载中...</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="d-flex align-items-center p-3 bg-light rounded">
                                    <div class="me-3">
                                        <i class="bi bi-currency-bitcoin fs-2 text-success"></i>
                                    </div>
                                    <div>
                                        <div class="text-muted small">C币</div>
                                        <div id="shop-c-coin-balance" class="fw-bold fs-5">加载中...</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 手机端余额显示 (Bootstrap Card) -->
                <div class="card d-md-none mb-4 border-0 shadow-sm" style="background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);">
                    <div class="card-header bg-transparent border-0 pb-2">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="card-title text-white mb-0 fw-bold">我的余额</h6>
                            <div class="btn-group" role="group">
                                {if !isset($payment_settings.show_recharge_button) || $payment_settings.show_recharge_button}
                                <button type="button" class="btn btn-outline-light btn-sm" onclick="showRechargeModal()" title="充值">
                                    <i class="bi bi-credit-card me-1"></i>充值
                                </button>
                                {/if}
                                <button type="button" class="btn btn-outline-light btn-sm" onclick="showCardModal()" title="周卡月卡">
                                    <i class="bi bi-calendar-check me-1"></i>特惠卡
                                </button>
                                <button type="button" class="btn btn-outline-light btn-sm" onclick="refreshBalance()" title="刷新余额">
                                    <i class="bi bi-arrow-clockwise"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body pt-2">
                        <div class="row g-2">
                            <!-- 泡点 -->
                            <div class="col-4">
                                <div class="card bg-white bg-opacity-25 border-0 text-center">
                                    <div class="card-body p-2">
                                        <div class="text-warning mb-1">
                                            <i class="bi bi-coin fs-4"></i>
                                        </div>
                                        <div class="text-white-50 small">泡点</div>
                                        <div id="shop-coin-balance-mobile" class="text-white fw-bold small">加载中...</div>
                                    </div>
                                </div>
                            </div>
                            <!-- 积分 -->
                            <div class="col-4">
                                <div class="card bg-white bg-opacity-25 border-0 text-center">
                                    <div class="card-body p-2">
                                        <div class="text-info mb-1">
                                            <i class="bi bi-star-fill fs-4"></i>
                                        </div>
                                        <div class="text-white-50 small">积分</div>
                                        <div id="shop-silver-balance-mobile" class="text-white fw-bold small">加载中...</div>
                                    </div>
                                </div>
                            </div>
                            <!-- C币 -->
                            <div class="col-4">
                                <div class="card bg-white bg-opacity-25 border-0 text-center">
                                    <div class="card-body p-2">
                                        <div class="text-success mb-1">
                                            <i class="bi bi-currency-bitcoin fs-4"></i>
                                        </div>
                                        <div class="text-white-50 small">C币</div>
                                        <div id="shop-c-coin-balance-mobile" class="text-white fw-bold small">加载中...</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 筛选区域 -->
        <section class="filter-section">

            <div class="filter-header">
                <h2 class="filter-title">商品筛选</h2>
                <div class="filter-controls-row">
                    <div class="filter-controls-left">
                        <!-- 使用新的下拉框组件 -->
                        <div class="filter-group">
                            <label for="category-filter" class="filter-label">商品分类</label>
                            <select id="category-filter" class="filter-select">
                                <option value="">全部分类</option>
                                <?php foreach(\app\service\ShopConfigService::getAllCategories() as $id => $name): ?>
                                <option value="<?= $id ?>"><?= $name ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <!-- 搜索框组 -->
                        <div class="filter-group search-group">
                            <label for="search-input" class="filter-label">商品搜索</label>
                            <div class="search-input-group">
                                <input type="text" id="search-input" class="form-control search-input" placeholder="搜索商品名称...">
                                <button id="search-btn" class="btn btn-primary search-btn">
                                    <i class="bi bi-search"></i>
                                    <span>搜索</span>
                                </button>
                            </div>
                        </div>

                        <!-- 手机端排序和视图控制 -->
                        <div class="mobile-controls d-md-none">
                            <div class="mobile-controls-row">
                                <!-- 排序控制 -->
                                <div class="filter-group mobile-sort-group">
                                    <label for="sort-select-mobile" class="filter-label">排序方式</label>
                                    <select id="sort-select-mobile" class="sort-select">
                                        <option value="default">默认排序</option>
                                        <option value="price_asc">价格从低到高</option>
                                        <option value="price_desc">价格从高到低</option>
                                        <option value="name_asc">名称A-Z</option>
                                        <option value="name_desc">名称Z-A</option>
                                        <option value="id_desc">最新商品</option>
                                    </select>
                                </div>

                                <!-- 视图控制 -->
                                <div class="filter-group mobile-view-group">
                                    <label class="filter-label">显示模式</label>
                                    <div class="view-controls mobile-view-controls">
                                        <button id="grid-view-mobile" class="view-btn active" title="网格视图">
                                            <i class="bi bi-grid-3x3-gap"></i>
                                            <span class="view-text">网格</span>
                                        </button>
                                        <button id="list-view-mobile" class="view-btn" title="列表视图">
                                            <i class="bi bi-list-ul"></i>
                                            <span class="view-text">列表</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="filter-controls-right">
                        <!-- 手机端快速筛选 -->
                        <div class="mobile-quick-filters d-md-none">
                            <button class="quick-filter-btn active" data-category="">全部</button>
                            <?php
                            // 获取所有分类并设置简短显示名称
                            $allCategories = \app\service\ShopConfigService::getAllCategories();
                            $shortNames = [
                                1 => '高级时装',
                                2 => '玛可时装',
                                3 => '辅助道具',
                                4 => '宠物',
                                5 => '坐骑',
                                6 => '料理',
                                7 => '积分',
                                8 => '其他'
                            ];
                            foreach ($allCategories as $id => $fullName):
                                $displayName = $shortNames[$id] ?? $fullName;
                            ?>
                            <button class="quick-filter-btn" data-category="<?= $id ?>"><?= $displayName ?></button>
                            <?php
                            endforeach;
                            ?>
                        </div>

                        <!-- 显示模式控件 -->
                        <div class="filter-group">
                            <label class="filter-label d-none d-md-block">显示模式</label>
                            <div class="view-controls">
                                <button id="grid-view" class="view-btn active" title="网格视图">
                                    <i class="bi bi-grid-3x3-gap"></i>
                                    <span class="view-text">网格</span>
                                </button>
                                <button id="list-view" class="view-btn" title="列表视图">
                                    <i class="bi bi-list-ul"></i>
                                    <span class="view-text">列表</span>
                                </button>
                            </div>
                        </div>
                        <!-- 桌面端排序控制 -->
                        <div class="sort-controls d-none d-md-block">
                            <div class="filter-group">
                                <label for="sort-select" class="filter-label">排序方式</label>
                                <select id="sort-select" class="sort-select">
                                    <option value="default">默认排序</option>
                                    <option value="price_asc">价格从低到高</option>
                                    <option value="price_desc">价格从高到低</option>
                                    <option value="name_asc">名称A-Z</option>
                                    <option value="name_desc">名称Z-A</option>
                                    <option value="id_desc">最新商品</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- 商品展示区域 -->
        <section class="products-section">
            <div class="products-header">
                <h2 class="products-title">商品列表</h2>
                <div class="products-info">
                    <span id="products-count"></span>
                </div>
            </div>
            
            <!-- 加载状态 -->
            <div id="loading-indicator" class="loading-indicator">
                <div class="loading-spinner"></div>
                <p>正在加载商品...</p>
            </div>
            
            <!-- 商品网格 -->
            <div id="products-grid" class="products-grid" style="display: none;">
                <!-- 商品项目将通过JavaScript动态加载 -->
            </div>
            
            <!-- 商品列表 -->
            <div id="products-list" class="products-list" style="display: none;">
                <!-- 商品项目将通过JavaScript动态加载 -->
            </div>
            
            <!-- 分页控件 -->
            <div id="pagination" class="pagination-container" style="display: none;">
                <!-- 分页组件将通过JavaScript动态生成 -->
            </div>
        </section>
    </main>
    
    <!-- 购买确认模态框 (Bootstrap 5.3) -->
    <div class="modal fade" id="purchaseModal" tabindex="-1" aria-labelledby="purchaseModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="purchaseModalLabel">确认购买</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="item-preview d-flex align-items-start mb-3">
                        <img id="modal-item-icon" src="" alt="商品图标" class="item-icon me-3" style="width: 64px; height: 64px; object-fit: cover;">
                        <div class="item-details flex-grow-1">
                            <h6 id="modal-item-name" class="mb-2">商品名称</h6>
                            <p id="modal-item-description" class="text-muted mb-2" style="font-size: 0.9em;">商品描述</p>
                            <div class="item-meta d-flex flex-column gap-1">
                                <div class="item-price">
                                    <span id="modal-item-price" class="fw-bold">价格</span>
                                    <span id="modal-item-currency" class="text-primary ms-1">货币类型</span>
                                </div>
                                <div id="modal-item-quantity" class="item-quantity text-muted" style="font-size: 0.85em; display: none;">
                                    <i class="bi bi-box me-1"></i>
                                    <span>实际获得数量：</span>
                                    <span id="modal-item-quantity-value" class="fw-semibold text-success">1</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="purchase-options">
                        <div class="row align-items-center mb-3">
                            <div class="col-sm-4">
                                <label for="quantity-input" class="form-label mb-0">购买数量：</label>
                            </div>
                            <div class="col-sm-8">
                                <div class="input-group" style="max-width: 150px;">
                                    <button class="btn btn-outline-secondary" type="button" onclick="decreaseQuantity()">-</button>
                                    <input type="number" class="form-control text-center" id="quantity-input" value="1" min="1" max="99">
                                    <button class="btn btn-outline-secondary" type="button" onclick="increaseQuantity()">+</button>
                                </div>
                            </div>
                        </div>
                        <div class="total-price p-3 bg-light rounded">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="fw-bold">总价：</span>
                                <div>
                                    <span id="total-price" class="fw-bold fs-5">0</span>
                                    <span id="total-currency" class="text-primary ms-1">货币</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="confirmPurchase()">确认购买</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 购买历史模态框 -->
    <div class="modal fade" id="history-modal" tabindex="-1" aria-labelledby="historyModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-xl modal-dialog-scrollable">
            <div class="modal-content border-0 shadow-lg rounded-4">
                <div class="modal-header bg-gradient bg-info text-white border-0 rounded-top-4">
                    <h1 class="modal-title fs-4 fw-bold d-flex align-items-center" id="historyModalLabel">
                        <i class="bi bi-clock-history me-2 fs-3"></i>
                        购买历史
                    </h1>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body p-4">
                    <div id="history-loading" class="text-center py-5">
                        <div class="spinner-border text-primary mb-3" style="width: 3rem; height: 3rem;" role="status">
                            <span class="visually-hidden">正在加载购买历史...</span>
                        </div>
                        <h5 class="text-muted">正在加载购买历史...</h5>
                    </div>
                    <div id="history-content" style="display: none;">
                        <!-- 筛选器 -->
                        <div class="row g-3 mb-4 p-3 bg-light rounded-3">
                            <div class="col-md-6">
                                <label for="history-search" class="form-label fw-semibold">
                                    <i class="bi bi-search me-1"></i>搜索商品
                                </label>
                                <input type="text" id="history-search" placeholder="输入商品名称..." class="form-control">
                            </div>
                            <div class="col-md-4">
                                <label for="history-status-filter" class="form-label fw-semibold">
                                    <i class="bi bi-check-circle me-1"></i>订单状态
                                </label>
                                <select id="history-status-filter" class="form-select">
                                    <option value="">全部状态</option>
                                    <option value="success">✅ 成功</option>
                                    <option value="pending">⏳ 处理中</option>
                                    <option value="failed">❌ 失败</option>
                                </select>
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <button onclick="filterHistory()" class="btn btn-primary w-100">
                                    <i class="bi bi-search me-1"></i>筛选
                                </button>
                            </div>
                        </div>

                        <!-- 购买历史列表 -->
                        <div id="history-list">
                            <!-- 购买历史项目将通过JavaScript动态加载 -->
                        </div>

                        <!-- 分页 -->
                        <div id="history-pagination" class="d-flex justify-content-center mt-4">
                            <!-- 分页按钮将通过JavaScript动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 充值模态框 -->
    <div class="modal fade" id="rechargeModal" tabindex="-1" aria-labelledby="rechargeModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content border-0 shadow-lg rounded-4">
                <div class="modal-header bg-gradient bg-success text-white border-0 rounded-top-4">
                    <h1 class="modal-title fs-4 fw-bold d-flex align-items-center" id="rechargeModalLabel">
                        <i class="bi bi-credit-card-2-front me-2 fs-3"></i>
                        <span id="rechargeModalTitle">充值</span>
                    </h1>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body p-4">
                    <!-- 充值表单 -->
                    <div id="rechargeFormContainer">
                        <form id="rechargeForm" class="needs-validation" novalidate>
                            <!-- 充值类型选择 -->
                            <div class="mb-4">
                                <label for="currencyType" class="form-label fw-semibold fs-5 mb-3">
                                    <i class="bi bi-currency-exchange me-2 text-success"></i>选择充值类型
                                </label>
                                <div class="row g-3">
                                    <div class="col-6">
                                        <input type="radio" class="btn-check" name="currencyType" id="c_coin" value="c_coin" required>
                                        <label class="btn btn-outline-success w-100 p-3" for="c_coin">
                                            <i class="bi bi-currency-bitcoin fs-2 d-block mb-2"></i>
                                            <strong>C币</strong>
                                            <small class="d-block text-muted">1元 = 1000 C币</small>
                                        </label>
                                    </div>
                                    <div class="col-6">
                                        <input type="radio" class="btn-check" name="currencyType" id="point" value="point" required>
                                        <label class="btn btn-outline-warning w-100 p-3" for="point">
                                            <i class="bi bi-coin fs-2 d-block mb-2"></i>
                                            <strong>泡点</strong>
                                            <small class="d-block text-muted">1元 = 10000 泡点</small>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- 支付方式选择 -->
                            <div class="mb-4">
                                <label class="form-label fw-semibold fs-5 mb-3">
                                    <i class="bi bi-credit-card me-2 text-primary"></i>选择支付方式
                                </label>
                                <div class="row g-3">
                                    <div class="col-6">
                                        <input type="radio" class="btn-check" name="payType" id="alipay" value="alipay" checked required>
                                        <label class="btn btn-outline-primary w-100 p-3" for="alipay">
                                            <i class="bi bi-alipay fs-2 d-block mb-2"></i>
                                            <strong>支付宝</strong>
                                        </label>
                                    </div>
                                    <div class="col-6">
                                        <input type="radio" class="btn-check" name="payType" id="wxpay" value="wxpay" required>
                                        <label class="btn btn-outline-success w-100 p-3" for="wxpay">
                                            <i class="bi bi-wechat fs-2 d-block mb-2"></i>
                                            <strong>微信支付</strong>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- 充值金额 -->
                            <div class="mb-4">
                                <label for="rechargeAmount" class="form-label fw-semibold fs-5 mb-3">
                                    <i class="bi bi-cash-coin me-2 text-danger"></i>充值金额
                                </label>
                                <div class="input-group input-group-lg">
                                    <span class="input-group-text bg-light">¥</span>
                                    <input type="number" class="form-control" id="rechargeAmount"
                                           min="0.01" max="10000" step="0.01" placeholder="请输入充值金额" required>
                                    <span class="input-group-text bg-light">元</span>
                                </div>
                                <div class="form-text mt-2">
                                    <div id="exchangeInfo" class="text-muted fs-6"></div>
                                    <small class="text-muted">充值范围：0.01 - 10,000 元</small>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- 支付二维码容器 -->
                    <div id="paymentQRContainer" style="display: none;">
                        <div class="text-center">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="payment-qr-section">
                                        <h6 class="mb-3">
                                            <i class="bi bi-qr-code"></i>
                                            扫码支付
                                        </h6>
                                        <div class="qr-code-container">
                                            <img id="paymentQRCode" src="" alt="支付二维码" class="img-fluid" style="max-width: 200px;">
                                        </div>
                                        <div class="mt-3">
                                            <small class="text-muted">请使用<span id="payMethodText"></span>扫描二维码完成支付</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="payment-info-section">
                                        <h6 class="mb-3">
                                            <i class="bi bi-info-circle"></i>
                                            订单信息
                                        </h6>
                                        <table class="table table-borderless table-sm">
                                            <tr>
                                                <td><strong>订单号：</strong></td>
                                                <td><small id="orderNumber">-</small></td>
                                            </tr>
                                            <tr>
                                                <td><strong>充值类型：</strong></td>
                                                <td id="orderCurrencyType">-</td>
                                            </tr>
                                            <tr>
                                                <td><strong>充值金额：</strong></td>
                                                <td><span class="text-danger fw-bold">¥<span id="orderAmount">0</span></span></td>
                                            </tr>
                                            <tr>
                                                <td><strong>获得数量：</strong></td>
                                                <td><span class="text-success fw-bold" id="orderRechargeAmount">0</span></td>
                                            </tr>
                                            <tr>
                                                <td><strong>支付状态：</strong></td>
                                                <td>
                                                    <span class="badge bg-warning" id="paymentStatus">等待支付</span>
                                                </td>
                                            </tr>
                                        </table>

                                        <div class="mt-3">
                                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="checkPaymentStatus()">
                                                <i class="bi bi-arrow-clockwise"></i>
                                                刷新状态
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 支付成功提示 -->
                    <div id="paymentSuccessContainer" style="display: none;">
                        <div class="text-center py-4">
                            <div class="text-success mb-3">
                                <i class="bi bi-check-circle-fill" style="font-size: 3rem;"></i>
                            </div>
                            <h5 class="text-success">支付成功！</h5>
                            <p class="text-muted">您的充值已完成，余额将在几分钟内到账</p>
                            <button type="button" class="btn btn-success" onclick="location.reload()">
                                <i class="bi bi-arrow-clockwise"></i>
                                刷新页面
                            </button>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <div id="rechargeFormFooter">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" onclick="submitRecharge()">
                            <i class="bi bi-credit-card"></i>
                            立即充值
                        </button>
                    </div>
                    <div id="paymentQRFooter" style="display: none;">
                        <button type="button" class="btn btn-secondary" onclick="resetRechargeModal()">
                            <i class="bi bi-arrow-left"></i>
                            重新充值
                        </button>
                        <button type="button" class="btn btn-outline-primary" data-bs-dismiss="modal">关闭</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 充值记录模态框 -->
    <div class="modal fade" id="rechargeHistoryModal" tabindex="-1" aria-labelledby="rechargeHistoryModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-xl modal-dialog-scrollable">
            <div class="modal-content border-0 shadow-lg rounded-4">
                <div class="modal-header bg-gradient bg-primary text-white border-0 rounded-top-4">
                    <h1 class="modal-title fs-4 fw-bold d-flex align-items-center" id="rechargeHistoryModalLabel">
                        <i class="bi bi-receipt-cutoff me-2 fs-3"></i>
                        充值记录
                    </h1>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body p-4">
                    <!-- 筛选器 -->
                    <div class="row g-3 mb-4 p-3 bg-light rounded-3">
                        <div class="col-md-3 col-6">
                            <label for="historyStatusFilter" class="form-label fw-semibold small">
                                <i class="bi bi-check-circle me-1"></i>订单状态
                            </label>
                            <select class="form-select" id="historyStatusFilter">
                                <option value="">全部状态</option>
                                <option value="0">⏳ 待支付</option>
                                <option value="1">✅ 已完成</option>
                                <option value="2">❌ 已失败</option>
                                <option value="3">🚫 已取消</option>
                            </select>
                        </div>
                        <div class="col-md-3 col-6">
                            <label for="historyCurrencyFilter" class="form-label fw-semibold small">
                                <i class="bi bi-currency-exchange me-1"></i>充值类型
                            </label>
                            <select class="form-select" id="historyCurrencyFilter">
                                <option value="">全部类型</option>
                                <option value="c_coin">🪙 C币</option>
                                <option value="point">💰 泡点</option>
                            </select>
                        </div>
                        <div class="col-md-3 col-6">
                            <label for="historyPayTypeFilter" class="form-label fw-semibold small">
                                <i class="bi bi-credit-card me-1"></i>支付方式
                            </label>
                            <select class="form-select" id="historyPayTypeFilter">
                                <option value="">全部支付方式</option>
                                <option value="alipay">💙 支付宝</option>
                                <option value="wxpay">💚 微信支付</option>
                            </select>
                        </div>
                        <div class="col-md-3 col-6 d-flex align-items-end">
                            <button type="button" class="btn btn-primary w-100" onclick="filterRechargeHistory()">
                                <i class="bi bi-funnel me-1"></i>筛选
                            </button>
                        </div>
                    </div>

                    <!-- 顶部分页 -->
                    <div id="rechargeTopPagination" class="top-pagination" style="display: none;">
                        <div class="d-flex justify-content-between align-items-center mb-3 p-3 bg-white border rounded">
                            <div class="d-flex align-items-center">
                                <span class="me-3 text-muted small">共 <span id="rechargeTotalRecords">0</span> 条记录</span>
                                <label class="form-label me-2 mb-0 small">每页显示:</label>
                                <select class="form-select form-select-sm" id="rechargeTopPageSizeFilter" style="width: auto;">
                                    <option value="10">10条</option>
                                    <option value="20" selected>20条</option>
                                    <option value="50">50条</option>
                                </select>
                            </div>
                            <nav>
                                <ul class="pagination pagination-sm mb-0" id="rechargeTopPaginationList">
                                    <!-- 分页按钮将通过JavaScript动态生成 -->
                                </ul>
                            </nav>
                        </div>
                    </div>

                    <!-- 充值记录内容 -->
                    <div id="rechargeHistoryContent">
                        <div class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <div class="mt-2">正在加载充值记录...</div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <div class="d-flex justify-content-between align-items-center w-100">
                        <div class="text-muted">
                            <small>显示第 <span id="historyPageStart">0</span> - <span id="historyPageEnd">0</span> 条，共 <span id="historyTotalCount">0</span> 条记录</small>
                        </div>
                        <div>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 卡片系统模态框 -->
    <div class="modal fade" id="cardModal" tabindex="-1" aria-labelledby="cardModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content border-0 shadow-lg rounded-4">
                <div class="modal-header bg-gradient bg-warning text-dark border-0 rounded-top-4">
                    <h1 class="modal-title fs-4 fw-bold d-flex align-items-center" id="cardModalLabel">
                        <i class="bi bi-calendar-check me-2 fs-3"></i>
                        特惠卡购买
                    </h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4">
                    <!-- 卡片列表视图 -->
                    <div id="cardListView">
                        <div id="cardContent">
                            <div class="text-center">
                                <div class="spinner-border text-warning" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                                <p class="mt-2 text-muted">正在加载卡片信息...</p>
                            </div>
                        </div>
                    </div>

                    <!-- 支付二维码视图 -->
                    <div id="cardPaymentView" style="display: none;">
                        <div class="text-center">
                            <div class="mb-4">
                                <h5 id="cardPaymentTitle" class="text-primary mb-3">扫码支付</h5>
                                <div class="card border-primary">
                                    <div class="card-body">
                                        <div class="row align-items-center">
                                            <div class="col-md-6 text-center">
                                                <img id="cardPaymentQR" src="" alt="支付二维码" class="img-fluid" style="max-width: 200px;">
                                                <p class="text-muted mt-2 small">请使用支付宝扫码支付</p>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="payment-info">
                                                    <div class="mb-3">
                                                        <label class="form-label text-muted small">订单号</label>
                                                        <div id="cardOrderNumber" class="fw-bold">-</div>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label class="form-label text-muted small">商品</label>
                                                        <div id="cardProductName" class="fw-bold">-</div>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label class="form-label text-muted small">金额</label>
                                                        <div id="cardPaymentAmount" class="fw-bold text-primary">¥0.00</div>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label class="form-label text-muted small">状态</label>
                                                        <div>
                                                            <span id="cardPaymentStatus" class="badge bg-warning">等待支付</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 支付成功视图 -->
                    <div id="cardSuccessView" style="display: none;">
                        <div class="text-center py-4">
                            <div class="text-success mb-3">
                                <i class="bi bi-check-circle-fill" style="font-size: 3rem;"></i>
                            </div>
                            <h5 class="text-success">购买成功！</h5>
                            <p class="text-muted">您的特惠卡已激活，即时奖励已发放</p>
                            <div id="cardRewardInfo" class="alert alert-success mt-3" style="display: none;">
                                <!-- 奖励信息将在这里显示 -->
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <div id="cardListFooter">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    </div>
                    <div id="cardPaymentFooter" style="display: none;">
                        <button type="button" class="btn btn-secondary" onclick="backToCardList()">返回</button>
                        <button type="button" class="btn btn-outline-primary" onclick="refreshCardPaymentStatus()">刷新状态</button>
                    </div>
                    <div id="cardSuccessFooter" style="display: none;">
                        <button type="button" class="btn btn-success" onclick="backToCardList()">继续购买</button>
                        <button type="button" class="btn btn-primary" data-bs-dismiss="modal">完成</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 确认购买模态框 -->
    <div class="modal fade" id="cardConfirmModal" tabindex="-1" aria-labelledby="cardConfirmModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content border-0 shadow-lg rounded-4">
                <div class="modal-header bg-gradient bg-primary text-white border-0 rounded-top-4">
                    <h1 class="modal-title fs-5 fw-bold d-flex align-items-center" id="cardConfirmModalLabel">
                        <i class="bi bi-credit-card me-2"></i>
                        确认购买
                    </h1>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4">
                    <div class="text-center mb-4">
                        <div class="display-1 text-primary mb-3">
                            <i id="confirmCardIcon" class="bi bi-calendar-week"></i>
                        </div>
                        <h5 id="confirmCardName" class="mb-3">周卡</h5>
                        <p id="confirmCardDescription" class="text-muted mb-4">购买即送500C币+神秘道具，连续7天每天可领取100C币+每日礼包</p>
                    </div>

                    <div class="card border-primary">
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="border-end">
                                        <div class="text-success fs-4">
                                            <i class="bi bi-gift"></i>
                                        </div>
                                        <small class="text-muted">即时奖励</small>
                                        <div id="confirmInstantReward" class="fw-bold small">500C币 + 道具x1</div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="text-info fs-4">
                                        <i class="bi bi-calendar-day"></i>
                                    </div>
                                    <small class="text-muted">每日奖励</small>
                                    <div id="confirmDailyReward" class="fw-bold small">100C币 + 道具x1</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-warning mt-3">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            <div>
                                <strong>支付金额：<span id="confirmCardPrice" class="text-primary">¥6.00</span></strong>
                                <div class="small text-muted">购买后立即生效，请确认支付</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-1"></i>取消
                    </button>
                    <button type="button" class="btn btn-primary" id="confirmPurchaseBtn" onclick="confirmCardPurchase()">
                        <i class="bi bi-credit-card me-1"></i>确认购买
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 卡片领取模态框 -->
    <div class="modal fade" id="cardClaimModal" tabindex="-1" aria-labelledby="cardClaimModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content border-0 shadow-lg rounded-4">
                <div class="modal-header bg-gradient bg-success text-white border-0 rounded-top-4">
                    <h1 class="modal-title fs-5 fw-bold d-flex align-items-center" id="cardClaimModalLabel">
                        <i class="bi bi-gift me-2"></i>
                        每日奖励
                    </h1>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4">
                    <div id="cardClaimContent">
                        <!-- 领取内容将通过JavaScript动态生成 -->
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-success" id="claimRewardBtn" onclick="claimDailyReward()">
                        <i class="bi bi-gift me-1"></i>领取奖励
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 通知容器 -->
    <div id="notification-container" class="notification-container">
        <!-- 通知消息将通过JavaScript动态添加 -->
    </div>
{/block}

{block name="script"}
    <script src="/static/js/utils.js"></script>
    <script src="/static/js/components.js"></script>
    <!-- 通用组件库 -->
    <script src="/static/js/bootstrap-components.js"></script>
    <script src="/static/js/shop.js"></script>
    <script>
        // 商城配置
        window.ShopConfig = <?php echo \app\service\ShopConfigService::getFrontendConfigJson(); ?>;

        // 配置助手对象
        window.ShopConfig.getCategoryName = function(id) {
            return this.categories[id] || '其他';
        };

        window.ShopConfig.getCurrencyName = function(id) {
            // 获取货币名称
            return this.currencies[id] ? this.currencies[id].name : (this.currency_types[id] ? this.currency_types[id].name : '未知');
        };

        window.ShopConfig.getCurrencyColorClass = function(id) {
            return this.currencies[id] ? this.currencies[id].colorClass : (this.currency_types[id] ? this.currency_types[id].color_class : 'bg-secondary');
        };

        // 强制清除缓存并重新加载配置
        if (window.ShopConfig && (!window.ShopConfig.currency_types || !window.ShopConfig.currency_types['3'] || window.ShopConfig.currency_types['3'].name === 'C币')) {
            // 检测到配置可能过期，强制重新加载
            setTimeout(async () => {
                await reloadShopConfig();
                updateCurrencyLabels();
            }, 500);
        }
    </script>
    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化通用组件
            initBootstrapComponents();

            // 初始化手机端菜单
            initMobileMenu();

            // 初始化商城组件
            initPageComponents();

            // 检查管理员权限
            checkAdminAccess();

            // 检查ShopConfig配置

            // 更新货币标签（强制重新加载配置）
            updateCurrencyLabels(true);

            // 监听配置更新
            setupConfigUpdateListener();

            // 加载商品列表
            ShopApp.loadItems();

            // 绑定事件监听器
            bindEventListeners();
        });

        // 初始化Bootstrap通用组件
        function initBootstrapComponents() {
            // 开始初始化Bootstrap组件

            // 检查BootstrapComponents是否已加载
            if (typeof BootstrapComponents === 'undefined') {
                console.error('BootstrapComponents 未加载');
                return;
            }

            // 手动初始化分类下拉框
            const categorySelect = document.getElementById('category-filter');
            if (categorySelect) {
                // 初始化分类下拉框
                try {
                    window.categoryDropdown = new BootstrapComponents.CustomDropdown(categorySelect, {
                        searchable: false,
                        placeholder: '全部分类'
                    });
                    // 分类下拉框初始化成功
                } catch (error) {
                    console.error('分类下拉框初始化失败:', error);
                }
            } else {
                console.error('找不到分类下拉框元素');
            }

            // 手动初始化桌面端排序下拉框
            const sortSelect = document.getElementById('sort-select');
            if (sortSelect) {
                // 初始化桌面端排序下拉框
                try {
                    window.sortDropdown = new BootstrapComponents.CustomDropdown(sortSelect, {
                        searchable: false,
                        placeholder: '默认排序'
                    });
                    // 桌面端排序下拉框初始化成功
                } catch (error) {
                    console.error('桌面端排序下拉框初始化失败:', error);
                }
            } else {
                console.error('找不到桌面端排序下拉框元素');
            }

            // 手动初始化手机端排序下拉框
            const sortSelectMobile = document.getElementById('sort-select-mobile');
            if (sortSelectMobile) {
                // 初始化手机端排序下拉框
                try {
                    window.sortDropdownMobile = new BootstrapComponents.CustomDropdown(sortSelectMobile, {
                        searchable: false,
                        placeholder: '默认排序'
                    });
                    // 手机端排序下拉框初始化成功
                } catch (error) {
                    console.error('手机端排序下拉框初始化失败:', error);
                }
            } else {
                console.error('找不到手机端排序下拉框元素');
            }

            // 初始化分页组件（将在ShopApp中调用）
            window.shopPagination = null;
            // Bootstrap组件初始化完成
        }

        // 检查管理员权限
        async function checkAdminAccess() {
            try {
                const response = await fetch('/admin/checkAdmin');
                const data = await response.json();

                // 管理员权限检查

                if (data.code === 200 && data.data.is_admin) {
                    // 显示桌面端管理员菜单
                    const desktopAdminMenu = document.getElementById('admin-menu-desktop');
                    if (desktopAdminMenu) {
                        desktopAdminMenu.style.display = 'block';
                    }

                    // 显示手机端管理员菜单
                    const mobileAdminMenu = document.getElementById('admin-menu-mobile');
                    if (mobileAdminMenu) {
                        mobileAdminMenu.style.display = 'flex';
                    }

                    // 管理菜单已显示
                }
            } catch (error) {
                console.error('检查管理员权限失败:', error);
            }
        }

        // 初始化手机端菜单
        function initMobileMenu() {
            const menuToggle = document.getElementById('mobile-menu-toggle');
            const mobileMenu = document.getElementById('mobile-menu');
            const menuOverlay = document.getElementById('mobile-menu-overlay');

            if (menuToggle && mobileMenu && menuOverlay) {
                // 切换菜单
                function toggleMenu() {
                    const isActive = mobileMenu.classList.contains('active');

                    if (isActive) {
                        closeMenu();
                    } else {
                        openMenu();
                    }
                }

                // 打开菜单
                function openMenu() {
                    mobileMenu.classList.add('active');
                    menuOverlay.classList.add('active');
                    menuToggle.querySelector('i').className = 'bi bi-x';
                    document.body.style.overflow = 'hidden'; // 防止背景滚动
                }

                // 关闭菜单
                function closeMenu() {
                    mobileMenu.classList.remove('active');
                    menuOverlay.classList.remove('active');
                    menuToggle.querySelector('i').className = 'bi bi-list';
                    document.body.style.overflow = ''; // 恢复滚动
                }

                // 菜单切换按钮点击事件
                menuToggle.addEventListener('click', function(e) {
                    e.stopPropagation();
                    toggleMenu();
                });

                // 点击遮罩关闭菜单
                menuOverlay.addEventListener('click', function() {
                    closeMenu();
                });

                // 点击菜单项后关闭菜单
                const menuItems = mobileMenu.querySelectorAll('.mobile-nav-item, .mobile-logout-btn');
                menuItems.forEach(item => {
                    item.addEventListener('click', function() {
                        closeMenu();
                    });
                });

                // ESC键关闭菜单
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Escape' && mobileMenu.classList.contains('active')) {
                        closeMenu();
                    }
                });
            }
        }
        
        // 绑定事件监听器
        function bindEventListeners() {
            // 搜索按钮
            document.getElementById('search-btn').addEventListener('click', function() {
                ShopApp.clearFilterCache(); // 清除筛选相关缓存
                ShopApp.handleFilter();
            });

            // 搜索输入框回车事件
            document.getElementById('search-input').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    ShopApp.clearFilterCache(); // 清除筛选相关缓存
                    ShopApp.handleFilter();
                }
            });

            // 分类筛选
            document.getElementById('category-filter').addEventListener('change', function() {
                ShopApp.clearFilterCache(); // 清除筛选相关缓存
                ShopApp.handleFilter();
            });

            // 桌面端排序选择
            const sortSelectDesktop = document.getElementById('sort-select');
            if (sortSelectDesktop) {
                sortSelectDesktop.addEventListener('change', function() {
                    // 同步到手机端排序下拉框
                    const sortSelectMobile = document.getElementById('sort-select-mobile');
                    if (sortSelectMobile) {
                        sortSelectMobile.value = this.value;
                    }
                    ShopApp.clearFilterCache(); // 清除筛选相关缓存
                    ShopApp.handleFilter();
                });
            }

            // 手机端排序选择
            const sortSelectMobile = document.getElementById('sort-select-mobile');
            if (sortSelectMobile) {
                sortSelectMobile.addEventListener('change', function() {
                    // 同步到桌面端排序下拉框
                    const sortSelectDesktop = document.getElementById('sort-select');
                    if (sortSelectDesktop) {
                        sortSelectDesktop.value = this.value;
                    }
                    ShopApp.clearFilterCache(); // 清除筛选相关缓存
                    ShopApp.handleFilter();
                });
            }

            // 手机端快速筛选按钮
            document.querySelectorAll('.quick-filter-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    // 更新按钮状态
                    document.querySelectorAll('.quick-filter-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');

                    // 同步到分类下拉框
                    const categoryValue = this.getAttribute('data-category');
                    const categorySelect = document.getElementById('category-filter');
                    if (categorySelect) {
                        categorySelect.value = categoryValue;
                        // 触发change事件
                        categorySelect.dispatchEvent(new Event('change', { bubbles: true }));
                    }

                    ShopApp.clearFilterCache();
                    ShopApp.handleFilter();
                });
            });
            
            // 手机端视图切换按钮
            const gridViewMobile = document.getElementById('grid-view-mobile');
            const listViewMobile = document.getElementById('list-view-mobile');

            if (gridViewMobile) {
                gridViewMobile.addEventListener('click', () => switchView('grid'));
            }
            if (listViewMobile) {
                listViewMobile.addEventListener('click', () => switchView('list'));
            }

            // 视图切换由ShopApp.js中的ViewToggle组件处理
        }
        
        // 切换视图模式（由ShopApp统一处理）
        function switchView(viewType, skipReload = false) {
            // 设置桌面端按钮状态
            const gridView = document.getElementById('grid-view');
            const listView = document.getElementById('list-view');

            // 设置手机端按钮状态
            const gridViewMobile = document.getElementById('grid-view-mobile');
            const listViewMobile = document.getElementById('list-view-mobile');

            if (viewType === 'grid') {
                // 桌面端
                if (gridView) gridView.classList.add('active');
                if (listView) listView.classList.remove('active');
                // 手机端
                if (gridViewMobile) gridViewMobile.classList.add('active');
                if (listViewMobile) listViewMobile.classList.remove('active');
            } else {
                // 桌面端
                if (listView) listView.classList.add('active');
                if (gridView) gridView.classList.remove('active');
                // 手机端
                if (listViewMobile) listViewMobile.classList.add('active');
                if (gridViewMobile) gridViewMobile.classList.remove('active');
            }

            // 调用ShopApp的视图切换方法
            if (window.ShopApp) {
                ShopApp.currentView = viewType;
                if (!skipReload) {
                    ShopApp.renderItems();
                }
                // 保存视图偏好
                storageUtils.local.set('shop_view_mode', viewType);
            }
        }
        
        // 购买历史相关函数
        function showPurchaseHistory() {
            // 使用Bootstrap模态框
            const modal = new bootstrap.Modal(document.getElementById('history-modal'));
            modal.show();

            // 加载购买历史数据
            loadPurchaseHistory();
        }

        function closePurchaseHistory() {
            // Bootstrap模态框会自动处理关闭
            const modal = bootstrap.Modal.getInstance(document.getElementById('history-modal'));
            if (modal) {
                modal.hide();
            }
        }

        async function loadPurchaseHistory(page = 1) {
            const loadingElement = document.getElementById('history-loading');
            const contentElement = document.getElementById('history-content');
            const listElement = document.getElementById('history-list');

            try {
                // 显示加载状态
                if (loadingElement) loadingElement.style.display = 'block';
                if (contentElement) contentElement.style.display = 'none';

                // 构建请求参数
                const params = new URLSearchParams({
                    page: page,
                    limit: 10
                });

                // 发送请求
                const response = await fetch(`/purchase-history/getHistoryList?${params}`);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();

                if (data.success && data.code === 0) {
                    renderPurchaseHistory(data.data);
                } else {
                    throw new Error(data.message || '加载失败');
                }

            } catch (error) {
                console.error('加载购买历史失败:', error);

                // 显示错误状态
                if (listElement) {
                    listElement.innerHTML = `
                        <div class="alert alert-danger">
                            <h5><i class="bi bi-exclamation-triangle"></i> 加载失败</h5>
                            <p>${error.message || '网络错误，请稍后重试'}</p>
                            <button class="btn btn-outline-danger" onclick="loadPurchaseHistory()">
                                <i class="bi bi-arrow-clockwise"></i> 重新加载
                            </button>
                        </div>
                    `;
                }
                if (contentElement) contentElement.style.display = 'block';
            } finally {
                // 隐藏加载状态
                if (loadingElement) loadingElement.style.display = 'none';
            }
        }

        function renderPurchaseHistory(data) {
            const contentElement = document.getElementById('history-content');
            const listElement = document.getElementById('history-list');

            if (!data || !data.list || data.list.length === 0) {
                if (listElement) {
                    listElement.innerHTML = `
                        <div class="text-center py-5">
                            <i class="bi bi-inbox display-1 text-muted"></i>
                            <h5 class="mt-3 text-muted">暂无购买记录</h5>
                            <p class="text-muted">您还没有购买过任何商品</p>
                        </div>
                    `;
                }
                if (contentElement) contentElement.style.display = 'block';
                return;
            }

            if (contentElement) contentElement.style.display = 'block';

            // 渲染历史记录列表
            let html = '';
            data.list.forEach(order => {
                const statusClass = getStatusClass(order.status);
                const statusText = getStatusText(order.status);
                const currencyIcon = getCurrencyIcon(order.currency_type);
                const currencyText = getCurrencyText(order.currency_type);

                // 统一布局，通过CSS控制桌面端和手机端显示
                html += `
                    <div class="history-item">
                        <div class="history-card">
                            <!-- 手机端顶部区域 -->
                            <div class="mobile-history-header d-md-none">
                                <div class="mobile-item-info">
                                    <h6 class="item-name mb-1">${order.item_name}</h6>
                                    <small class="text-muted">订单号: ${order.order_no || order.id}</small>
                                </div>
                                <div class="mobile-status">
                                    <span class="status-badge ${statusClass}">${statusText}</span>
                                </div>
                            </div>

                            <!-- 手机端主体区域 -->
                            <div class="mobile-history-body d-md-none">
                                <div class="item-icon-container">
                                    <img src="${order.item_icon || '/static/img/default.png'}"
                                         alt="${order.item_name}"
                                         class="item-icon"
                                         onerror="this.src='/static/img/default.png'">
                                </div>
                                <div class="mobile-details">
                                    <div class="quantity-info">数量: ${order.quantity}</div>
                                </div>
                                <div class="mobile-price">
                                    <div class="currency-info">
                                        <i class="bi ${currencyIcon}"></i>
                                        <span class="currency-name">${currencyText}</span>
                                    </div>
                                    <div class="price-amount">${formatNumber(order.total_price)}</div>
                                </div>
                            </div>

                            <!-- 手机端底部区域 -->
                            <div class="mobile-history-footer d-md-none">
                                <span class="purchase-time">${formatDateTime(order.create_time)}</span>
                            </div>

                            <!-- 桌面端布局 -->
                            <div class="desktop-layout">
                                <!-- 商品图标 -->
                                <div class="item-icon-container">
                                    <img src="${order.item_icon || '/static/img/default.png'}"
                                         alt="${order.item_name}"
                                         class="item-icon"
                                         onerror="this.src='/static/img/default.png'">
                                </div>

                                <!-- 商品信息 -->
                                <div class="item-details">
                                    <div class="item-header">
                                        <h6 class="item-name">${order.item_name}</h6>
                                        <span class="status-badge ${statusClass}">${statusText}</span>
                                    </div>
                                    <div class="item-meta">
                                        <span class="order-no">订单号: ${order.order_no || order.id}</span>
                                        <span class="quantity">数量: ${order.quantity}</span>
                                    </div>
                                </div>

                                <!-- 价格信息 -->
                                <div class="price-section">
                                    <div class="currency-info">
                                        <i class="bi ${currencyIcon}"></i>
                                        <span class="currency-name">${currencyText}</span>
                                    </div>
                                    <div class="price-amount">${formatNumber(order.total_price || order.price || 0)}</div>
                                </div>

                                <!-- 时间信息 -->
                                <div class="time-section">
                                    <small class="purchase-time">${formatDateTime(order.create_time)}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });

            listElement.innerHTML = html;

            // 渲染分页
            renderHistoryPagination(data);
        }

        function renderHistoryPagination(data) {
            const paginationElement = document.getElementById('history-pagination');
            if (!paginationElement || !data.pages || data.pages <= 1) {
                if (paginationElement) paginationElement.innerHTML = '';
                return;
            }

            // 检测是否为手机端
            const isMobile = window.innerWidth <= 768;
            const maxPages = isMobile ? 1 : 2; // 手机端只显示当前页前后1页，桌面端显示前后2页

            let html = '<nav><ul class="pagination justify-content-center">';

            // 上一页
            if (data.page > 1) {
                html += `<li class="page-item"><a class="page-link" href="#" onclick="loadPurchaseHistory(${data.page - 1})">上一页</a></li>`;
            }

            // 页码
            const startPage = Math.max(1, data.page - maxPages);
            const endPage = Math.min(data.pages, data.page + maxPages);

            for (let i = startPage; i <= endPage; i++) {
                const activeClass = i === data.page ? 'active' : '';
                html += `<li class="page-item ${activeClass}"><a class="page-link" href="#" onclick="loadPurchaseHistory(${i})">${i}</a></li>`;
            }

            // 下一页
            if (data.page < data.pages) {
                html += `<li class="page-item"><a class="page-link" href="#" onclick="loadPurchaseHistory(${data.page + 1})">下一页</a></li>`;
            }

            html += '</ul></nav>';
            paginationElement.innerHTML = html;
        }

        function getStatusClass(status) {
            // 根据PurchaseOrder模型的状态常量
            switch (parseInt(status)) {
                case 1: return 'bg-warning';    // 待处理
                case 2: return 'bg-info';       // 处理中
                case 3: return 'bg-success';    // 已完成
                case 4: return 'bg-danger';     // 失败
                case 5: return 'bg-secondary';  // 已取消
                case 6: return 'bg-primary';    // 已退款
                default: return 'bg-secondary';
            }
        }

        function getStatusText(status) {
            // 根据PurchaseOrder模型的状态常量
            switch (parseInt(status)) {
                case 1: return '待处理';
                case 2: return '处理中';
                case 3: return '已完成';
                case 4: return '失败';
                case 5: return '已取消';
                case 6: return '已退款';
                default: return '未知状态';
            }
        }

        function getCurrencyText(type) {
            // 获取货币名称

            // 直接从配置对象获取
            if (window.ShopConfig) {
                const typeStr = String(type);

                // 尝试从currency_types获取
                if (window.ShopConfig.currency_types && window.ShopConfig.currency_types[typeStr]) {
                    const currencyName = window.ShopConfig.currency_types[typeStr].name;
                    // 从currency_types获取货币名称
                    return currencyName;
                }

                // 尝试从currencies获取
                if (window.ShopConfig.currencies && window.ShopConfig.currencies[typeStr]) {
                    const currencyName = window.ShopConfig.currencies[typeStr].name;
                    // 从currencies获取货币名称
                    return currencyName;
                }

                // 使用getCurrencyName方法
                if (window.ShopConfig.getCurrencyName) {
                    const numericType = parseInt(type);
                    if (!isNaN(numericType)) {
                        const currencyName = window.ShopConfig.getCurrencyName(numericType);
                        // 从getCurrencyName方法获取货币名称
                        if (currencyName && currencyName !== '未知') {
                            return currencyName;
                        }
                    }
                }
            }

            // 使用降级配置
            // 降级到本地配置
            switch (String(type)) {
                case '1': return '泡点';
                case '2': return '积分';
                case '3': return 'C币';
                // 兼容旧的字符串格式
                case 'coin': return '泡点';
                case 'silver': return '积分';
                case 'c_coin': return 'C币';
                case 'POINT': return '泡点';
                case 'SCORE': return '积分';
                case 'COIN': return 'C币';
                default: return '未知货币';
            }
        }

        // 强制重新加载ShopConfig配置
        async function reloadShopConfig() {
            try {
                // 开始重新加载ShopConfig配置
                const response = await fetch('/api/getShopConfig?t=' + Date.now());
                const result = await response.json();

                if (result.success && result.data) {
                    // 更新全局配置
                    Object.assign(window.ShopConfig, result.data);
                    // ShopConfig配置已重新加载

                    // 重新设置助手方法
                    window.ShopConfig.getCurrencyName = function(id) {
                        return this.currencies[id] ? this.currencies[id].name : (this.currency_types[id] ? this.currency_types[id].name : '未知');
                    };

                    return true;
                } else {
                    console.error('API返回失败:', result);
                }
            } catch (error) {
                console.error('重新加载ShopConfig失败:', error);
            }
            return false;
        }

        // 更新页面上所有货币标签的函数
        async function updateCurrencyLabels(forceReload = false) {
            // 如果需要强制重新加载配置
            if (forceReload) {
                await reloadShopConfig();
            }
            // 更新桌面端余额显示标签
            const desktopLabels = [
                { containerId: 'shop-coin-balance', type: '1' },
                { containerId: 'shop-silver-balance', type: '2' },
                { containerId: 'shop-c-coin-balance', type: '3' }
            ];

            desktopLabels.forEach(item => {
                const balanceElement = document.getElementById(item.containerId);
                if (balanceElement) {
                    const labelElement = balanceElement.parentElement.querySelector('.text-muted.small');
                    if (labelElement) {
                        labelElement.textContent = getCurrencyText(item.type);
                    }
                }
            });

            // 更新移动端余额显示标签
            const mobileLabels = [
                { containerId: 'shop-coin-balance-mobile', type: '1' },
                { containerId: 'shop-silver-balance-mobile', type: '2' },
                { containerId: 'shop-c-coin-balance-mobile', type: '3' }
            ];

            mobileLabels.forEach(item => {
                const balanceElement = document.getElementById(item.containerId);
                if (balanceElement) {
                    const labelElement = balanceElement.parentElement.querySelector('.text-white-50.small');
                    if (labelElement) {
                        labelElement.textContent = getCurrencyText(item.type);
                    }
                }
            });

            // 更新充值页面的货币标签
            updateRechargeLabels();

            // 更新历史记录筛选器
            updateHistoryFilterLabels();
        }

        // 更新充值页面标签
        function updateRechargeLabels() {
            // 更新C币充值标签
            const cCoinLabel = document.querySelector('label[for="c_coin"] strong');
            if (cCoinLabel) {
                cCoinLabel.textContent = getCurrencyText('3');
            }

            // 更新泡点充值标签
            const pointLabel = document.querySelector('label[for="point"] strong');
            if (pointLabel) {
                pointLabel.textContent = getCurrencyText('1');
            }
        }

        // 更新历史记录筛选器标签
        function updateHistoryFilterLabels() {
            const cCoinOption = document.querySelector('#historyCurrencyFilter option[value="c_coin"]');
            if (cCoinOption) {
                cCoinOption.textContent = `🪙 ${getCurrencyText('3')}`;
            }

            const pointOption = document.querySelector('#historyCurrencyFilter option[value="point"]');
            if (pointOption) {
                pointOption.textContent = `💰 ${getCurrencyText('1')}`;
            }
        }

        // 设置配置更新监听器
        function setupConfigUpdateListener() {
            // 监听localStorage变化
            window.addEventListener('storage', function(e) {
                if (e.key === 'shop_config_update') {
                    // 检测到配置更新，重新加载货币标签
                    updateCurrencyLabels(true); // 强制重新加载配置
                }
            });

            // 监听自定义事件（同页面内的更新）
            window.addEventListener('shopConfigUpdated', function() {
                // 检测到同页面配置更新，重新加载货币标签
                updateCurrencyLabels(true); // 强制重新加载配置
            });
        }

        // 测试重新加载配置的函数
        async function testReloadConfig() {
            // 手动测试重新加载配置

            const success = await reloadShopConfig();
            if (success) {
                // 重新加载后的ShopConfig
                updateCurrencyLabels();
                alert('配置重新加载成功！货币3的名称：' + getCurrencyText('3'));
            } else {
                alert('配置重新加载失败！');
            }
        }

        function getCurrencyIcon(type) {
            // 数据库中存储的是数字类型
            switch (String(type)) {
                case '1': return 'bi-coin';
                case '2': return 'bi-star-fill';
                case '3': return 'bi-currency-bitcoin';
                // 兼容旧的字符串格式
                case 'coin': return 'bi-coin';
                case 'silver': return 'bi-star-fill';
                case 'c_coin': return 'bi-currency-bitcoin';
                case 'POINT': return 'bi-coin';
                case 'SCORE': return 'bi-star-fill';
                case 'COIN': return 'bi-currency-bitcoin';
                default: return 'bi-question-circle';
            }
        }

        function formatNumber(num) {
            return new Intl.NumberFormat('zh-CN').format(num);
        }

        function formatDateTime(dateStr) {
            const date = new Date(dateStr);
            return date.toLocaleString('zh-CN');
        }

        // 充值相关函数
        function showRechargeModal() {
            const modal = new bootstrap.Modal(document.getElementById('rechargeModal'));
            modal.show();

            // 绑定充值类型和金额变化事件
            bindRechargeEvents();
        }

        // 存储从API获取的充值比例
        let rechargeRates = {};

        // 加载充值比例
        async function loadRechargeRates() {
            try {
                const response = await fetch('/recharge/rates');
                const data = await response.json();

                if (data.code === 200) {
                    rechargeRates = data.data;
                    // 充值比例加载成功
                } else {
                    console.error('获取充值比例失败:', data.message);
                    // 使用默认比例
                    rechargeRates = {
                        exchange_rates: {
                            c_coin: 100,
                            point: 1000
                        }
                    };
                }
            } catch (error) {
                console.error('加载充值比例失败:', error);
                // 使用默认比例
                rechargeRates = {
                    exchange_rates: {
                        c_coin: 100,
                        point: 1000
                    }
                };
            }
        }

        function bindRechargeEvents() {
            const rechargeAmount = document.getElementById('rechargeAmount');
            const exchangeInfo = document.getElementById('exchangeInfo');

            if (!rechargeAmount || !exchangeInfo) {
                console.error('充值表单元素未找到');
                return;
            }

            // 加载充值比例
            loadRechargeRates();

            function updateExchangeInfo() {
                const currencyTypeRadio = document.querySelector('input[name="currencyType"]:checked');
                const type = currencyTypeRadio ? currencyTypeRadio.value : '';
                const amount = parseFloat(rechargeAmount.value) || 0;

                if (type && amount > 0 && rechargeRates.exchange_rates) {
                    let rate = 0;
                    let currencyName = '';
                    let icon = '';

                    if (type === 'c_coin') {
                        rate = rechargeRates.exchange_rates.c_coin || 100; // 从API获取C币比例
                        currencyName = getCurrencyText('3');
                        icon = '🪙';
                    } else if (type === 'point') {
                        rate = rechargeRates.exchange_rates.point || 1000; // 从API获取泡点比例
                        currencyName = getCurrencyText('1');
                        icon = '💰';
                    }

                    const totalCurrency = Math.floor(amount * rate);
                    exchangeInfo.innerHTML = `<strong class="text-success">${icon} 将获得 ${totalCurrency.toLocaleString()} ${currencyName}</strong>`;
                } else {
                    exchangeInfo.innerHTML = '';
                }
            }

            // 绑定事件到radio按钮和金额输入框
            const currencyRadios = document.querySelectorAll('input[name="currencyType"]');
            if (currencyRadios.length > 0) {
                currencyRadios.forEach(radio => {
                    radio.addEventListener('change', updateExchangeInfo);
                });
            }

            if (rechargeAmount) {
                rechargeAmount.addEventListener('input', updateExchangeInfo);
            }
        }

        async function submitRecharge() {
            // 确保充值比例已加载
            if (!rechargeRates.exchange_rates) {
                // 充值比例未加载，正在重新加载
                await loadRechargeRates();
            }

            // 获取选中的充值类型
            const currencyTypeRadio = document.querySelector('input[name="currencyType"]:checked');
            const currencyType = currencyTypeRadio ? currencyTypeRadio.value : '';

            const amount = parseFloat(document.getElementById('rechargeAmount').value);

            // 获取选中的支付方式
            const payTypeRadio = document.querySelector('input[name="payType"]:checked');
            const payType = payTypeRadio ? payTypeRadio.value : '';

            if (!currencyType || !amount || amount <= 0 || !payType) {
                alert('请填写完整的充值信息');
                return;
            }

            if (amount < 0.01 || amount > 10000) {
                alert('充值金额必须在0.01-10000元之间');
                return;
            }

            // 显示加载状态
            const submitBtn = document.querySelector('#rechargeFormFooter .btn-primary');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 创建订单中...';
            submitBtn.disabled = true;

            try {
                const response = await fetch('/recharge/create', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        currency_type: currencyType,
                        amount: amount,
                        pay_type: payType
                    })
                });

                const data = await response.json();

                if (data.code === 200 && data.data) {
                    // 显示二维码支付界面
                    showPaymentQR(data.data, currencyType, amount, payType);
                } else {
                    alert(data.message || '创建充值订单失败');
                    // 恢复按钮状态
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }
            } catch (error) {
                console.error('充值失败:', error);
                alert('网络错误，请稍后重试');
                // 恢复按钮状态
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }
        }

        function showPaymentQR(paymentData, currencyType, amount, payType, productName = '') {
            // 记录当前支付类型
            window.currentPaymentType = currencyType;

            // 隐藏表单，显示二维码
            document.getElementById('rechargeFormContainer').style.display = 'none';
            document.getElementById('rechargeFormFooter').style.display = 'none';
            document.getElementById('paymentQRContainer').style.display = 'block';
            document.getElementById('paymentQRFooter').style.display = 'flex';

            // 更新模态框标题
            const title = currencyType === 'card_purchase' ? '卡片购买 - 扫码支付' : '扫码支付';
            document.getElementById('rechargeModalTitle').textContent = title;

            // 设置二维码

            let qrCodeUrl = '';
            if (paymentData.qrcode_img) {
                qrCodeUrl = paymentData.qrcode_img;
            } else if (paymentData.qrcode_url) {
                qrCodeUrl = paymentData.qrcode_url;
            } else if (paymentData.qrcode) {
                qrCodeUrl = paymentData.qrcode;
            } else if (paymentData.img) {
                qrCodeUrl = paymentData.img;
            }

            if (qrCodeUrl) {
                document.getElementById('paymentQRCode').src = qrCodeUrl;
                // QR Code URL set
            } else {
                console.error('No QR code URL found in payment data');
                alert('未能获取支付二维码，请重试');
                return;
            }

            // 设置支付方式文本
            const payMethodText = payType === 'alipay' ? '支付宝' : '微信';
            document.getElementById('payMethodText').textContent = payMethodText;

            // 设置订单信息
            document.getElementById('orderNumber').textContent = paymentData.out_trade_no || paymentData.trade_no || '-';

            if (currencyType === 'card_purchase') {
                // 卡片购买
                document.getElementById('orderCurrencyType').textContent = productName || '特惠卡';
                document.getElementById('orderAmount').textContent = amount;
                document.getElementById('orderRechargeAmount').textContent = '购买成功后立即生效';
            } else {
                // 普通充值
                document.getElementById('orderCurrencyType').textContent = currencyType === 'c_coin' ? getCurrencyText('3') : getCurrencyText('1');
                document.getElementById('orderAmount').textContent = amount;

                // 计算获得数量（使用从API获取的正确比例）
                let rate = 0;
                if (rechargeRates.exchange_rates) {
                    rate = currencyType === 'c_coin' ?
                        (rechargeRates.exchange_rates.c_coin || 1) :
                        (rechargeRates.exchange_rates.point || 100);
                } else {
                    // 如果没有获取到比例，使用默认值
                    rate = currencyType === 'c_coin' ? 1 : 100;
                }
                const rechargeAmount = Math.floor(amount * rate);
                document.getElementById('orderRechargeAmount').textContent = rechargeAmount + (currencyType === 'c_coin' ? ` ${getCurrencyText('3')}` : ` ${getCurrencyText('1')}`);
            }

            // 保存订单号用于状态查询
            window.currentOrderNo = paymentData.out_trade_no || paymentData.trade_no;

            // 开始轮询支付状态
            startPaymentStatusPolling();
        }

        function resetRechargeModal() {
            // 重置表单显示
            document.getElementById('rechargeFormContainer').style.display = 'block';
            document.getElementById('rechargeFormFooter').style.display = 'flex';
            document.getElementById('paymentQRContainer').style.display = 'none';
            document.getElementById('paymentQRFooter').style.display = 'none';
            document.getElementById('paymentSuccessContainer').style.display = 'none';

            // 重置模态框标题
            document.getElementById('rechargeModalTitle').textContent = '充值';

            // 重置表单
            document.getElementById('rechargeForm').reset();
            document.getElementById('exchangeInfo').textContent = '';

            // 重置按钮状态
            const submitBtn = document.querySelector('#rechargeFormFooter .btn-primary');
            submitBtn.innerHTML = '<i class="bi bi-credit-card"></i> 立即充值';
            submitBtn.disabled = false;

            // 停止轮询
            if (window.paymentPollingTimer) {
                clearInterval(window.paymentPollingTimer);
                window.paymentPollingTimer = null;
            }
        }

        function startPaymentStatusPolling() {
            if (window.paymentPollingTimer) {
                clearInterval(window.paymentPollingTimer);
            }

            window.paymentPollingTimer = setInterval(async () => {
                await checkPaymentStatus();
            }, 3000); // 每3秒检查一次

            // 30分钟后停止轮询
            setTimeout(() => {
                if (window.paymentPollingTimer) {
                    clearInterval(window.paymentPollingTimer);
                    window.paymentPollingTimer = null;
                }
            }, 30 * 60 * 1000);
        }

        async function checkPaymentStatus() {
            if (!window.currentOrderNo) return;

            try {
                const response = await fetch(`/recharge/query?out_trade_no=${window.currentOrderNo}`);
                const data = await response.json();

                if (data.code === 200 && data.data) {
                    const status = data.data.status;
                    const statusElement = document.getElementById('paymentStatus');

                    if (status === 1) {
                        // 支付成功
                        statusElement.className = 'badge bg-success';
                        statusElement.textContent = '支付成功';

                        // 停止轮询
                        if (window.paymentPollingTimer) {
                            clearInterval(window.paymentPollingTimer);
                            window.paymentPollingTimer = null;
                        }

                        // 显示成功页面
                        setTimeout(() => {
                            showPaymentSuccess();
                        }, 1000);

                    } else if (status === 2) {
                        // 支付失败
                        statusElement.className = 'badge bg-danger';
                        statusElement.textContent = '支付失败';

                        // 停止轮询
                        if (window.paymentPollingTimer) {
                            clearInterval(window.paymentPollingTimer);
                            window.paymentPollingTimer = null;
                        }
                    } else if (status === 3) {
                        // 已取消
                        statusElement.className = 'badge bg-secondary';
                        statusElement.textContent = '已取消';

                        // 停止轮询
                        if (window.paymentPollingTimer) {
                            clearInterval(window.paymentPollingTimer);
                            window.paymentPollingTimer = null;
                        }
                    }
                }
            } catch (error) {
                console.error('查询支付状态失败:', error);
            }
        }

        function showPaymentSuccess() {
            // 隐藏二维码，显示成功页面
            document.getElementById('paymentQRContainer').style.display = 'none';
            document.getElementById('paymentQRFooter').style.display = 'none';
            document.getElementById('paymentSuccessContainer').style.display = 'block';

            // 更新模态框标题
            document.getElementById('rechargeModalTitle').textContent = '支付成功';

            // 如果是卡片购买，刷新卡片状态
            if (window.currentPaymentType === 'card_purchase') {
                // 延迟刷新卡片信息，确保后端处理完成
                setTimeout(async () => {
                    try {
                        await loadCardInfo();
                        showNotification('卡片购买成功！即时奖励已发放', 'success');
                    } catch (error) {
                        console.error('刷新卡片信息失败:', error);
                    }
                }, 2000);
            }
        }

        function showRechargeHistory() {
            const modal = new bootstrap.Modal(document.getElementById('rechargeHistoryModal'));
            modal.show();
            loadRechargeHistory();
        }

        async function loadRechargeHistory(page = 1, statusFilter = '', currencyFilter = '', payTypeFilter = '') {
            const content = document.getElementById('rechargeHistoryContent');

            try {
                content.innerHTML = `
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <div class="mt-2">正在加载充值记录...</div>
                    </div>
                `;

                // 构建查询参数
                const params = new URLSearchParams({
                    page: page,
                    limit: 10
                });

                if (statusFilter) params.append('status', statusFilter);
                if (currencyFilter) params.append('currency_type', currencyFilter);
                if (payTypeFilter) params.append('pay_type', payTypeFilter);

                const response = await fetch(`/recharge/history?${params}`);
                const data = await response.json();

                if (data.code === 200 && data.data) {
                    renderRechargeHistory(data.data);
                    renderRechargeTopPagination(data.data);
                } else {
                    throw new Error(data.message || '加载失败');
                }
            } catch (error) {
                console.error('加载充值记录失败:', error);
                content.innerHTML = `
                    <div class="text-center py-4 text-danger">
                        <i class="bi bi-exclamation-triangle"></i>
                        <div class="mt-2">${error.message || '加载失败，请稍后重试'}</div>
                        <button class="btn btn-outline-primary mt-2" onclick="loadRechargeHistory()">
                            <i class="bi bi-arrow-clockwise"></i> 重新加载
                        </button>
                    </div>
                `;
            }
        }

        function renderRechargeHistory(data) {
            const content = document.getElementById('rechargeHistoryContent');

            if (!data.list || data.list.length === 0) {
                content.innerHTML = `
                    <div class="text-center py-5">
                        <i class="bi bi-inbox display-1 text-muted"></i>
                        <h5 class="mt-3 text-muted">暂无充值记录</h5>
                        <p class="text-muted">您还没有进行过充值</p>
                    </div>
                `;
                return;
            }

            // 桌面端表格布局 - Bootstrap 5.3 设计
            let desktopHtml = '<div class="d-none d-md-block"><div class="table-responsive">';
            desktopHtml += '<table class="table table-hover table-striped">';
            desktopHtml += `
                <thead class="table-dark">
                    <tr>
                        <th scope="col" style="min-width: 220px;">订单信息</th>
                        <th scope="col" style="min-width: 110px;">充值类型</th>
                        <th scope="col" style="min-width: 110px;">支付方式</th>
                        <th scope="col" style="min-width: 140px;">金额/数量</th>
                        <th scope="col" style="min-width: 90px;">状态</th>
                        <th scope="col" style="min-width: 180px;">时间</th>
                        <th scope="col" style="min-width: 100px;">操作</th>
                    </tr>
                </thead>
                <tbody>
            `;

            data.list.forEach(record => {
                const statusClass = getRechargeStatusClass(record.status);
                const statusText = getRechargeStatusText(record.status);
                const currencyName = record.currency_type === 'c_coin' ? getCurrencyText('3') : getCurrencyText('1');
                const payTypeName = record.pay_type === 'alipay' ? '支付宝' : '微信支付';
                const currencyIcon = record.currency_type === 'c_coin' ? 'bi-currency-bitcoin text-success' : 'bi-coin text-warning';
                const payIcon = record.pay_type === 'alipay' ? 'bi-alipay text-primary' : 'bi-wechat text-success';

                desktopHtml += `
                    <tr>
                        <td>
                            <div class="d-flex flex-column">
                                <div class="fw-bold text-primary">
                                    <i class="bi bi-receipt me-1"></i>
                                    <span class="font-monospace">${record.out_trade_no}</span>
                                </div>
                                <small class="text-muted">
                                    <i class="bi bi-clock me-1"></i>
                                    ${formatDateTime(record.create_time)}
                                </small>
                            </div>
                        </td>
                        <td class="text-center">
                            <div class="d-flex align-items-center justify-content-center">
                                <i class="${currencyIcon} fs-3 me-2"></i>
                                <span class="fw-bold">${currencyName}</span>
                            </div>
                        </td>
                        <td class="text-center">
                            <div class="d-flex align-items-center justify-content-center">
                                <i class="${payIcon} fs-3 me-2"></i>
                                <span class="fw-bold">${payTypeName}</span>
                            </div>
                        </td>
                        <td class="text-center">
                            <div class="d-flex flex-column align-items-center">
                                <div class="text-danger fw-bold fs-4">¥${record.money || record.amount}</div>
                                <div class="text-success fw-bold">+${record.recharge_amount} ${currencyName}</div>
                            </div>
                        </td>
                        <td class="text-center">
                            <span class="badge ${statusClass} fs-6 px-3 py-2">${statusText}</span>
                        </td>
                        <td>
                            <div class="d-flex flex-column">
                                <div class="fw-bold">创建时间</div>
                                <small class="text-muted">${formatDateTime(record.create_time)}</small>
                                ${record.pay_time ? `
                                <div class="fw-bold text-success mt-1">支付时间</div>
                                <small class="text-success">${formatDateTime(record.pay_time)}</small>
                                ` : ''}
                            </div>
                        </td>
                        <td class="text-center">
                            ${record.status === 0 ? `
                            <div class="btn-group-vertical" role="group">
                                <button type="button" class="btn btn-outline-primary btn-sm mb-1" onclick="queryOrderStatus('${record.out_trade_no}')" title="刷新状态">
                                    <i class="bi bi-arrow-clockwise me-1"></i>刷新
                                </button>
                                <button type="button" class="btn btn-outline-danger btn-sm" onclick="cancelOrder('${record.out_trade_no}')" title="取消订单">
                                    <i class="bi bi-x-circle me-1"></i>取消
                                </button>
                            </div>
                            ` : '<span class="text-muted">-</span>'}
                        </td>
                    </tr>
                `;
            });

            desktopHtml += '</tbody></table></div></div>';

            // 手机端卡片
            let mobileHtml = '<div class="d-block d-md-none">';

            data.list.forEach(record => {
                const statusClass = getRechargeStatusClass(record.status);
                const statusText = getRechargeStatusText(record.status);
                const currencyName = record.currency_type === 'c_coin' ? getCurrencyText('3') : getCurrencyText('1');
                const payTypeName = record.pay_type === 'alipay' ? '支付宝' : '微信支付';
                const currencyIcon = record.currency_type === 'c_coin' ? 'bi-currency-bitcoin text-success' : 'bi-coin text-warning';
                const payIcon = record.pay_type === 'alipay' ? 'bi-alipay text-primary' : 'bi-wechat text-success';

                mobileHtml += `
                    <div class="recharge-record-card">
                        <div class="recharge-record-header">
                            <div>
                                <h6 class="mb-1">
                                    <i class="${currencyIcon}"></i>
                                    ${currencyName}充值
                                </h6>
                                <small class="text-muted font-monospace">${record.out_trade_no}</small>
                            </div>
                            <span class="badge ${statusClass}">${statusText}</span>
                        </div>

                        <div class="recharge-record-details">
                            <div class="recharge-record-detail">
                                <div class="recharge-record-detail-label">支付方式</div>
                                <div class="recharge-record-detail-value">
                                    <i class="${payIcon}"></i>
                                    ${payTypeName}
                                </div>
                            </div>
                            <div class="recharge-record-detail">
                                <div class="recharge-record-detail-label">支付金额</div>
                                <div class="recharge-record-detail-value text-danger fw-bold">¥${record.money || record.amount}</div>
                            </div>
                            <div class="recharge-record-detail">
                                <div class="recharge-record-detail-label">获得数量</div>
                                <div class="recharge-record-detail-value text-success fw-bold">${record.recharge_amount}</div>
                            </div>
                            <div class="recharge-record-detail">
                                <div class="recharge-record-detail-label">创建时间</div>
                                <div class="recharge-record-detail-value">${formatDateTime(record.create_time)}</div>
                            </div>
                            ${record.pay_time ? `
                            <div class="recharge-record-detail">
                                <div class="recharge-record-detail-label">支付时间</div>
                                <div class="recharge-record-detail-value">${formatDateTime(record.pay_time)}</div>
                            </div>
                            ` : ''}
                        </div>
                    </div>
                `;
            });

            mobileHtml += '</div>';

            // 分页
            let paginationHtml = '';
            if (data.pages > 1) {
                // 检测是否为手机端
                const isMobile = window.innerWidth <= 768;
                const maxPages = isMobile ? 1 : 2; // 手机端只显示当前页前后1页

                paginationHtml += '<nav class="mt-3"><ul class="pagination justify-content-center">';

                if (data.page > 1) {
                    paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="loadRechargeHistory(${data.page - 1})">上一页</a></li>`;
                }

                const startPage = Math.max(1, data.page - maxPages);
                const endPage = Math.min(data.pages, data.page + maxPages);

                for (let i = startPage; i <= endPage; i++) {
                    const activeClass = i === data.page ? 'active' : '';
                    paginationHtml += `<li class="page-item ${activeClass}"><a class="page-link" href="#" onclick="loadRechargeHistory(${i})">${i}</a></li>`;
                }

                if (data.page < data.pages) {
                    paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="loadRechargeHistory(${data.page + 1})">下一页</a></li>`;
                }

                paginationHtml += '</ul></nav>';
            }

            // 更新分页信息
            updateRechargeHistoryPagination(data);

            content.innerHTML = desktopHtml + mobileHtml + paginationHtml;

            // 启动待支付订单的超时监控
            if (window.OrderTimeoutManager && data.list) {
                data.list.forEach(record => {
                    if (record.status === 0) { // 待支付状态
                        window.OrderTimeoutManager.startMonitoring(record.out_trade_no, record.create_time);
                    }
                });
            }
        }

        // 渲染充值记录顶部分页
        function renderRechargeTopPagination(data) {
            const topPaginationContainer = document.getElementById('rechargeTopPagination');
            const topPaginationList = document.getElementById('rechargeTopPaginationList');
            const topTotalRecords = document.getElementById('rechargeTotalRecords');

            if (!topPaginationContainer || !topPaginationList) return;

            // 更新总记录数
            if (topTotalRecords) {
                topTotalRecords.textContent = data.total || 0;
            }

            if (!data.pages || data.pages <= 1) {
                topPaginationContainer.style.display = 'none';
                return;
            }

            topPaginationContainer.style.display = 'block';

            let paginationHtml = '';

            // 上一页
            if (data.page > 1) {
                paginationHtml += `
                    <li class="page-item">
                        <a class="page-link" href="#" onclick="loadRechargeHistory(${data.page - 1})">
                            <i class="bi bi-chevron-left"></i>
                        </a>
                    </li>
                `;
            }

            // 页码
            const isMobile = window.innerWidth <= 768;
            const maxPages = isMobile ? 1 : 2; // 手机端只显示当前页前后1页

            const startPage = Math.max(1, data.page - maxPages);
            const endPage = Math.min(data.pages, data.page + maxPages);

            for (let i = startPage; i <= endPage; i++) {
                const activeClass = i === data.page ? 'active' : '';
                paginationHtml += `
                    <li class="page-item ${activeClass}">
                        <a class="page-link" href="#" onclick="loadRechargeHistory(${i})">${i}</a>
                    </li>
                `;
            }

            // 下一页
            if (data.page < data.pages) {
                paginationHtml += `
                    <li class="page-item">
                        <a class="page-link" href="#" onclick="loadRechargeHistory(${data.page + 1})">
                            <i class="bi bi-chevron-right"></i>
                        </a>
                    </li>
                `;
            }

            topPaginationList.innerHTML = paginationHtml;
        }

        function updateRechargeHistoryPagination(data) {
            const start = (data.page - 1) * data.limit + 1;
            const end = Math.min(data.page * data.limit, data.total);

            document.getElementById('historyPageStart').textContent = data.total > 0 ? start : 0;
            document.getElementById('historyPageEnd').textContent = end;
            document.getElementById('historyTotalCount').textContent = data.total;
        }

        function filterRechargeHistory() {
            const statusFilter = document.getElementById('historyStatusFilter').value;
            const currencyFilter = document.getElementById('historyCurrencyFilter').value;
            const payTypeFilter = document.getElementById('historyPayTypeFilter').value;

            // 重新加载数据，传入筛选参数
            loadRechargeHistory(1, statusFilter, currencyFilter, payTypeFilter);
        }

        async function queryOrderStatus(outTradeNo) {
            try {
                const response = await fetch(`/recharge/query?out_trade_no=${outTradeNo}`);
                const data = await response.json();

                if (data.code === 200 && data.data) {
                    // 刷新充值记录列表
                    loadRechargeHistory();

                    if (data.data.status === 1) {
                        showNotification('订单状态已更新：支付成功！', 'success');
                        // 停止监控已支付的订单
                        if (window.OrderTimeoutManager) {
                            window.OrderTimeoutManager.stopMonitoring(outTradeNo);
                        }
                    } else if (data.data.status === 2) {
                        showNotification('订单状态已更新：支付失败', 'error');
                        // 停止监控已失败的订单
                        if (window.OrderTimeoutManager) {
                            window.OrderTimeoutManager.stopMonitoring(outTradeNo);
                        }
                    } else if (data.data.status === 3) {
                        showNotification('订单状态已更新：已取消', 'info');
                        // 停止监控已取消的订单
                        if (window.OrderTimeoutManager) {
                            window.OrderTimeoutManager.stopMonitoring(outTradeNo);
                        }
                    } else {
                        showNotification('订单状态已刷新', 'info');
                    }
                } else {
                    showNotification(data.message || '查询失败', 'error');
                }
            } catch (error) {
                console.error('查询订单状态失败:', error);
                showNotification('网络错误，请稍后重试', 'error');
            }
        }

        async function cancelOrder(outTradeNo) {
            if (!confirm('确定要取消这个订单吗？')) {
                return;
            }

            try {
                const response = await fetch('/recharge/cancel', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        out_trade_no: outTradeNo
                    })
                });

                const data = await response.json();

                if (data.code === 200) {
                    showNotification('订单已取消', 'success');
                    // 刷新充值记录列表
                    loadRechargeHistory();
                } else {
                    showNotification(data.message || '取消失败', 'error');
                }
            } catch (error) {
                console.error('取消订单失败:', error);
                showNotification('网络错误，请稍后重试', 'error');
            }
        }

        function getRechargeStatusClass(status) {
            switch (parseInt(status)) {
                case 1: return 'bg-warning';    // 待支付
                case 2: return 'bg-success';    // 已完成
                case 3: return 'bg-danger';     // 已失败
                case 4: return 'bg-secondary';  // 已取消
                default: return 'bg-secondary';
            }
        }

        function getRechargeStatusText(status) {
            switch (parseInt(status)) {
                case 0: return '待支付';
                case 1: return '已完成';
                case 2: return '已失败';
                case 3: return '已取消';
                default: return '未知状态';
            }
        }

        function formatDateTime(dateTimeStr) {
            if (!dateTimeStr) return '-';

            try {
                const date = new Date(dateTimeStr);
                const now = new Date();
                const diff = now - date;
                const days = Math.floor(diff / (1000 * 60 * 60 * 24));

                if (days === 0) {
                    // 今天，显示时间
                    return date.toLocaleTimeString('zh-CN', {
                        hour: '2-digit',
                        minute: '2-digit'
                    });
                } else if (days === 1) {
                    // 昨天
                    return '昨天 ' + date.toLocaleTimeString('zh-CN', {
                        hour: '2-digit',
                        minute: '2-digit'
                    });
                } else if (days < 7) {
                    // 一周内，显示星期
                    const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
                    return '周' + weekdays[date.getDay()] + ' ' + date.toLocaleTimeString('zh-CN', {
                        hour: '2-digit',
                        minute: '2-digit'
                    });
                } else {
                    // 超过一周，显示完整日期
                    return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', {
                        hour: '2-digit',
                        minute: '2-digit'
                    });
                }
            } catch (error) {
                return dateTimeStr;
            }
        }

        // 页面特定的初始化代码
        function initPageComponents() {
            // 商城页面组件初始化完成

            // 恢复用户视图偏好（初始化时不重新加载数据）
            const savedViewMode = storageUtils.local.get('shop_view_mode', 'grid');
            switchView(savedViewMode, true); // 传入true跳过数据重新加载
        }

        // 订单超时管理器
        window.OrderTimeoutManager = {
            timers: new Map(),
            timeoutMinutes: 30, // 订单超时时间（分钟）

            // 开始监控订单超时
            startMonitoring(outTradeNo, createTime) {
                // 清除已存在的定时器
                this.stopMonitoring(outTradeNo);

                const createTimestamp = new Date(createTime).getTime();
                const timeoutTimestamp = createTimestamp + (this.timeoutMinutes * 60 * 1000);
                const now = Date.now();

                // 如果订单已经超时
                if (now >= timeoutTimestamp) {
                    this.handleTimeout(outTradeNo);
                    return;
                }

                // 计算剩余时间
                const remainingTime = timeoutTimestamp - now;

                // 设置超时定时器
                const timer = setTimeout(() => {
                    this.handleTimeout(outTradeNo);
                }, remainingTime);

                this.timers.set(outTradeNo, {
                    timer: timer,
                    timeoutTime: timeoutTimestamp
                });

                // 订单超时监控
            },

            // 停止监控订单
            stopMonitoring(outTradeNo) {
                const timerInfo = this.timers.get(outTradeNo);
                if (timerInfo) {
                    clearTimeout(timerInfo.timer);
                    this.timers.delete(outTradeNo);
                }
            },

            // 处理订单超时
            async handleTimeout(outTradeNo) {
                // 订单已超时

                try {
                    // 自动取消超时订单
                    const response = await fetch('/recharge/cancel', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            out_trade_no: outTradeNo
                        })
                    });

                    const data = await response.json();

                    if (data.code === 200) {
                        showNotification('订单已超时自动取消', 'info');
                        // 刷新充值记录列表
                        if (typeof loadRechargeHistory === 'function') {
                            loadRechargeHistory();
                        }
                    } else {
                        console.error('自动取消订单失败:', data.message);
                    }
                } catch (error) {
                    console.error('自动取消订单异常:', error);
                }

                // 清除定时器
                this.stopMonitoring(outTradeNo);
            },

            // 获取订单剩余时间（分钟）
            getRemainingTime(outTradeNo) {
                const timerInfo = this.timers.get(outTradeNo);
                if (!timerInfo) return 0;

                const now = Date.now();
                const remaining = timerInfo.timeoutTime - now;
                return Math.max(0, Math.round(remaining / 1000 / 60));
            },

            // 清除所有定时器
            clearAll() {
                this.timers.forEach((timerInfo, outTradeNo) => {
                    clearTimeout(timerInfo.timer);
                });
                this.timers.clear();
            }
        };

        // 页面卸载时清除所有定时器
        window.addEventListener('beforeunload', function() {
            if (window.OrderTimeoutManager) {
                window.OrderTimeoutManager.clearAll();
            }

            // 清除卡片支付轮询定时器
            if (window.cardPaymentPollingTimer) {
                clearInterval(window.cardPaymentPollingTimer);
                window.cardPaymentPollingTimer = null;
            }
        });

        // ==================== 卡片系统功能 ====================

        // 卡片系统全局变量
        let cardData = {
            cards: [],
            userStatus: {}
        };

        // 显示卡片模态框
        async function showCardModal() {
            const modal = new bootstrap.Modal(document.getElementById('cardModal'));
            modal.show();

            // 加载卡片信息
            await loadCardInfo();
        }

        // 加载卡片信息
        async function loadCardInfo() {
            try {
                const response = await fetch('/card/info', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const data = await response.json();

                if (data.code === 0) {
                    cardData = {
                        cards: data.data.cards || [],
                        userStatus: data.data.user_status || {}
                    };
                    renderCardContent();
                } else {
                    document.getElementById('cardContent').innerHTML = `
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            ${data.message || '加载卡片信息失败'}
                        </div>
                    `;
                }
            } catch (error) {
                console.error('加载卡片信息失败:', error);
                // 重置数据
                cardData = {
                    cards: [],
                    userStatus: {}
                };
                document.getElementById('cardContent').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        网络错误，请稍后重试
                    </div>
                `;
            }
        }

        // 渲染奖励物品
        function renderRewardItems(rewardDetails) {
            if (!rewardDetails || rewardDetails.length === 0) {
                return '<span class="text-muted small">暂无奖励</span>';
            }

            return rewardDetails.map(reward => {
                if (reward.type === 'currency') {
                    return `
                        <div class="d-inline-flex align-items-center me-3 mb-2 px-2 py-1 bg-light rounded" style="border: 1px solid #dee2e6;">
                            <i class="${reward.icon} text-warning me-2" style="font-size: 14px;"></i>
                            <span class="small fw-medium">${reward.display}</span>
                        </div>
                    `;
                } else if (reward.type === 'item') {
                    return `
                        <div class="d-inline-flex align-items-center me-3 mb-2 px-2 py-1 bg-light rounded" style="border: 1px solid #dee2e6;" title="${reward.explanation}">
                            <img src="${reward.icon}" alt="${reward.name}" class="me-2" style="width: 18px; height: 18px; object-fit: contain;">
                            <span class="small fw-medium">${reward.display}</span>
                        </div>
                    `;
                }
                return '';
            }).join('');
        }

        // 渲染卡片内容
        function renderCardContent() {
            const cardContent = document.getElementById('cardContent');

            if (!cardData.cards || cardData.cards.length === 0) {
                cardContent.innerHTML = `
                    <div class="text-center text-muted">
                        <i class="bi bi-inbox fs-1"></i>
                        <p class="mt-2">暂无可用的特惠卡</p>
                    </div>
                `;
                return;
            }

            // 桌面端布局
            let desktopHtml = '<div class="row g-3 d-none d-md-flex">';

            // 手机端布局
            let mobileHtml = '<div class="d-md-none">';

            cardData.cards.forEach(card => {
                const userCard = cardData.userStatus && cardData.userStatus[card.card_type] ? cardData.userStatus[card.card_type] : null;
                const isOwned = !!userCard;
                const canClaim = userCard && userCard.can_claim_today;

                // 桌面端卡片（保持原有样式）
                desktopHtml += `
                    <div class="col-md-6">
                        <div class="card h-100 ${isOwned ? 'border-success' : 'border-primary'} position-relative">
                            ${isOwned ? '<div class="position-absolute top-0 end-0 p-2"><span class="badge bg-success">已拥有</span></div>' : ''}
                            <div class="card-header bg-${isOwned ? 'success' : 'primary'} text-white">
                                <h6 class="card-title mb-0 d-flex align-items-center">
                                    <i class="${card.icon} me-2"></i>
                                    ${card.name}
                                    ${!isOwned ? `<span class="badge bg-light text-dark ms-auto">¥${card.price}</span>` : ''}
                                </h6>
                            </div>
                            <div class="card-body">
                                <p class="card-text small text-muted">${card.description}</p>

                                <div class="mb-3">
                                    <h6 class="text-success mb-2">
                                        <i class="bi bi-gift me-1"></i>即时奖励
                                    </h6>
                                    <div class="reward-items">
                                        ${renderRewardItems(card.instant_reward_details)}
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <h6 class="text-info mb-2">
                                        <i class="bi bi-calendar-day me-1"></i>每日奖励
                                    </h6>
                                    <div class="reward-items">
                                        ${renderRewardItems(card.daily_reward_details)}
                                    </div>
                                </div>

                                ${isOwned ? `
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between text-small">
                                            <span>剩余天数:</span>
                                            <span class="fw-bold text-primary">${userCard.remaining_days}天</span>
                                        </div>
                                        <div class="progress mt-1" style="height: 6px;">
                                            <div class="progress-bar bg-success" style="width: ${(userCard.claimed_days / userCard.total_days) * 100}%"></div>
                                        </div>
                                        <small class="text-muted">已领取 ${userCard.claimed_days}/${userCard.total_days} 天</small>
                                    </div>
                                ` : ''}
                            </div>
                            <div class="card-footer bg-transparent">
                                ${isOwned ?
                                    (canClaim ?
                                        `<button class="btn btn-success w-100" onclick="showClaimModal('${card.card_type}')">
                                            <i class="bi bi-gift me-1"></i>领取今日奖励
                                        </button>` :
                                        `<button class="btn btn-secondary w-100" disabled>
                                            <i class="bi bi-check-circle me-1"></i>今日已领取
                                        </button>`
                                    ) :
                                    `<button class="btn btn-primary w-100" onclick="purchaseCard(${card.id})">
                                        <i class="bi bi-cart-plus me-1"></i>购买 ¥${card.price}
                                    </button>`
                                }
                            </div>
                        </div>
                    </div>
                `;

                // 手机端卡片（紧凑设计）
                mobileHtml += `
                    <div class="mobile-card-item mb-3">
                        <div class="card border-0 shadow-sm" style="border-radius: 16px; overflow: hidden;">
                            <!-- 卡片头部 -->
                            <div class="mobile-card-header" style="background: linear-gradient(135deg, ${isOwned ? '#28a745' : '#007bff'} 0%, ${isOwned ? '#20c997' : '#6610f2'} 100%); padding: 1rem; color: white; position: relative;">
                                ${isOwned ? '<div class="position-absolute top-0 end-0 p-2"><span class="badge bg-light text-success">已拥有</span></div>' : ''}
                                <div class="d-flex align-items-center justify-content-between">
                                    <div class="d-flex align-items-center">
                                        <div class="mobile-card-icon me-3" style="font-size: 2rem; opacity: 0.9;">
                                            <i class="${card.icon}"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-1 fw-bold">${card.name}</h6>
                                            <small style="opacity: 0.9;">${card.description}</small>
                                        </div>
                                    </div>
                                    ${!isOwned ? `<div class="mobile-card-price text-end">
                                        <div class="fs-5 fw-bold">¥${card.price}</div>
                                    </div>` : ''}
                                </div>
                            </div>

                            <!-- 卡片内容 -->
                            <div class="card-body p-3">
                                <!-- 奖励展示 -->
                                <div class="row g-2 mb-3">
                                    <div class="col-6">
                                        <div class="mobile-reward-section">
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="bi bi-gift text-success me-1"></i>
                                                <small class="fw-semibold text-success">即时奖励</small>
                                            </div>
                                            <div class="mobile-reward-items">
                                                ${renderMobileRewardItems(card.instant_reward_details)}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="mobile-reward-section">
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="bi bi-calendar-day text-info me-1"></i>
                                                <small class="fw-semibold text-info">每日奖励</small>
                                            </div>
                                            <div class="mobile-reward-items">
                                                ${renderMobileRewardItems(card.daily_reward_details)}
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                ${isOwned ? `
                                    <!-- 进度信息 -->
                                    <div class="mobile-progress-section mb-3">
                                        <div class="row g-2 text-center">
                                            <div class="col-4">
                                                <div class="mobile-stat-item">
                                                    <div class="mobile-stat-number text-primary">${userCard.remaining_days}</div>
                                                    <div class="mobile-stat-label">剩余天数</div>
                                                </div>
                                            </div>
                                            <div class="col-4">
                                                <div class="mobile-stat-item">
                                                    <div class="mobile-stat-number text-success">${userCard.claimed_days}</div>
                                                    <div class="mobile-stat-label">已领取</div>
                                                </div>
                                            </div>
                                            <div class="col-4">
                                                <div class="mobile-stat-item">
                                                    <div class="mobile-stat-number text-info">${userCard.total_days}</div>
                                                    <div class="mobile-stat-label">总天数</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="progress mt-2" style="height: 8px; border-radius: 4px;">
                                            <div class="progress-bar bg-success" style="width: ${(userCard.claimed_days / userCard.total_days) * 100}%"></div>
                                        </div>
                                    </div>
                                ` : ''}

                                <!-- 操作按钮 -->
                                <div class="mobile-card-action">
                                    ${isOwned ?
                                        (canClaim ?
                                            `<button class="btn btn-success w-100 mobile-action-btn" onclick="showClaimModal('${card.card_type}')" style="border-radius: 12px; padding: 0.75rem;">
                                                <i class="bi bi-gift me-2"></i>领取今日奖励
                                            </button>` :
                                            `<button class="btn btn-outline-secondary w-100 mobile-action-btn" disabled style="border-radius: 12px; padding: 0.75rem;">
                                                <i class="bi bi-check-circle me-2"></i>今日已领取
                                            </button>`
                                        ) :
                                        `<button class="btn btn-primary w-100 mobile-action-btn" onclick="purchaseCard(${card.id})" style="border-radius: 12px; padding: 0.75rem; background: linear-gradient(135deg, #007bff 0%, #6610f2 100%); border: none;">
                                            <i class="bi bi-cart-plus me-2"></i>立即购买 ¥${card.price}
                                        </button>`
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });

            desktopHtml += '</div>';
            mobileHtml += '</div>';

            cardContent.innerHTML = desktopHtml + mobileHtml;
        }

        // 渲染手机端奖励项目
        function renderMobileRewardItems(rewardDetails) {
            if (!rewardDetails || rewardDetails.length === 0) {
                return '<small class="text-muted">无</small>';
            }

            return rewardDetails.map(reward => {
                if (reward.type === 'currency') {
                    // 使用与桌面端一致的显示方式
                    const currencyIcon = reward.currency_type === 'c_coin' ? '💰' :
                                       reward.currency_type === 'point' ? '⭐' : '💎';
                    return `<div class="mobile-reward-item">${currencyIcon} ${reward.display || (reward.amount + (reward.currency_type === 'c_coin' ? 'C币' : reward.currency_type === 'point' ? '泡点' : '积分'))}</div>`;
                } else if (reward.type === 'item') {
                    // 使用与桌面端一致的显示方式
                    return `<div class="mobile-reward-item">🎁 ${reward.display || (reward.name + ' x' + reward.amount)}</div>`;
                }
                return '';
            }).join('');
        }

        // 购买卡片（使用独立的模态框支付）
        async function purchaseCard(cardId) {
            const card = cardData.cards.find(c => c.id === cardId);
            if (!card) {
                showNotification('卡片信息不存在', 'error');
                return;
            }

            // 显示确认购买模态框
            showCardConfirmModal(card);
        }

        // 显示确认购买模态框
        function showCardConfirmModal(card) {
            // 设置卡片信息
            document.getElementById('confirmCardIcon').className = `bi ${card.icon}`;
            document.getElementById('confirmCardName').textContent = card.name;
            document.getElementById('confirmCardDescription').textContent = card.description;
            document.getElementById('confirmCardPrice').textContent = `¥${card.price}`;
            document.getElementById('confirmInstantReward').innerHTML = renderRewardItems(card.instant_reward_details);
            document.getElementById('confirmDailyReward').innerHTML = renderRewardItems(card.daily_reward_details);

            // 保存卡片信息用于确认购买
            window.pendingPurchaseCard = card;

            // 重置按钮状态
            const confirmBtn = document.getElementById('confirmPurchaseBtn');
            confirmBtn.disabled = false;
            confirmBtn.innerHTML = '<i class="bi bi-credit-card me-1"></i>确认购买';

            // 显示确认模态框
            const confirmModal = new bootstrap.Modal(document.getElementById('cardConfirmModal'));
            confirmModal.show();
        }

        // 确认购买卡片
        async function confirmCardPurchase() {
            const card = window.pendingPurchaseCard;
            if (!card) {
                showNotification('卡片信息丢失', 'error');
                return;
            }

            // 设置按钮加载状态
            const confirmBtn = document.getElementById('confirmPurchaseBtn');
            const originalText = confirmBtn.innerHTML;
            confirmBtn.disabled = true;
            confirmBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status"></span>创建订单中...';

            // 执行购买逻辑

            try {
                // 创建充值订单（用于卡片购买）
                const requestData = {
                    amount: parseFloat(card.price),
                    currency_type: 'card_purchase',
                    pay_type: 'alipay', // 默认使用支付宝
                    card_id: card.id,
                    card_type: card.card_type
                };

                // 发送卡片购买请求

                const response = await fetch('/recharge/create', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });

                const data = await response.json();
                // 卡片购买响应

                if (data.code === 200 || data.code === 0) {
                    // 订单创建成功，显示支付界面

                    // 关闭确认模态框
                    bootstrap.Modal.getInstance(document.getElementById('cardConfirmModal')).hide();

                    // 显示支付界面
                    showCardPaymentView(data.data, card);

                    showNotification('订单创建成功，请扫码支付', 'success');
                } else {
                    console.error('订单创建失败:', data);
                    showNotification(data.message || '创建订单失败', 'error');

                    // 恢复按钮状态
                    confirmBtn.disabled = false;
                    confirmBtn.innerHTML = originalText;
                }
            } catch (error) {
                console.error('创建卡片订单失败:', error);
                showNotification('网络错误，请稍后重试', 'error');

                // 恢复按钮状态
                confirmBtn.disabled = false;
                confirmBtn.innerHTML = originalText;
            }
        }

        // 显示领取模态框
        function showClaimModal(cardType) {
            const userCard = cardData.userStatus && cardData.userStatus[cardType] ? cardData.userStatus[cardType] : null;
            if (!userCard) {
                showNotification('卡片信息不存在', 'error');
                return;
            }

            const card = cardData.cards.find(c => c.card_type === cardType);
            if (!card) {
                showNotification('卡片配置不存在', 'error');
                return;
            }

            // 设置模态框内容
            document.getElementById('cardClaimModalLabel').innerHTML = `
                <i class="bi bi-gift me-2"></i>
                ${card.name} - 每日奖励
            `;

            document.getElementById('cardClaimContent').innerHTML = `
                <div class="text-center mb-4">
                    <div class="display-1 text-success">
                        <i class="bi bi-gift"></i>
                    </div>
                    <h5 class="mt-3">今日奖励内容</h5>
                    <div class="reward-items text-center">
                        ${renderRewardItems(card.daily_reward_details)}
                    </div>
                </div>

                <div class="row text-center">
                    <div class="col-4">
                        <div class="border rounded p-3">
                            <div class="text-primary fs-4">
                                <i class="bi bi-calendar-check"></i>
                            </div>
                            <small class="text-muted">剩余天数</small>
                            <div class="fw-bold">${userCard.remaining_days}天</div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="border rounded p-3">
                            <div class="text-success fs-4">
                                <i class="bi bi-check-circle"></i>
                            </div>
                            <small class="text-muted">已领取</small>
                            <div class="fw-bold">${userCard.claimed_days}天</div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="border rounded p-3">
                            <div class="text-info fs-4">
                                <i class="bi bi-calendar3"></i>
                            </div>
                            <small class="text-muted">总天数</small>
                            <div class="fw-bold">${userCard.total_days}天</div>
                        </div>
                    </div>
                </div>
            `;

            // 设置领取按钮的卡片类型
            document.getElementById('claimRewardBtn').setAttribute('data-card-type', cardType);

            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('cardClaimModal'));
            modal.show();
        }

        // 显示卡片支付界面
        function showCardPaymentView(paymentData, card) {
            // 隐藏卡片列表，显示支付界面
            document.getElementById('cardListView').style.display = 'none';
            document.getElementById('cardPaymentView').style.display = 'block';
            document.getElementById('cardSuccessView').style.display = 'none';

            // 更新footer
            document.getElementById('cardListFooter').style.display = 'none';
            document.getElementById('cardPaymentFooter').style.display = 'block';
            document.getElementById('cardSuccessFooter').style.display = 'none';

            // 设置支付信息
            document.getElementById('cardPaymentTitle').textContent = `购买 ${card.name}`;
            document.getElementById('cardOrderNumber').textContent = paymentData.out_trade_no || '-';
            document.getElementById('cardProductName').textContent = card.name;
            document.getElementById('cardPaymentAmount').textContent = `¥${card.price}`;

            // 设置二维码
            if (paymentData.qrcode_img) {
                document.getElementById('cardPaymentQR').src = paymentData.qrcode_img;
            } else if (paymentData.qrcode_url) {
                document.getElementById('cardPaymentQR').src = paymentData.qrcode_url;
            }

            // 保存订单号用于状态查询
            window.currentCardOrderNo = paymentData.out_trade_no;
            window.currentCardInfo = card;

            // 开始轮询支付状态
            startCardPaymentPolling();
        }

        // 返回卡片列表
        function backToCardList() {
            // 停止支付状态轮询
            if (window.cardPaymentPollingTimer) {
                clearInterval(window.cardPaymentPollingTimer);
                window.cardPaymentPollingTimer = null;
            }

            // 显示卡片列表，隐藏其他界面
            document.getElementById('cardListView').style.display = 'block';
            document.getElementById('cardPaymentView').style.display = 'none';
            document.getElementById('cardSuccessView').style.display = 'none';

            // 更新footer
            document.getElementById('cardListFooter').style.display = 'block';
            document.getElementById('cardPaymentFooter').style.display = 'none';
            document.getElementById('cardSuccessFooter').style.display = 'none';

            // 刷新卡片信息
            loadCardInfo();
        }

        // 显示支付成功界面
        function showCardSuccessView(rewardInfo) {
            // 停止支付状态轮询
            if (window.cardPaymentPollingTimer) {
                clearInterval(window.cardPaymentPollingTimer);
                window.cardPaymentPollingTimer = null;
            }

            // 显示成功界面
            document.getElementById('cardListView').style.display = 'none';
            document.getElementById('cardPaymentView').style.display = 'none';
            document.getElementById('cardSuccessView').style.display = 'block';

            // 更新footer
            document.getElementById('cardListFooter').style.display = 'none';
            document.getElementById('cardPaymentFooter').style.display = 'none';
            document.getElementById('cardSuccessFooter').style.display = 'block';

            // 显示奖励信息
            if (rewardInfo && rewardInfo.length > 0) {
                const rewardDiv = document.getElementById('cardRewardInfo');
                let rewardText = '即时奖励：';
                rewardInfo.forEach(reward => {
                    if (reward.type === 'currency') {
                        const currencyName = reward.currency_type === 'c_coin' ? 'C币' :
                                           reward.currency_type === 'point' ? '泡点' : '积分';
                        rewardText += ` ${reward.amount}${currencyName}`;
                    } else if (reward.type === 'item') {
                        rewardText += ` 道具#${reward.item_id} x${reward.number}`;
                    }
                });
                rewardDiv.textContent = rewardText;
                rewardDiv.style.display = 'block';
            }
        }

        // 开始卡片支付状态轮询
        function startCardPaymentPolling() {
            if (window.cardPaymentPollingTimer) {
                clearInterval(window.cardPaymentPollingTimer);
            }

            window.cardPaymentPollingTimer = setInterval(async () => {
                await checkCardPaymentStatus();
            }, 3000); // 每3秒检查一次
        }

        // 检查卡片支付状态
        async function checkCardPaymentStatus() {
            if (!window.currentCardOrderNo) return;

            try {
                const response = await fetch(`/recharge/query?out_trade_no=${window.currentCardOrderNo}`);
                const data = await response.json();

                if (data.code === 200 && data.data) {
                    const status = data.data.status;
                    const statusElement = document.getElementById('cardPaymentStatus');

                    if (status === 1) {
                        // 支付成功
                        statusElement.className = 'badge bg-success';
                        statusElement.textContent = '支付成功';

                        // 显示成功界面
                        setTimeout(() => {
                            showCardSuccessView(window.currentCardInfo ? window.currentCardInfo.instant_reward_parsed : null);
                        }, 1000);

                        // 刷新余额
                        if (typeof refreshBalance === 'function') {
                            refreshBalance();
                        }

                    } else if (status === 2) {
                        // 支付失败
                        statusElement.className = 'badge bg-danger';
                        statusElement.textContent = '支付失败';

                        // 停止轮询
                        if (window.cardPaymentPollingTimer) {
                            clearInterval(window.cardPaymentPollingTimer);
                            window.cardPaymentPollingTimer = null;
                        }
                    }
                }
            } catch (error) {
                console.error('查询卡片支付状态失败:', error);
            }
        }

        // 手动刷新卡片支付状态
        async function refreshCardPaymentStatus() {
            await checkCardPaymentStatus();
        }

        // 领取每日奖励
        async function claimDailyReward() {
            const cardType = document.getElementById('claimRewardBtn').getAttribute('data-card-type');
            if (!cardType) {
                showNotification('卡片类型错误', 'error');
                return;
            }

            try {
                const response = await fetch('/card/claim', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ card_type: cardType })
                });

                const data = await response.json();

                if (data.code === 0) {
                    showNotification('每日奖励领取成功！', 'success');

                    // 关闭模态框
                    bootstrap.Modal.getInstance(document.getElementById('cardClaimModal')).hide();

                    // 刷新卡片信息
                    await loadCardInfo();

                    // 刷新余额
                    if (typeof refreshBalance === 'function') {
                        refreshBalance();
                    }
                } else {
                    showNotification(data.message || '领取失败', 'error');
                }
            } catch (error) {
                console.error('领取每日奖励失败:', error);
                showNotification('网络错误，请稍后重试', 'error');
            }
        }

    </script>
{/block}
