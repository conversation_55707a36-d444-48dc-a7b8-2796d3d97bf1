{extend name="layout/base"}

{block name="title"}商城维护中 - 游戏商城{/block}

{block name="head"}
<style>
.maintenance-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem 1rem;
}

.maintenance-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 3rem 2rem;
    text-align: center;
    max-width: 600px;
    width: 100%;
    position: relative;
    overflow: hidden;
}

.maintenance-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
    border-radius: 20px 20px 0 0;
}

.maintenance-icon {
    font-size: 4rem;
    color: #ff6b6b;
    margin-bottom: 1.5rem;
    animation: pulse 2s infinite;
}

.maintenance-title {
    color: #2c3e50;
    font-weight: 700;
    font-size: 2.5rem;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.maintenance-subtitle {
    color: #7f8c8d;
    font-size: 1.2rem;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.maintenance-message {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    border-left: 4px solid #ff6b6b;
}

.maintenance-message p {
    color: #495057;
    font-size: 1.1rem;
    margin: 0;
    line-height: 1.6;
}

.maintenance-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.feature-item {
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    font-size: 2rem;
    color: #4ecdc4;
    margin-bottom: 1rem;
}

.feature-title {
    color: #2c3e50;
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
}

.feature-desc {
    color: #7f8c8d;
    font-size: 0.9rem;
    line-height: 1.5;
}

.maintenance-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn {
    border-radius: 25px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    color: white;
    text-decoration: none;
}

.btn-outline {
    background: transparent;
    color: #667eea;
    border: 2px solid #667eea;
}

.btn-outline:hover {
    background: #667eea;
    color: white;
    text-decoration: none;
}

.maintenance-footer {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.maintenance-footer p {
    color: #95a5a6;
    font-size: 0.9rem;
    margin: 0;
}

.countdown-container {
    background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    color: white;
}

.countdown-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.countdown-timer {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.countdown-item {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 0.75rem;
    min-width: 60px;
    text-align: center;
}

.countdown-number {
    font-size: 1.5rem;
    font-weight: 700;
    display: block;
}

.countdown-label {
    font-size: 0.8rem;
    opacity: 0.9;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.maintenance-card {
    animation: float 6s ease-in-out infinite;
}

@media (max-width: 768px) {
    .maintenance-container {
        padding: 1rem 0.5rem;
    }
    
    .maintenance-card {
        padding: 2rem 1.5rem;
    }
    
    .maintenance-title {
        font-size: 2rem;
    }
    
    .maintenance-subtitle {
        font-size: 1rem;
    }
    
    .maintenance-features {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .maintenance-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .btn {
        width: 100%;
        max-width: 250px;
    }
}
</style>
{/block}

{block name="content"}
<div class="maintenance-container">
    <div class="maintenance-card">
        <div class="maintenance-icon">
            <i class="bi bi-tools"></i>
        </div>
        
        <h1 class="maintenance-title">商城维护中</h1>
        <p class="maintenance-subtitle">我们正在对商城进行升级维护，为您带来更好的购物体验</p>
        
        <div class="maintenance-message">
            <p id="maintenance-text"><?php echo $maintenance_message ?? '商城正在维护中，请稍后再试。'; ?></p>
        </div>
        
        <!-- 倒计时（可选） -->
        <div class="countdown-container d-none" id="countdown-container">
            <div class="countdown-title">
                <i class="bi bi-clock me-2"></i>预计恢复时间
            </div>
            <div class="countdown-timer">
                <div class="countdown-item">
                    <span class="countdown-number" id="hours">00</span>
                    <span class="countdown-label">小时</span>
                </div>
                <div class="countdown-item">
                    <span class="countdown-number" id="minutes">00</span>
                    <span class="countdown-label">分钟</span>
                </div>
                <div class="countdown-item">
                    <span class="countdown-number" id="seconds">00</span>
                    <span class="countdown-label">秒</span>
                </div>
            </div>
        </div>
        
        <div class="maintenance-features">
            <div class="feature-item">
                <div class="feature-icon">
                    <i class="bi bi-shield-check"></i>
                </div>
                <div class="feature-title">安全升级</div>
                <div class="feature-desc">提升系统安全性，保护您的账户和交易安全</div>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">
                    <i class="bi bi-lightning"></i>
                </div>
                <div class="feature-title">性能优化</div>
                <div class="feature-desc">优化系统性能，提供更快的加载速度</div>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">
                    <i class="bi bi-star"></i>
                </div>
                <div class="feature-title">新功能</div>
                <div class="feature-desc">即将推出更多精彩功能和商品</div>
            </div>
        </div>
        
        <div class="maintenance-actions">
            <a href="javascript:location.reload()" class="btn btn-primary">
                <i class="bi bi-arrow-clockwise me-2"></i>刷新页面
            </a>
        </div>
        
        <div class="maintenance-footer">
            <p>
                <i class="bi bi-info-circle me-1"></i>
                如有紧急问题，请联系客服或查看官方公告
            </p>
        </div>
    </div>
</div>
{/block}

{block name="scripts"}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 自动刷新检查商城状态（每30秒）
    setInterval(function() {
        fetch('/api/shop/status')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data.shop_enabled) {
                    // 商城已恢复，自动跳转
                    window.location.href = '/shop';
                }
            })
            .catch(error => {
                console.log('检查商城状态失败:', error);
            });
    }, 30000);
    
    // 可选：倒计时功能
    // 如果需要显示倒计时，可以设置目标时间
    // startCountdown('2025-07-06 20:00:00');
});

function startCountdown(targetTime) {
    const countdownContainer = document.getElementById('countdown-container');
    const hoursElement = document.getElementById('hours');
    const minutesElement = document.getElementById('minutes');
    const secondsElement = document.getElementById('seconds');
    
    countdownContainer.classList.remove('d-none');
    
    function updateCountdown() {
        const now = new Date().getTime();
        const target = new Date(targetTime).getTime();
        const distance = target - now;
        
        if (distance < 0) {
            countdownContainer.innerHTML = '<div class="countdown-title">维护已完成，正在检查状态...</div>';
            return;
        }
        
        const hours = Math.floor(distance / (1000 * 60 * 60));
        const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((distance % (1000 * 60)) / 1000);
        
        hoursElement.textContent = hours.toString().padStart(2, '0');
        minutesElement.textContent = minutes.toString().padStart(2, '0');
        secondsElement.textContent = seconds.toString().padStart(2, '0');
    }
    
    updateCountdown();
    setInterval(updateCountdown, 1000);
}
</script>
{/block}
