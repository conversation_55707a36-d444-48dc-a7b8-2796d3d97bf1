{extend name="admin/layout/base" /}

{block name="title"}订单管理{/block}

{block name="css"}
<style>
/* 订单管理页面专用样式 */
.order-status {
    font-weight: 600;
}

.order-amount {
    font-weight: 600;
    color: #28a745;
}

.order-actions .btn {
    margin-right: 0.25rem;
    margin-bottom: 0.25rem;
}

/* 刷新按钮旋转动画 */
.spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* 双UI系统样式 */
.desktop-ui .mobile-ui {
    display: none !important;
}

.mobile-ui .desktop-ui {
    display: none !important;
}

/* 桌面端样式 */
@media (min-width: 769px) {
    .mobile-ui {
        display: none !important;
    }
    .desktop-ui {
        display: block !important;
    }
}

/* 筛选器样式 */
.filters-container {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
}

.filters-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 1rem;
    align-items: end;
}

/* 手机端样式 */
@media (max-width: 768px) {
    .desktop-ui {
        display: none !important;
    }
    .mobile-ui {
        display: block !important;
    }

    .table-responsive {
        font-size: 0.875rem;
    }

    .order-actions .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }

    /* 手机端筛选器样式 */
    .filters-container {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .filters-row {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
    }

    .filters-row .col {
        width: 100%;
    }

    .filters-row .d-flex {
        flex-direction: row;
        gap: 0.5rem;
    }

    .filters-row .flex-fill {
        flex: 1;
    }
}
</style>
{/block}

{block name="content"}
<!-- 版本: v1.1.0 - 修复按钮500错误 -->
<!-- 页面头部 -->
<div class="page-header">
    <h1 class="page-title">
        <i class="bi bi-receipt"></i>
        订单管理
    </h1>
    <p class="page-description">管理系统订单，查看订单信息和状态</p>
</div>

<!-- 统计卡片 - 桌面端 -->
<div class="desktop-ui">
    <div class="row g-3 mb-4">
        <!-- 总订单数 -->
        <div class="col-md-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="rounded-circle bg-primary bg-opacity-10 p-3">
                            <i class="bi bi-receipt text-primary fs-2"></i>
                        </div>
                    </div>
                    <h3 class="text-primary mb-1" id="totalOrders">-</h3>
                    <p class="text-muted mb-0">订单总数</p>
                </div>
            </div>
        </div>

        <!-- 已完成订单 -->
        <div class="col-md-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="rounded-circle bg-success bg-opacity-10 p-3">
                            <i class="bi bi-check-circle text-success fs-2"></i>
                        </div>
                    </div>
                    <h3 class="text-success mb-1" id="completedOrders">-</h3>
                    <p class="text-muted mb-0">已完成</p>
                </div>
            </div>
        </div>

        <!-- 待处理订单 -->
        <div class="col-md-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="rounded-circle bg-warning bg-opacity-10 p-3">
                            <i class="bi bi-clock text-warning fs-2"></i>
                        </div>
                    </div>
                    <h3 class="text-warning mb-1" id="pendingOrders">-</h3>
                    <p class="text-muted mb-0">待处理</p>
                </div>
            </div>
        </div>


    </div>
</div>

<!-- 统计卡片 - 手机端 -->
<div class="mobile-ui">
    <div class="row g-2 mb-3">
        <div class="col-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center p-3">
                    <div class="text-primary fs-4" id="totalOrdersMobile">-</div>
                    <small class="text-muted">总订单</small>
                </div>
            </div>
        </div>
        <div class="col-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center p-3">
                    <div class="text-success fs-4" id="completedOrdersMobile">-</div>
                    <small class="text-muted">已完成</small>
                </div>
            </div>
        </div>
        <div class="col-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center p-3">
                    <div class="text-warning fs-4" id="pendingOrdersMobile">-</div>
                    <small class="text-muted">待处理</small>
                </div>
            </div>
        </div>

    </div>
</div>

<!-- 筛选器 -->
<div class="filters-container">
    <div class="filters-row">
        <div class="col">
            <label for="searchInput" class="form-label">搜索订单</label>
            <div class="input-group">
                <span class="input-group-text"><i class="bi bi-search"></i></span>
                <input type="text" class="form-control" id="searchInput" placeholder="输入订单号或用户名...">
            </div>
        </div>

        <div class="col">
            <label for="statusFilter" class="form-label">订单状态</label>
            <select class="form-select" id="statusFilter">
                <option value="">全部状态</option>
                <option value="1">待处理</option>
                <option value="2">处理中</option>
                <option value="3">已完成</option>
                <option value="4">失败</option>
                <option value="5">已取消</option>
            </select>
        </div>

        <div class="col">
            <label for="dateRange" class="form-label">日期范围</label>
            <select class="form-select" id="dateRange">
                <option value="">全部时间</option>
                <option value="today">今天</option>
                <option value="yesterday">昨天</option>
                <option value="week">本周</option>
                <option value="month">本月</option>
            </select>
        </div>

        <div class="col">
            <label class="form-label">&nbsp;</label>
            <div class="d-flex gap-2">
                <button type="button" class="btn btn-primary flex-fill" id="searchButton">
                    <i class="bi bi-search"></i> 搜索
                </button>
                <button type="button" class="btn btn-outline-secondary" id="refreshButton" title="刷新数据">
                    <i class="bi bi-arrow-clockwise"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 订单表格 - 桌面端 -->
<div class="admin-card desktop-ui">
    <div class="admin-card-header">
        <h5 class="mb-0">
            <i class="bi bi-table"></i>
            订单列表
        </h5>
        <button type="button" class="btn btn-success btn-sm" id="exportButton">
            <i class="bi bi-download"></i> 导出数据
        </button>
    </div>
    <div class="admin-card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0" id="ordersTable">
                <thead>
                    <tr>
                        <th>订单号</th>
                        <th>用户</th>
                        <th>商品</th>
                        <th>金额</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="ordersTableBody">
                    <tr>
                        <td colspan="7" class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <div class="mt-2">正在加载订单数据...</div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    <div class="admin-card-footer">
        <div class="d-flex justify-content-between align-items-center">
            <div class="text-muted">
                显示第 <span id="pageStart">0</span> - <span id="pageEnd">0</span> 条，共 <span id="totalCount">0</span> 条记录
            </div>
            <div id="pagination"></div>
        </div>
    </div>
</div>

<!-- 手机端订单列表 -->
<div class="mobile-ui">
    <div id="mobileOrdersList">
        <!-- 加载状态 -->
        <div class="text-center py-4" id="mobileLoadingState">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <div class="mt-2 small">正在加载订单数据...</div>
        </div>
    </div>
</div>

<!-- 手机端分页容器 -->
<div class="mobile-ui mt-3">
    <div id="paginationMobile"></div>
</div>

{/block}

{block name="admin_js"}
<script>
// 商城配置
const OrderShopConfig = <?php echo \app\service\ShopConfigService::getFrontendConfigJson(); ?>;

console.log('OrderShopConfig loaded:', OrderShopConfig);
console.log('OrderShopConfig.orderStatus:', OrderShopConfig.orderStatus);

// 测试状态3
console.log('Testing status 3:', OrderShopConfig.orderStatus[3]);
console.log('Testing status "3":', OrderShopConfig.orderStatus["3"]);

// 使用购买历史相同的状态映射方法
const statusMap = {
    1: '待处理', 2: '处理中', 3: '已完成', 4: '失败', 5: '已取消', 6: '已退款',
    'pending': '待处理', 'processing': '处理中', 'completed': '已完成',
    'failed': '失败', 'cancelled': '已取消', 'refunded': '已退款'
};

const statusColorMap = {
    1: 'bg-warning', 2: 'bg-info', 3: 'bg-success', 4: 'bg-danger', 5: 'bg-secondary', 6: 'bg-dark',
    'pending': 'bg-warning', 'processing': 'bg-info', 'completed': 'bg-success',
    'failed': 'bg-danger', 'cancelled': 'bg-secondary', 'refunded': 'bg-dark'
};

const currencyMap = {'1': '泡点', '2': '积分', '3': 'C币'};

// 配置助手对象
OrderShopConfig.getCurrencyName = function(id) {
    // 首先尝试从全局配置获取
    if (window.ShopConfig) {
        if (window.ShopConfig.currency_types && window.ShopConfig.currency_types[String(id)]) {
            return window.ShopConfig.currency_types[String(id)].name;
        }
        if (window.ShopConfig.currencies && window.ShopConfig.currencies[String(id)]) {
            return window.ShopConfig.currencies[String(id)].name;
        }
    }

    // 降级到本地配置
    return currencyMap[id] || this.currencies[id]?.name || '未知';
};

OrderShopConfig.getOrderStatusName = function(status) {
    console.log('getOrderStatusName called with status:', status, 'type:', typeof status);

    // 处理数字状态
    if (typeof status === 'number' || !isNaN(parseInt(status))) {
        const numId = parseInt(status);
        const result = statusMap[numId];
        console.log('Numeric status result:', result);
        return result || '未知状态';
    }

    // 处理字符串状态
    const result = statusMap[status];
    console.log('String status result:', result);
    return result || '未知状态';
};

OrderShopConfig.getOrderStatusColorClass = function(status) {
    console.log('getOrderStatusColorClass called with status:', status, 'type:', typeof status);

    // 处理数字状态
    if (typeof status === 'number' || !isNaN(parseInt(status))) {
        const numId = parseInt(status);
        const result = statusColorMap[numId];
        console.log('Numeric status color result:', result);
        return result || 'bg-secondary';
    }

    // 处理字符串状态
    const result = statusColorMap[status];
    console.log('String status color result:', result);
    return result || 'bg-secondary';
};

// 订单管理页面JavaScript
class OrderManagement {
    constructor() {
        this.currentPage = 1;
        this.pageSize = 10;
        this.totalPages = 1;
        this.searchTerm = '';
        this.statusFilter = '';
        this.dateRange = '';
        this.itemIcons = {}; // 存储商品图标
        this.isProcessing = false; // 防止重复操作
        this.cacheBreaker = null; // 缓存清除时间戳
        this.init();
    }

    async init() {
        await this.loadShopConfig();
        this.bindEvents();
        this.loadStats();
        this.loadOrders();
    }

    // 加载ShopConfig配置
    async loadShopConfig() {
        try {
            const response = await fetch('/api/getShopConfig?t=' + Date.now());
            const result = await response.json();
            if (result.success && result.data) {
                window.ShopConfig = result.data;
                console.log('订单管理页面ShopConfig已更新:', window.ShopConfig);
                return true;
            }
        } catch (error) {
            console.error('加载ShopConfig失败:', error);
        }
        return false;
    }

    bindEvents() {
        // 搜索按钮
        document.getElementById('searchButton').addEventListener('click', () => {
            this.search();
        });

        // 搜索框回车
        document.getElementById('searchInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.search();
            }
        });

        // 筛选器变化
        document.getElementById('statusFilter').addEventListener('change', () => {
            this.search();
        });

        document.getElementById('dateRange').addEventListener('change', () => {
            this.search();
        });

        // 导出按钮
        document.getElementById('exportButton').addEventListener('click', () => {
            this.exportOrders();
        });

        // 刷新按钮
        document.getElementById('refreshButton').addEventListener('click', () => {
            this.refresh();
        });
    }

    search() {
        this.searchTerm = document.getElementById('searchInput').value.trim();
        this.statusFilter = document.getElementById('statusFilter').value;
        this.dateRange = document.getElementById('dateRange').value;
        this.currentPage = 1;
        this.loadOrders();
    }

    async refresh() {
        // 获取刷新按钮
        const refreshButton = document.getElementById('refreshButton');
        const originalHtml = refreshButton.innerHTML;

        // 设置加载状态
        refreshButton.disabled = true;
        refreshButton.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i>';

        // 显示刷新提示
        AdminUtils.showToast('正在刷新数据...', 'info');

        try {
            // 1. 先清除服务器端缓存
            await this.clearServerCache();

            // 2. 清除客户端缓存
            this.clearClientCache();

            // 3. 重新加载统计数据和订单列表（强制刷新）
            await Promise.all([
                this.loadStats(true),
                this.loadOrders(true)
            ]);

            // 显示刷新完成提示
            AdminUtils.showToast('数据刷新完成', 'success');
        } catch (error) {
            console.error('刷新失败:', error);
            AdminUtils.showToast('刷新失败，请稍后重试', 'danger');
        } finally {
            // 恢复按钮状态
            refreshButton.disabled = false;
            refreshButton.innerHTML = originalHtml;
        }
    }

    /**
     * 清除服务器端缓存
     */
    async clearServerCache() {
        try {
            // 调用后端API清除缓存
            const response = await fetch('/admin/settings/clearCache', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'same-origin'
            });

            if (response.ok) {
                const result = await response.json();
                if (result.success) {
                    console.log('服务器端缓存清除成功');
                } else {
                    console.warn('服务器端缓存清除失败:', result.message);
                }
            } else {
                console.warn('服务器端缓存清除失败，HTTP状态:', response.status);
            }
        } catch (error) {
            console.warn('清除服务器端缓存时出错:', error);
            // 不抛出错误，继续执行刷新
        }
    }

    /**
     * 清除客户端缓存
     */
    clearClientCache() {
        // 清除商品图标缓存
        this.itemIcons = {};

        // 清除可能的浏览器缓存（通过添加时间戳）
        this.cacheBreaker = Date.now();

        console.log('客户端缓存已清除，新的缓存清除时间戳:', this.cacheBreaker);
    }

    async loadStats(forceRefresh = false) {
        try {
            // 添加缓存清除参数
            let url = '/admin/order/getOrderStats';
            if (forceRefresh || this.cacheBreaker) {
                const timestamp = forceRefresh ? Date.now() : this.cacheBreaker;
                url += `?_t=${timestamp}`;
                console.log('统计API使用缓存清除参数:', timestamp);
            }
            const response = await fetch(url);
            const result = await response.json();

            console.log('统计API响应:', result);

            if (result.code === 200 || result.success) {
                const stats = result.data || result;

                // 更新桌面端统计
                document.getElementById('totalOrders').textContent = AdminUtils.formatNumber(stats.total || 0);
                document.getElementById('completedOrders').textContent = AdminUtils.formatNumber(stats.completed || 0);
                document.getElementById('pendingOrders').textContent = AdminUtils.formatNumber(stats.pending || 0);

                // 更新手机端统计
                document.getElementById('totalOrdersMobile').textContent = AdminUtils.formatNumber(stats.total || 0);
                document.getElementById('completedOrdersMobile').textContent = AdminUtils.formatNumber(stats.completed || 0);
                document.getElementById('pendingOrdersMobile').textContent = AdminUtils.formatNumber(stats.pending || 0);
            }
        } catch (error) {
            console.error('加载统计数据失败:', error);
        }
    }

    async loadOrders(forceRefresh = false) {
        const tbody = document.getElementById('ordersTableBody');
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <div class="mt-2">正在加载订单数据...</div>
                </td>
            </tr>
        `;

        try {
            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.pageSize,
                search: this.searchTerm,
                status: this.statusFilter,
                date_range: this.dateRange
            });

            // 强制刷新时总是添加缓存清除参数
            if (forceRefresh || this.cacheBreaker) {
                const timestamp = forceRefresh ? Date.now() : this.cacheBreaker;
                params.set('_t', timestamp);
                console.log('使用缓存清除参数:', timestamp);
            }

            const response = await fetch(`/admin/order/getOrderList?${params}`);
            const result = await response.json();

            console.log('订单API响应:', result);

            if (result.code === 200 || result.success) {
                const data = result.data || result;
                console.log('订单数据:', data);

                const orders = data.list || data.orders || data;
                const total = data.total || data.count || 0;

                console.log('订单列表:', orders, '总数:', total);

                this.renderOrders(orders);
                this.updatePagination(total);

                // 批量加载商品图标
                this.loadItemIcons(orders);
            } else {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" class="text-center py-4 text-danger">
                            <i class="bi bi-exclamation-triangle"></i>
                            <div class="mt-2">${result.message || '加载失败'}</div>
                        </td>
                    </tr>
                `;
            }
        } catch (error) {
            console.error('加载订单列表失败:', error);
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center py-4 text-danger">
                        <i class="bi bi-wifi-off"></i>
                        <div class="mt-2">网络错误，请稍后重试</div>
                    </td>
                </tr>
            `;
        }
    }

    renderOrders(orders) {
        // 渲染桌面端表格
        this.renderDesktopOrders(orders);

        // 渲染手机端列表
        this.renderMobileOrders(orders);
    }

    renderDesktopOrders(orders) {
        const tbody = document.getElementById('ordersTableBody');

        if (!orders || orders.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center py-4 text-muted">
                        <i class="bi bi-inbox"></i>
                        <div class="mt-2">暂无订单数据</div>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = orders.map(order => `
            <tr>
                <td>
                    <div class="d-flex align-items-center">
                        <i class="bi bi-receipt text-primary me-2"></i>
                        <div>
                            <div class="fw-medium">${order.order_no || order.id}</div>
                            <small class="text-muted">ID: ${order.id}</small>
                        </div>
                    </div>
                </td>
                <td>
                    <div class="d-flex align-items-center">
                        <i class="bi bi-person text-info me-2"></i>
                        <div>
                            <div class="fw-medium">${order.username || order.user_id}</div>
                            <small class="text-muted">${order.user_nickname || ''}</small>
                        </div>
                    </div>
                </td>
                <td data-item-id="${order.item_id || ''}">
                    <div class="d-flex align-items-center">
                        <img class="item-icon me-2" src="/static/img/default.png" alt="商品图标" style="width: 24px; height: 24px;">
                        <div>
                            <div class="fw-medium">${order.item_name || '未知商品'}</div>
                            <small class="text-muted">数量: ${order.quantity || 1}</small>
                        </div>
                    </div>
                </td>
                <td>
                    <div class="d-flex align-items-center">
                        <i class="bi bi-currency-dollar text-warning me-2"></i>
                        <div>
                            <span class="order-amount">${AdminUtils.formatNumber(order.total_price || order.amount || 0)}</span>
                            <div><small class="text-muted">${OrderShopConfig.getCurrencyName(order.currency_type)}</small></div>
                        </div>
                    </div>
                </td>
                <td>
                    <span class="badge ${OrderShopConfig.getOrderStatusColorClass(order.status_code || order.status)}">
                        ${OrderShopConfig.getOrderStatusName(order.status_code || order.status)}
                    </span>
                </td>
                <td>
                    <div class="d-flex align-items-center">
                        <i class="bi bi-clock text-muted me-2"></i>
                        <div>${this.formatDateTime(order.create_time || order.created_at)}</div>
                    </div>
                </td>
                <td>
                    <div class="order-actions">
                        <button class="btn btn-sm btn-outline-primary" onclick="orderManagement.viewOrder(${order.id})" title="查看详情">
                            <i class="bi bi-eye"></i>
                        </button>
                        ${(order.status_code === 1 || order.status === 'pending') ?
                            `<button class="btn btn-sm btn-outline-success" onclick="orderManagement.forceCompleteOrder(${order.id})" title="强制完成">
                                <i class="bi bi-check-circle"></i>
                            </button>` : ''
                        }
                        ${(order.status_code === 2 || order.status === 'processing') ?
                            `<button class="btn btn-sm btn-outline-success" onclick="orderManagement.forceCompleteOrder(${order.id})" title="强制完成">
                                <i class="bi bi-check-circle"></i>
                            </button>` : ''
                        }
                        ${(order.status_code === 4 || order.status === 'failed') ?
                            `<button class="btn btn-sm btn-outline-warning" onclick="orderManagement.resendOrder(${order.id})" title="补发订单">
                                <i class="bi bi-arrow-repeat"></i>
                            </button>` : ''
                        }
                        ${(order.status_code === 1 || order.status === 'pending' || order.status_code === 2 || order.status === 'processing' || order.status_code === 4 || order.status === 'failed') ?
                            `<button class="btn btn-sm btn-outline-danger" onclick="orderManagement.cancelOrder(${order.id})" title="取消订单">
                                <i class="bi bi-x"></i>
                            </button>` : ''
                        }
                    </div>
                </td>
            </tr>
        `).join('');
    }

    renderMobileOrders(orders) {
        const container = document.getElementById('mobileOrdersList');

        if (!orders || orders.length === 0) {
            container.innerHTML = `
                <div class="text-center py-4 text-muted">
                    <i class="bi bi-inbox fs-1"></i>
                    <div class="mt-2">暂无订单数据</div>
                </div>
            `;
            return;
        }

        container.innerHTML = orders.map(order => `
            <div class="card mb-2 border-0 shadow-sm">
                <div class="card-body p-3">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <div class="d-flex align-items-center" data-item-id="${order.item_id || ''}">
                            <img class="item-icon me-2" src="/static/img/default.png" alt="商品图标" style="width: 32px; height: 32px;">
                            <div>
                                <h6 class="mb-1">${order.item_name || '未知商品'}</h6>
                                <small class="text-muted"><i class="bi bi-receipt me-1"></i>${order.order_no || order.id}</small>
                            </div>
                        </div>
                        <span class="badge ${OrderShopConfig.getOrderStatusColorClass(order.status_code || order.status)}">
                            ${OrderShopConfig.getOrderStatusName(order.status_code || order.status)}
                        </span>
                    </div>

                    <div class="row g-2 mb-2">
                        <div class="col-6">
                            <small class="text-muted"><i class="bi bi-person me-1"></i>用户</small>
                            <div class="fw-medium">${order.username || order.user_id}</div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted"><i class="bi bi-hash me-1"></i>数量</small>
                            <div class="fw-medium">${order.quantity || 1}</div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted"><i class="bi bi-currency-dollar me-1"></i>金额</small>
                            <div class="fw-medium text-success">${AdminUtils.formatNumber(order.total_price || order.amount || 0)} ${OrderShopConfig.getCurrencyName(order.currency_type)}</div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted"><i class="bi bi-clock me-1"></i>时间</small>
                            <div class="fw-medium">${this.formatDateTime(order.create_time || order.created_at)}</div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-end">
                        <div class="btn-group" role="group">
                            <button class="btn btn-outline-primary btn-sm" onclick="orderManagement.viewOrder(${order.id})" title="查看详情">
                                <i class="bi bi-eye"></i>
                            </button>
                            ${(order.status_code === 1 || order.status === 'pending') ?
                                `<button class="btn btn-outline-success btn-sm" onclick="orderManagement.forceCompleteOrder(${order.id})" title="强制完成">
                                    <i class="bi bi-check-circle"></i>
                                </button>` : ''
                            }
                            ${(order.status_code === 2 || order.status === 'processing') ?
                                `<button class="btn btn-outline-success btn-sm" onclick="orderManagement.forceCompleteOrder(${order.id})" title="强制完成">
                                    <i class="bi bi-check-circle"></i>
                                </button>` : ''
                            }
                            ${(order.status_code === 4 || order.status === 'failed') ?
                                `<button class="btn btn-outline-warning btn-sm" onclick="orderManagement.resendOrder(${order.id})" title="补发订单">
                                    <i class="bi bi-arrow-repeat"></i>
                                </button>` : ''
                            }
                            ${(order.status_code === 1 || order.status === 'pending' || order.status_code === 2 || order.status === 'processing' || order.status_code === 4 || order.status === 'failed') ?
                                `<button class="btn btn-outline-danger btn-sm" onclick="orderManagement.cancelOrder(${order.id})" title="取消订单">
                                    <i class="bi bi-x"></i>
                                </button>` : ''
                            }
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }



    updatePagination(total) {
        this.totalPages = Math.ceil(total / this.pageSize);

        const start = (this.currentPage - 1) * this.pageSize + 1;
        const end = Math.min(this.currentPage * this.pageSize, total);

        document.getElementById('pageStart').textContent = total > 0 ? start : 0;
        document.getElementById('pageEnd').textContent = end;
        document.getElementById('totalCount').textContent = total;

        console.log('Order updatePagination called - total:', total, 'totalPages:', this.totalPages, 'currentPage:', this.currentPage);

        // 创建或更新桌面端分页组件
        if (!this.pagination) {
            this.pagination = new AdminPagination('#pagination', {
                currentPage: this.currentPage,
                totalPages: this.totalPages,
                onPageChange: (page) => {
                    this.currentPage = page;
                    this.loadOrders();
                }
            });
        } else {
            this.pagination.update(this.currentPage, this.totalPages);
        }

        // 创建或更新手机端分页组件
        const mobilePaginationContainer = document.getElementById('paginationMobile');
        if (mobilePaginationContainer) {
            if (!this.paginationMobile) {
                console.log('Creating mobile pagination component for orders');
                this.paginationMobile = new AdminPagination('#paginationMobile', {
                    currentPage: this.currentPage,
                    totalPages: this.totalPages,
                    onPageChange: (page) => {
                        this.currentPage = page;
                        this.loadOrders();
                    }
                });
            } else {
                console.log('Updating mobile pagination component for orders');
                this.paginationMobile.update(this.currentPage, this.totalPages);
            }
        }
    }

    viewOrder(id) {
        AdminUtils.showToast('查看订单详情功能开发中', 'info');
    }

    async processOrder(id) {
        const confirmed = await AdminUtils.confirm('确定要处理这个订单吗？', '处理订单');
        if (!confirmed) return;

        try {
            const response = await fetch('/admin/order/processOrder', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ id, action: 'process' })
            });

            const result = await response.json();

            if (result.code === 200 || result.success) {
                AdminUtils.showToast('订单处理成功', 'success');
                this.loadOrders();
                this.loadStats();
            } else {
                AdminUtils.showToast(result.message || '处理失败', 'danger');
            }
        } catch (error) {
            console.error('处理订单失败:', error);
            AdminUtils.showToast('网络错误，请稍后重试', 'danger');
        }
    }



    async viewOrder(orderId) {
        try {
            const response = await fetch(`/admin/order/getOrderDetail?order_id=${orderId}`);
            const result = await response.json();

            if (result.success && result.code === 0) {
                const order = result.data;

                // 显示订单详情模态框
                const modalHtml = `
                    <div class="modal fade" id="orderDetailModal" tabindex="-1">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">订单详情</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label class="form-label">订单号</label>
                                            <div class="form-control-plaintext">${order.order_no || order.id}</div>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">状态</label>
                                            <div class="form-control-plaintext">
                                                <span class="badge ${OrderShopConfig.getOrderStatusColorClass(order.status_code || order.status)}">
                                                    ${OrderShopConfig.getOrderStatusName(order.status_code || order.status)}
                                                </span>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">用户</label>
                                            <div class="form-control-plaintext">${order.username || order.user_id}</div>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">商品</label>
                                            <div class="form-control-plaintext d-flex align-items-center">
                                                <img class="item-icon me-2" src="${this.itemIcons[order.item_id] || '/static/img/default.png'}" alt="商品图标" style="width: 32px; height: 32px;">
                                                ${order.item_name}
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">数量</label>
                                            <div class="form-control-plaintext">${order.quantity}</div>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">金额</label>
                                            <div class="form-control-plaintext">${AdminUtils.formatNumber(order.total_price)} ${OrderShopConfig.getCurrencyName(order.currency_type)}</div>
                                        </div>
                                        <div class="col-12">
                                            <label class="form-label">创建时间</label>
                                            <div class="form-control-plaintext">${this.formatDateTime(order.create_time)}</div>
                                        </div>
                                        ${order.error_message ? `
                                            <div class="col-12">
                                                <label class="form-label">错误信息</label>
                                                <div class="form-control-plaintext text-danger">${order.error_message}</div>
                                            </div>
                                        ` : ''}
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // 移除已存在的模态框
                const existingModal = document.getElementById('orderDetailModal');
                if (existingModal) {
                    existingModal.remove();
                }

                // 添加新模态框
                document.body.insertAdjacentHTML('beforeend', modalHtml);

                // 显示模态框
                const modal = new bootstrap.Modal(document.getElementById('orderDetailModal'));
                modal.show();

            } else {
                AdminUtils.showToast(result.message || '获取订单详情失败', 'danger');
            }
        } catch (error) {
            console.error('查看订单详情失败:', error);
            AdminUtils.showToast('网络错误，请稍后重试', 'danger');
        }
    }

    async resendOrder(orderId) {
        // 防止重复点击
        if (this.isProcessing) {
            AdminUtils.showToast('操作进行中，请稍候...', 'warning');
            return;
        }

        const confirmed = await AdminUtils.confirm('确定要补发这个订单吗？补发将重新发送商品给用户。', '补发订单');
        if (!confirmed) return;

        // 设置处理状态
        this.isProcessing = true;

        // 找到对应的按钮并设置加载状态
        const buttons = document.querySelectorAll(`[onclick*="orderManagement.resendOrder(${orderId})"]`);
        const originalTexts = [];

        buttons.forEach((button, index) => {
            originalTexts[index] = button.innerHTML;
            button.disabled = true;
            button.innerHTML = '<i class="bi bi-arrow-repeat spin"></i>';
        });

        try {
            const response = await fetch('/admin/order/resendOrder', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'same-origin',
                body: JSON.stringify({
                    order_id: orderId,
                    reason: '管理员手动补发'
                })
            });

            console.log('补发订单响应状态:', response.status);

            if (!response.ok) {
                // 尝试解析错误响应
                try {
                    const errorResult = await response.json();
                    throw new Error(errorResult.message || `HTTP error! status: ${response.status}`);
                } catch (parseError) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
            }

            const result = await response.json();

            console.log('补发订单响应:', result);

            if (result.success && result.code === 0) {
                AdminUtils.showToast('订单补发成功', 'success');
                // 清除缓存并重新加载数据
                await this.clearServerCache();
                this.clearClientCache();
                await this.loadOrders(true);
                await this.loadStats(true);
            } else {
                AdminUtils.showToast(result.message || '补发失败', 'danger');
            }
        } catch (error) {
            console.error('补发订单失败:', error);
            AdminUtils.showToast('补发订单失败: ' + error.message, 'danger');
        } finally {
            // 恢复按钮状态
            buttons.forEach((button, index) => {
                button.disabled = false;
                button.innerHTML = originalTexts[index];
            });

            // 清除处理状态
            this.isProcessing = false;
        }
    }

    async forceCompleteOrder(orderId) {
        // 防止重复点击
        if (this.isProcessing) {
            AdminUtils.showToast('操作进行中，请稍候...', 'warning');
            return;
        }

        const confirmed = await AdminUtils.confirm('确定要强制完成这个订单吗？这将直接标记订单为已完成状态。', '强制完成订单');
        if (!confirmed) return;

        // 设置处理状态
        this.isProcessing = true;

        // 找到对应的按钮并设置加载状态
        const buttons = document.querySelectorAll(`[onclick*="orderManagement.forceCompleteOrder(${orderId})"]`);
        const originalTexts = [];

        buttons.forEach((button, index) => {
            originalTexts[index] = button.innerHTML;
            button.disabled = true;
            button.innerHTML = '<i class="bi bi-check-circle spin"></i>';
        });

        try {
            const response = await fetch('/admin/order/forceCompleteOrder', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'same-origin',
                body: JSON.stringify({
                    order_id: orderId,
                    reason: '管理员强制完成'
                })
            });

            console.log('强制完成订单响应状态:', response.status);

            if (!response.ok) {
                // 尝试解析错误响应
                try {
                    const errorResult = await response.json();
                    throw new Error(errorResult.message || `HTTP error! status: ${response.status}`);
                } catch (parseError) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
            }

            const result = await response.json();

            console.log('强制完成订单响应:', result);

            if (result.success && result.code === 0) {
                AdminUtils.showToast('订单已强制完成', 'success');
                // 清除缓存并重新加载数据
                await this.clearServerCache();
                this.clearClientCache();
                await this.loadOrders(true);
                await this.loadStats(true);
            } else {
                AdminUtils.showToast(result.message || '强制完成失败', 'danger');
            }
        } catch (error) {
            console.error('强制完成订单失败:', error);
            AdminUtils.showToast('强制完成订单失败: ' + error.message, 'danger');
        } finally {
            // 恢复按钮状态
            buttons.forEach((button, index) => {
                button.disabled = false;
                button.innerHTML = originalTexts[index];
            });

            // 清除处理状态
            this.isProcessing = false;
        }
    }

    async cancelOrder(orderId) {
        // 防止重复点击
        if (this.isProcessing) {
            AdminUtils.showToast('操作进行中，请稍候...', 'warning');
            return;
        }

        const confirmed = await AdminUtils.confirm('确定要取消这个订单吗？', '取消订单');
        if (!confirmed) return;

        // 设置处理状态
        this.isProcessing = true;

        // 找到对应的按钮并设置加载状态
        const buttons = document.querySelectorAll(`[onclick*="orderManagement.cancelOrder(${orderId})"]`);
        const originalTexts = [];

        buttons.forEach((button, index) => {
            originalTexts[index] = button.innerHTML;
            button.disabled = true;
            button.innerHTML = '<i class="bi bi-x spin"></i>';
        });

        try {
            const response = await fetch('/admin/order/updateOrderStatus', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'same-origin',
                body: JSON.stringify({
                    order_id: orderId,
                    status: 'cancelled', // 已取消
                    reason: '管理员手动取消'
                })
            });

            console.log('取消订单响应状态:', response.status);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();

            console.log('取消订单响应:', result);

            if (result.success && result.code === 0) {
                AdminUtils.showToast('订单取消成功', 'success');
                // 清除缓存并重新加载数据
                await this.clearServerCache();
                this.clearClientCache();
                await this.loadOrders(true);
                await this.loadStats(true);
            } else {
                AdminUtils.showToast(result.message || '取消失败', 'danger');
            }
        } catch (error) {
            console.error('取消订单失败:', error);
            AdminUtils.showToast('取消订单失败: ' + error.message, 'danger');
        } finally {
            // 恢复按钮状态
            buttons.forEach((button, index) => {
                button.disabled = false;
                button.innerHTML = originalTexts[index];
            });

            // 清除处理状态
            this.isProcessing = false;
        }
    }

    async loadItemIcons(orders) {
        if (!orders || orders.length === 0) return;

        // 提取所有商品ID
        const itemIds = [...new Set(orders.map(order => order.item_id).filter(id => id))];

        if (itemIds.length === 0) return;

        try {
            const response = await fetch('/api/getItemIcon', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    item_ids: itemIds
                })
            });

            const result = await response.json();

            if (result.code === 200 && result.data) {
                console.log('商品图标加载成功:', result.data);
                this.itemIcons = result.data;

                // 更新已渲染的订单图标
                this.updateOrderIcons();
            } else {
                console.error('商品图标加载失败:', result.message);
            }
        } catch (error) {
            console.error('商品图标加载异常:', error);
        }
    }

    updateOrderIcons() {
        // 更新桌面端和手机端的商品图标
        document.querySelectorAll('[data-item-id]').forEach(element => {
            const itemId = element.getAttribute('data-item-id');
            if (itemId && this.itemIcons[itemId]) {
                const iconUrl = this.itemIcons[itemId];

                const imgElement = element.querySelector('.item-icon');
                if (imgElement) {
                    imgElement.src = iconUrl;
                    console.log('Updated icon for item', itemId, 'to', iconUrl);
                }
            }
        });
    }

    formatDateTime(dateStr) {
        if (!dateStr) return '-';
        try {
            const date = new Date(dateStr);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        } catch (error) {
            console.error('日期格式化失败:', error);
            return dateStr;
        }
    }

    exportOrders() {
        AdminUtils.showToast('导出功能开发中', 'info');
    }
}

// 初始化订单管理
let orderManagement;

// 外联方式：直接使用orderManagement对象的方法
document.addEventListener('DOMContentLoaded', () => {
    // 初始化双UI系统
    if (typeof initDualUISystem === 'function') {
        initDualUISystem();
    } else {
        // 如果函数不存在，手动初始化双UI系统
        const isMobile = window.innerWidth <= 768;

        // 移除所有UI类
        document.body.classList.remove('mobile-ui', 'desktop-ui');

        // 添加正确的UI类
        document.body.classList.add(isMobile ? 'mobile-ui' : 'desktop-ui');

        console.log('双UI系统已初始化:', isMobile ? 'mobile-ui' : 'desktop-ui');
        console.log('Body classes:', document.body.className);

        // 监听窗口大小变化
        window.addEventListener('resize', () => {
            const newIsMobile = window.innerWidth <= 768;
            document.body.classList.remove('mobile-ui', 'desktop-ui');
            document.body.classList.add(newIsMobile ? 'mobile-ui' : 'desktop-ui');
            console.log('UI模式切换:', newIsMobile ? 'mobile-ui' : 'desktop-ui');
        });
    }

    // 初始化订单管理
    try {
        orderManagement = new OrderManagement();
        console.log('订单管理对象初始化成功:', orderManagement);

        // 验证关键方法是否存在
        if (typeof orderManagement.resendOrder !== 'function') {
            console.error('resendOrder方法不存在!');
        }
        if (typeof orderManagement.forceCompleteOrder !== 'function') {
            console.error('forceCompleteOrder方法不存在!');
        }
        if (typeof orderManagement.cancelOrder !== 'function') {
            console.error('cancelOrder方法不存在!');
        }

    } catch (error) {
        console.error('订单管理对象初始化失败:', error);
    }
});
</script>
{/block}
