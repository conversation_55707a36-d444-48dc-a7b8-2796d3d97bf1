{extend name="admin/layout/base" /}

{block name="css"}
    <link href="/static/css/admin-settings.css" rel="stylesheet">
{/block}

{block name="content"}
<div class="page-header">
    <div class="ui-card">
        <div class="ui-card-header">
            <h1 class="page-title">
                <i class="bi bi-gear"></i>
                系统设置
            </h1>
            <p class="page-description text-muted mb-0">管理系统配置和参数设置</p>
        </div>
    </div>
</div>

<div class="page-content">
    <!-- 设置导航标签 -->
    <div class="ui-card mb-4">
        <div class="ui-card-body">
            <ul class="nav nav-tabs" id="settingsTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab">
                        <i class="bi bi-gear"></i> 基本设置
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="shop-tab" data-bs-toggle="tab" data-bs-target="#shop" type="button" role="tab">
                        <i class="bi bi-shop"></i> 商城设置
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="payment-tab" data-bs-toggle="tab" data-bs-target="#payment" type="button" role="tab">
                        <i class="bi bi-credit-card"></i> 支付设置
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="system-tab" data-bs-toggle="tab" data-bs-target="#system" type="button" role="tab">
                        <i class="bi bi-cpu"></i> 系统监控
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="logs-tab" data-bs-toggle="tab" data-bs-target="#logs" type="button" role="tab">
                        <i class="bi bi-file-text"></i> 日志管理
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="admin-tab" data-bs-toggle="tab" data-bs-target="#admin" type="button" role="tab">
                        <i class="bi bi-person-gear"></i> 管理员管理
                    </button>
                </li>
            </ul>
        </div>
    </div>

    <!-- 设置内容 -->
    <div class="tab-content" id="settingsTabContent">
        <!-- 基本设置 -->
        <div class="tab-pane fade show active" id="general" role="tabpanel">
            <div class="ui-card">
                <div class="ui-card-header">
                    <h5 class="mb-0">基本设置</h5>
                </div>
                <div class="ui-card-body">
                    <form id="generalSettingsForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="siteName" class="form-label">网站名称</label>
                                    <input type="text" class="form-control" id="siteName" name="site_name" placeholder="请输入网站名称">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="siteDescription" class="form-label">网站描述</label>
                                    <input type="text" class="form-control" id="siteDescription" name="site_description" placeholder="请输入网站描述">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="adminEmail" class="form-label">管理员邮箱</label>
                                    <input type="email" class="form-control" id="adminEmail" name="admin_email" placeholder="请输入管理员邮箱">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="timezone" class="form-label">时区设置</label>
                                    <select class="form-select" id="timezone" name="timezone">
                                        <option value="Asia/Shanghai">Asia/Shanghai</option>
                                        <option value="UTC">UTC</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <!-- 全局配置管理 -->
                        <div class="mb-4">
                            <h6 class="text-muted mb-3">全局配置管理</h6>

                            <!-- 货币配置管理 -->
                            <div class="card mb-3">
                                <div class="card-header">
                                    <h6 class="mb-0">货币配置管理</h6>
                                </div>
                                <div class="card-body">
                                    <div id="currencyManagement">
                                        <!-- 货币配置将通过JavaScript加载 -->
                                    </div>
                                </div>
                            </div>

                            <!-- 分类配置管理 -->
                            <div class="card border-0 shadow-sm">
                                <div class="card-header bg-light border-0 d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0 text-dark">
                                        <i class="bi bi-tags text-primary me-2"></i>商品分类管理
                                    </h6>
                                    <button type="button" class="btn btn-primary btn-sm rounded-pill" onclick="showAddCategoryModal()">
                                        <i class="bi bi-plus-circle me-1"></i>添加分类
                                    </button>
                                </div>
                                <div class="card-body">
                                    <div id="categoryManagement">
                                        <!-- 分类配置将通过JavaScript加载 -->
                                        <div class="text-center py-5">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">加载中...</span>
                                            </div>
                                            <div class="mt-3 text-muted">正在加载分类配置...</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="maintenanceMode" class="form-label">维护模式</label>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="maintenanceMode" name="maintenance_mode">
                                <label class="form-check-label" for="maintenanceMode">
                                    启用维护模式
                                </label>
                            </div>
                        </div>
                        <button type="button" class="btn btn-primary" onclick="saveGeneralSettings()">
                            <i class="bi bi-check"></i> 保存设置
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- 商城设置 -->
        <div class="tab-pane fade" id="shop" role="tabpanel">
            <div class="ui-card">
                <div class="ui-card-header">
                    <h5 class="mb-0">商城设置</h5>
                </div>
                <div class="ui-card-body">
                    <form id="shopSettingsForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="shopEnabled" class="form-label">商城状态</label>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="shopEnabled" name="shop_enabled">
                                        <label class="form-check-label" for="shopEnabled">
                                            启用商城
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="itemsPerPage" class="form-label">每页商品数</label>
                                    <input type="number" class="form-control" id="itemsPerPage" name="items_per_page" min="1" max="100" value="12">
                                    <div class="form-text">设置商城每页显示的商品数量</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="maxPurchaseQuantity" class="form-label">最大购买数量</label>
                                    <input type="number" class="form-control" id="maxPurchaseQuantity" name="max_purchase_quantity" min="1" max="999" value="99">
                                    <div class="form-text">设置单次购买的最大数量</div>
                                </div>
                            </div>
                        </div>
                        <button type="button" class="btn btn-primary" onclick="saveShopSettings()">
                            <i class="bi bi-check"></i> 保存设置
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- 支付设置 -->
        <div class="tab-pane fade" id="payment" role="tabpanel">
            <div class="ui-card">
                <div class="ui-card-header">
                    <h5 class="mb-0">支付设置</h5>
                </div>
                <div class="ui-card-body">
                    <form id="paymentSettingsForm">
                        <div class="mb-4">
                            <label class="form-label fw-semibold text-dark mb-3">
                                <i class="bi bi-credit-card text-primary me-2"></i>支付方式配置
                            </label>
                            <div class="row g-3">
                                <!-- C币支付 -->
                                <div class="col-12 col-sm-6 col-xl-3">
                                    <div class="card h-100 border-0 shadow-sm">
                                        <div class="card-body p-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="enableCcoin">
                                                <label class="form-check-label w-100" for="enableCcoin">
                                                    <div class="d-flex align-items-center">
                                                        <span class="badge bg-success rounded-circle me-2" style="width: 24px; height: 24px; display: flex; align-items: center; justify-content: center;">
                                                            <i class="bi bi-currency-dollar"></i>
                                                        </span>
                                                        <span class="fw-medium">C币支付</span>
                                                    </div>
                                                    <small class="text-muted d-block mt-1">启用C币作为支付方式</small>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 泡点支付 -->
                                <div class="col-12 col-sm-6 col-xl-3">
                                    <div class="card h-100 border-0 shadow-sm">
                                        <div class="card-body p-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="enableBubblePoint">
                                                <label class="form-check-label w-100" for="enableBubblePoint">
                                                    <div class="d-flex align-items-center">
                                                        <span class="badge bg-primary rounded-circle me-2" style="width: 24px; height: 24px; display: flex; align-items: center; justify-content: center;">
                                                            <i class="bi bi-circle-fill"></i>
                                                        </span>
                                                        <span class="fw-medium">泡点支付</span>
                                                    </div>
                                                    <small class="text-muted d-block mt-1">启用泡点作为支付方式</small>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 积分支付 -->
                                <div class="col-12 col-sm-6 col-xl-3">
                                    <div class="card h-100 border-0 shadow-sm">
                                        <div class="card-body p-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="enableHahaPoint">
                                                <label class="form-check-label w-100" for="enableHahaPoint">
                                                    <div class="d-flex align-items-center">
                                                        <span class="badge bg-warning rounded-circle me-2" style="width: 24px; height: 24px; display: flex; align-items: center; justify-content: center;">
                                                            <i class="bi bi-star-fill"></i>
                                                        </span>
                                                        <span class="fw-medium">积分支付</span>
                                                    </div>
                                                    <small class="text-muted d-block mt-1">启用积分作为支付方式</small>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 显示充值按钮 -->
                                <div class="col-12 col-sm-6 col-xl-3">
                                    <div class="card h-100 border-0 shadow-sm">
                                        <div class="card-body p-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="showRechargeButton">
                                                <label class="form-check-label w-100" for="showRechargeButton">
                                                    <div class="d-flex align-items-center">
                                                        <span class="badge bg-info rounded-circle me-2" style="width: 24px; height: 24px; display: flex; align-items: center; justify-content: center;">
                                                            <i class="bi bi-plus-circle-fill"></i>
                                                        </span>
                                                        <span class="fw-medium">显示充值按钮</span>
                                                    </div>
                                                    <small class="text-muted d-block mt-1">在前端显示充值功能</small>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- 充值比例设置 -->
                        <div class="mb-4">
                            <h6 class="text-muted mb-3">充值比例设置</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="ccoinRechargeRatio" class="form-label">C币充值比例</label>
                                        <div class="input-group">
                                            <span class="input-group-text">1元 =</span>
                                            <input type="number" class="form-control" id="ccoinRechargeRatio" name="ccoin_recharge_ratio" min="1" value="100" placeholder="100">
                                            <span class="input-group-text">C币</span>
                                        </div>
                                        <div class="form-text">设置充值1元人民币可以获得多少C币</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="coinRechargeRatio" class="form-label">泡点充值比例</label>
                                        <div class="input-group">
                                            <span class="input-group-text">1元 =</span>
                                            <input type="number" class="form-control" id="coinRechargeRatio" name="coin_recharge_ratio" min="1" value="1000" placeholder="1000">
                                            <span class="input-group-text">泡点</span>
                                        </div>
                                        <div class="form-text">设置充值1元人民币可以获得多少泡点</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 充值赠送设置 -->
                        <div class="mb-4">
                            <h6 class="text-muted mb-3">充值赠送设置</h6>

                            <!-- 同货币赠送 -->
                            <div class="mb-3">
                                <label class="form-label fw-bold">同货币赠送</label>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <div class="form-check form-switch mb-2">
                                                <input class="form-check-input" type="checkbox" id="ccoinBonusEnabled" name="ccoin_bonus_enabled">
                                                <label class="form-check-label" for="ccoinBonusEnabled">
                                                    启用C币充值赠送C币
                                                </label>
                                            </div>
                                            <div class="input-group">
                                                <span class="input-group-text">每100C币赠送</span>
                                                <input type="number" class="form-control" id="ccoinBonusRatio" name="ccoin_bonus_ratio" min="0" value="10" placeholder="10">
                                                <span class="input-group-text">C币</span>
                                            </div>
                                            <div class="form-text">例如：充值获得100C币，额外赠送10C币</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <div class="form-check form-switch mb-2">
                                                <input class="form-check-input" type="checkbox" id="coinBonusEnabled" name="coin_bonus_enabled">
                                                <label class="form-check-label" for="coinBonusEnabled">
                                                    启用泡点充值赠送泡点
                                                </label>
                                            </div>
                                            <div class="input-group">
                                                <span class="input-group-text">每1000泡点赠送</span>
                                                <input type="number" class="form-control" id="coinBonusRatio" name="coin_bonus_ratio" min="0" value="100" placeholder="100">
                                                <span class="input-group-text">泡点</span>
                                            </div>
                                            <div class="form-text">例如：充值获得1000泡点，额外赠送100泡点</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 赠送积分 -->
                            <div class="mb-3">
                                <label class="form-label fw-bold">赠送积分</label>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <div class="form-check form-switch mb-2">
                                                <input class="form-check-input" type="checkbox" id="ccoinSilverBonusEnabled" name="ccoin_silver_bonus_enabled">
                                                <label class="form-check-label" for="ccoinSilverBonusEnabled">
                                                    启用C币充值赠送积分
                                                </label>
                                            </div>
                                            <div class="input-group">
                                                <span class="input-group-text">每充值1元赠送</span>
                                                <input type="number" class="form-control" id="ccoinSilverBonusRatio" name="ccoin_silver_bonus_ratio" min="0" value="100" placeholder="100">
                                                <span class="input-group-text">积分</span>
                                            </div>
                                            <div class="form-text">例如：充值10元C币，额外赠送1000积分</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <div class="form-check form-switch mb-2">
                                                <input class="form-check-input" type="checkbox" id="coinSilverBonusEnabled" name="coin_silver_bonus_enabled">
                                                <label class="form-check-label" for="coinSilverBonusEnabled">
                                                    启用泡点充值赠送积分
                                                </label>
                                            </div>
                                            <div class="input-group">
                                                <span class="input-group-text">每充值1元赠送</span>
                                                <input type="number" class="form-control" id="coinSilverBonusRatio" name="coin_silver_bonus_ratio" min="0" value="10" placeholder="10">
                                                <span class="input-group-text">积分</span>
                                            </div>
                                            <div class="form-text">例如：充值10元泡点，额外赠送100积分</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 充值限额设置 -->
                        <div class="mb-4">
                            <h6 class="text-muted mb-3">充值限额设置</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="minRechargeAmount" class="form-label">最小充值金额</label>
                                        <input type="number" class="form-control" id="minRechargeAmount" name="min_recharge_amount" min="1" value="1">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="maxRechargeAmount" class="form-label">最大充值金额</label>
                                        <input type="number" class="form-control" id="maxRechargeAmount" name="max_recharge_amount" min="1" value="10000">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button type="button" class="btn btn-primary" onclick="savePaymentSettings()">
                            <i class="bi bi-check"></i> 保存设置
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- 系统监控 -->
        <div class="tab-pane fade" id="system" role="tabpanel">
            <div class="ui-card">
                <div class="ui-card-header">
                    <h5 class="mb-0">系统监控</h5>
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="refreshSystemInfo()">
                        <i class="bi bi-arrow-clockwise"></i> 刷新
                    </button>
                </div>
                <div class="ui-card-body">
                    <div id="systemMonitorContent">
                        <div class="text-center">
                            <div class="loading-spinner">
                                <i class="bi bi-arrow-clockwise"></i>
                                <span>加载中...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 日志管理 -->
        <div class="tab-pane fade" id="logs" role="tabpanel">
            <div class="ui-card">
                <div class="ui-card-header">
                    <h5 class="mb-0">日志管理</h5>
                    <div class="card-tools">
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="refreshLogList()">
                            <i class="bi bi-arrow-clockwise"></i> 刷新
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearAllLogs()">
                            <i class="bi bi-trash"></i> 清空日志
                        </button>
                    </div>
                </div>
                <div class="ui-card-body">
                    <div id="logManagementContent">
                        <div class="text-center">
                            <div class="loading-spinner">
                                <i class="bi bi-arrow-clockwise"></i>
                                <span>加载中...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 管理员管理 -->
        <div class="tab-pane fade" id="admin" role="tabpanel">
            <div class="ui-card">
                <div class="ui-card-header">
                    <h5 class="mb-0">管理员管理</h5>
                    <div class="card-tools">
                        <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addAdminModal">
                            <i class="bi bi-plus-circle"></i> 添加管理员
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="loadAdminList()">
                            <i class="bi bi-arrow-clockwise"></i> 刷新列表
                        </button>
                    </div>
                </div>
                <div class="ui-card-body">
                    <div class="mb-3">
                        <small class="text-muted">当前管理员总数: <span id="adminCount">0</span></small>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>用户名</th>
                                    <th>邮箱</th>
                                    <th>权限级别</th>
                                    <th>状态</th>
                                    <th>创建时间</th>
                                    <th>最后登录</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="adminTableBody">
                                <tr>
                                    <td colspan="7" class="text-center text-muted">
                                        <div class="py-4">
                                            <i class="bi bi-hourglass-split"></i>
                                            正在加载管理员列表...
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Bootstrap 货币编辑模态框 -->
<div class="modal fade" id="currencyModal" tabindex="-1" aria-labelledby="currencyModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="currencyModalLabel">编辑货币配置</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="currencyForm">
                    <input type="hidden" id="currencyId" name="currency_id">
                    <div class="mb-3">
                        <label for="currencyName" class="form-label">货币名称 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="currencyName" name="name" required>
                        <div class="form-text">显示在界面上的货币名称</div>
                    </div>
                    <div class="mb-3">
                        <label for="currencyShortName" class="form-label">简称 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="currencyShortName" name="short_name" required>
                        <div class="form-text">货币的简短名称</div>
                    </div>
                    <div class="mb-3">
                        <label for="currencySymbol" class="form-label">符号</label>
                        <input type="text" class="form-control" id="currencySymbol" name="symbol" placeholder="如：💰、₿、🪙">
                        <div class="form-text">可选的货币符号或图标</div>
                    </div>
                    <div class="mb-3">
                        <label for="currencyColorClass" class="form-label">颜色样式</label>
                        <select class="form-select" id="currencyColorClass" name="color_class">
                            <option value="bg-primary">蓝色 (Primary)</option>
                            <option value="bg-secondary">灰色 (Secondary)</option>
                            <option value="bg-success">绿色 (Success)</option>
                            <option value="bg-danger">红色 (Danger)</option>
                            <option value="bg-warning">黄色 (Warning)</option>
                            <option value="bg-info">青色 (Info)</option>
                            <option value="bg-dark">黑色 (Dark)</option>
                        </select>
                        <div class="form-text">用于在界面上显示的颜色主题</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle"></i> 取消
                </button>
                <button type="button" class="btn btn-primary" onclick="saveCurrency()">
                    <i class="bi bi-check-circle"></i> 保存
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Bootstrap 分类编辑模态框 -->
<div class="modal fade" id="categoryModal" tabindex="-1" aria-labelledby="categoryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="categoryModalLabel">编辑分类</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="categoryForm">
                    <input type="hidden" id="categoryId" name="category_id">
                    <div class="mb-3">
                        <label for="categoryName" class="form-label">分类名称 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="categoryName" name="category_name" required>
                        <div class="form-text">商品分类的显示名称</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle"></i> 取消
                </button>
                <button type="button" class="btn btn-primary" onclick="saveCategory()">
                    <i class="bi bi-check-circle"></i> 保存
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 添加管理员模态框 -->
<div class="modal fade" id="addAdminModal" tabindex="-1" aria-labelledby="addAdminModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addAdminModalLabel">添加管理员</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addAdminForm">
                    <div class="mb-3">
                        <label for="adminUsername" class="form-label">用户名 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="adminUsername" name="username" required
                               placeholder="请输入用户名（3-20位字母数字下划线）">
                        <div class="form-text">用户名只能包含字母、数字和下划线，长度3-20位</div>
                    </div>
                    <div class="mb-3">
                        <label for="adminPassword" class="form-label">密码 <span class="text-danger">*</span></label>
                        <input type="password" class="form-control" id="adminPassword" name="password" required
                               placeholder="请输入密码（至少6位）">
                        <div class="form-text">密码长度不能少于6位</div>
                    </div>
                    <div class="mb-3">
                        <label for="addAdminEmail" class="form-label">邮箱</label>
                        <input type="email" class="form-control" id="addAdminEmail" name="email"
                               placeholder="请输入邮箱地址（可选）">
                    </div>
                    <div class="mb-3">
                        <label for="adminLevel" class="form-label">权限级别</label>
                        <select class="form-select" id="adminLevel" name="level">
                            <option value="1">超级管理员</option>
                            <option value="2" selected>普通管理员</option>
                        </select>
                        <div class="form-text">超级管理员拥有所有权限，普通管理员权限受限</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle"></i> 取消
                </button>
                <button type="button" class="btn btn-primary" onclick="submitAddAdmin()">
                    <i class="bi bi-check-circle"></i> 添加管理员
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 编辑管理员模态框 -->
<div class="modal fade" id="editAdminModal" tabindex="-1" aria-labelledby="editAdminModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editAdminModalLabel">编辑管理员</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editAdminForm">
                    <input type="hidden" id="editAdminUsername" name="username">
                    <div class="mb-3">
                        <label class="form-label">用户名</label>
                        <input type="text" class="form-control" id="editAdminUsernameDisplay" readonly>
                        <div class="form-text">用户名不可修改</div>
                    </div>
                    <div class="mb-3">
                        <label for="editAdminEmail" class="form-label">邮箱</label>
                        <input type="email" class="form-control" id="editAdminEmail" name="email"
                               placeholder="请输入邮箱地址">
                    </div>
                    <div class="mb-3">
                        <label for="editAdminLevel" class="form-label">权限级别</label>
                        <select class="form-select" id="editAdminLevel" name="level">
                            <option value="1">超级管理员</option>
                            <option value="2">普通管理员</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="editAdminStatus" class="form-label">状态</label>
                        <select class="form-select" id="editAdminStatus" name="status">
                            <option value="1">启用</option>
                            <option value="0">禁用</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle"></i> 取消
                </button>
                <button type="button" class="btn btn-primary" onclick="submitEditAdmin()">
                    <i class="bi bi-check-circle"></i> 保存修改
                </button>
            </div>
        </div>
    </div>
</div>

{/block}

{block name="js"}
    <script>
        // 商城配置 - 防止重复声明
        if (typeof window.ShopConfig === 'undefined') {
            window.ShopConfig = <?php echo \app\service\ShopConfigService::getFrontendConfigJson(); ?>;
        }
    </script>
    <script src="/static/js/modules/admin-settings.js?v=<?php echo time(); ?>"></script>
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，开始初始化');
            console.log('AdminSettingsManager 类型:', typeof AdminSettingsManager);
            console.log('ShopConfig:', window.ShopConfig);

            // 更新支付设置的货币名称
            updatePaymentCurrencyLabels();

            if (typeof AdminSettingsManager !== 'undefined') {
                try {
                    window.settingsManager = new AdminSettingsManager();
                    window.settingsManager.init();
                    console.log('设置管理器初始化成功');

                    // 更新支付设置中的货币标签
                    setTimeout(() => {
                        updatePaymentCurrencyLabels();
                    }, 1000);
                } catch (error) {
                    console.error('设置管理器初始化失败:', error);
                }
            } else {
                console.error('AdminSettingsManager 未定义');
            }

            // 添加标签页切换事件监听器
            const adminTab = document.getElementById('admin-tab');
            if (adminTab) {
                adminTab.addEventListener('shown.bs.tab', function (event) {
                    // 当切换到管理员管理标签页时，加载管理员列表
                    loadAdminList();
                });
            }
        });

        // 更新支付设置的货币名称
        function updatePaymentCurrencyLabels() {
            if (window.ShopConfig && window.ShopConfig.currency_types) {
                // 更新C币支付标签
                const ccoinLabel = document.querySelector('label[for="enableCcoin"]');
                if (ccoinLabel && window.ShopConfig.currency_types[3]) {
                    ccoinLabel.innerHTML = window.ShopConfig.currency_types[3].name + '支付';
                }

                // 更新泡点支付标签
                const bubblePointLabel = document.querySelector('label[for="enableBubblePoint"]');
                if (bubblePointLabel && window.ShopConfig.currency_types[1]) {
                    bubblePointLabel.innerHTML = window.ShopConfig.currency_types[1].name + '支付';
                }

                // 更新积分支付标签
                const hahaPointLabel = document.querySelector('label[for="enableHahaPoint"]');
                if (hahaPointLabel && window.ShopConfig.currency_types[2]) {
                    hahaPointLabel.innerHTML = window.ShopConfig.currency_types[2].name + '支付';
                }
            }
        }

        // 全局函数
        function saveGeneralSettings() {
            if (window.settingsManager) {
                window.settingsManager.saveGeneralSettings();
            }
        }

        function savePaymentSettings() {
            if (window.settingsManager) {
                window.settingsManager.savePaymentSettings();
            }
        }

        function saveShopSettings() {
            if (window.settingsManager) {
                window.settingsManager.saveShopSettings();
            }
        }

        // 更新支付设置中的货币标签
        function updatePaymentCurrencyLabels() {
            // 获取货币名称的函数
            function getCurrencyName(type) {
                // 首先尝试从全局配置获取
                if (window.ShopConfig && window.ShopConfig.currency_types) {
                    const currency = window.ShopConfig.currency_types[String(type)];
                    if (currency) {
                        return currency.name;
                    }
                }

                // 降级到本地配置
                switch (String(type)) {
                    case '1': return '泡点';
                    case '2': return '积分';
                    case '3': return 'C币';
                    default: return '未知货币';
                }
            }

            // 更新C币支付标签
            const cCoinLabel = document.querySelector('label[for="enableCcoin"] .fw-medium');
            if (cCoinLabel) {
                cCoinLabel.textContent = getCurrencyName('3') + '支付';
            }

            // 更新泡点支付标签
            const pointLabel = document.querySelector('label[for="enableBubblePoint"] .fw-medium');
            if (pointLabel) {
                pointLabel.textContent = getCurrencyName('1') + '支付';
            }

            // 更新积分支付标签
            const silverLabel = document.querySelector('label[for="enableHahaPoint"] .fw-medium');
            if (silverLabel) {
                silverLabel.textContent = getCurrencyName('2') + '支付';
            }
        }

        // 全局配置管理函数
        function editCurrency(id, name, shortName, symbol, colorClass) {
            if (window.settingsManager) {
                window.settingsManager.editCurrency(id, name, shortName, symbol, colorClass);
            }
        }

        function saveCurrency() {
            if (window.settingsManager) {
                window.settingsManager.saveCurrency();
            }
        }

        function showAddCategoryModal() {
            if (window.settingsManager) {
                window.settingsManager.showAddCategoryModal();
            }
        }

        function editCategory(id, name) {
            if (window.settingsManager) {
                window.settingsManager.editCategory(id, name);
            }
        }

        function saveCategory() {
            if (window.settingsManager) {
                window.settingsManager.saveCategory();
            }
        }

        function deleteCategory(id, name) {
            if (window.settingsManager) {
                window.settingsManager.deleteCategory(id, name);
            }
        }

        // 通用Toast通知函数
        function showToast(message, type = 'info') {
            // 创建Toast元素
            const toastId = 'toast-' + Date.now();
            const toastHtml = `
                <div id="${toastId}" class="toast align-items-center text-white bg-${type === 'success' ? 'success' : type === 'danger' ? 'danger' : 'primary'} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="d-flex">
                        <div class="toast-body">
                            ${message}
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                </div>
            `;

            // 获取或创建Toast容器
            let toastContainer = document.querySelector('.toast-container');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
                toastContainer.style.zIndex = '9999';
                document.body.appendChild(toastContainer);
            }

            // 添加Toast到容器
            toastContainer.insertAdjacentHTML('beforeend', toastHtml);

            // 显示Toast
            const toastElement = document.getElementById(toastId);
            const toast = new bootstrap.Toast(toastElement, {
                autohide: true,
                delay: 3000
            });

            toast.show();

            // 自动移除Toast元素
            toastElement.addEventListener('hidden.bs.toast', function () {
                toastElement.remove();
            });
        }

        // 管理员管理相关函数
        let currentAdmins = [];

        // 加载管理员列表
        async function loadAdminList() {
            try {
                const response = await fetch('/admin/getAdminList', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const data = await response.json();

                if (data.code === 0 || data.success) {
                    currentAdmins = data.data || [];
                    renderAdminList(currentAdmins);
                    document.getElementById('adminCount').textContent = currentAdmins.length;
                } else {
                    showToast('加载管理员列表失败: ' + (data.message || '未知错误'), 'danger');
                }
            } catch (error) {
                console.error('加载管理员列表失败:', error);
                showToast('加载管理员列表失败，请稍后重试', 'danger');
            }
        }

        // 渲染管理员列表
        function renderAdminList(admins) {
            const tbody = document.getElementById('adminTableBody');

            if (!admins || admins.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" class="text-center text-muted">
                            <div class="py-4">
                                <i class="bi bi-inbox"></i>
                                暂无管理员数据
                            </div>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = admins.map(admin => `
                <tr>
                    <td>
                        <strong>${admin.username}</strong>
                        ${admin.username === 'admin' ? '<span class="badge bg-warning ms-1">系统</span>' : ''}
                    </td>
                    <td>${admin.email || '-'}</td>
                    <td>
                        <span class="badge ${admin.level === 1 ? 'bg-danger' : 'bg-primary'}">
                            ${admin.level === 1 ? '超级管理员' : '普通管理员'}
                        </span>
                    </td>
                    <td>
                        <span class="badge ${admin.status === 1 ? 'bg-success' : 'bg-secondary'}">
                            ${admin.status === 1 ? '启用' : '禁用'}
                        </span>
                    </td>
                    <td>${admin.create_time || '-'}</td>
                    <td>${admin.last_login || '-'}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button type="button" class="btn btn-outline-primary" onclick="editAdmin('${admin.username}')">
                                <i class="bi bi-pencil"></i> 编辑
                            </button>
                            ${admin.username !== 'admin' ? `
                                <button type="button" class="btn btn-outline-danger" onclick="deleteAdmin('${admin.username}')">
                                    <i class="bi bi-trash"></i> 删除
                                </button>
                            ` : ''}
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        // 提交添加管理员
        async function submitAddAdmin() {
            const form = document.getElementById('addAdminForm');
            const formData = new FormData(form);

            // 验证表单
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const data = Object.fromEntries(formData);

            try {
                const response = await fetch('/admin/addAdmin', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();

                if (result.code === 0 || result.success) {
                    showToast('管理员添加成功', 'success');
                    bootstrap.Modal.getInstance(document.getElementById('addAdminModal')).hide();
                    form.reset();
                    loadAdminList();
                } else {
                    showToast('添加管理员失败: ' + (result.message || '未知错误'), 'danger');
                }
            } catch (error) {
                console.error('添加管理员失败:', error);
                showToast('添加管理员失败，请稍后重试', 'danger');
            }
        }

        // 编辑管理员
        function editAdmin(username) {
            const admin = currentAdmins.find(a => a.username === username);
            if (!admin) {
                showToast('管理员信息不存在', 'danger');
                return;
            }

            // 填充编辑表单
            document.getElementById('editAdminUsername').value = admin.username;
            document.getElementById('editAdminUsernameDisplay').value = admin.username;
            document.getElementById('editAdminEmail').value = admin.email || '';
            document.getElementById('editAdminLevel').value = admin.level || 2;
            document.getElementById('editAdminStatus').value = admin.status || 1;

            // 显示编辑模态框
            const modal = new bootstrap.Modal(document.getElementById('editAdminModal'));
            modal.show();
        }

        // 提交编辑管理员
        async function submitEditAdmin() {
            const form = document.getElementById('editAdminForm');
            const formData = new FormData(form);
            const username = formData.get('username');

            const data = {
                email: formData.get('email'),
                level: parseInt(formData.get('level')),
                status: parseInt(formData.get('status'))
            };

            try {
                const response = await fetch(`/admin/updateAdmin/${username}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();

                if (result.code === 0 || result.success) {
                    showToast('管理员信息更新成功', 'success');
                    bootstrap.Modal.getInstance(document.getElementById('editAdminModal')).hide();
                    loadAdminList();
                } else {
                    showToast('更新管理员信息失败: ' + (result.message || '未知错误'), 'danger');
                }
            } catch (error) {
                console.error('更新管理员信息失败:', error);
                showToast('更新管理员信息失败，请稍后重试', 'danger');
            }
        }

        // 删除管理员
        function deleteAdmin(username) {
            if (username === 'admin') {
                showToast('不能删除系统管理员账户', 'danger');
                return;
            }

            if (confirm(`确定要删除管理员 "${username}" 吗？此操作不可撤销。`)) {
                performDeleteAdmin(username);
            }
        }

        // 执行删除管理员
        async function performDeleteAdmin(username) {
            try {
                const response = await fetch(`/admin/deleteAdmin/${username}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const result = await response.json();

                if (result.code === 0 || result.success) {
                    showToast('管理员删除成功', 'success');
                    loadAdminList();
                } else {
                    showToast('删除管理员失败: ' + (result.message || '未知错误'), 'danger');
                }
            } catch (error) {
                console.error('删除管理员失败:', error);
                showToast('删除管理员失败，请稍后重试', 'danger');
            }
        }

    </script>
{/block}
