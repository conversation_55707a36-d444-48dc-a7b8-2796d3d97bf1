{extend name="admin/layout/base" /}

{block name="css"}
<style>
.item-card {
    cursor: pointer;
    transition: all 0.2s ease;
}

.item-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.item-card.selected {
    border-color: #0d6efd;
    background-color: #f8f9ff;
}

.border-4 {
    border-width: 4px !important;
}

.fs-2 {
    font-size: 1.5rem !important;
}
</style>
{/block}

{block name="content"}
<div class="admin-card">
    <div class="admin-card-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h5 class="mb-0">
                    <i class="bi bi-gift text-primary me-2"></i>道具发送
                </h5>
                <p class="text-muted mb-0 small">向用户发送游戏道具</p>
            </div>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-primary btn-sm" onclick="openSendItemModal()">
                    <i class="bi bi-plus-circle me-1"></i>发送道具
                </button>
            </div>
        </div>
    </div>
    <div class="admin-card-body">

        <!-- 发送记录统计卡片 -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card border-start border-primary border-4 h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1">
                                <div class="small text-primary text-uppercase fw-bold">今日发送</div>
                                <div class="h5 mb-0 fw-bold" id="todayCount">0</div>
                            </div>
                            <div class="text-primary">
                                <i class="bi bi-calendar-day fs-2"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card border-start border-success border-4 h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1">
                                <div class="small text-success text-uppercase fw-bold">本周发送</div>
                                <div class="h5 mb-0 fw-bold" id="weekCount">0</div>
                            </div>
                            <div class="text-success">
                                <i class="bi bi-calendar-week fs-2"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card border-start border-info border-4 h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1">
                                <div class="small text-info text-uppercase fw-bold">本月发送</div>
                                <div class="h5 mb-0 fw-bold" id="monthCount">0</div>
                            </div>
                            <div class="text-info">
                                <i class="bi bi-calendar-month fs-2"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card border-start border-warning border-4 h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1">
                                <div class="small text-warning text-uppercase fw-bold">总计发送</div>
                                <div class="h5 mb-0 fw-bold" id="totalCount">0</div>
                            </div>
                            <div class="text-warning">
                                <i class="bi bi-gift fs-2"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 发送记录 -->
        <div class="admin-card">
            <div class="admin-card-header">
                <h6 class="mb-0 fw-bold text-primary">发送记录</h6>
            </div>
            <div class="admin-card-body">
                <!-- 道具发送记录标题 -->
                <div class="mb-3">
                    <h6 class="mb-0 fw-bold text-primary">
                        <i class="bi bi-gift me-1"></i>道具发送记录
                    </h6>
                </div>

                <!-- 道具发送记录内容 -->
                <div id="item-records">
                        <!-- 道具记录筛选 -->
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="itemSearchUsername" class="form-label">用户名</label>
                                <input type="text" class="form-control" id="itemSearchUsername" placeholder="输入用户名搜索">
                            </div>
                            <div class="col-md-4">
                                <label for="itemDateRange" class="form-label">日期范围</label>
                                <input type="text" class="form-control" id="itemDateRange" placeholder="选择日期范围">
                            </div>
                            <div class="col-md-4 d-flex align-items-end">
                                <button type="button" class="btn btn-primary me-2" onclick="searchItemHistory()">
                                    <i class="bi bi-search me-1"></i>搜索
                                </button>
                                <button type="button" class="btn btn-outline-secondary" onclick="resetItemSearch()">
                                    <i class="bi bi-arrow-clockwise me-1"></i>重置
                                </button>
                            </div>
                        </div>

                        <!-- 道具发送记录表格 -->
                        <div class="table-responsive">
                            <table class="table table-hover" id="itemDeliveryTable">
                                <thead>
                                    <tr>
                                        <th>发送时间</th>
                                        <th>管理员</th>
                                        <th>接收用户</th>
                                        <th>道具名称</th>
                                        <th>数量</th>
                                        <th>IOO属性</th>
                                        <th>时间限制</th>
                                        <th>发送模式</th>
                                        <th>发送原因</th>
                                        <th>状态</th>
                                        <th>游戏编号</th>
                                    </tr>
                                </thead>
                                <tbody id="itemDeliveryTableBody">
                                    <tr>
                                        <td colspan="11" class="text-center py-4">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">加载中...</span>
                                            </div>
                                            <div class="mt-2">正在加载道具发送记录...</div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- 道具记录分页 -->
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div class="text-muted">
                                显示第 <span id="itemPageStart">0</span> - <span id="itemPageEnd">0</span> 条，共 <span id="itemTotalRecords">0</span> 条记录
                            </div>
                            <div id="itemPagination"></div>
                        </div>
                    </div>
            </div>
        </div>
    </div>
</div>

<!-- 发送道具模态框 -->
<div class="modal fade" id="sendItemModal" tabindex="-1" aria-labelledby="sendItemModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="sendItemModalLabel">
                    <i class="bi bi-gift me-2"></i>发送道具
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="sendItemForm">
                    <!-- 用户信息和发送模式 -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="recipientUsername" class="form-label">接收用户 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="recipientUsername" name="username" required>
                                <div class="form-text">请输入游戏中的用户名</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sendMode" class="form-label">发送模式 <span class="text-danger">*</span></label>
                                <select class="form-select" id="sendMode" name="send_mode">
                                    <option value="batch">批量发送（一次性发送所有数量）</option>
                                    <option value="individual">逐条发送（按数量分别发送）</option>
                                </select>
                                <div class="form-text">批量发送：发送1个包含指定数量的道具；逐条发送：发送多个单独的道具</div>
                            </div>
                        </div>
                    </div>

                    <!-- 道具选择方式 -->
                    <div class="mb-3">
                        <label class="form-label">道具选择方式</label>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="item_select_mode" id="selectFromList" value="list" checked onchange="toggleItemSelectMode()">
                                    <label class="form-check-label" for="selectFromList">
                                        从列表选择
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="item_select_mode" id="inputItemId" value="id" onchange="toggleItemSelectMode()">
                                    <label class="form-check-label" for="inputItemId">
                                        直接输入道具ID
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 道具选择区域 -->
                    <div id="itemSelectArea">
                        <!-- 从列表选择 -->
                        <div id="listSelectArea" class="mb-3">
                            <label for="selectedItemName" class="form-label">选择道具 <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="selectedItemName" name="item_name" readonly placeholder="点击选择道具">
                                <input type="hidden" id="selectedItemId" name="item_id">
                                <button type="button" class="btn btn-outline-secondary" onclick="openItemSelectModal()">
                                    <i class="bi bi-search"></i> 选择
                                </button>
                            </div>
                        </div>

                        <!-- 直接输入ID -->
                        <div id="idInputArea" class="mb-3" style="display: none;">
                            <label for="directItemId" class="form-label">道具ID <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="directItemId" name="direct_item_id" placeholder="请输入道具ID">
                            <div class="form-text">直接输入iteminfo表中的ItemID</div>
                        </div>
                    </div>

                    <!-- 道具属性 -->
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="itemQuantity" class="form-label">数量 (IO)</label>
                                <input type="number" class="form-control" id="itemQuantity" name="quantity" value="1" min="1" max="999">
                                <div class="form-text">道具数量</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="itemIOO" class="form-label">道具属性 (IOO)</label>
                                <input type="number" class="form-control" id="itemIOO" name="ioo" value="0" min="0">
                                <div class="form-text">对应ItemOp2字段</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="itemTimeLimit" class="form-label">时间限制 (秒)</label>
                                <input type="number" class="form-control" id="itemTimeLimit" name="time_limit" value="0" min="0">
                                <div class="form-text">对应ItemLimit字段，0表示永久</div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="sendReason" class="form-label">发送原因</label>
                        <textarea class="form-control" id="sendReason" name="reason" rows="3" placeholder="请输入发送原因（可选）">管理员发送</textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="submitSendItem()">
                    <i class="bi bi-send me-1"></i>发送道具
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 道具选择模态框 -->
<div class="modal fade" id="itemSelectModal" tabindex="-1" aria-labelledby="itemSelectModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="itemSelectModalLabel">
                    <i class="bi bi-box me-2"></i>选择道具
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- 道具搜索 -->
                <div class="row mb-3">
                    <div class="col-md-8">
                        <input type="text" class="form-control" id="itemSearch" placeholder="搜索道具名称（支持模糊搜索）">
                    </div>
                    <div class="col-md-4">
                        <button type="button" class="btn btn-primary w-100" onclick="searchItems()">
                            <i class="bi bi-search"></i> 搜索道具
                        </button>
                    </div>
                </div>
                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-1"></i>
                    <strong>提示：</strong>这里显示的是游戏中所有可用的道具（共34,000+个），而不是商城中的商品。您可以搜索道具名称来快速找到需要的道具。
                </div>
                
                <!-- 道具列表 -->
                <div id="itemList" class="row">
                    <div class="col-12 text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <div class="mt-2">正在加载道具列表...</div>
                    </div>
                </div>
                
                <!-- 道具分页 -->
                <div id="itemPagination" class="mt-3"></div>
            </div>
        </div>
    </div>
</div>


{/block}

{block name="script"}
<script>
// 版本: 2024-01-07-v2 - 强制刷新缓存
console.log('道具发送页面脚本加载 - 版本: 2024-01-07-v2');

// 安全打开发送道具模态框
function openSendItemModal() {
    console.log('openSendItemModal 函数被调用');
    try {
        const modalElement = document.getElementById('sendItemModal');
        if (!modalElement) {
            console.error('找不到发送道具模态框元素');
            alert('模态框元素不存在');
            return;
        }

        if (typeof bootstrap === 'undefined' || !bootstrap.Modal) {
            console.error('Bootstrap Modal不可用');
            alert('Bootstrap Modal不可用');
            return;
        }

        // 创建模态框实例
        const modal = new bootstrap.Modal(modalElement, {
            backdrop: true,
            keyboard: true,
            focus: true
        });

        modal.show();
        console.log('发送道具模态框已打开');
    } catch (error) {
        console.error('打开发送道具模态框失败:', error);
        alert('打开模态框失败: ' + error.message);
    }
}

// 明确将函数添加到window对象
window.openSendItemModal = openSendItemModal;



// 安全打开道具选择模态框
function openItemSelectModal() {
    console.log('openItemSelectModal 函数被调用');
    try {
        const modalElement = document.getElementById('itemSelectModal');
        if (!modalElement) {
            console.error('找不到道具选择模态框元素');
            alert('模态框元素不存在');
            return;
        }

        if (typeof bootstrap === 'undefined' || !bootstrap.Modal) {
            console.error('Bootstrap Modal不可用');
            alert('Bootstrap Modal不可用');
            return;
        }

        // 创建模态框实例
        const modal = new bootstrap.Modal(modalElement, {
            backdrop: true,
            keyboard: true,
            focus: true
        });

        modal.show();
        console.log('道具选择模态框已打开');

        // 加载道具列表
        setTimeout(() => {
            try {
                loadItemList(1);
            } catch (error) {
                console.error('加载道具列表失败:', error);
            }
        }, 100);
    } catch (error) {
        console.error('打开道具选择模态框失败:', error);
        alert('打开模态框失败: ' + error.message);
    }
}

// 明确将函数添加到window对象
window.openItemSelectModal = openItemSelectModal;

// 检查认证状态
async function checkAuthStatus() {
    try {
        console.log('开始检查认证状态...');
        console.log('当前页面cookies:', document.cookie);

        const response = await fetch('/admin/checkAdmin', {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        });

        console.log('认证检查响应状态:', response.status);
        console.log('认证检查响应头:', response.headers.get('set-cookie'));

        if (response.ok) {
            const result = await response.json();
            console.log('认证检查结果:', result);

            if (result.code === 200 || result.code === 0 || result.success) {
                console.log('✅ 用户已登录且具有管理员权限');

                // 测试API请求的session
                console.log('测试API请求的session...');
                await testApiSession();

                return true;
            } else {
                console.warn('❌ 认证失败:', result.message);
                showAlert('认证失败: ' + result.message, 'warning');
                return false;
            }
        } else {
            console.error('❌ 认证检查请求失败:', response.status);
            const text = await response.text();
            console.error('响应内容:', text.substring(0, 200));
            showAlert('认证检查失败，请刷新页面重试', 'danger');
            return false;
        }
    } catch (error) {
        console.error('❌ 认证检查异常:', error);
        showAlert('认证检查异常: ' + error.message, 'danger');
        return false;
    }
}

// 测试API请求的session
async function testApiSession() {
    try {
        const response = await fetch('/admin/api/item-delivery/getStats', {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        });

        console.log('API测试响应状态:', response.status);
        console.log('API测试响应头Content-Type:', response.headers.get('content-type'));

        const result = await response.json();
        console.log('API测试结果:', result);

        if (result.code === 401) {
            console.warn('⚠️ API请求认证失败，但页面认证成功 - 可能存在session问题');
        } else if (result.code === 200) {
            console.log('✅ API请求认证成功');
        }
    } catch (error) {
        console.error('API测试失败:', error);
    }
}

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查Bootstrap是否正确加载
    if (typeof bootstrap === 'undefined') {
        console.error('Bootstrap未正确加载！');
        alert('页面初始化失败：Bootstrap未加载');
        return;
    }

    // 检查Bootstrap组件是否可用
    if (!bootstrap.Modal || !bootstrap.Toast) {
        console.error('Bootstrap组件不完整！');
        alert('页面初始化失败：Bootstrap组件不完整');
        return;
    }

    console.log('Bootstrap版本:', bootstrap.Tooltip.VERSION || 'Unknown');
    console.log('Bootstrap Modal可用:', typeof bootstrap.Modal);
    console.log('Bootstrap Toast可用:', typeof bootstrap.Toast);

    // 检查认证状态
    console.log('检查认证状态...');
    checkAuthStatus();

    // 初始化页面数据
    try {
        loadDeliveryStats();
        loadItemDeliveryHistory();
        initDatePicker();
    } catch (error) {
        console.error('页面初始化失败:', error);
        alert('页面初始化失败: ' + error.message);
    }

    // 初始化完成
    console.log('道具发送页面初始化完成');

    // 测试函数是否存在
    console.log('openSendItemModal函数是否存在:', typeof openSendItemModal);
    console.log('openItemSelectModal函数是否存在:', typeof openItemSelectModal);
});

// 初始化道具记录日期选择器
function initDatePicker() {
    // 这里可以集成日期选择器插件，如flatpickr
    // 暂时使用简单的日期输入
}



// 加载发送统计
async function loadDeliveryStats() {
    try {
        console.log('开始加载统计数据...');
        const response = await fetch('/admin/api/item-delivery/getStats', {
            method: 'GET',
            credentials: 'same-origin', // 包含cookies
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        });

        console.log('统计API响应状态:', response.status);
        console.log('统计API响应头:', response.headers.get('content-type'));

        if (!response.ok) {
            console.error('统计API响应不正常:', response.status, response.statusText);
            const text = await response.text();
            console.error('响应内容:', text.substring(0, 200));
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        console.log('统计API响应:', result);

        if (result.code === 200 || result.success) {
            const stats = result.data;
            document.getElementById('todayCount').textContent = stats.today || 0;
            document.getElementById('weekCount').textContent = stats.week || 0;
            document.getElementById('monthCount').textContent = stats.month || 0;
            document.getElementById('totalCount').textContent = stats.total || 0;
        } else {
            console.error('统计API返回错误:', result);
            showAlert('加载统计数据失败: ' + (result.message || '未知错误'), 'warning');
            // 设置默认值
            document.getElementById('todayCount').textContent = '0';
            document.getElementById('weekCount').textContent = '0';
            document.getElementById('monthCount').textContent = '0';
            document.getElementById('totalCount').textContent = '0';
        }
    } catch (error) {
        console.error('加载统计数据失败:', error);
        showAlert('加载统计数据失败: ' + error.message, 'danger');
        // 设置默认值
        document.getElementById('todayCount').textContent = '0';
        document.getElementById('weekCount').textContent = '0';
        document.getElementById('monthCount').textContent = '0';
        document.getElementById('totalCount').textContent = '0';
    }
}

// ==================== 道具发送记录功能 ====================

// 加载道具发送记录
async function loadItemDeliveryHistory(page = 1) {
    try {
        const username = document.getElementById('itemSearchUsername').value;
        const dateRange = document.getElementById('itemDateRange').value;

        const params = new URLSearchParams({
            page: page,
            limit: 20,
            username: username,
            date_range: dateRange,
            type: 'item'
        });

        const response = await fetch(`/admin/api/item-delivery/getDeliveryHistory?${params}`, {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        });

        console.log('道具发送记录API响应状态:', response.status);
        if (!response.ok) {
            const text = await response.text();
            console.error('道具发送记录API响应内容:', text.substring(0, 200));
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();

        console.log('道具发送记录API响应:', result);

        if (result.code === 200 || result.success) {
            const data = result.data || result;
            renderItemDeliveryTable(data.list || []);
            updateItemPagination(data.total || 0, data.page || 1, data.limit || 20);
        } else {
            console.error('道具发送记录API返回错误:', result);
            renderItemDeliveryTable([]);
            showAlert('加载道具发送记录失败: ' + (result.message || '未知错误'), 'danger');
        }
    } catch (error) {
        console.error('加载道具发送记录失败:', error);
        showAlert('加载道具发送记录失败', 'danger');
    }
}



// 渲染道具发送记录表格
function renderItemDeliveryTable(records) {
    const tbody = document.getElementById('itemDeliveryTableBody');

    if (!records || records.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="11" class="text-center py-4 text-muted">
                    <i class="bi bi-inbox"></i>
                    <div class="mt-2">暂无道具发送记录</div>
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = records.map(record => `
        <tr>
            <td>${record.create_time}</td>
            <td>${record.admin_user}</td>
            <td>${record.username}</td>
            <td>
                <i class="bi-gift text-primary me-1"></i>
                ${record.item_name}
            </td>
            <td>${record.quantity}</td>
            <td>${record.ioo || 0}</td>
            <td>${record.time_limit ? formatTimeLimit(record.time_limit) : '永久'}</td>
            <td>
                <span class="badge ${record.delivery_mode === 'individual' ? 'bg-info' : 'bg-primary'}">
                    ${record.delivery_mode === 'individual' ? '逐条发送' : '批量发送'}
                </span>
            </td>
            <td>${record.reason || '-'}</td>
            <td>
                <span class="badge ${record.status === 1 ? 'bg-success' : 'bg-danger'}">
                    ${record.status === 1 ? '成功' : '失败'}
                </span>
            </td>
            <td>${record.unique_num || '-'}</td>
        </tr>
    `).join('');
}





// 格式化时间限制显示
function formatTimeLimit(seconds) {
    if (seconds === 0) return '永久';

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    if (hours > 0) {
        return `${hours}小时${minutes > 0 ? minutes + '分钟' : ''}`;
    } else if (minutes > 0) {
        return `${minutes}分钟${remainingSeconds > 0 ? remainingSeconds + '秒' : ''}`;
    } else {
        return `${remainingSeconds}秒`;
    }
}

// 更新道具记录分页
function updateItemPagination(total, currentPage, limit) {
    const totalPages = Math.ceil(total / limit);
    const start = (currentPage - 1) * limit + 1;
    const end = Math.min(currentPage * limit, total);

    document.getElementById('itemPageStart').textContent = start;
    document.getElementById('itemPageEnd').textContent = end;
    document.getElementById('itemTotalRecords').textContent = total;

    // 生成分页按钮
    const pagination = document.getElementById('itemPagination');
    let paginationHtml = '';

    if (totalPages > 1) {
        paginationHtml += `<nav><ul class="pagination">`;

        // 上一页
        if (currentPage > 1) {
            paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="loadItemDeliveryHistory(${currentPage - 1})">上一页</a></li>`;
        }

        // 页码
        for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
            paginationHtml += `<li class="page-item ${i === currentPage ? 'active' : ''}">
                <a class="page-link" href="#" onclick="loadItemDeliveryHistory(${i})">${i}</a>
            </li>`;
        }

        // 下一页
        if (currentPage < totalPages) {
            paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="loadItemDeliveryHistory(${currentPage + 1})">下一页</a></li>`;
        }

        paginationHtml += `</ul></nav>`;
    }

    pagination.innerHTML = paginationHtml;
}



// ==================== 搜索功能 ====================

// 搜索道具发送记录
function searchItemHistory() {
    loadItemDeliveryHistory(1);
}

// 重置道具搜索
function resetItemSearch() {
    document.getElementById('itemSearchUsername').value = '';
    document.getElementById('itemDateRange').value = '';
    loadItemDeliveryHistory(1);
}



// 加载分类列表
async function loadCategories() {
    try {
        const response = await fetch('/admin/api/getGlobalConfig', {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        });

        console.log('分类API响应状态:', response.status);
        if (!response.ok) {
            const text = await response.text();
            console.error('分类API响应内容:', text.substring(0, 200));
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();

        if (result.code === 200 || result.code === 0 || result.success) {
            const categories = result.data.categories;
            const categorySelect = document.getElementById('itemCategory');

            categorySelect.innerHTML = '<option value="">全部分类</option>';
            Object.keys(categories).forEach(id => {
                categorySelect.innerHTML += `<option value="${id}">${categories[id]}</option>`;
            });
        }
    } catch (error) {
        console.error('加载分类失败:', error);
    }
}

// 加载道具列表
async function loadItemList(page = 1) {
    try {
        const search = document.getElementById('itemSearch').value;

        const params = new URLSearchParams({
            page: page,
            limit: 12,
            search: search
        });

        const response = await fetch(`/admin/api/item-delivery/getItemList?${params}`, {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        });

        console.log('道具列表API响应状态:', response.status);
        if (!response.ok) {
            const text = await response.text();
            console.error('道具列表API响应内容:', text.substring(0, 200));
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();

        if (result.code === 200) {
            renderItemList(result.data.items || result.data.list || []);
            renderItemPagination(result.data.total, result.data.page, result.data.limit);
        } else {
            showAlert('加载道具列表失败: ' + result.message, 'danger');
        }
    } catch (error) {
        console.error('加载道具列表失败:', error);
        showAlert('加载道具列表失败', 'danger');
    }
}

// 渲染道具列表
function renderItemList(items) {
    const itemList = document.getElementById('itemList');

    if (!items || items.length === 0) {
        itemList.innerHTML = `
            <div class="col-12 text-center py-4 text-muted">
                <i class="bi bi-inbox"></i>
                <div class="mt-2">暂无道具数据</div>
            </div>
        `;
        return;
    }

    itemList.innerHTML = items.map(item => `
        <div class="col-md-4 col-lg-3 mb-3">
            <div class="card h-100 item-card" onclick="selectItem(${item.id}, '${item.name.replace(/'/g, '\\\'')}')" title="${item.item_info || ''}">
                <div class="card-body text-center">
                    <img src="${item.icon || '/static/img/default.png'}" alt="${item.name}" class="mb-2" style="width: 48px; height: 48px;" onerror="this.src='/static/img/default.png'">
                    <h6 class="card-title" style="font-size: 0.9rem;">${item.name}</h6>
                    <p class="card-text small text-muted">道具ID: ${item.item}</p>
                    <p class="card-text small text-info">
                        <i class="bi bi-info-circle"></i> 游戏道具
                    </p>
                </div>
            </div>
        </div>
    `).join('');
}

// 渲染道具分页
function renderItemPagination(total, currentPage, limit) {
    const totalPages = Math.ceil(total / limit);
    const pagination = document.getElementById('itemPagination');
    currentPage = parseInt(currentPage);

    let paginationHtml = '';

    if (totalPages > 1) {
        paginationHtml += `<nav><ul class="pagination justify-content-center">`;

        // 首页
        if (currentPage > 3) {
            paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="loadItemList(1)">首页</a></li>`;
        }

        // 上一页
        if (currentPage > 1) {
            paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="loadItemList(${currentPage - 1})">上一页</a></li>`;
        }

        // 页码（只显示当前页前后各2页，最多5个页码）
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 2);

        for (let i = startPage; i <= endPage; i++) {
            paginationHtml += `<li class="page-item ${i === currentPage ? 'active' : ''}">
                <a class="page-link" href="#" onclick="loadItemList(${i})">${i}</a>
            </li>`;
        }

        // 下一页
        if (currentPage < totalPages) {
            paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="loadItemList(${currentPage + 1})">下一页</a></li>`;
        }

        // 末页
        if (currentPage < totalPages - 2) {
            paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="loadItemList(${totalPages})">末页</a></li>`;
        }

        paginationHtml += `</ul></nav>`;

        // 添加页面信息和跳转功能
        paginationHtml += `
            <div class="d-flex justify-content-between align-items-center mt-2">
                <small class="text-muted">
                    第 ${currentPage} 页，共 ${totalPages} 页，总计 ${total} 个道具
                </small>
                <div class="input-group" style="width: 200px;">
                    <input type="number" class="form-control form-control-sm" id="jumpToPage" placeholder="跳转到页" min="1" max="${totalPages}">
                    <button class="btn btn-outline-secondary btn-sm" onclick="jumpToPage()">跳转</button>
                </div>
            </div>
        `;
    }

    pagination.innerHTML = paginationHtml;
}



// 搜索道具
function searchItems() {
    loadItemList(1);
}

// 跳转到指定页面
function jumpToPage() {
    const pageInput = document.getElementById('jumpToPage');
    const page = parseInt(pageInput.value);

    if (page && page > 0) {
        loadItemList(page);
        pageInput.value = '';
    } else {
        showAlert('请输入有效的页码', 'warning');
    }
}

// 切换道具选择模式
function toggleItemSelectMode() {
    const listMode = document.getElementById('selectFromList').checked;
    const listArea = document.getElementById('listSelectArea');
    const idArea = document.getElementById('idInputArea');

    if (listMode) {
        listArea.style.display = 'block';
        idArea.style.display = 'none';
        // 清空直接输入的ID
        document.getElementById('directItemId').value = '';
    } else {
        listArea.style.display = 'none';
        idArea.style.display = 'block';
        // 清空选择的道具
        document.getElementById('selectedItemName').value = '';
        document.getElementById('selectedItemId').value = '';
    }
}

// 选择道具
function selectItem(itemId, itemName) {
    document.getElementById('selectedItemId').value = itemId;
    document.getElementById('selectedItemName').value = itemName;

    // 关闭道具选择模态框
    const itemSelectModalElement = document.getElementById('itemSelectModal');
    if (itemSelectModalElement) {
        try {
            const itemSelectModal = bootstrap.Modal.getInstance(itemSelectModalElement);
            if (itemSelectModal) {
                itemSelectModal.hide();
            } else {
                // 如果没有实例，创建新的实例
                const newModal = new bootstrap.Modal(itemSelectModalElement);
                newModal.hide();
            }
        } catch (error) {
            console.error('关闭道具选择模态框失败:', error);
            // 尝试使用jQuery方式关闭（如果可用）
            if (typeof $ !== 'undefined') {
                $(itemSelectModalElement).modal('hide');
            }
        }
    }
}

// 提交发送道具
async function submitSendItem() {
    try {
        const form = document.getElementById('sendItemForm');
        const formData = new FormData(form);

        // 获取道具选择模式
        const selectMode = document.querySelector('input[name="item_select_mode"]:checked').value;
        let itemId = '';

        if (selectMode === 'list') {
            itemId = document.getElementById('selectedItemId').value;
            if (!itemId) {
                showAlert('请选择道具', 'warning');
                return;
            }
        } else {
            itemId = document.getElementById('directItemId').value;
            if (!itemId) {
                showAlert('请输入道具ID', 'warning');
                return;
            }
        }

        // 验证表单
        const username = formData.get('username');
        if (!username) {
            showAlert('请输入接收用户名', 'warning');
            return;
        }

        const quantity = parseInt(formData.get('quantity')) || 1;
        const ioo = parseInt(document.getElementById('itemIOO').value) || 0;
        const timeLimit = parseInt(document.getElementById('itemTimeLimit').value) || 0;
        const sendMode = document.getElementById('sendMode').value;

        const data = {
            username: username,
            item_id: itemId,
            quantity: quantity,
            ioo: ioo,
            time_limit: timeLimit,
            send_mode: sendMode,
            reason: formData.get('reason') || '管理员发送'
        };

        const response = await fetch('/admin/api/item-delivery/sendItem', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });

        const result = await response.json();

        console.log('发送道具API响应:', result);

        if (result.code === 200 || result.success) {
            showAlert('道具发送成功！', 'success');

            // 关闭模态框
            const sendModalElement = document.getElementById('sendItemModal');
            if (sendModalElement) {
                try {
                    const sendModal = bootstrap.Modal.getInstance(sendModalElement);
                    if (sendModal) {
                        sendModal.hide();
                    } else {
                        // 如果没有实例，创建新的实例
                        const newModal = new bootstrap.Modal(sendModalElement);
                        newModal.hide();
                    }
                } catch (error) {
                    console.error('关闭发送模态框失败:', error);
                    // 尝试使用jQuery方式关闭（如果可用）
                    if (typeof $ !== 'undefined') {
                        $(sendModalElement).modal('hide');
                    }
                }
            }

            // 重置表单
            form.reset();
            document.getElementById('selectedItemName').value = '';
            document.getElementById('selectedItemId').value = '';

            // 刷新数据
            loadDeliveryStats();
            loadItemDeliveryHistory();
        } else {
            console.error('发送道具API返回错误:', result);
            showAlert('发送失败: ' + (result.message || '未知错误'), 'danger');
        }
    } catch (error) {
        console.error('发送道具失败:', error);
        showAlert('发送道具失败', 'danger');
    }
}

// 显示提示信息
function showAlert(message, type = 'info') {
    try {
        // 检查Bootstrap是否可用
        if (typeof bootstrap === 'undefined') {
            console.error('Bootstrap未加载，无法显示Toast');
            alert(message); // 降级到原生alert
            return;
        }

        // 创建Toast提示
        const toastHtml = `
            <div class="toast align-items-center text-white bg-${type === 'success' ? 'success' : type === 'danger' ? 'danger' : type === 'warning' ? 'warning' : 'info'} border-0" role="alert">
                <div class="d-flex">
                    <div class="toast-body">${message}</div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            </div>
        `;

        // 添加到页面
        let toastContainer = document.getElementById('toastContainer');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'toastContainer';
            toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
            toastContainer.style.zIndex = '9999';
            document.body.appendChild(toastContainer);
        }

        toastContainer.insertAdjacentHTML('beforeend', toastHtml);

        // 显示Toast
        const toastElement = toastContainer.lastElementChild;
        if (toastElement && bootstrap.Toast) {
            const toast = new bootstrap.Toast(toastElement, {
                autohide: true,
                delay: 3000
            });
            toast.show();

            // 自动移除
            toastElement.addEventListener('hidden.bs.toast', () => {
                toastElement.remove();
            });
        } else {
            console.error('无法创建Toast元素');
            alert(message); // 降级到原生alert
        }
    } catch (error) {
        console.error('显示Toast失败:', error);
        alert(message); // 降级到原生alert
    }
}

// 立即测试函数是否可用
console.log('=== 立即测试函数可用性 ===');
console.log('openSendItemModal:', typeof openSendItemModal);
console.log('openItemSelectModal:', typeof openItemSelectModal);
console.log('window.openSendItemModal:', typeof window.openSendItemModal);
console.log('window.openItemSelectModal:', typeof window.openItemSelectModal);

// 测试函数调用
try {
    console.log('测试调用 openSendItemModal...');
    if (typeof openSendItemModal === 'function') {
        console.log('openSendItemModal 函数存在且可调用');
    } else {
        console.error('openSendItemModal 函数不存在或不是函数');
    }
} catch (error) {
    console.error('测试 openSendItemModal 时出错:', error);
}

</script>
{/block}
