{extend name="admin/layout/base" /}

{block name="css"}
<style>
/* 仪表盘专用样式 */
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.dashboard-stat-card {
    background: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.dashboard-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.dashboard-stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    flex-shrink: 0;
}

.dashboard-stat-icon.users { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.dashboard-stat-icon.items { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.dashboard-stat-icon.orders { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
.dashboard-stat-icon.online { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
.dashboard-stat-icon.bubble { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }
.dashboard-stat-icon.credits { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); }
.dashboard-stat-icon.coins { background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); }
.dashboard-stat-icon.sales { background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); }

.dashboard-stat-content {
    flex: 1;
    min-width: 0;
}

.dashboard-stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #212529;
    line-height: 1;
    margin-bottom: 0.25rem;
}

.dashboard-stat-label {
    color: #6c757d;
    font-size: 0.875rem;
    font-weight: 500;
}

.dashboard-section {
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
    margin-bottom: 2rem;
}

.dashboard-section-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e9ecef;
    background: #f8f9fa;
    border-radius: 0.5rem 0.5rem 0 0;
    display: flex;
    justify-content: between;
    align-items: center;
}

.dashboard-section-title {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #212529;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.dashboard-section-body {
    padding: 1.5rem;
}

.order-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.order-stat-item {
    text-align: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 0.375rem;
    border: 1px solid #e9ecef;
}

.order-stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: #212529;
    margin-bottom: 0.25rem;
}

.order-stat-label {
    color: #6c757d;
    font-size: 0.875rem;
}

.currency-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.loading-placeholder {
    display: inline-block;
    width: 60px;
    height: 20px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: 4px;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* 移动端优化 */
@media (max-width: 768px) {
    .dashboard-stats {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .dashboard-stat-card {
        padding: 1rem;
    }

    .dashboard-stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }

    .dashboard-stat-number {
        font-size: 1.5rem;
    }

    .dashboard-section-header {
        padding: 1rem;
    }

    .dashboard-section-body {
        padding: 1rem;
    }

    .order-stats-grid,
    .currency-stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }

    .order-stat-item {
        padding: 0.75rem;
    }

    .order-stat-number {
        font-size: 1.25rem;
    }
}

@media (max-width: 480px) {
    .order-stats-grid,
    .currency-stats-grid {
        grid-template-columns: 1fr;
    }

    .dashboard-stat-card {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }
}
</style>
{/block}

{block name="content"}
<!-- 页面头部 -->
<div class="page-header">
    <h1 class="page-title">
        <i class="bi bi-speedometer2"></i>
        仪表盘
    </h1>
    <p class="page-description">系统概览和关键指标</p>
</div>

<!-- 核心统计卡片 -->
<div class="dashboard-stats">
    <div class="dashboard-stat-card">
        <div class="dashboard-stat-icon users">
            <i class="bi bi-people"></i>
        </div>
        <div class="dashboard-stat-content">
            <div class="dashboard-stat-number" id="totalUsers">
                <span class="loading-placeholder"></span>
            </div>
            <div class="dashboard-stat-label">用户总数</div>
        </div>
    </div>

    <div class="dashboard-stat-card">
        <div class="dashboard-stat-icon items">
            <i class="bi bi-box"></i>
        </div>
        <div class="dashboard-stat-content">
            <div class="dashboard-stat-number" id="totalItems">
                <span class="loading-placeholder"></span>
            </div>
            <div class="dashboard-stat-label">商品数量</div>
        </div>
    </div>

    <div class="dashboard-stat-card">
        <div class="dashboard-stat-icon orders">
            <i class="bi bi-receipt"></i>
        </div>
        <div class="dashboard-stat-content">
            <div class="dashboard-stat-number" id="totalOrders">
                <span class="loading-placeholder"></span>
            </div>
            <div class="dashboard-stat-label">总订单</div>
        </div>
    </div>

    <div class="dashboard-stat-card">
        <div class="dashboard-stat-icon online">
            <i class="bi bi-wifi"></i>
        </div>
        <div class="dashboard-stat-content">
            <div class="dashboard-stat-number" id="onlineUsers">
                <span class="loading-placeholder"></span>
            </div>
            <div class="dashboard-stat-label">在线用户</div>
        </div>
    </div>
</div>

<!-- 订单概览 -->
<div class="dashboard-section">
    <div class="dashboard-section-header">
        <h3 class="dashboard-section-title">
            <i class="bi bi-receipt"></i>
            订单概览
        </h3>
        <a href="/admin/orders" class="btn btn-sm btn-outline-primary">查看全部</a>
    </div>
    <div class="dashboard-section-body">
        <div class="order-stats-grid">
            <div class="order-stat-item">
                <div class="order-stat-number" id="totalOrdersOverview">
                    <span class="loading-placeholder"></span>
                </div>
                <div class="order-stat-label">总订单</div>
            </div>
            <div class="order-stat-item">
                <div class="order-stat-number" id="todayOrders">
                    <span class="loading-placeholder"></span>
                </div>
                <div class="order-stat-label">今日订单</div>
            </div>
            <div class="order-stat-item">
                <div class="order-stat-number" id="yesterdayOrders">
                    <span class="loading-placeholder"></span>
                </div>
                <div class="order-stat-label">昨日订单</div>
            </div>
            <div class="order-stat-item">
                <div class="order-stat-number" id="todaySales">
                    <span class="loading-placeholder"></span>
                </div>
                <div class="order-stat-label">今日销售额</div>
            </div>
        </div>
    </div>
</div>

<!-- 游戏货币统计 -->
<div class="dashboard-section">
    <div class="dashboard-section-header">
        <h3 class="dashboard-section-title">
            <i class="bi bi-currency-exchange"></i>
            游戏货币统计
        </h3>
    </div>
    <div class="dashboard-section-body">
        <div class="currency-stats-grid">
            <div class="dashboard-stat-card">
                <div class="dashboard-stat-icon bubble">
                    <i class="bi bi-droplet-fill"></i>
                </div>
                <div class="dashboard-stat-content">
                    <div class="dashboard-stat-number" id="totalBubblePoints">
                        <span class="loading-placeholder"></span>
                    </div>
                    <div class="dashboard-stat-label" id="bubblePointLabel">泡点总量</div>
                </div>
            </div>

            <div class="dashboard-stat-card">
                <div class="dashboard-stat-icon credits">
                    <i class="bi bi-star-fill"></i>
                </div>
                <div class="dashboard-stat-content">
                    <div class="dashboard-stat-number" id="totalCredits">
                        <span class="loading-placeholder"></span>
                    </div>
                    <div class="dashboard-stat-label" id="creditsLabel">积分总量</div>
                </div>
            </div>

            <div class="dashboard-stat-card">
                <div class="dashboard-stat-icon coins">
                    <i class="bi bi-coin"></i>
                </div>
                <div class="dashboard-stat-content">
                    <div class="dashboard-stat-number" id="totalCCoins">
                        <span class="loading-placeholder"></span>
                    </div>
                    <div class="dashboard-stat-label" id="cCoinsLabel">C币总量</div>
                </div>
            </div>
        </div>
    </div>
</div>

{/block}

{block name="js"}
<script>
// 仪表盘数据管理
class Dashboard {
    constructor() {
        this.init();
    }

    init() {
        this.loadAllData();
        this.updateCurrencyLabels();
        // 每30秒刷新一次数据
        setInterval(() => {
            this.loadAllData();
        }, 30000);
    }

    // 更新货币标签
    async updateCurrencyLabels() {
        // 获取最新的货币配置
        await this.loadShopConfig();

        // 获取货币名称的函数
        function getCurrencyName(type) {
            // 首先尝试从全局配置获取
            if (window.ShopConfig) {
                if (window.ShopConfig.currency_types && window.ShopConfig.currency_types[String(type)]) {
                    return window.ShopConfig.currency_types[String(type)].name;
                }
                if (window.ShopConfig.currencies && window.ShopConfig.currencies[String(type)]) {
                    return window.ShopConfig.currencies[String(type)].name;
                }
            }

            // 降级到本地配置
            switch (String(type)) {
                case '1': return '泡点';
                case '2': return '积分';
                case '3': return 'C币';
                default: return '未知货币';
            }
        }

        // 更新标签
        const bubblePointLabel = document.getElementById('bubblePointLabel');
        if (bubblePointLabel) {
            bubblePointLabel.textContent = getCurrencyName('1') + '总量';
        }

        const creditsLabel = document.getElementById('creditsLabel');
        if (creditsLabel) {
            creditsLabel.textContent = getCurrencyName('2') + '总量';
        }

        const cCoinsLabel = document.getElementById('cCoinsLabel');
        if (cCoinsLabel) {
            cCoinsLabel.textContent = getCurrencyName('3') + '总量';
        }

        console.log('仪表盘货币标签已更新');
    }

    // 加载ShopConfig配置
    async loadShopConfig() {
        try {
            const response = await fetch('/api/getShopConfig?t=' + Date.now());
            const result = await response.json();
            if (result.success && result.data) {
                window.ShopConfig = result.data;
                console.log('仪表盘ShopConfig已更新:', window.ShopConfig);
                return true;
            }
        } catch (error) {
            console.error('加载ShopConfig失败:', error);
        }
        return false;
    }

    async loadAllData() {
        try {
            // 使用现有的getStatsFlat API获取所有统计数据
            await this.loadStatsFlat();
        } catch (error) {
            console.error('加载仪表盘数据失败:', error);
        }
    }

    async loadStatsFlat() {
        try {
            const response = await fetch('/admin/getStatsFlat');
            const result = await response.json();

            if (result.success && result.code === 200) {
                const stats = result.data;
                console.log('获取到的统计数据:', stats);

                // 更新基础统计
                this.updateElement('totalUsers', this.formatNumber(stats.total_users || 0));
                this.updateElement('totalItems', this.formatNumber(stats.total_items || 0));
                this.updateElement('totalOrders', this.formatNumber(stats.total_orders || 0));
                this.updateElement('onlineUsers', this.formatNumber(stats.online_users || 0));

                // 更新订单统计
                this.updateElement('totalOrdersOverview', this.formatNumber(stats.total_orders || 0));
                this.updateElement('todayOrders', this.formatNumber(stats.today_orders || 0));
                this.updateElement('yesterdayOrders', this.formatNumber(stats.yesterday_orders || 0));
                this.updateElement('todaySales', '¥' + this.formatNumber(stats.today_sales || 0));

                // 更新货币统计
                this.updateElement('totalBubblePoints', this.formatNumber(stats.total_bubble_point || 0));
                this.updateElement('totalCredits', this.formatNumber(stats.total_haha_point || 0));
                this.updateElement('totalCCoins', this.formatNumber(stats.total_ccoin || 0));

            } else {
                console.error('API返回错误:', result);
                this.showError('统计数据');
            }
        } catch (error) {
            console.error('加载统计数据失败:', error);
            this.showError('统计数据');
        }
    }

    updateElement(id, value) {
        const element = document.getElementById(id);
        if (element) {
            element.innerHTML = value;
        }
    }

    formatNumber(num) {
        if (num === null || num === undefined) return '0';
        return new Intl.NumberFormat('zh-CN').format(num);
    }

    showError(type) {
        console.error(`${type}数据加载失败`);
        // 可以在这里添加错误提示UI
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new Dashboard();
});
</script>
{/block}
