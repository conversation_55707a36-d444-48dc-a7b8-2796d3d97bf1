{extend name="admin/layout/base" /}

{block name="css"}
    <style>
    /* 手机端优化样式 */
    @media (max-width: 768px) {
        .page-header .ui-card-header h1 {
            font-size: 1.5rem;
        }

        .page-header .ui-card-header p {
            font-size: 0.9rem;
        }

        /* 表单优化 */
        .form-row-mobile {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .form-row-mobile .col-md-3,
        .form-row-mobile .col-md-6,
        .form-row-mobile .col-md-9 {
            width: 100%;
            max-width: 100%;
        }

        /* 余额卡片优化 */
        .balance-card-mobile {
            margin-bottom: 1rem;
        }

        .balance-card-mobile .card {
            margin-bottom: 0.5rem;
        }

        /* 表格优化 */
        .table-responsive-mobile {
            font-size: 0.85rem;
        }

        .table-responsive-mobile th,
        .table-responsive-mobile td {
            padding: 0.5rem 0.25rem;
            white-space: nowrap;
        }

        /* 筛选区域优化 */
        .filters-mobile {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .filters-mobile .d-flex {
            flex-direction: column;
            gap: 0.5rem;
        }

        .filters-mobile input,
        .filters-mobile select,
        .filters-mobile button {
            width: 100%;
        }

        /* 按钮优化 */
        .btn-mobile {
            width: 100%;
            margin-bottom: 0.5rem;
        }

        /* 分页样式优化 */
        .pagination {
            margin-bottom: 0;
        }

        .pagination .page-link {
            padding: 0.5rem 0.75rem;
            font-size: 0.9rem;
            color: #6c757d;
            background-color: #fff;
            border: 1px solid #dee2e6;
            text-decoration: none;
            transition: all 0.2s ease-in-out;
        }

        .pagination .page-link:hover {
            color: #0d6efd;
            background-color: #e9ecef;
            border-color: #dee2e6;
        }

        .pagination .page-item.active .page-link {
            color: #fff;
            background-color: #0d6efd;
            border-color: #0d6efd;
            font-weight: 600;
        }

        .pagination .page-item.disabled .page-link {
            color: #6c757d;
            background-color: #fff;
            border-color: #dee2e6;
            cursor: not-allowed;
        }



        /* 确保手机端分页容器可见 */
        #rechargePaginationMobile {
            display: block !important;
            visibility: visible !important;
        }

        /* 分页容器样式 */
        nav[aria-label="充值记录分页"] {
            margin: 1rem 0;
        }

        /* 确保分页按钮正确显示 */
        .pagination .page-item .page-link {
            display: block;
            position: relative;
            color: #6c757d;
            text-decoration: none;
            background-color: #fff;
            border: 1px solid #dee2e6;
            transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }

        .pagination .page-item:first-child .page-link {
            border-top-left-radius: 0.375rem;
            border-bottom-left-radius: 0.375rem;
        }

        .pagination .page-item:last-child .page-link {
            border-top-right-radius: 0.375rem;
            border-bottom-right-radius: 0.375rem;
        }

        /* 页码信息样式 */
        .pagination-info {
            margin-top: 0.5rem;
            color: #6c757d;
            font-size: 0.875rem;
        }

        /* 手机端分页信息样式 */
        .mobile-page-info {
            margin-top: 0.75rem;
            color: #6c757d;
            font-size: 0.8rem;
            text-align: center;
        }
    </style>
{/block}

{block name="content"}
<div class="page-header">
    <div class="ui-card">
        <div class="ui-card-header">
            <h1 class="page-title">
                <i class="bi bi-credit-card"></i>
                充值管理
            </h1>
            <p class="page-description text-muted mb-0">管理用户充值和余额操作</p>
        </div>
    </div>
</div>

<div class="page-content">
    <!-- 用户查询区域 -->
    <div class="ui-card mb-4">
        <div class="ui-card-header">
            <h5 class="mb-0">用户查询</h5>
        </div>
        <div class="ui-card-body">
            <!-- 桌面端布局 -->
            <div class="row g-3 d-none d-md-flex">
                <div class="col-md-6">
                    <label class="form-label">用户账号</label>
                    <input type="text" class="form-control" id="queryAccount" placeholder="请输入用户账号">
                </div>
                <div class="col-md-6 d-flex align-items-end">
                    <button type="button" class="btn btn-primary" onclick="queryUserBalance()">
                        <i class="bi bi-search"></i> 查询余额
                    </button>
                </div>
            </div>

            <!-- 手机端布局 -->
            <div class="d-md-none">
                <div class="mb-3">
                    <label class="form-label">用户账号</label>
                    <input type="text" class="form-control" id="queryAccountMobile" placeholder="请输入用户账号">
                </div>
                <button type="button" class="btn btn-primary btn-mobile" onclick="queryUserBalanceMobile()">
                    <i class="bi bi-search"></i> 查询余额
                </button>
            </div>
        </div>
    </div>

    <!-- 用户余额显示 -->
    <div class="ui-card mb-4" id="balanceCard" style="display: none;">
        <div class="ui-card-header">
            <h5 class="mb-0">用户余额</h5>
        </div>
        <div class="ui-card-body">
            <!-- 桌面端余额显示 -->
            <div id="balanceContent" class="d-none d-md-block"></div>
            <!-- 手机端余额显示 -->
            <div id="balanceContentMobile" class="d-md-none"></div>
        </div>
    </div>
    <!-- 充值操作区域 -->
    <div class="ui-card mb-4">
        <div class="ui-card-header">
            <h5 class="mb-0">充值操作</h5>
        </div>
        <div class="ui-card-body">
            <!-- 桌面端表单 -->
            <form id="rechargeForm" class="d-none d-md-block">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">用户账号</label>
                        <input type="text" class="form-control" id="rechargeAccount" placeholder="请输入用户账号" required>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">货币类型</label>
                        <select class="form-select" id="rechargeCurrency" required>
                            <option value="">请选择货币类型</option>
                            <option value="c_coin">C币</option>
                            <option value="point">泡点</option>
                            <option value="score">积分</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">操作类型</label>
                        <select class="form-select" id="rechargeOperation" required>
                            <option value="">请选择操作</option>
                            <option value="add">增加</option>
                            <option value="subtract">减少</option>
                            <option value="set">设置</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">金额</label>
                        <input type="number" class="form-control" id="rechargeAmount" placeholder="请输入金额" min="0" step="0.01" required>
                    </div>
                    <div class="col-md-9">
                        <label class="form-label">操作原因</label>
                        <input type="text" class="form-control" id="rechargeReason" placeholder="请输入操作原因（可选）">
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-success w-100">
                            <i class="bi bi-plus-circle"></i> 执行充值
                        </button>
                    </div>
                </div>
            </form>

            <!-- 手机端表单 -->
            <form id="rechargeFormMobile" class="d-md-none">
                <div class="form-row-mobile">
                    <div class="mb-3">
                        <label class="form-label">用户账号</label>
                        <input type="text" class="form-control" id="rechargeAccountMobile" placeholder="请输入用户账号" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">货币类型</label>
                        <select class="form-select" id="rechargeCurrencyMobile" required>
                            <option value="">请选择货币类型</option>
                            <option value="c_coin">C币</option>
                            <option value="point">泡点</option>
                            <option value="score">积分</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">操作类型</label>
                        <select class="form-select" id="rechargeOperationMobile" required>
                            <option value="">请选择操作</option>
                            <option value="add">增加</option>
                            <option value="subtract">减少</option>
                            <option value="set">设置</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">金额</label>
                        <input type="number" class="form-control" id="rechargeAmountMobile" placeholder="请输入金额" min="0" step="0.01" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">操作原因</label>
                        <input type="text" class="form-control" id="rechargeReasonMobile" placeholder="请输入操作原因（可选）">
                    </div>
                    <button type="submit" class="btn btn-success btn-mobile">
                        <i class="bi bi-plus-circle"></i> 执行充值
                    </button>
                </div>
            </form>
        </div>
    </div>
    <!-- 充值记录 -->
    <div class="ui-card">
        <!-- 桌面端标题栏 -->
        <div class="ui-card-header d-none d-md-flex justify-content-between align-items-center">
            <h5 class="mb-0">充值记录</h5>
            <div class="d-flex gap-2">
                <input type="text" class="form-control form-control-sm" id="recordSearch" placeholder="搜索用户账号..." style="width: 200px;">
                <select class="form-select form-select-sm" id="currencyFilter" style="width: auto;">
                    <option value="">全部货币</option>
                    <option value="c_coin">C币</option>
                    <option value="point">泡点</option>
                    <option value="score">积分</option>
                </select>
                <button type="button" class="btn btn-outline-primary btn-sm" onclick="refreshRechargeHistory()">
                    <i class="bi bi-arrow-clockwise"></i> 刷新
                </button>
            </div>
        </div>

        <!-- 手机端标题栏 -->
        <div class="ui-card-header d-md-none">
            <h5 class="mb-3">充值记录</h5>
            <div class="filters-mobile">
                <input type="text" class="form-control form-control-sm" id="recordSearchMobile" placeholder="搜索用户账号...">
                <select class="form-select form-select-sm" id="currencyFilterMobile">
                    <option value="">全部货币</option>
                    <option value="c_coin">C币</option>
                    <option value="point">泡点</option>
                    <option value="score">积分</option>
                </select>
                <button type="button" class="btn btn-outline-primary btn-sm" onclick="refreshRechargeHistoryMobile()">
                    <i class="bi bi-arrow-clockwise"></i> 刷新
                </button>
            </div>
        </div>
        <div class="ui-card-body">
            <!-- 桌面端表格 -->
            <div class="table-responsive d-none d-md-block">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>时间</th>
                            <th>用户账号</th>
                            <th>货币类型</th>
                            <th>操作类型</th>
                            <th>金额</th>
                            <th>操作前余额</th>
                            <th>操作后余额</th>
                            <th>操作原因</th>
                            <th>操作员</th>
                        </tr>
                    </thead>
                    <tbody id="rechargeHistoryTable">
                        <tr>
                            <td colspan="9" class="text-center text-muted">暂无充值记录</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 手机端卡片列表 -->
            <div class="d-md-none" id="rechargeHistoryMobile">
                <div class="text-center text-muted py-4">暂无充值记录</div>
            </div>

            <!-- 桌面端分页 -->
            <div class="d-none d-md-block">
                <nav aria-label="充值记录分页">
                    <ul class="pagination justify-content-center" id="rechargePagination">
                    </ul>
                </nav>
            </div>

            <!-- 手机端分页 -->
            <div class="d-md-none">
                <nav aria-label="充值记录分页">
                    <ul class="pagination justify-content-center" id="rechargePaginationMobile">
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>
{/block}

{block name="js"}
<script>
// 工具函数 - 如果AdminUtils不存在则使用fallback
function showToast(message, type = 'info') {
    if (typeof AdminUtils !== 'undefined' && AdminUtils.showToast) {
        AdminUtils.showToast(message, type);
    } else {
        // Fallback to alert
        alert(message);
    }
}

// 加载ShopConfig配置
async function loadShopConfig() {
    try {
        const response = await fetch('/api/getShopConfig?t=' + Date.now());
        const result = await response.json();
        if (result.success && result.data) {
            window.ShopConfig = result.data;
            console.log('充值管理页面ShopConfig已更新:', window.ShopConfig);
            return true;
        }
    } catch (error) {
        console.error('加载ShopConfig失败:', error);
    }
    return false;
}

// 检查认证状态
async function checkAuthStatus() {
    try {
        const response = await fetch('/admin/dashboard', {
            method: 'GET',
            headers: {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
            }
        });

        if (response.status === 403 || response.status === 401) {
            showToast('登录已过期，请重新登录', 'warning');
            setTimeout(() => {
                window.location.href = '/login';
            }, 2000);
            return false;
        }

        return response.ok;
    } catch (error) {
        console.error('检查认证状态失败:', error);
        return true; // 网络错误时不强制跳转
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', async function() {
    console.log('DOMContentLoaded - AdminPagination available:', typeof AdminPagination);
    console.log('DOMContentLoaded - window.AdminPagination available:', typeof window.AdminPagination);

    // 先检查认证状态
    const isAuthenticated = await checkAuthStatus();
    if (!isAuthenticated) {
        return; // 如果认证失败，停止初始化
    }

    await loadShopConfig();

    // 延迟一下确保所有脚本加载完成
    setTimeout(() => {
        loadRechargeHistory();
    }, 200);

    // 绑定桌面端表单提交事件
    const desktopForm = document.getElementById('rechargeForm');
    if (desktopForm) {
        desktopForm.addEventListener('submit', function(e) {
            e.preventDefault();
            executeRecharge();
        });
    }

    // 绑定手机端表单提交事件
    const mobileForm = document.getElementById('rechargeFormMobile');
    if (mobileForm) {
        mobileForm.addEventListener('submit', function(e) {
            e.preventDefault();
            executeRechargeMobile();
        });
    }
});

// 查询用户余额
function queryUserBalance() {
    const account = document.getElementById('queryAccount').value.trim();

    if (!account) {
        showToast('请输入用户账号', 'warning');
        return;
    }

    // 显示加载状态
    showToast('正在查询用户余额...', 'info');

    fetch('/admin/recharge/getUserBalance', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            account: account
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success || data.code === 200 || data.code === 0) {
            displayUserBalance(data.data);
            showToast('查询成功', 'success');
        } else {
            showToast('查询失败: ' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('查询用户余额失败:', error);
        showToast('查询失败，请稍后重试', 'danger');
    });
}

// 手机端查询用户余额
function queryUserBalanceMobile() {
    const account = document.getElementById('queryAccountMobile').value.trim();

    if (!account) {
        showToast('请输入用户账号', 'warning');
        return;
    }

    // 显示加载状态
    showToast('正在查询用户余额...', 'info');

    fetch('/admin/recharge/getUserBalance', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            account: account
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success || data.code === 200 || data.code === 0) {
            displayUserBalanceMobile(data.data);
            showToast('查询成功', 'success');
        } else {
            showToast('查询失败: ' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('查询用户余额失败:', error);
        showToast('查询失败，请稍后重试', 'danger');
    });
}

// 显示用户余额
function displayUserBalance(balanceData) {
    const balanceCard = document.getElementById('balanceCard');
    const balanceContent = document.getElementById('balanceContent');

    let html = '<div class="row">';

    if (balanceData.balances) {
        Object.keys(balanceData.balances).forEach(currency => {
            const amount = balanceData.balances[currency];
            html += `
                <div class="col-md-4 mb-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <h5 class="card-title">${getCurrencyName(currency)}</h5>
                            <h3 class="card-text">${amount}</h3>
                        </div>
                    </div>
                </div>
            `;
        });
    }

    html += '</div>';
    balanceContent.innerHTML = html;
    balanceCard.style.display = 'block';
}

// 手机端显示用户余额
function displayUserBalanceMobile(balanceData) {
    const balanceCard = document.getElementById('balanceCard');
    const balanceContent = document.getElementById('balanceContentMobile');

    let html = '<div class="balance-card-mobile">';

    if (balanceData.balances) {
        Object.keys(balanceData.balances).forEach(currency => {
            const amount = balanceData.balances[currency];
            html += `
                <div class="card bg-primary text-white mb-2">
                    <div class="card-body text-center py-3">
                        <h6 class="card-title mb-1">${getCurrencyName(currency)}</h6>
                        <h4 class="card-text mb-0">${amount}</h4>
                    </div>
                </div>
            `;
        });
    }

    html += '</div>';
    balanceContent.innerHTML = html;
    balanceCard.style.display = 'block';
}

// 获取货币名称
function getCurrencyName(currency) {
    // 首先尝试从全局配置获取
    if (window.ShopConfig) {
        // 映射货币类型
        const typeMap = {
            'c_coin': '3',
            'point': '1',
            'score': '2'
        };

        const typeId = typeMap[currency];
        if (typeId) {
            if (window.ShopConfig.currency_types && window.ShopConfig.currency_types[typeId]) {
                return window.ShopConfig.currency_types[typeId].name;
            }
            if (window.ShopConfig.currencies && window.ShopConfig.currencies[typeId]) {
                return window.ShopConfig.currencies[typeId].name;
            }
        }
    }

    // 降级到本地配置
    const names = {
        'c_coin': 'C币',
        'point': '泡点',
        'score': '积分'
    };
    return names[currency] || currency;
}

// 执行充值操作
function executeRecharge() {
    const account = document.getElementById('rechargeAccount').value.trim();
    const currency = document.getElementById('rechargeCurrency').value;
    const operation = document.getElementById('rechargeOperation').value;
    const amount = document.getElementById('rechargeAmount').value;
    const reason = document.getElementById('rechargeReason').value.trim();

    if (!account || !currency || !operation || !amount) {
        showToast('请填写所有必填字段', 'warning');
        return;
    }

    if (parseFloat(amount) <= 0) {
        showToast('金额必须大于0', 'warning');
        return;
    }

    // 显示加载状态
    showToast('正在执行充值操作...', 'info');

    fetch('/admin/recharge/doRecharge', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            account: account,
            currency: currency,
            operation: operation,
            amount: parseFloat(amount),
            reason: reason
        })
    })
    .then(response => {
        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);

        if (response.status === 403) {
            showToast('权限不足，请重新登录', 'danger');
            // 可以选择重定向到登录页面
            setTimeout(() => {
                window.location.href = '/login';
            }, 2000);
            return Promise.reject('权限不足');
        }

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return response.json();
    })
    .then(data => {
        console.log('Response data:', data);
        if (data.success || data.code === 200 || data.code === 0) {
            showToast('充值操作成功', 'success');
            document.getElementById('rechargeForm').reset();
            document.getElementById('balanceCard').style.display = 'none';
            loadRechargeHistory();
        } else {
            showToast('充值操作失败: ' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('充值操作失败:', error);
        showToast('充值操作失败，请稍后重试', 'danger');
    });
}

// 手机端执行充值操作
function executeRechargeMobile() {
    const account = document.getElementById('rechargeAccountMobile').value.trim();
    const currency = document.getElementById('rechargeCurrencyMobile').value;
    const operation = document.getElementById('rechargeOperationMobile').value;
    const amount = document.getElementById('rechargeAmountMobile').value;
    const reason = document.getElementById('rechargeReasonMobile').value.trim();

    if (!account || !currency || !operation || !amount) {
        showToast('请填写所有必填字段', 'warning');
        return;
    }

    if (parseFloat(amount) <= 0) {
        showToast('金额必须大于0', 'warning');
        return;
    }

    // 显示加载状态
    showToast('正在执行充值操作...', 'info');

    fetch('/admin/recharge/doRecharge', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            account: account,
            currency: currency,
            operation: operation,
            amount: parseFloat(amount),
            reason: reason
        })
    })
    .then(response => {
        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);

        if (response.status === 403) {
            showToast('权限不足，请重新登录', 'danger');
            // 可以选择重定向到登录页面
            setTimeout(() => {
                window.location.href = '/login';
            }, 2000);
            return Promise.reject('权限不足');
        }

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return response.json();
    })
    .then(data => {
        console.log('Response data:', data);
        if (data.success || data.code === 200 || data.code === 0) {
            showToast('充值操作成功', 'success');
            document.getElementById('rechargeFormMobile').reset();
            document.getElementById('balanceCard').style.display = 'none';
            loadRechargeHistory();
        } else {
            showToast('充值操作失败: ' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('充值操作失败:', error);
        showToast('充值操作失败，请稍后重试', 'danger');
    });
}

// 加载充值记录
function loadRechargeHistory(page = 1) {
    // 获取搜索参数（优先使用桌面端，如果不存在则使用手机端）
    const searchDesktop = document.getElementById('recordSearch');
    const searchMobile = document.getElementById('recordSearchMobile');
    const currencyDesktop = document.getElementById('currencyFilter');
    const currencyMobile = document.getElementById('currencyFilterMobile');

    const search = (searchDesktop ? searchDesktop.value.trim() : '') ||
                   (searchMobile ? searchMobile.value.trim() : '');
    const currency = (currencyDesktop ? currencyDesktop.value : '') ||
                     (currencyMobile ? currencyMobile.value : '');

    const params = new URLSearchParams({
        page: page,
        limit: 20
    });

    if (search) params.append('account', search);
    if (currency) params.append('currency', currency);

    fetch('/admin/recharge/getHistory', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            page: page,
            limit: 20,
            account: search,
            currency: currency
        })
    })
        .then(response => response.json())
        .then(data => {
            if (data.success || data.code === 200 || data.code === 0) {
                displayRechargeHistory(data.data);
            }
        })
        .catch(error => {
            console.error('加载充值记录失败:', error);
        });
}

// 显示充值记录
function displayRechargeHistory(data) {
    const tbody = document.getElementById('rechargeHistoryTable');
    const mobileContainer = document.getElementById('rechargeHistoryMobile');

    if (!data.list || data.list.length === 0) {
        // 桌面端显示
        if (tbody) {
            tbody.innerHTML = '<tr><td colspan="9" class="text-center text-muted">暂无充值记录</td></tr>';
        }
        // 手机端显示
        if (mobileContainer) {
            mobileContainer.innerHTML = '<div class="text-center text-muted py-4">暂无充值记录</div>';
        }

        // 隐藏分页
        const pagination = document.getElementById('rechargePagination');
        const paginationMobile = document.getElementById('rechargePaginationMobile');
        if (pagination) pagination.innerHTML = '';
        if (paginationMobile) paginationMobile.innerHTML = '';

        return;
    }

    // 桌面端表格显示
    if (tbody) {
        let tableHtml = '';
        data.list.forEach(record => {
            tableHtml += `
                <tr>
                    <td>${record.create_time || record.created_at}</td>
                    <td>${record.username}</td>
                    <td>${getCurrencyName(record.currency_type)}</td>
                    <td>${getOperationName(record.operation)}</td>
                    <td>${record.amount}</td>
                    <td>${record.old_balance || '-'}</td>
                    <td>${record.new_balance || '-'}</td>
                    <td>${record.reason || '-'}</td>
                    <td>${record.admin_user || '-'}</td>
                </tr>
            `;
        });
        tbody.innerHTML = tableHtml;
    }

    // 手机端卡片显示
    if (mobileContainer) {
        let mobileHtml = '';
        data.list.forEach(record => {
            mobileHtml += `
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="row g-2">
                            <div class="col-6">
                                <small class="text-muted">时间</small>
                                <div class="fw-medium">${record.create_time || record.created_at}</div>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">用户</small>
                                <div class="fw-medium">${record.username}</div>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">货币类型</small>
                                <div class="fw-medium">${getCurrencyName(record.currency_type)}</div>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">操作类型</small>
                                <div class="fw-medium">${getOperationName(record.operation)}</div>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">金额</small>
                                <div class="fw-medium text-primary">${record.amount}</div>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">操作员</small>
                                <div class="fw-medium">${record.admin_user || '-'}</div>
                            </div>
                            <div class="col-12">
                                <small class="text-muted">余额变化</small>
                                <div class="fw-medium">${record.old_balance || '-'} → ${record.new_balance || '-'}</div>
                            </div>
                            ${record.reason ? `
                            <div class="col-12">
                                <small class="text-muted">操作原因</small>
                                <div class="fw-medium">${record.reason}</div>
                            </div>
                            ` : ''}
                        </div>
                    </div>
                </div>
            `;
        });
        mobileContainer.innerHTML = mobileHtml;
    }

    // 处理分页
    updateRechargePagination(data);
}

// 更新充值记录分页
function updateRechargePagination(data) {
    const total = data.total || 0;
    const currentPage = data.page || 1;
    const limit = data.limit || 20;
    const totalPages = Math.ceil(total / limit);



    // 如果没有数据或只有一页，隐藏分页
    if (totalPages <= 1) {
        const pagination = document.getElementById('rechargePagination');
        const paginationMobile = document.getElementById('rechargePaginationMobile');
        if (pagination) {
            pagination.innerHTML = '';
        }
        if (paginationMobile) {
            paginationMobile.innerHTML = '';
        }

        return;
    }

    // 检查AdminPagination是否可用
    if (typeof AdminPagination === 'undefined') {
        console.error('AdminPagination is not available, using fallback');
        createFallbackPagination(currentPage, totalPages);
        return;
    }

    // 使用全局的AdminPagination组件 - 桌面端
    const paginationContainer = document.getElementById('rechargePagination');
    if (paginationContainer) {
        try {
            // 清理之前的实例
            paginationContainer.innerHTML = '';

            window.rechargePagination = new AdminPagination('#rechargePagination', {
                currentPage: currentPage,
                totalPages: totalPages,
                onPageChange: (page) => {
                    loadRechargeHistory(page);
                }
            });
        } catch (error) {
            console.error('Error with desktop AdminPagination:', error);
            createFallbackPagination(currentPage, totalPages, 'rechargePagination');
        }
    }

    // 手机端使用简化的Bootstrap分页
    const paginationMobileContainer = document.getElementById('rechargePaginationMobile');
    if (paginationMobileContainer) {
        createFallbackPagination(currentPage, totalPages, 'rechargePaginationMobile');
    }
}

// 备用分页实现
function createFallbackPagination(currentPage, totalPages, containerId = 'rechargePagination') {
    const container = document.getElementById(containerId);
    if (!container) return;

    const isMobile = containerId.includes('Mobile');
    let html = '';

    if (isMobile) {
        // 手机端简化分页：只显示上一页、当前页、下一页（使用内联样式强制应用）
        if (currentPage > 1) {
            html += `<li class="page-item" style="margin: 0 2px !important; flex: none !important; width: auto !important;"><a class="page-link" href="#" onclick="loadRechargeHistory(${currentPage - 1})" style="width: 40px !important; height: 40px !important; padding: 0.5rem !important; display: flex !important; align-items: center !important; justify-content: center !important; font-size: 0.9rem !important; border-radius: 6px !important;">‹</a></li>`;
        }

        html += `<li class="page-item active" style="margin: 0 2px !important; flex: none !important; width: auto !important;"><a class="page-link" href="#" style="width: 40px !important; height: 40px !important; padding: 0.5rem !important; display: flex !important; align-items: center !important; justify-content: center !important; font-size: 0.9rem !important; background-color: #007bff !important; border-color: #007bff !important; color: white !important; border-radius: 6px !important;">${currentPage}</a></li>`;

        if (currentPage < totalPages) {
            html += `<li class="page-item" style="margin: 0 2px !important; flex: none !important; width: auto !important;"><a class="page-link" href="#" onclick="loadRechargeHistory(${currentPage + 1})" style="width: 40px !important; height: 40px !important; padding: 0.5rem !important; display: flex !important; align-items: center !important; justify-content: center !important; font-size: 0.9rem !important; border-radius: 6px !important;">›</a></li>`;
        }

        // 添加页码信息
        container.innerHTML = html;

        // 强制应用容器样式
        container.style.cssText = 'display: flex !important; justify-content: center !important; gap: 4px !important; flex-wrap: nowrap !important; padding: 0 !important; margin-bottom: 0 !important; list-style: none !important;';

        // 在分页后添加页码信息
        const navElement = container.closest('nav');
        if (navElement) {
            // 移除之前的页码信息
            const existingInfo = navElement.parentNode.querySelector('.mobile-page-info');
            if (existingInfo) {
                existingInfo.remove();
            }

            // 添加新的页码信息
            const infoDiv = document.createElement('div');
            infoDiv.className = 'mobile-page-info text-center mt-2';
            infoDiv.innerHTML = `<small class="text-muted">第 ${currentPage} 页，共 ${totalPages} 页</small>`;
            navElement.parentNode.insertBefore(infoDiv, navElement.nextSibling);
        }
    } else {
        // 桌面端标准分页
        if (currentPage > 1) {
            html += `<li class="page-item"><a class="page-link" href="#" onclick="loadRechargeHistory(${currentPage - 1})">上一页</a></li>`;
        }

        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 2);

        for (let i = startPage; i <= endPage; i++) {
            const activeClass = i === currentPage ? 'active' : '';
            html += `<li class="page-item ${activeClass}"><a class="page-link" href="#" onclick="loadRechargeHistory(${i})">${i}</a></li>`;
        }

        if (currentPage < totalPages) {
            html += `<li class="page-item"><a class="page-link" href="#" onclick="loadRechargeHistory(${currentPage + 1})">下一页</a></li>`;
        }

        container.innerHTML = html;
    }
}



// 获取操作类型名称
function getOperationName(operation) {
    const names = {
        'add': '增加',
        'subtract': '减少',
        'set': '设置'
    };
    return names[operation] || operation;
}

// 刷新充值记录
function refreshRechargeHistory() {
    showToast('正在刷新数据...', 'info');
    loadRechargeHistory();
}

// 手机端刷新充值记录
function refreshRechargeHistoryMobile() {
    showToast('正在刷新数据...', 'info');
    loadRechargeHistory();
}


</script>
{/block}


