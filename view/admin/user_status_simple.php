{extend name="admin/layout/base" /}

{block name="css"}
<link href="/static/css/user-status.css" rel="stylesheet">
{/block}

{block name="content"}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="bi bi-person-check me-2"></i>
                        用户状态管理
                    </h1>
                    <p class="text-muted mb-0">管理用户封禁状态，查看封禁记录和在线用户</p>
                </div>
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-outline-primary" onclick="refreshData()">
                        <i class="bi bi-arrow-clockwise"></i>
                        刷新数据
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="row mb-4">
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="card border-0 shadow-sm stats-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="stats-icon bg-primary bg-opacity-10">
                                <i class="bi bi-people-fill text-primary fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="fw-bold text-dark fs-5" id="totalUsers">-</div>
                            <div class="text-muted small">总用户数</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="card border-0 shadow-sm stats-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="stats-icon bg-success bg-opacity-10">
                                <i class="bi bi-person-check-fill text-success fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="fw-bold text-dark fs-5" id="activeUsers">-</div>
                            <div class="text-muted small">正常用户</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="card border-0 shadow-sm stats-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="stats-icon bg-danger bg-opacity-10">
                                <i class="bi bi-person-x-fill text-danger fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="fw-bold text-dark fs-5" id="bannedUsers">-</div>
                            <div class="text-muted small">封禁用户</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="card border-0 shadow-sm stats-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="stats-icon bg-warning bg-opacity-10">
                                <i class="bi bi-clock-history text-warning fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="fw-bold text-dark fs-5" id="todayOperations">-</div>
                            <div class="text-muted small">今日操作</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 功能选项卡 -->
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-white border-bottom">
            <ul class="nav nav-tabs card-header-tabs" id="userStatusTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="ban-records-tab" data-bs-toggle="tab" data-bs-target="#ban-records" type="button" role="tab">
                        <i class="bi bi-list-ul me-2"></i>
                        封禁记录
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="user-management-tab" data-bs-toggle="tab" data-bs-target="#user-management" type="button" role="tab">
                        <i class="bi bi-gear me-2"></i>
                        批量操作
                    </button>
                </li>
            </ul>
        </div>
        <div class="card-body">
            <div class="tab-content" id="userStatusTabContent">
                <!-- 封禁记录选项卡 -->
                <div class="tab-pane fade show active" id="ban-records" role="tabpanel">
                    <!-- 搜索和筛选 -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="bi bi-search"></i>
                                </span>
                                <input type="text" class="form-control" id="banRecordsSearch" placeholder="搜索用户名...">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="banRecordsStatus">
                                <option value="">全部状态</option>
                                <option value="1">正常</option>
                                <option value="0">封禁</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <input type="text" class="form-control" id="banRecordsDateRange" placeholder="选择日期范围">
                        </div>
                        <div class="col-md-2">
                            <button type="button" class="btn btn-primary w-100" onclick="searchBanRecords()">
                                <i class="bi bi-search"></i>
                                搜索
                            </button>
                        </div>
                    </div>

                    <!-- 批量操作工具栏 -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-primary" onclick="selectAllRecords()">
                                    <i class="bi bi-check-all"></i>
                                    全选
                                </button>
                                <button type="button" class="btn btn-outline-secondary" onclick="clearSelection()">
                                    <i class="bi bi-x-circle"></i>
                                    清除选择
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6 text-end">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-danger" onclick="batchBanUsers()" disabled id="batchBanBtn">
                                    <i class="bi bi-person-x"></i>
                                    批量封禁
                                </button>
                                <button type="button" class="btn btn-outline-success" onclick="batchUnbanUsers()" disabled id="batchUnbanBtn">
                                    <i class="bi bi-person-check"></i>
                                    批量解封
                                </button>
                            </div>
                            <span class="text-muted ms-2" id="selectedCount">已选择 0 个用户</span>
                        </div>
                    </div>

                    <!-- 封禁记录表格 -->
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th width="50">
                                        <input type="checkbox" class="form-check-input" id="selectAllCheckbox" onchange="toggleSelectAll()">
                                    </th>
                                    <th>用户名</th>
                                    <th>状态</th>
                                    <th>封禁时间</th>
                                    <th>解封时间</th>
                                    <th>最后更新</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="banRecordsTableBody">
                                <tr>
                                    <td colspan="7" class="text-center py-4">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">加载中...</span>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    <nav aria-label="封禁记录分页">
                        <ul class="pagination justify-content-center" id="banRecordsPagination">
                        </ul>
                    </nav>
                </div>

                <!-- 批量操作选项卡 -->
                <div class="tab-pane fade" id="user-management" role="tabpanel">
                    <!-- 单个用户操作 -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card operation-card">
                                <div class="card-body">
                                    <h5 class="card-title">
                                        <i class="bi bi-person-x text-danger me-2"></i>
                                        封禁用户
                                    </h5>
                                    <p class="card-text text-muted">封禁指定用户账号</p>
                                    <div class="mb-3">
                                        <label for="banUsername" class="form-label">用户名</label>
                                        <input type="text" class="form-control" id="banUsername" placeholder="输入要封禁的用户名">
                                    </div>
                                    <div class="mb-3">
                                        <label for="banReason" class="form-label">封禁原因</label>
                                        <textarea class="form-control" id="banReason" rows="3" placeholder="输入封禁原因（可选）"></textarea>
                                    </div>
                                    <button type="button" class="btn btn-danger" onclick="banUser()">
                                        <i class="bi bi-person-x"></i>
                                        封禁用户
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card operation-card">
                                <div class="card-body">
                                    <h5 class="card-title">
                                        <i class="bi bi-person-check text-success me-2"></i>
                                        解封用户
                                    </h5>
                                    <p class="card-text text-muted">解封指定用户账号</p>
                                    <div class="mb-3">
                                        <label for="unbanUsername" class="form-label">用户名</label>
                                        <input type="text" class="form-control" id="unbanUsername" placeholder="输入要解封的用户名">
                                    </div>
                                    <div class="mb-3">
                                        <label for="unbanReason" class="form-label">解封原因</label>
                                        <textarea class="form-control" id="unbanReason" rows="3" placeholder="输入解封原因（可选）"></textarea>
                                    </div>
                                    <button type="button" class="btn btn-success" onclick="unbanUser()">
                                        <i class="bi bi-person-check"></i>
                                        解封用户
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 批量用户操作 -->
                    <div class="card operation-card">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="bi bi-people-fill text-primary me-2"></i>
                                批量用户操作
                            </h5>
                            <p class="card-text text-muted">批量处理多个用户的封禁/解封操作</p>

                            <div class="row">
                                <div class="col-md-8">
                                    <div class="mb-3">
                                        <label for="batchUsernames" class="form-label">用户名列表</label>
                                        <textarea class="form-control" id="batchUsernames" rows="5" placeholder="输入用户名，每行一个用户名&#10;例如：&#10;user1&#10;user2&#10;user3"></textarea>
                                        <div class="form-text">每行输入一个用户名，支持批量处理</div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="batchReason" class="form-label">操作原因</label>
                                        <input type="text" class="form-control" id="batchReason" placeholder="输入操作原因（可选）">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">操作类型</label>
                                        <div class="d-grid gap-2">
                                            <button type="button" class="btn btn-danger" onclick="batchBanUsersFromTextarea()">
                                                <i class="bi bi-person-x"></i>
                                                批量封禁
                                            </button>
                                            <button type="button" class="btn btn-success" onclick="batchUnbanUsersFromTextarea()">
                                                <i class="bi bi-person-check"></i>
                                                批量解封
                                            </button>
                                            <button type="button" class="btn btn-outline-secondary" onclick="clearBatchForm()">
                                                <i class="bi bi-arrow-clockwise"></i>
                                                清空表单
                                            </button>
                                        </div>
                                    </div>
                                    <div class="alert alert-info">
                                        <small>
                                            <i class="bi bi-info-circle me-1"></i>
                                            批量操作会逐个处理用户，请耐心等待操作完成
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 确认模态框 -->
<div class="modal fade" id="confirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmModalTitle">确认操作</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="confirmModalBody">
                确定要执行此操作吗？
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmModalAction">确认</button>
            </div>
        </div>
    </div>
</div>
{/block}

{block name="js"}
<script src="/static/js/user-status.js"></script>
{/block}
