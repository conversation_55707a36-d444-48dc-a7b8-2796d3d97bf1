{extend name="admin/layout/base" /}

{block name="css"}
<style>
        .card-config-item {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background: #f8f9fa;
        }
        .reward-item {
            display: inline-block;
            background: #e9ecef;
            padding: 4px 8px;
            border-radius: 4px;
            margin: 2px;
            font-size: 0.875rem;
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
        }
        .stats-card .card-body {
            padding: 1.5rem;
        }
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        .user-card-item {
            border-left: 4px solid #007bff;
            background: #f8f9fa;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 0 8px 8px 0;
        }
        .user-card-item.expired {
            border-left-color: #dc3545;
            background: #fff5f5;
        }
        .progress-thin {
            height: 6px;
        }
        .reward-editor-item {
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 10px;
            background: white;
        }
        .reward-editor-item:hover {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.1);
        }

        /* 手机端优化样式 */
        @media (max-width: 768px) {
            /* 页面标题优化 */
            .mobile-header {
                padding: 1rem;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                margin: -1rem -1rem 1rem -1rem;
                border-radius: 0 0 1rem 1rem;
            }

            .mobile-header h1 {
                font-size: 1.5rem;
                margin-bottom: 0.25rem;
            }

            .mobile-header p {
                font-size: 0.9rem;
                opacity: 0.9;
                margin-bottom: 0;
            }

            /* 手机端按钮优化 */
            .mobile-action-btn {
                position: fixed;
                bottom: 20px;
                right: 20px;
                z-index: 1000;
                border-radius: 50%;
                width: 56px;
                height: 56px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                border: none;
            }

            /* 统计卡片手机端优化 */
            .mobile-stats-card {
                margin-bottom: 1rem;
                border-radius: 12px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                border: none;
                background: white;
                transition: transform 0.2s ease, box-shadow 0.2s ease;
            }

            .mobile-stats-card:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            }

            .mobile-stats-card .card-body {
                padding: 1rem;
            }

            .mobile-stats-number {
                font-size: 1.5rem;
                font-weight: bold;
                margin-bottom: 0.25rem;
                line-height: 1.2;
            }

            .mobile-stats-label {
                font-size: 0.8rem;
                color: #6c757d;
                font-weight: 500;
                line-height: 1.2;
            }

            .mobile-stats-icon {
                font-size: 1.5rem;
                opacity: 0.8;
            }

            .mobile-stats-icon i {
                width: 32px;
                height: 32px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            /* 统计卡片颜色主题 */
            .mobile-stats-card:nth-child(1) {
                border-left: 4px solid #007bff;
            }

            .mobile-stats-card:nth-child(2) {
                border-left: 4px solid #28a745;
            }

            .mobile-stats-card:nth-child(3) {
                border-left: 4px solid #dc3545;
            }

            .mobile-stats-card:nth-child(4) {
                border-left: 4px solid #17a2b8;
            }

            /* 统计数字动画效果 */
            .mobile-stats-number {
                animation: countUp 0.8s ease-out;
            }

            @keyframes countUp {
                from {
                    opacity: 0;
                    transform: translateY(10px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            /* 图标旋转动画 */
            .mobile-stats-icon i {
                transition: transform 0.3s ease;
            }

            .mobile-stats-card:hover .mobile-stats-icon i {
                transform: scale(1.1);
            }

            /* 手机端快速统计 */
            .mobile-quick-stats {
                display: flex;
                justify-content: space-around;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 12px;
                padding: 0.75rem;
                backdrop-filter: blur(10px);
            }

            .mobile-quick-stat-item {
                text-align: center;
                color: white;
            }

            .mobile-quick-stat-number {
                font-size: 1.25rem;
                font-weight: bold;
                margin-bottom: 0.25rem;
                text-shadow: 0 1px 2px rgba(0,0,0,0.1);
            }

            .mobile-quick-stat-label {
                font-size: 0.7rem;
                opacity: 0.9;
                font-weight: 500;
            }

            /* 手机端标签页优化 */
            .mobile-nav-tabs {
                background: #f8f9fa;
                border-radius: 12px;
                padding: 4px;
                margin-bottom: 1rem;
                border: none;
            }

            .mobile-nav-tabs .nav-link {
                border: none;
                border-radius: 8px;
                padding: 0.75rem 1rem;
                margin: 0 2px;
                background: transparent;
                color: #6c757d;
                font-weight: 500;
                transition: all 0.3s ease;
            }

            .mobile-nav-tabs .nav-link.active {
                background: white;
                color: #007bff;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }

            .mobile-nav-tabs .nav-link .badge {
                font-size: 0.7rem;
                padding: 2px 6px;
            }

            /* 卡片配置手机端优化 */
            .mobile-card-config {
                border-radius: 12px;
                margin-bottom: 1rem;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                border: none;
            }

            .mobile-card-config .card-header {
                background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                border-bottom: 1px solid #dee2e6;
                border-radius: 12px 12px 0 0;
                padding: 1rem;
            }

            .mobile-card-config .card-body {
                padding: 1rem;
            }

            /* 用户卡片手机端优化 */
            .mobile-user-card {
                border-radius: 12px;
                margin-bottom: 1rem;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                border: none;
                border-left: 4px solid #007bff;
            }

            .mobile-user-card.expired {
                border-left-color: #dc3545;
            }

            .mobile-user-card .card-body {
                padding: 1rem;
            }

            /* 奖励项目手机端优化 */
            .mobile-reward-item {
                display: inline-block;
                background: #e3f2fd;
                color: #1976d2;
                padding: 4px 8px;
                border-radius: 8px;
                margin: 2px;
                font-size: 0.8rem;
                font-weight: 500;
            }

            /* 进度条手机端优化 */
            .mobile-progress {
                height: 8px;
                border-radius: 4px;
                margin: 0.5rem 0;
            }

            /* 表单优化 */
            .mobile-form-group {
                margin-bottom: 1rem;
            }

            .mobile-form-control {
                border-radius: 8px;
                padding: 0.75rem;
                font-size: 1rem;
            }

            /* 模态框手机端优化 */
            .modal-dialog {
                margin: 1rem;
                max-width: calc(100% - 2rem);
            }

            .modal-content {
                border-radius: 12px;
            }

            .modal-header {
                border-radius: 12px 12px 0 0;
                padding: 1rem 1.5rem;
            }

            .modal-body {
                padding: 1.5rem;
            }

            .modal-footer {
                border-radius: 0 0 12px 12px;
                padding: 1rem 1.5rem;
            }
        }

        /* 改进的标签页样式 */
        .nav-tabs {
            border-bottom: 2px solid #e9ecef;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 8px 8px 0 0;
            padding: 8px 8px 0 8px;
        }

        .nav-tabs .nav-link {
            border: 2px solid transparent;
            border-radius: 8px 8px 0 0;
            color: #6c757d;
            font-weight: 500;
            padding: 16px 20px;
            margin-right: 6px;
            background: #ffffff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            min-width: 180px;
            text-align: left;
        }

        .nav-tabs .nav-link:hover {
            color: #495057;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-color: #dee2e6;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .nav-tabs .nav-link.active {
            color: #0d6efd;
            background: linear-gradient(135deg, #ffffff 0%, #f0f8ff 100%);
            border-color: #0d6efd #0d6efd #ffffff;
            border-bottom-color: #ffffff;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(13, 110, 253, 0.2);
            transform: translateY(-1px);
        }

        .nav-tabs .nav-link.active::before {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #0d6efd, #6610f2);
            border-radius: 2px;
        }

        .nav-tabs .nav-link i {
            font-size: 1.1em;
            margin-right: 6px;
        }

        .nav-tabs .nav-link.active i {
            color: #0d6efd;
        }

        /* 标签页徽章样式 */
        .nav-tabs .nav-link .badge {
            font-size: 0.7em;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 600;
            min-width: 20px;
            text-align: center;
        }

        .nav-tabs .nav-link:hover .badge {
            transform: scale(1.1);
            transition: transform 0.2s ease;
        }

        .nav-tabs .nav-link.active .badge {
            background-color: #0d6efd !important;
            color: white;
            box-shadow: 0 2px 4px rgba(13, 110, 253, 0.3);
        }

        /* 标签页内容区域样式 */
        .tab-content {
            background: #ffffff;
            border-radius: 0 0 8px 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .tab-pane {
            padding: 0;
        }

        .tab-pane .card {
            border: none;
            border-radius: 0 0 8px 8px;
            box-shadow: none;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .nav-tabs {
                padding: 4px 4px 0 4px;
            }

            .nav-tabs .nav-link {
                padding: 10px 16px;
                margin-right: 2px;
                font-size: 0.9em;
            }

            .nav-tabs .nav-link i {
                font-size: 1em;
                margin-right: 4px;
            }
        }
</style>
{/block}

{block name="content"}
<div class="container-fluid">
    <!-- 桌面端页面标题 -->
    <div class="d-none d-md-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">
                <i class="bi bi-credit-card-2-front me-2"></i>周卡月卡管理
            </h1>
            <p class="text-muted mb-0">管理周卡月卡配置和用户卡片</p>
        </div>
        <div>
            <button class="btn btn-primary" onclick="showGrantCardModal()">
                <i class="bi bi-plus-circle me-1"></i>发放卡片
            </button>
        </div>
    </div>

    <!-- 手机端页面标题 -->
    <div class="d-md-none mobile-header">
        <h1>
            <i class="bi bi-credit-card-2-front me-2"></i>周卡月卡管理
        </h1>
        <p>管理周卡月卡配置和用户卡片</p>

        <!-- 手机端快速统计 -->
        <div class="mobile-quick-stats mt-3" id="mobileQuickStats">
            <!-- 快速统计将通过JavaScript加载 -->
        </div>
    </div>

    <!-- 手机端浮动按钮 -->
    <button class="btn btn-primary mobile-action-btn d-md-none" onclick="showGrantCardModal()" title="发放卡片">
        <i class="bi bi-plus-lg"></i>
    </button>

            <!-- 统计卡片 -->
            <div class="row mb-4" id="statsCards">
                <!-- 统计数据将通过JavaScript加载 -->
            </div>

            <!-- 桌面端选项卡 -->
            <ul class="nav nav-tabs mb-4 d-none d-md-flex" id="cardTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="configs-tab" data-bs-toggle="tab" data-bs-target="#configs" type="button" role="tab" aria-controls="configs" aria-selected="true">
                        <div class="d-flex align-items-center justify-content-center">
                            <i class="bi bi-gear me-2"></i>
                            <div class="text-center">
                                <div class="fw-semibold">卡片配置</div>
                                <small class="text-muted" style="font-size: 0.75em;">管理周卡月卡设置</small>
                            </div>
                            <span class="badge bg-primary ms-2" id="configCount">2</span>
                        </div>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="user-cards-tab" data-bs-toggle="tab" data-bs-target="#user-cards" type="button" role="tab" aria-controls="user-cards" aria-selected="false">
                        <div class="d-flex align-items-center justify-content-center">
                            <i class="bi bi-people me-2"></i>
                            <div class="text-center">
                                <div class="fw-semibold">用户卡片</div>
                                <small class="text-muted" style="font-size: 0.75em;">查看用户卡片记录</small>
                            </div>
                            <span class="badge bg-success ms-2" id="userCardCount">-</span>
                        </div>
                    </button>
                </li>
            </ul>

            <!-- 手机端选项卡 -->
            <ul class="nav mobile-nav-tabs d-md-none" id="cardTabsMobile" role="tablist">
                <li class="nav-item flex-fill" role="presentation">
                    <button class="nav-link active w-100" id="configs-tab-mobile" data-bs-toggle="tab" data-bs-target="#configs" type="button" role="tab" aria-controls="configs" aria-selected="true">
                        <i class="bi bi-gear me-1"></i>
                        <span>配置</span>
                        <span class="badge bg-primary ms-1" id="configCountMobile">2</span>
                    </button>
                </li>
                <li class="nav-item flex-fill" role="presentation">
                    <button class="nav-link w-100" id="user-cards-tab-mobile" data-bs-toggle="tab" data-bs-target="#user-cards" type="button" role="tab" aria-controls="user-cards" aria-selected="false">
                        <i class="bi bi-people me-1"></i>
                        <span>用户卡片</span>
                        <span class="badge bg-success ms-1" id="userCardCountMobile">-</span>
                    </button>
                </li>
            </ul>

            <!-- 选项卡内容 -->
            <div class="tab-content" id="cardTabContent">
                <!-- 卡片配置选项卡 -->
                <div class="tab-pane fade show active" id="configs" role="tabpanel">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-gear me-2"></i>卡片配置管理
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="cardConfigsList">
                                <!-- 卡片配置列表将通过JavaScript加载 -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 用户卡片选项卡 -->
                <div class="tab-pane fade" id="user-cards" role="tabpanel">
                    <div class="card">
                        <!-- 桌面端标题和筛选 -->
                        <div class="card-header d-none d-md-block">
                            <div class="row align-items-center">
                                <div class="col">
                                    <h5 class="card-title mb-0">
                                        <i class="bi bi-people me-2"></i>用户卡片管理
                                    </h5>
                                </div>
                                <div class="col-auto">
                                    <div class="row g-2">
                                        <div class="col-auto">
                                            <input type="text" class="form-control form-control-sm" id="searchUser" placeholder="搜索用户ID">
                                        </div>
                                        <div class="col-auto">
                                            <select class="form-select form-select-sm" id="filterCardType">
                                                <option value="">所有类型</option>
                                                <option value="weekly">周卡</option>
                                                <option value="monthly">月卡</option>
                                            </select>
                                        </div>
                                        <div class="col-auto">
                                            <select class="form-select form-select-sm" id="filterStatus">
                                                <option value="">所有状态</option>
                                                <option value="active">有效</option>
                                                <option value="expired">过期</option>
                                            </select>
                                        </div>
                                        <div class="col-auto">
                                            <button class="btn btn-outline-primary btn-sm" onclick="loadUserCards()">
                                                <i class="bi bi-search"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 手机端标题和筛选 -->
                        <div class="card-header d-md-none">
                            <h5 class="card-title mb-3">
                                <i class="bi bi-people me-2"></i>用户卡片管理
                            </h5>
                            <div class="row g-2">
                                <div class="col-12">
                                    <input type="text" class="form-control mobile-form-control" id="searchUserMobile" placeholder="搜索用户ID">
                                </div>
                                <div class="col-6">
                                    <select class="form-select mobile-form-control" id="filterCardTypeMobile">
                                        <option value="">所有类型</option>
                                        <option value="weekly">周卡</option>
                                        <option value="monthly">月卡</option>
                                    </select>
                                </div>
                                <div class="col-6">
                                    <select class="form-select mobile-form-control" id="filterStatusMobile">
                                        <option value="">所有状态</option>
                                        <option value="active">有效</option>
                                        <option value="expired">过期</option>
                                    </select>
                                </div>
                                <div class="col-12 mt-2">
                                    <button class="btn btn-primary w-100 mobile-form-control" onclick="loadUserCardsMobile()">
                                        <i class="bi bi-search me-2"></i>搜索
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="userCardsList">
                                <!-- 用户卡片列表将通过JavaScript加载 -->
                            </div>
                            
                            <!-- 桌面端分页 -->
                            <nav aria-label="用户卡片分页" class="mt-4 d-none d-md-block">
                                <ul class="pagination justify-content-center" id="userCardsPagination">
                                    <!-- 分页将通过JavaScript生成 -->
                                </ul>
                            </nav>

                            <!-- 手机端分页 -->
                            <nav aria-label="用户卡片分页" class="mt-4 d-md-none">
                                <ul class="pagination justify-content-center" id="userCardsPaginationMobile">
                                    <!-- 手机端分页将通过JavaScript生成 -->
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
    </div>
</div>

<!-- 发放卡片模态框 -->
    <div class="modal fade" id="grantCardModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-plus-circle me-2"></i>发放卡片
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="grantCardForm">
                        <div class="mb-3">
                            <label for="grantUserId" class="form-label">用户ID</label>
                            <input type="text" class="form-control" id="grantUserId" required>
                            <div class="form-text">请输入要发放卡片的用户游戏ID</div>
                        </div>
                        <div class="mb-3">
                            <label for="grantCardId" class="form-label">卡片类型</label>
                            <select class="form-select" id="grantCardId" required>
                                <option value="">请选择卡片类型</option>
                                <!-- 选项将通过JavaScript加载 -->
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="grantDuration" class="form-label">有效期（天）</label>
                            <input type="number" class="form-control" id="grantDuration" min="1" max="365" value="7" required>
                            <div class="form-text">卡片的有效天数，1-365天</div>
                        </div>
                        <div class="mb-3">
                            <label for="grantReason" class="form-label">发放原因</label>
                            <input type="text" class="form-control" id="grantReason" value="管理员发放" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="confirmGrantCard()">
                        <i class="bi bi-check-circle me-1"></i>确认发放
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 延长卡片模态框 -->
    <div class="modal fade" id="extendCardModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-clock me-2"></i>延长卡片
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="extendCardForm">
                        <input type="hidden" id="extendUserCardId">
                        <div class="mb-3">
                            <label class="form-label">卡片信息</label>
                            <div class="card bg-light">
                                <div class="card-body">
                                    <div id="extendCardInfo">
                                        <!-- 卡片信息将通过JavaScript填充 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="extendDays" class="form-label">延长天数</label>
                            <input type="number" class="form-control" id="extendDays" min="1" max="365" value="7" required>
                            <div class="form-text">要延长的天数，1-365天</div>
                        </div>
                        <div class="mb-3">
                            <label for="extendReason" class="form-label">延长原因</label>
                            <input type="text" class="form-control" id="extendReason" value="管理员延期" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-warning" onclick="confirmExtendCard()">
                        <i class="bi bi-check-circle me-1"></i>确认延长
                    </button>
                </div>
            </div>
        </div>
    </div>

<!-- 编辑卡片配置模态框 -->
<div class="modal fade" id="editCardConfigModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-pencil me-2"></i>编辑卡片配置
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editCardConfigForm">
                    <input type="hidden" id="editCardId">

                    <!-- 基本信息 -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="editCardName" class="form-label">卡片名称</label>
                            <input type="text" class="form-control" id="editCardName" required>
                        </div>
                        <div class="col-md-6">
                            <label for="editCardType" class="form-label">卡片类型</label>
                            <select class="form-select" id="editCardType" disabled>
                                <option value="weekly">周卡</option>
                                <option value="monthly">月卡</option>
                            </select>
                            <div class="form-text">卡片类型不可修改</div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="editCardDescription" class="form-label">卡片描述</label>
                        <textarea class="form-control" id="editCardDescription" rows="3"></textarea>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="editCardPrice" class="form-label">价格（元）</label>
                            <input type="number" class="form-control" id="editCardPrice" min="0" step="0.01" required>
                        </div>
                        <div class="col-md-4">
                            <label for="editCardDuration" class="form-label">有效期（天）</label>
                            <input type="number" class="form-control" id="editCardDuration" min="1" max="365" required>
                        </div>
                        <div class="col-md-4">
                            <label for="editCardIcon" class="form-label">图标</label>
                            <input type="text" class="form-control" id="editCardIcon" placeholder="bi-calendar-week">
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="editCardStatus" class="form-label">状态</label>
                            <select class="form-select" id="editCardStatus">
                                <option value="1">启用</option>
                                <option value="0">禁用</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="editCardSortOrder" class="form-label">排序</label>
                            <input type="number" class="form-control" id="editCardSortOrder" min="0" value="0">
                        </div>
                    </div>

                    <!-- 奖励配置 -->
                    <div class="mb-3">
                        <label class="form-label">购买即得配置</label>
                        <div class="card bg-light">
                            <div class="card-body">
                                <div id="instantRewardEditor">
                                    <!-- 购买即得编辑器将通过JavaScript生成 -->
                                </div>
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="addRewardItem('instant')">
                                    <i class="bi bi-plus me-1"></i>添加奖励
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">每日奖励配置</label>
                        <div class="card bg-light">
                            <div class="card-body">
                                <div id="dailyRewardEditor">
                                    <!-- 每日奖励编辑器将通过JavaScript生成 -->
                                </div>
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="addRewardItem('daily')">
                                    <i class="bi bi-plus me-1"></i>添加奖励
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" onclick="resetEditCardConfigForm()">取消</button>
                <button type="button" class="btn btn-primary" onclick="confirmUpdateCardConfig()">
                    <i class="bi bi-check-circle me-1"></i>保存修改
                </button>
            </div>
        </div>
    </div>
</div>
{/block}

{block name="js"}
<script src="/static/js/admin-cards.js"></script>
<script>
// 手机端功能支持
function loadUserCardsMobile() {
    // 同步手机端筛选条件到桌面端
    const searchUser = document.getElementById('searchUserMobile').value;
    const filterCardType = document.getElementById('filterCardTypeMobile').value;
    const filterStatus = document.getElementById('filterStatusMobile').value;

    document.getElementById('searchUser').value = searchUser;
    document.getElementById('filterCardType').value = filterCardType;
    document.getElementById('filterStatus').value = filterStatus;

    // 调用桌面端的加载函数
    loadUserCards();
}

// 同步计数器更新
function updateMobileCounters() {
    const configCount = document.getElementById('configCount').textContent;
    const userCardCount = document.getElementById('userCardCount').textContent;

    const configCountMobile = document.getElementById('configCountMobile');
    const userCardCountMobile = document.getElementById('userCardCountMobile');

    if (configCountMobile) configCountMobile.textContent = configCount;
    if (userCardCountMobile) userCardCountMobile.textContent = userCardCount;
}

// 同步标签页状态
function syncTabStates() {
    // 桌面端和手机端标签页同步
    const desktopTabs = document.querySelectorAll('#cardTabs .nav-link');
    const mobileTabs = document.querySelectorAll('#cardTabsMobile .nav-link');

    desktopTabs.forEach((tab, index) => {
        tab.addEventListener('shown.bs.tab', function() {
            if (mobileTabs[index]) {
                mobileTabs[index].classList.add('active');
                mobileTabs[index].setAttribute('aria-selected', 'true');
            }
            mobileTabs.forEach((mTab, mIndex) => {
                if (mIndex !== index) {
                    mTab.classList.remove('active');
                    mTab.setAttribute('aria-selected', 'false');
                }
            });
        });
    });

    mobileTabs.forEach((tab, index) => {
        tab.addEventListener('shown.bs.tab', function() {
            if (desktopTabs[index]) {
                desktopTabs[index].classList.add('active');
                desktopTabs[index].setAttribute('aria-selected', 'true');
            }
            desktopTabs.forEach((dTab, dIndex) => {
                if (dIndex !== index) {
                    dTab.classList.remove('active');
                    dTab.setAttribute('aria-selected', 'false');
                }
            });
        });
    });
}

// 增强标签页交互效果
document.addEventListener('DOMContentLoaded', function() {
    // 初始化手机端功能
    syncTabStates();

    // 监听计数器变化
    const observer = new MutationObserver(updateMobileCounters);
    const configCountElement = document.getElementById('configCount');
    const userCardCountElement = document.getElementById('userCardCount');

    if (configCountElement) {
        observer.observe(configCountElement, { childList: true, subtree: true });
    }
    if (userCardCountElement) {
        observer.observe(userCardCountElement, { childList: true, subtree: true });
    }

    // 标签页切换事件
    const tabButtons = document.querySelectorAll('#cardTabs .nav-link, #cardTabsMobile .nav-link');

    tabButtons.forEach(button => {
        button.addEventListener('shown.bs.tab', function(e) {
            // 更新aria-selected属性
            tabButtons.forEach(btn => btn.setAttribute('aria-selected', 'false'));
            e.target.setAttribute('aria-selected', 'true');

            // 添加切换动画效果
            const targetPane = document.querySelector(e.target.getAttribute('data-bs-target'));
            if (targetPane) {
                targetPane.style.opacity = '0';
                targetPane.style.transform = 'translateY(10px)';

                setTimeout(() => {
                    targetPane.style.transition = 'all 0.3s ease';
                    targetPane.style.opacity = '1';
                    targetPane.style.transform = 'translateY(0)';
                }, 50);
            }
        });

        // 鼠标悬停效果
        button.addEventListener('mouseenter', function() {
            if (!this.classList.contains('active')) {
                this.style.transform = 'translateY(-3px)';
                this.style.boxShadow = '0 6px 12px rgba(0,0,0,0.2)';
            }
        });

        button.addEventListener('mouseleave', function() {
            if (!this.classList.contains('active')) {
                this.style.transform = 'translateY(-2px)';
                this.style.boxShadow = '0 4px 8px rgba(0,0,0,0.15)';
            }
        });
    });

    // 添加标签页指示器
    function addTabIndicator() {
        const activeTab = document.querySelector('#cardTabs .nav-link.active');
        if (activeTab) {
            let indicator = document.querySelector('.tab-indicator');
            if (!indicator) {
                indicator = document.createElement('div');
                indicator.className = 'tab-indicator';
                indicator.style.cssText = `
                    position: absolute;
                    bottom: -2px;
                    height: 3px;
                    background: linear-gradient(90deg, #0d6efd, #6610f2);
                    border-radius: 2px;
                    transition: all 0.3s ease;
                    z-index: 10;
                `;
                document.querySelector('#cardTabs').appendChild(indicator);
            }

            const rect = activeTab.getBoundingClientRect();
            const tabsRect = document.querySelector('#cardTabs').getBoundingClientRect();

            indicator.style.left = (rect.left - tabsRect.left) + 'px';
            indicator.style.width = rect.width + 'px';
        }
    }

    // 初始化指示器
    addTabIndicator();

    // 窗口大小改变时重新计算指示器位置
    window.addEventListener('resize', addTabIndicator);

    // 更新标签页徽章数量
    function updateTabBadges() {
        // 更新用户卡片数量
        const userCardRows = document.querySelectorAll('#userCardsTableBody tr:not(.no-data)');
        const userCardCount = userCardRows.length;
        const userCardBadge = document.getElementById('userCardCount');
        if (userCardBadge) {
            userCardBadge.textContent = userCardCount;
            userCardBadge.className = userCardCount > 0 ? 'badge bg-success ms-2' : 'badge bg-secondary ms-2';
        }
    }

    // 监听数据更新事件
    const tableObserver = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.target.id === 'userCardsTableBody') {
                updateTabBadges();
            }
        });
    });

    // 开始观察用户卡片表格变化
    const userCardsTable = document.getElementById('userCardsTableBody');
    if (userCardsTable) {
        tableObserver.observe(userCardsTable, { childList: true, subtree: true });
    }

    // 初始更新徽章
    setTimeout(updateTabBadges, 1000);
});
</script>
{/block}
