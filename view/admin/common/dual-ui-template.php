<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle ?? '管理面板'; ?> - 管理面板</title>
    
    <!-- CSS 文件 -->
    <link href="/static/css/bootstrap.min.css" rel="stylesheet">
    <link href="/static/css/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/sidebar.css" rel="stylesheet">
    <link href="/static/css/dual-ui.css" rel="stylesheet">
    
    <!-- JavaScript 文件 -->
    <script src="/static/js/dual-ui-manager.js"></script>
    <script src="/static/js/desktop-ui-components.js"></script>
    <script src="/static/js/mobile-ui-components.js"></script>
    <script src="/static/js/sidebar.js"></script>
    <script src="/static/js/admin.js"></script>
    
    <!-- 页面特定样式 -->
    <?php if (isset($pageStyles)): ?>
        <style><?php echo $pageStyles; ?></style>
    <?php endif; ?>
</head>
<body>
    <!-- 侧边栏 -->
    <?php include __DIR__ . '/sidebar.php'; ?>
    
    <!-- 主内容区域 -->
    <div class="main-content ui-container">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="ui-card">
                <div class="ui-card-header">
                    <h1 class="page-title">
                        <?php if (isset($pageIcon)): ?>
                            <i class="<?php echo $pageIcon; ?>"></i>
                        <?php endif; ?>
                        <?php echo $pageTitle ?? '页面标题'; ?>
                    </h1>
                    <?php if (isset($pageDescription)): ?>
                        <p class="page-description text-muted mb-0"><?php echo $pageDescription; ?></p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- 页面内容 -->
        <div class="page-content">
            <?php
            // 包含页面特定内容
            if (isset($contentFile) && file_exists($contentFile)) {
                include $contentFile;
            } elseif (isset($pageContent)) {
                echo $pageContent;
            } else {
                echo '<div class="ui-card"><div class="ui-card-body">页面内容未定义</div></div>';
            }
            ?>
        </div>
    </div>
    
    <!-- 页面特定脚本 -->
    <?php if (isset($pageScripts)): ?>
        <script><?php echo $pageScripts; ?></script>
    <?php endif; ?>
    
    <!-- 初始化双UI系统 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 确保双UI系统已初始化
            if (window.dualUI) {
                console.log('双UI系统已就绪');
                
                // 注册页面特定组件
                if (typeof initPageComponents === 'function') {
                    initPageComponents();
                }
            }
        });
    </script>
</body>
</html>
