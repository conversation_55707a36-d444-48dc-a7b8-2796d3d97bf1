{extend name="admin/layout/base" /}

{block name="css"}
<style>
/* 用户管理页面专用样式 */
.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
}

.status-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.user-actions .btn {
    margin-right: 0.25rem;
    margin-bottom: 0.25rem;
}

/* 角色名显示样式 */
.character-names-container {
    max-width: 100%;
}

.character-names-list {
    max-height: 120px;
    overflow-y: auto;
    padding: 0.25rem;
    background-color: #f8f9fa;
    border-radius: 0.375rem;
    border: 1px solid #dee2e6;
}

.character-names-list .badge {
    display: inline-block;
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    vertical-align: top;
}

.character-names-list .badge:hover {
    max-width: none;
    white-space: normal;
    word-break: break-all;
}

/* 手机端UI样式 */
.mobile-ui .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
}

.mobile-ui .stat-card {
    padding: 0.75rem;
}

.mobile-ui .stat-number {
    font-size: 1.25rem;
}

.mobile-ui .stat-label {
    font-size: 0.75rem;
}

.mobile-ui .filters-container {
    padding: 0.75rem;
}

.mobile-ui .filters-row {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.mobile-ui .filters-row .col {
    width: 100%;
}

/* 手机端用户卡片样式 */
.mobile-ui .user-card {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 0.75rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
}

.mobile-ui .user-card-header {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
}

.mobile-ui .user-card-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1.25rem;
    margin-right: 0.75rem;
}

.mobile-ui .user-card-info h6 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
}

.mobile-ui .user-card-info .text-muted {
    font-size: 0.875rem;
    margin: 0;
}

.mobile-ui .user-card-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
}

.mobile-ui .user-card-detail {
    display: flex;
    flex-direction: column;
}

.mobile-ui .user-card-detail-label {
    font-size: 0.75rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.mobile-ui .user-card-detail-value {
    font-size: 0.875rem;
    font-weight: 500;
}

.mobile-ui .user-card-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
}

.mobile-ui .user-card-actions .btn {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 6px;
}

/* 隐藏桌面端表格 */
.mobile-ui .table-responsive {
    display: none;
}

/* 显示手机端用户列表 */
.mobile-ui .mobile-user-list {
    display: block;
}

/* 桌面端隐藏手机端用户列表 */
.desktop-ui .mobile-user-list {
    display: none;
}

@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.875rem;
    }

    .user-actions .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
}
</style>
{/block}

{block name="content"}
<!-- 页面头部 -->
<div class="page-header">
    <h1 class="page-title">
        <i class="bi bi-people"></i>
        用户管理
    </h1>
    <p class="page-description">管理系统用户，查看用户信息和状态</p>
</div>

<!-- 统计卡片 -->
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-icon primary">
            <i class="bi bi-people"></i>
        </div>
        <div class="stat-content">
            <div class="stat-number" id="totalUsers">-</div>
            <div class="stat-label">总用户数</div>
        </div>
    </div>

    <div class="stat-card">
        <div class="stat-icon success">
            <i class="bi bi-person-check"></i>
        </div>
        <div class="stat-content">
            <div class="stat-number" id="activeUsers">-</div>
            <div class="stat-label">活跃用户</div>
        </div>
    </div>

    <div class="stat-card">
        <div class="stat-icon warning">
            <i class="bi bi-person-plus"></i>
        </div>
        <div class="stat-content">
            <div class="stat-number" id="newUsers">-</div>
            <div class="stat-label">今日新增</div>
        </div>
    </div>

    <div class="stat-card">
        <div class="stat-icon info">
            <i class="bi bi-graph-up"></i>
        </div>
        <div class="stat-content">
            <div class="stat-number" id="onlineUsers">-</div>
            <div class="stat-label">在线用户</div>
        </div>
    </div>
</div>

<!-- 筛选器 -->
<div class="filters-container">
    <div class="filters-row">
        <div class="col">
            <label for="searchInput" class="form-label">搜索用户</label>
            <div class="input-group">
                <span class="input-group-text"><i class="bi bi-search"></i></span>
                <input type="text" class="form-control" id="searchInput" placeholder="输入用户名或昵称...">
            </div>
        </div>

        <div class="col">
            <label for="statusFilter" class="form-label">用户状态</label>
            <select class="form-select" id="statusFilter">
                <option value="">全部状态</option>
                <option value="1">正常用户</option>
                    <option value="0">封禁用户</option>
            </select>
        </div>

        <div class="col">
            <label for="roleFilter" class="form-label">用户角色</label>
            <select class="form-select" id="roleFilter">
                <option value="">全部角色</option>
                <option value="admin">管理员</option>
                <option value="user">普通用户</option>
            </select>
        </div>

        <div class="col">
            <label class="form-label">&nbsp;</label>
            <button type="button" class="btn btn-primary d-block w-100" id="searchButton" onclick="window.userManagement.search()">
                <i class="bi bi-search"></i> 搜索
            </button>
        </div>
    </div>
</div>

<!-- 用户表格 -->
<div class="admin-card">
    <div class="admin-card-header">
        <h5 class="mb-0">
            <i class="bi bi-table"></i>
            用户列表
        </h5>
    </div>
    <div class="admin-card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0" id="usersTable">
                <thead>
                    <tr>
                        <th>用户</th>
                        <th>用户名</th>
                        <th>状态</th>
                        <th>角色</th>
                        <th>注册时间</th>
                        <th>最后登录</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="usersTableBody">
                    <tr>
                        <td colspan="7" class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <div class="mt-2">正在加载用户数据...</div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 手机端用户列表 -->
        <div class="mobile-user-list">
            <div id="mobileUserContainer">
                <!-- 手机端用户卡片将在这里动态生成 -->
            </div>
        </div>
    </div>
    <div class="admin-card-footer">
        <div class="d-flex justify-content-between align-items-center">
            <div class="text-muted">
                显示第 <span id="pageStart">0</span> - <span id="pageEnd">0</span> 条，共 <span id="totalCount">0</span> 条记录
            </div>
            <div id="pagination"></div>
        </div>
    </div>
</div>

<!-- 手机端分页容器 -->
<div class="mobile-ui mt-3">
    <div id="paginationMobile"></div>
</div>
<!-- 用户详情模态框 -->
<div class="modal fade" id="userDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-person-circle"></i>
                    用户详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="userDetailContent">
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <div class="mt-2">正在加载用户详情...</div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 用户操作确认模态框 -->
<div class="modal fade" id="userActionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="userActionTitle">确认操作</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="userActionContent">
                确定要执行此操作吗？
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmUserAction">确认</button>
            </div>
        </div>
    </div>
</div>
{/block}

{block name="js"}
<script>
// 商城配置 - 防止重复声明
if (typeof window.ShopConfig === 'undefined') {
    window.ShopConfig = <?php echo \app\service\ShopConfigService::getFrontendConfigJson(); ?>;
}

// 配置助手对象 - 防止重复添加
if (!window.ShopConfig.getUserStatusName) {
    window.ShopConfig.getUserStatusName = function(id) {
        return this.userStatus[id] ? this.userStatus[id].name : '未知';
    };

    window.ShopConfig.getUserStatusColorClass = function(id) {
        return this.userStatus[id] ? this.userStatus[id].colorClass : 'bg-secondary';
    };
}

// 用户管理页面JavaScript - 防止重复声明
if (typeof window.UserManagement === 'undefined') {
    window.UserManagement = class UserManagement {
    constructor() {
        this.currentPage = 1;
        this.pageSize = 10;
        this.totalPages = 1;
        this.searchTerm = '';
        this.statusFilter = '';
        this.roleFilter = '';
        this.init();
    }

    async init() {
        await this.loadShopConfig();
        this.bindEvents();
        this.loadStats();
        this.loadUsers();
    }

    // 加载ShopConfig配置
    async loadShopConfig() {
        try {
            const response = await fetch('/api/getShopConfig?t=' + Date.now());
            const result = await response.json();
            if (result.success && result.data) {
                window.ShopConfig = result.data;
                console.log('用户管理页面ShopConfig已更新:', window.ShopConfig);
                return true;
            }
        } catch (error) {
            console.error('加载ShopConfig失败:', error);
        }
        return false;
    }

    bindEvents() {
        // 搜索按钮
        document.getElementById('searchButton').addEventListener('click', () => {
            this.search();
        });

        // 搜索框回车
        document.getElementById('searchInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.search();
            }
        });

        // 筛选器变化
        document.getElementById('statusFilter').addEventListener('change', () => {
            this.search();
        });

        document.getElementById('roleFilter').addEventListener('change', () => {
            this.search();
        });
    }

    search() {
        this.searchTerm = document.getElementById('searchInput').value.trim();
        this.statusFilter = document.getElementById('statusFilter').value;
        this.roleFilter = document.getElementById('roleFilter').value;
        this.currentPage = 1;
        this.loadUsers();
    }

    async loadStats() {
        try {
            const response = await fetch('/admin/user/getUserStats');
            const result = await response.json();

            if (result.code === 200 || result.success) {
                const stats = result.data || result;
                document.getElementById('totalUsers').textContent = AdminUtils.formatNumber(stats.total || stats.total_users || 0);
                document.getElementById('activeUsers').textContent = AdminUtils.formatNumber(stats.active || stats.active_users || 0);
                document.getElementById('newUsers').textContent = AdminUtils.formatNumber(stats.newToday || stats.new_today || 0);
                document.getElementById('onlineUsers').textContent = AdminUtils.formatNumber(stats.online || stats.online_users || 0);
            }
        } catch (error) {
            console.error('加载统计数据失败:', error);
        }
    }

    async loadUsers() {
        const tbody = document.getElementById('usersTableBody');
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <div class="mt-2">正在加载用户数据...</div>
                </td>
            </tr>
        `;

        try {
            const params = new URLSearchParams({
                page: this.currentPage,
                pageSize: this.pageSize,
                search: this.searchTerm,
                status: this.statusFilter,
                role: this.roleFilter
            });

            const response = await fetch(`/admin/user/getUserList?${params}`);
            const result = await response.json();

            if (result.code === 200 || result.success) {
                const data = result.data || result;
                this.renderUsers(data.list || data.users || data);
                this.updatePagination(data.total || data.count || 0);
            } else {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" class="text-center py-4 text-danger">
                            <i class="bi bi-exclamation-triangle"></i>
                            <div class="mt-2">${result.message || '加载失败'}</div>
                        </td>
                    </tr>
                `;
            }
        } catch (error) {
            console.error('加载用户列表失败:', error);
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center py-4 text-danger">
                        <i class="bi bi-wifi-off"></i>
                        <div class="mt-2">网络错误，请稍后重试</div>
                    </td>
                </tr>
            `;
        }
    }

    renderUsers(users) {
        const tbody = document.getElementById('usersTableBody');
        const mobileContainer = document.getElementById('mobileUserContainer');

        if (!users || users.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center py-4 text-muted">
                        <i class="bi bi-inbox"></i>
                        <div class="mt-2">暂无用户数据</div>
                    </td>
                </tr>
            `;

            if (mobileContainer) {
                mobileContainer.innerHTML = `
                    <div class="text-center py-5 text-muted">
                        <i class="bi bi-inbox" style="font-size: 3rem;"></i>
                        <div class="mt-3">暂无用户数据</div>
                    </div>
                `;
            }
            return;
        }

        tbody.innerHTML = users.map(user => `
            <tr>
                <td>
                    <div class="d-flex align-items-center">
                        <div class="user-avatar me-2">
                            ${user.nickname ? user.nickname.charAt(0).toUpperCase() : user.username.charAt(0).toUpperCase()}
                        </div>
                        <div>
                            <div class="fw-medium">${user.nickname || '未设置'}</div>
                            <small class="text-muted">${user.email || ''}</small>
                        </div>
                    </div>
                </td>
                <td>
                    <span class="fw-medium">${user.username}</span>
                </td>
                <td>
                    <span class="badge status-badge ${this.getStatusClass(user.status)}">
                        ${this.getStatusText(user.status)}
                    </span>
                </td>
                <td>
                    <span class="badge bg-info">${this.getRoleText(user.role)}</span>
                </td>
                <td>
                    <small>${AdminUtils.formatDate(user.reg_date)}</small>
                </td>
                <td>
                    <small>${user.last_login ? AdminUtils.formatDate(user.last_login) : '从未登录'}</small>
                </td>
                <td>
                    <div class="user-actions">
                        <button class="btn btn-sm btn-outline-primary" onclick="window.userManagement.viewUser('${user.username}')">
                            <i class="bi bi-eye"></i>
                        </button>
                        ${user.status === 1 ?
                            `<button class="btn btn-sm btn-outline-warning" onclick="window.userManagement.banUser('${user.username}')">
                                <i class="bi bi-person-x"></i>
                            </button>` :
                            `<button class="btn btn-sm btn-outline-success" onclick="window.userManagement.unbanUser('${user.username}')">
                                <i class="bi bi-person-check"></i>
                            </button>`
                        }
                    </div>
                </td>
            </tr>
        `).join('');

        // 渲染手机端用户卡片
        if (mobileContainer) {
            mobileContainer.innerHTML = users.map(user => `
                <div class="user-card">
                    <div class="user-card-header">
                        <div class="user-card-avatar">
                            ${user.username.charAt(0).toUpperCase()}
                        </div>
                        <div class="user-card-info">
                            <h6>${user.username}</h6>
                            <p class="text-muted">${user.nickname || '未设置昵称'}</p>
                        </div>
                    </div>

                    <div class="user-card-details">
                        <div class="user-card-detail">
                            <div class="user-card-detail-label">状态</div>
                            <div class="user-card-detail-value">
                                <span class="badge ${this.getStatusClass(user.status)}">${user.status === 1 ? '正常' : '封禁'}</span>
                            </div>
                        </div>
                        <div class="user-card-detail">
                            <div class="user-card-detail-label">角色</div>
                            <div class="user-card-detail-value">
                                <span class="badge bg-info">${user.role === 'admin' ? '管理员' : '普通用户'}</span>
                            </div>
                        </div>
                        <div class="user-card-detail">
                            <div class="user-card-detail-label">${this.getCurrencyName('1')}</div>
                            <div class="user-card-detail-value">${AdminUtils.formatNumber(user.point)}</div>
                        </div>
                        <div class="user-card-detail">
                            <div class="user-card-detail-label">注册时间</div>
                            <div class="user-card-detail-value">${user.register_time || '未知'}</div>
                        </div>
                    </div>

                    <div class="user-card-actions">
                        <button class="btn btn-outline-primary btn-sm" onclick="window.userManagement.viewUser('${user.username}')">
                            <i class="bi bi-eye"></i> 查看
                        </button>
                        ${user.status === 1 ?
                            `<button class="btn btn-outline-warning btn-sm" onclick="window.userManagement.banUser('${user.username}')">
                                <i class="bi bi-person-x"></i> 封禁
                            </button>` :
                            `<button class="btn btn-outline-success btn-sm" onclick="window.userManagement.unbanUser('${user.username}')">
                                <i class="bi bi-person-check"></i> 解封
                            </button>`
                        }
                    </div>
                </div>
            `).join('');
        }
    }

    getStatusClass(status) {
        switch (status) {
            case 1: return 'bg-success';
            case 0: return 'bg-danger';
            default: return 'bg-secondary';
        }
    }

    // 获取货币名称
    getCurrencyName(type) {
        // 首先尝试从全局配置获取
        if (window.ShopConfig) {
            if (window.ShopConfig.currency_types && window.ShopConfig.currency_types[String(type)]) {
                return window.ShopConfig.currency_types[String(type)].name;
            }
            if (window.ShopConfig.currencies && window.ShopConfig.currencies[String(type)]) {
                return window.ShopConfig.currencies[String(type)].name;
            }
        }

        // 降级到本地配置
        switch (String(type)) {
            case '1': return '泡点';
            case '2': return '积分';
            case '3': return 'C币';
            default: return '未知货币';
        }
    }

    getStatusText(status) {
        switch (status) {
            case 1: return '正常';
            case 0: return '封禁';
            default: return '未知';
        }
    }

    getRoleText(role) {
        switch (role) {
            case 'admin': return '管理员';
            case 'user': return '普通用户';
            default: return '普通用户';
        }
    }

    // 渲染角色名列表
    renderCharacterNames(characterNames, characterCount) {
        if (!characterNames || !Array.isArray(characterNames) || characterNames.length === 0) {
            return '<span class="text-muted">无角色</span>';
        }

        let html = '<div class="character-names-container">';

        // 显示角色数量统计
        html += `<div class="mb-2">
            <span class="badge bg-primary">
                <i class="bi bi-person-fill"></i>
                共 ${characterCount || characterNames.length} 个角色
            </span>
        </div>`;

        // 显示角色名列表
        html += '<div class="character-names-list">';
        characterNames.forEach((charName, index) => {
            const displayName = charName || '未知角色';
            const badgeClass = index % 2 === 0 ? 'bg-info' : 'bg-success';
            html += `
                <span class="badge ${badgeClass} me-1 mb-1" style="font-size: 0.85em;">
                    <i class="bi bi-person-circle"></i>
                    ${this.escapeHtml(displayName)}
                </span>
            `;
        });
        html += '</div>';

        html += '</div>';
        return html;
    }

    // HTML转义函数
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    updatePagination(total) {
        this.totalPages = Math.ceil(total / this.pageSize);

        // 更新分页信息
        const start = (this.currentPage - 1) * this.pageSize + 1;
        const end = Math.min(this.currentPage * this.pageSize, total);

        document.getElementById('pageStart').textContent = total > 0 ? start : 0;
        document.getElementById('pageEnd').textContent = end;
        document.getElementById('totalCount').textContent = total;

        console.log('User updatePagination called - total:', total, 'totalPages:', this.totalPages, 'currentPage:', this.currentPage);

        // 创建或更新桌面端分页组件
        if (!this.pagination) {
            this.pagination = new AdminPagination('#pagination', {
                currentPage: this.currentPage,
                totalPages: this.totalPages,
                onPageChange: (page) => {
                    this.currentPage = page;
                    this.loadUsers();
                }
            });
        } else {
            this.pagination.update(this.currentPage, this.totalPages);
        }

        // 创建或更新手机端分页组件（如果存在手机端分页容器）
        const mobilePaginationContainer = document.getElementById('paginationMobile');
        if (mobilePaginationContainer) {
            if (!this.paginationMobile) {
                console.log('Creating mobile pagination component for users');
                this.paginationMobile = new AdminPagination('#paginationMobile', {
                    currentPage: this.currentPage,
                    totalPages: this.totalPages,
                    onPageChange: (page) => {
                        this.currentPage = page;
                        this.loadUsers();
                    }
                });
            } else {
                console.log('Updating mobile pagination component for users');
                this.paginationMobile.update(this.currentPage, this.totalPages);
            }
        }
    }

    async viewUser(username) {
        const modalElement = document.getElementById('userDetailModal');
        const modal = new bootstrap.Modal(modalElement);
        const content = document.getElementById('userDetailContent');

        content.innerHTML = `
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <div class="mt-2">正在加载用户详情...</div>
            </div>
        `;

        // 修复焦点问题
        modalElement.addEventListener('shown.bs.modal', function () {
            // 移除aria-hidden属性
            modalElement.removeAttribute('aria-hidden');
            // 确保模态框可以获得焦点
            modalElement.setAttribute('tabindex', '-1');
        }, { once: true });

        modalElement.addEventListener('hidden.bs.modal', function () {
            // 恢复aria-hidden属性
            modalElement.setAttribute('aria-hidden', 'true');
        }, { once: true });

        modal.show();

        try {
            const response = await fetch(`/admin/getUserDetail?username=${username}`);
            const result = await response.json();

            if (result.code === 200) {
                const user = result.data;
                content.innerHTML = this.renderUserDetail(user);
            } else {
                content.innerHTML = `
                    <div class="text-center py-4 text-danger">
                        <i class="bi bi-exclamation-triangle"></i>
                        <div class="mt-2">${result.message || '加载失败'}</div>
                    </div>
                `;
            }
        } catch (error) {
            console.error('加载用户详情失败:', error);
            content.innerHTML = `
                <div class="text-center py-4 text-danger">
                    <i class="bi bi-wifi-off"></i>
                    <div class="mt-2">网络错误，请稍后重试</div>
                </div>
            `;
        }
    }

    renderUserDetail(user) {
        return `
            <div class="row">
                <div class="col-md-4 text-center">
                    <div class="user-avatar mx-auto mb-3" style="width: 80px; height: 80px; font-size: 2rem;">
                        ${user.nickname ? user.nickname.charAt(0).toUpperCase() : user.username.charAt(0).toUpperCase()}
                    </div>
                    <h5>${user.nickname || '未设置昵称'}</h5>
                    <p class="text-muted">@${user.username}</p>
                </div>
                <div class="col-md-8">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>用户名:</strong></td>
                            <td>${user.username}</td>
                        </tr>
                        <tr>
                            <td><strong>昵称:</strong></td>
                            <td>${user.nickname || '未设置'}</td>
                        </tr>
                        <tr>
                            <td><strong>邮箱:</strong></td>
                            <td>${user.email || '未设置'}</td>
                        </tr>
                        <tr>
                            <td><strong>状态:</strong></td>
                            <td>
                                <span class="badge ${this.getStatusClass(user.status)}">
                                    ${this.getStatusText(user.status)}
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>角色:</strong></td>
                            <td>
                                <span class="badge bg-info">${this.getRoleText(user.role)}</span>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>注册时间:</strong></td>
                            <td>${AdminUtils.formatDate(user.reg_date)}</td>
                        </tr>
                        <tr>
                            <td><strong>最后登录:</strong></td>
                            <td>${user.last_login ? AdminUtils.formatDate(user.last_login) : '从未登录'}</td>
                        </tr>
                        <tr>
                            <td><strong>角色名:</strong></td>
                            <td>${this.renderCharacterNames(user.character_names, user.character_count)}</td>
                        </tr>
                    </table>

                    <!-- 货币余额区域 -->
                    <div class="mt-3">
                        <h6 class="mb-3"><i class="bi bi-wallet2"></i> 货币余额</h6>
                        <div class="row g-2">
                            <div class="col-4">
                                <div class="card border-warning">
                                    <div class="card-body text-center p-2">
                                        <div class="text-warning mb-1">
                                            <i class="bi bi-coin"></i>
                                        </div>
                                        <div class="small text-muted">${this.getCurrencyName('1')}</div>
                                        <div class="fw-bold" id="user-coin-balance">
                                            ${user.currency_balances ? user.currency_balances.coin || 0 : '加载中...'}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="card border-info">
                                    <div class="card-body text-center p-2">
                                        <div class="text-info mb-1">
                                            <i class="bi bi-star-fill"></i>
                                        </div>
                                        <div class="small text-muted">${this.getCurrencyName('2')}</div>
                                        <div class="fw-bold" id="user-silver-balance">
                                            ${user.currency_balances ? user.currency_balances.silver || 0 : '加载中...'}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="card border-success">
                                    <div class="card-body text-center p-2">
                                        <div class="text-success mb-1">
                                            <i class="bi bi-currency-bitcoin"></i>
                                        </div>
                                        <div class="small text-muted">${this.getCurrencyName('3')}</div>
                                        <div class="fw-bold" id="user-ccoin-balance">
                                            ${user.currency_balances ? user.currency_balances.c_coin || 0 : '加载中...'}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    async banUser(username) {
        const confirmed = await AdminUtils.confirm(`确定要封禁用户 "${username}" 吗？`, '封禁用户');
        if (!confirmed) return;

        try {
            const response = await fetch('/admin/banUser', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ username })
            });

            const result = await response.json();

            if (result.code === 200) {
                AdminUtils.showToast('用户封禁成功', 'success');
                this.loadUsers();
                this.loadStats();
            } else {
                AdminUtils.showToast(result.message || '封禁失败', 'danger');
            }
        } catch (error) {
            console.error('封禁用户失败:', error);
            AdminUtils.showToast('网络错误，请稍后重试', 'danger');
        }
    }

    async unbanUser(username) {
        const confirmed = await AdminUtils.confirm(`确定要解封用户 "${username}" 吗？`, '解封用户');
        if (!confirmed) return;

        try {
            const response = await fetch('/admin/unbanUser', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ username })
            });

            const result = await response.json();

            if (result.code === 200) {
                AdminUtils.showToast('用户解封成功', 'success');
                this.loadUsers();
                this.loadStats();
            } else {
                AdminUtils.showToast(result.message || '解封失败', 'danger');
            }
        } catch (error) {
            console.error('解封用户失败:', error);
            AdminUtils.showToast('网络错误，请稍后重试', 'danger');
        }
    }
    };
}

// 初始化双UI系统
function initDualUISystem() {
    // 检测设备类型
    const isMobile = window.innerWidth <= 768 || /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

    // 设置UI模式
    if (isMobile) {
        document.body.classList.add('mobile-ui');
        document.body.classList.remove('desktop-ui');
    } else {
        document.body.classList.add('desktop-ui');
        document.body.classList.remove('mobile-ui');
    }

    console.log('双UI系统已初始化:', isMobile ? 'mobile-ui' : 'desktop-ui');
}

// 监听窗口大小变化
window.addEventListener('resize', function() {
    const isMobileNow = window.innerWidth <= 768;
    if (isMobileNow) {
        document.body.classList.add('mobile-ui');
        document.body.classList.remove('desktop-ui');
    } else {
        document.body.classList.add('desktop-ui');
        document.body.classList.remove('mobile-ui');
    }
});

// 初始化用户管理 - 防止重复声明
if (typeof window.userManagement === 'undefined') {
    window.userManagement = null;
}

document.addEventListener('DOMContentLoaded', () => {
    // 初始化双UI系统
    initDualUISystem();

    // 初始化用户管理
    if (window.UserManagement && !window.userManagement) {
        window.userManagement = new window.UserManagement();
    }
});
</script>
{/block}
