<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{$pageTitle|default='管理面板'} - 管理面板</title>
    
    <!-- Bootstrap 5.3 CSS (本地) -->
    <link href="/static/css/bootstrap.min.css" rel="stylesheet">
    <link href="/static/css/bootstrap-icons.min.css" rel="stylesheet">

    <!-- 自定义管理面板样式 -->
    <link href="/static/css/admin-base.css" rel="stylesheet">
    {block name="css"}{/block}
    
    <!-- 页面特定样式 -->
    {block name="style"}{/block}
</head>
<body>
    <!-- 侧边栏 -->
    {include file="admin/layout/sidebar" /}

    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 页面内容 -->
        <div class="page-content">
            {block name="content"}
                <div class="admin-card">
                    <div class="admin-card-body">页面内容未定义</div>
                </div>
            {/block}
        </div>
    </div>
    
    <!-- Bootstrap 5.3 JavaScript (本地) -->
    <script src="/static/js/bootstrap.bundle.min.js"></script>

    <!-- 管理面板JavaScript -->
    <script src="/static/js/admin-base.js"></script>

    <!-- 管理面板工具类 -->
    <script>
        // AdminUtils 工具类
        window.AdminUtils = {
            // 格式化数字
            formatNumber: function(num, decimals = 0) {
                if (num === null || num === undefined || isNaN(num)) return '0';
                return Number(num).toLocaleString('zh-CN', {
                    minimumFractionDigits: decimals,
                    maximumFractionDigits: decimals
                });
            },

            // 格式化日期
            formatDate: function(date, format = 'YYYY-MM-DD HH:mm:ss') {
                if (!date) return '-';
                const d = new Date(date);
                if (isNaN(d.getTime())) return '-';

                const year = d.getFullYear();
                const month = String(d.getMonth() + 1).padStart(2, '0');
                const day = String(d.getDate()).padStart(2, '0');
                const hours = String(d.getHours()).padStart(2, '0');
                const minutes = String(d.getMinutes()).padStart(2, '0');
                const seconds = String(d.getSeconds()).padStart(2, '0');

                return format
                    .replace('YYYY', year)
                    .replace('MM', month)
                    .replace('DD', day)
                    .replace('HH', hours)
                    .replace('mm', minutes)
                    .replace('ss', seconds);
            },

            // 显示Toast消息
            showToast: function(message, type = 'info') {
                // 创建toast容器（如果不存在）
                let toastContainer = document.getElementById('toast-container');
                if (!toastContainer) {
                    toastContainer = document.createElement('div');
                    toastContainer.id = 'toast-container';
                    toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
                    toastContainer.style.zIndex = '9999';
                    document.body.appendChild(toastContainer);
                }

                // 创建toast元素
                const toastId = 'toast-' + Date.now();
                const toastHtml = `
                    <div id="${toastId}" class="toast align-items-center text-white bg-${type === 'error' ? 'danger' : type} border-0" role="alert">
                        <div class="d-flex">
                            <div class="toast-body">${message}</div>
                            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                        </div>
                    </div>
                `;

                toastContainer.insertAdjacentHTML('beforeend', toastHtml);

                // 显示toast
                const toastElement = document.getElementById(toastId);
                const toast = new bootstrap.Toast(toastElement, { delay: 3000 });
                toast.show();

                // 自动清理
                toastElement.addEventListener('hidden.bs.toast', function() {
                    toastElement.remove();
                });
            }
        };
    </script>

    <!-- 页面特定JS -->
    {block name="js"}{/block}
    {block name="admin_js"}{/block}
    
    <!-- 页面特定脚本 -->
    {block name="script"}
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // 确保双UI系统已初始化
                if (window.dualUI) {
                    console.log('双UI系统已就绪');
                    
                    // 注册页面特定组件
                    if (typeof initPageComponents === 'function') {
                        initPageComponents();
                    }
                }
            });
        </script>
    {/block}
</body>
</html>
