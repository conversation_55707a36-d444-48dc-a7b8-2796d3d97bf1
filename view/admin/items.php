{extend name="admin/layout/base" /}

{block name="css"}
<style>
/* 双UI系统控制 */
/* 默认显示桌面端UI */
.desktop-ui {
    display: block;
}

.mobile-ui {
    display: none;
}

/* 手机端显示控制 */
body.mobile-ui .desktop-ui {
    display: none !important;
}

body.mobile-ui .mobile-ui {
    display: block !important;
}

/* 桌面端显示控制 */
body.desktop-ui .desktop-ui {
    display: block !important;
}

body.desktop-ui .mobile-ui {
    display: none !important;
}

/* 媒体查询作为后备方案 */
@media (max-width: 768px) {
    .desktop-ui {
        display: none !important;
    }

    .mobile-ui {
        display: block !important;
    }
}

@media (min-width: 769px) {
    .desktop-ui {
        display: block !important;
    }

    .mobile-ui {
        display: none !important;
    }
}
/* 商品管理页面专用样式 */
.item-image {
    width: 40px;
    height: 40px;
    border-radius: 0.375rem;
    object-fit: cover;
    border: 1px solid #e9ecef;
}

.item-price {
    font-weight: 600;
    color: #28a745;
}

.item-actions .btn {
    margin-right: 0.25rem;
    margin-bottom: 0.25rem;
}

.stat-icon.danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
}

@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.875rem;
    }

    .item-actions .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
}
</style>
{/block}

{block name="content"}
<!-- 页面头部 -->
<div class="page-header">
    <h1 class="page-title">
        <i class="bi bi-box"></i>
        商品管理
    </h1>
    <p class="page-description">管理系统商品，查看商品信息和状态</p>
</div>

<!-- 统计卡片 - 桌面端 -->
<div class="desktop-ui">
    <div class="row g-3 mb-4">
        <!-- 总商品数 - 主要统计 -->
        <div class="col-md-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="rounded-circle bg-primary bg-opacity-10 p-3">
                            <i class="bi bi-box text-primary fs-2"></i>
                        </div>
                    </div>
                    <h3 class="text-primary mb-1" id="totalItems">-</h3>
                    <p class="text-muted mb-0">商品总数</p>
                </div>
            </div>
        </div>

        <!-- 上架商品 -->
        <div class="col-md-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="rounded-circle bg-success bg-opacity-10 p-3">
                            <i class="bi bi-check-circle text-success fs-2"></i>
                        </div>
                    </div>
                    <h3 class="text-success mb-1" id="activeItems">-</h3>
                    <p class="text-muted mb-0">上架商品</p>
                    <small class="text-success">
                        <i class="bi bi-arrow-up"></i>
                        <span id="activePercentage">0%</span>
                    </small>
                </div>
            </div>
        </div>

        <!-- 下架商品 -->
        <div class="col-md-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="rounded-circle bg-warning bg-opacity-10 p-3">
                            <i class="bi bi-pause-circle text-warning fs-2"></i>
                        </div>
                    </div>
                    <h3 class="text-warning mb-1" id="inactiveItems">-</h3>
                    <p class="text-muted mb-0">下架商品</p>
                    <small class="text-warning">
                        <i class="bi bi-arrow-down"></i>
                        <span id="inactivePercentage">0%</span>
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 统计卡片 - 手机端 -->
<div class="mobile-ui">
    <div class="row g-2 mb-3">
        <div class="col-4">
            <div class="card text-center border-0 bg-primary bg-opacity-10">
                <div class="card-body p-2">
                    <i class="bi bi-box text-primary fs-4"></i>
                    <div class="fw-bold text-primary" id="totalItemsMobile">-</div>
                    <small class="text-muted">总数</small>
                </div>
            </div>
        </div>
        <div class="col-4">
            <div class="card text-center border-0 bg-success bg-opacity-10">
                <div class="card-body p-2">
                    <i class="bi bi-check-circle text-success fs-4"></i>
                    <div class="fw-bold text-success" id="activeItemsMobile">-</div>
                    <small class="text-muted">上架</small>
                </div>
            </div>
        </div>
        <div class="col-4">
            <div class="card text-center border-0 bg-warning bg-opacity-10">
                <div class="card-body p-2">
                    <i class="bi bi-exclamation-triangle text-warning fs-4"></i>
                    <div class="fw-bold text-warning" id="inactiveItemsMobile">-</div>
                    <small class="text-muted">下架</small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 筛选器 - 桌面端 -->
<div class="filters-container desktop-ui">
    <div class="filters-row">
        <div class="col">
            <label for="searchInput" class="form-label">搜索商品</label>
            <div class="input-group">
                <span class="input-group-text"><i class="bi bi-search"></i></span>
                <input type="text" class="form-control" id="searchInput" placeholder="输入商品名称或ID...">
            </div>
        </div>

        <div class="col">
            <label for="categoryFilter" class="form-label">商品分类</label>
            <select class="form-select" id="categoryFilter">
                <option value="">全部分类</option>
                <?php foreach(\app\service\ShopConfigService::getAllCategories() as $id => $name): ?>
                <option value="<?= $id ?>"><?= $name ?></option>
                <?php endforeach; ?>
            </select>
        </div>

        <div class="col">
            <select class="form-select" id="statusFilter">
                <option value="">全部状态</option>
                <option value="1">上架</option>
                <option value="2">下架</option>
            </select>
        </div>

        <div class="col">
            <label for="currencyFilter" class="form-label">货币类型</label>
            <select class="form-select" id="currencyFilter">
                <option value="">全部货币</option>
                <?php foreach(\app\service\ShopConfigService::getAllCurrencyTypes() as $id => $info): ?>
                <option value="<?= $id ?>"><?= $info['name'] ?></option>
                <?php endforeach; ?>
            </select>
        </div>

        <div class="col">
            <label class="form-label">&nbsp;</label>
            <button type="button" class="btn btn-primary d-block w-100" id="searchButton">
                <i class="bi bi-search"></i> 搜索
            </button>
        </div>

        <div class="col">
            <label class="form-label">&nbsp;</label>
            <button type="button" class="btn btn-success d-block w-100" id="addItemButton">
                <i class="bi bi-plus"></i> 添加商品
            </button>
        </div>

        <div class="col">
            <label class="form-label">&nbsp;</label>
            <button type="button" class="btn btn-outline-secondary d-block w-100" id="clearCacheButton" title="清除缓存">
                <i class="bi bi-arrow-clockwise"></i> 清除缓存
            </button>
        </div>
    </div>

    <!-- 缓存状态显示 -->
    <div class="row mt-2">
        <div class="col-12">
            <small class="text-muted">
                <i class="bi bi-database"></i>
                缓存状态: <span id="cacheStatus">未知</span> |
                缓存项数: <span id="cacheCount">0</span> |
                <span id="cacheInfo"></span>
            </small>
        </div>
    </div>
</div>

<!-- 筛选器 - 手机端 -->
<div class="mobile-ui">
    <!-- 搜索栏和操作按钮 -->
    <div class="row g-2 mb-3">
        <div class="col-8">
            <div class="input-group">
                <span class="input-group-text"><i class="bi bi-search"></i></span>
                <input type="text" class="form-control" id="searchInputMobile" placeholder="搜索商品...">
            </div>
        </div>
        <div class="col-2">
            <button class="btn btn-outline-secondary w-100" type="button" data-bs-toggle="collapse" data-bs-target="#mobileFilters">
                <i class="bi bi-funnel"></i>
            </button>
        </div>
        <div class="col-2">
            <button class="btn btn-success w-100" type="button" id="addItemButtonMobile">
                <i class="bi bi-plus"></i>
            </button>
        </div>
    </div>

    <!-- 可折叠的筛选器 -->
    <div class="collapse" id="mobileFilters">
        <div class="card card-body mb-3">
            <div class="row g-2">
                <div class="col-6">
                    <label for="categoryFilterMobile" class="form-label small">分类</label>
                    <select class="form-select form-select-sm" id="categoryFilterMobile">
                        <option value="">全部</option>
                        <?php foreach(\app\service\ShopConfigService::getAllCategories() as $id => $name): ?>
                        <option value="<?= $id ?>"><?= $name ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-6">
                    <label for="statusFilterMobile" class="form-label small">状态</label>
                    <select class="form-select form-select-sm" id="statusFilterMobile">
                        <option value="">全部</option>
                        <option value="1">上架</option>
                        <option value="2">下架</option>
                    </select>
                </div>
                <div class="col-12">
                    <label for="currencyFilterMobile" class="form-label small">货币</label>
                    <select class="form-select form-select-sm" id="currencyFilterMobile">
                        <option value="">全部货币</option>
                        <?php foreach(\app\service\ShopConfigService::getAllCurrencyTypes() as $id => $info): ?>
                        <option value="<?= $id ?>"><?= $info['name'] ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-6">
                    <button type="button" class="btn btn-primary btn-sm w-100" id="searchButtonMobile">
                        <i class="bi bi-search"></i> 搜索
                    </button>
                </div>
                <div class="col-6">
                    <button type="button" class="btn btn-outline-secondary btn-sm w-100" id="clearCacheButtonMobile">
                        <i class="bi bi-arrow-clockwise"></i> 清除缓存
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 手机端缓存状态 -->
    <div class="text-center mb-3">
        <small class="text-muted">
            <i class="bi bi-database"></i> <span id="cacheStatusMobile">未知</span> |
            <span id="cacheCountMobile">0</span>项
        </small>
    </div>
</div>

<!-- 商品表格 - 桌面端 -->
<div class="admin-card desktop-ui">
    <div class="admin-card-header">
        <h5 class="mb-0">
            <i class="bi bi-table"></i>
            商品列表
        </h5>
    </div>
    <div class="admin-card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0" id="itemsTable">
                <thead>
                    <tr>
                        <th>商品</th>
                        <th>分类</th>
                        <th>价格</th>
                        <th>货币</th>
                        <th>发送模式</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="itemsTableBody">
                    <tr>
                        <td colspan="7" class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <div class="mt-2">正在加载商品数据...</div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    <div class="admin-card-footer">
        <div class="d-flex justify-content-between align-items-center">
            <div class="text-muted">
                显示第 <span id="pageStart">0</span> - <span id="pageEnd">0</span> 条，共 <span id="totalCount">0</span> 条记录
            </div>
            <div id="pagination"></div>
        </div>
    </div>
</div>

<!-- 商品列表 - 手机端 -->
<div class="mobile-ui">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h6 class="mb-0">
            <i class="bi bi-box"></i> 商品列表
        </h6>
        <small class="text-muted">
            共 <span id="totalCountMobile">0</span> 件商品
        </small>
    </div>

    <!-- 手机端商品列表容器 -->
    <div id="mobileItemsList">
        <!-- 加载状态 -->
        <div class="text-center py-4" id="mobileLoadingState">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <div class="mt-2 small">正在加载商品数据...</div>
        </div>
    </div>

    <!-- 手机端分页信息 -->
    <div class="d-flex justify-content-between align-items-center mt-3 mb-2">
        <small class="text-muted">
            第 <span id="pageStartMobile">0</span>-<span id="pageEndMobile">0</span> 条
        </small>
        <div id="paginationMobile"></div>
    </div>
</div>
        </div>
    </div>
</div>

<!-- 商品详情模态框 -->
<div class="modal fade" id="itemDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="itemModalTitle">商品详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="itemForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="itemName" class="form-label">商品名称</label>
                                <input type="text" class="form-control" id="itemName" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="itemId" class="form-label">商品ID</label>
                                <input type="text" class="form-control" id="itemId" name="item" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="itemPrice" class="form-label">价格</label>
                                <input type="number" class="form-control" id="itemPrice" name="price" min="0" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="itemPriceType" class="form-label">货币类型</label>
                                <select class="form-select" id="itemPriceType" name="price_type" required>
                                    <!-- 货币类型选项将动态加载 -->
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="itemClass" class="form-label">商品分类</label>
                                <select class="form-select" id="itemClass" name="class" required>
                                    <!-- 分类选项将动态加载 -->
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="itemStatus" class="form-label">状态</label>
                                <select class="form-select" id="itemStatus" name="status" required>
                                    <option value="1">上架</option>
                                    <option value="2">下架</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="itemRestriction" class="form-label">购买限制</label>
                                <select class="form-select" id="itemRestriction" name="restriction">
                                    <!-- 限制类型选项将动态加载 -->
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="itemLimitQuantity" class="form-label">限购数量</label>
                                <input type="number" class="form-control" id="itemLimitQuantity" name="limit_quantity" min="1" value="1">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="itemDeliveryMode" class="form-label">发送模式</label>
                                <select class="form-select" id="itemDeliveryMode" name="delivery_mode">
                                    <option value="1">批量发送</option>
                                    <option value="2">逐条发送</option>
                                </select>
                                <div class="form-text">
                                    <small class="text-muted">
                                        <i class="bi bi-info-circle"></i>
                                        批量发送：一次性发送所有数量，速度快；逐条发送：逐个发送，更稳定
                                    </small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <!-- 预留空间，可以添加其他字段 -->
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="itemInfo" class="form-label">商品描述</label>
                        <textarea class="form-control" id="itemInfo" name="item_info" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="saveItemButton">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 商品详情模态框 -->
<div class="modal fade" id="itemDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="itemModalTitle">商品详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="itemForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="itemName" class="form-label">商品名称</label>
                                <input type="text" class="form-control" id="itemName" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="itemId" class="form-label">商品ID</label>
                                <input type="text" class="form-control" id="itemId" name="item" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="itemPrice" class="form-label">价格</label>
                                <input type="number" class="form-control" id="itemPrice" name="price" min="0" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="itemPriceType" class="form-label">货币类型</label>
                                <select class="form-select" id="itemPriceType" name="price_type" required>
                                    <option value="CNY">人民币 (CNY)</option>
                                    <option value="USD">美元 (USD)</option>
                                    <option value="POINT">积分 (POINT)</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="itemClass" class="form-label">商品分类</label>
                                <select class="form-select" id="itemClass" name="class" required>
                                    <!-- 分类选项将动态加载 -->
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="itemStatus" class="form-label">状态</label>
                                <select class="form-select" id="itemStatus" name="status" required>
                                    <option value="1">上架</option>
                                    <option value="2">下架</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="itemInfo" class="form-label">商品描述</label>
                        <textarea class="form-control" id="itemInfo" name="item_info" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="saveItemButton">保存</button>
            </div>
        </div>
    </div>
</div>

{/block}

{block name="js"}
<script>
// 商城配置 - 防止重复声明
if (typeof window.ShopConfig === 'undefined') {
    window.ShopConfig = <?php echo \app\service\ShopConfigService::getFrontendConfigJson(); ?>;

    // 配置助手对象
    window.ShopConfig.getCategoryName = function(id) {
        return this.categories[id] || '其他';
    };

    window.ShopConfig.getCurrencyName = function(id) {
        return this.currencies[id] ? this.currencies[id].name : '未知';
    };

    window.ShopConfig.getCurrencyColorClass = function(id) {
        return this.currencies[id] ? this.currencies[id].colorClass : 'bg-secondary';
    };

    window.ShopConfig.getItemStatusName = function(id) {
        return this.itemStatus[id] ? this.itemStatus[id].name : '未知';
    };

    window.ShopConfig.getItemStatusColorClass = function(id) {
        return this.itemStatus[id] ? this.itemStatus[id].colorClass : 'bg-secondary';
    };
}

// 商品管理页面JavaScript
class ItemManagement {
    constructor() {
        this.currentPage = 1;
        this.pageSize = 10;
        this.totalPages = 1;
        this.searchTerm = '';
        this.categoryFilter = '';
        this.statusFilter = '';
        this.currencyFilter = '';

        // 缓存相关
        this.cache = new Map();
        this.cacheExpiry = new Map();
        this.cacheTimeout = 5 * 60 * 1000; // 5分钟缓存
        this.isLoading = false;

        this.init();
    }

    init() {
        this.bindEvents();
        this.loadStats();
        this.loadItems();
        this.updateCacheStatus();

        // 定期清理过期缓存（每分钟检查一次）
        this.cacheCleanupInterval = setInterval(() => {
            this.cleanExpiredCache();
        }, 60000);

        // 页面可见性变化时清理过期缓存
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                this.cleanExpiredCache();
            }
        });

        // 页面卸载时清理定时器
        window.addEventListener('beforeunload', () => {
            this.cleanup();
        });
    }

    bindEvents() {
        // 搜索按钮
        document.getElementById('searchButton').addEventListener('click', () => {
            this.search();
        });

        // 搜索框回车
        document.getElementById('searchInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.search();
            }
        });

        // 筛选器变化
        document.getElementById('categoryFilter').addEventListener('change', () => {
            this.search();
        });

        document.getElementById('statusFilter').addEventListener('change', () => {
            this.search();
        });

        document.getElementById('currencyFilter').addEventListener('change', () => {
            this.search();
        });

        // 添加商品按钮
        document.getElementById('addItemButton').addEventListener('click', () => {
            this.showAddItemModal();
        });

        // 清除缓存按钮
        document.getElementById('clearCacheButton').addEventListener('click', () => {
            this.clearAllCache();
            this.updateCacheStatus();
            AdminUtils.showToast('缓存已清除', 'success');
        });

        // 手机端事件绑定
        this.bindMobileEvents();
    }

    bindMobileEvents() {
        // 手机端搜索按钮
        document.getElementById('searchButtonMobile').addEventListener('click', () => {
            this.searchMobile();
        });

        // 手机端搜索框回车
        document.getElementById('searchInputMobile').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.searchMobile();
            }
        });

        // 手机端筛选器变化
        document.getElementById('categoryFilterMobile').addEventListener('change', () => {
            this.searchMobile();
        });

        document.getElementById('statusFilterMobile').addEventListener('change', () => {
            this.searchMobile();
        });

        document.getElementById('currencyFilterMobile').addEventListener('change', () => {
            this.searchMobile();
        });

        // 手机端添加商品按钮
        document.getElementById('addItemButtonMobile').addEventListener('click', () => {
            this.showAddItemModal();
        });

        // 手机端清除缓存按钮
        document.getElementById('clearCacheButtonMobile').addEventListener('click', () => {
            this.clearAllCache();
            this.updateCacheStatus();
            AdminUtils.showToast('缓存已清除', 'success');
        });
    }

    searchMobile() {
        this.searchTerm = document.getElementById('searchInputMobile').value.trim();
        this.categoryFilter = document.getElementById('categoryFilterMobile').value;
        this.statusFilter = document.getElementById('statusFilterMobile').value;
        this.currencyFilter = document.getElementById('currencyFilterMobile').value;
        this.currentPage = 1;
        this.clearFilterCache(); // 清除筛选相关缓存
        this.loadItems(); // 搜索时立即加载，不使用防抖

        // 同步到桌面端筛选器
        document.getElementById('searchInput').value = this.searchTerm;
        document.getElementById('categoryFilter').value = this.categoryFilter;
        document.getElementById('statusFilter').value = this.statusFilter;
        document.getElementById('currencyFilter').value = this.currencyFilter;
    }

    search() {
        this.searchTerm = document.getElementById('searchInput').value.trim();
        this.categoryFilter = document.getElementById('categoryFilter').value;
        this.statusFilter = document.getElementById('statusFilter').value;
        this.currencyFilter = document.getElementById('currencyFilter').value;
        this.currentPage = 1;
        this.clearFilterCache(); // 清除筛选相关缓存
        this.loadItems(); // 搜索时立即加载，不使用防抖

        // 同步到手机端筛选器
        document.getElementById('searchInputMobile').value = this.searchTerm;
        document.getElementById('categoryFilterMobile').value = this.categoryFilter;
        document.getElementById('statusFilterMobile').value = this.statusFilter;
        document.getElementById('currencyFilterMobile').value = this.currencyFilter;
    }

    // 缓存管理方法
    generateCacheKey(page, search, category, status, currency) {
        return `items_${page}_${search}_${category}_${status}_${currency}_${this.pageSize}`;
    }

    isCacheValid(key) {
        const expiry = this.cacheExpiry.get(key);
        return expiry && Date.now() < expiry;
    }

    setCache(key, data) {
        this.cache.set(key, data);
        this.cacheExpiry.set(key, Date.now() + this.cacheTimeout);
    }

    getCache(key) {
        if (this.isCacheValid(key)) {
            return this.cache.get(key);
        }
        // 清除过期缓存
        this.cache.delete(key);
        this.cacheExpiry.delete(key);
        return null;
    }

    clearAllCache() {
        this.cache.clear();
        this.cacheExpiry.clear();
        console.log('商品管理缓存已清除');
    }

    clearFilterCache() {
        // 清除筛选相关的缓存
        const keysToDelete = [];
        for (let key of this.cache.keys()) {
            if (key.startsWith('items_')) {
                keysToDelete.push(key);
            }
        }
        keysToDelete.forEach(key => {
            this.cache.delete(key);
            this.cacheExpiry.delete(key);
        });
        console.log('筛选缓存已清除');
        this.updateCacheStatus();
    }

    // 更新缓存状态显示
    updateCacheStatus() {
        const cacheCount = this.cache.size;
        const validCacheCount = Array.from(this.cache.keys()).filter(key => this.isCacheValid(key)).length;

        document.getElementById('cacheCount').textContent = cacheCount;

        if (cacheCount === 0) {
            document.getElementById('cacheStatus').textContent = '空';
            document.getElementById('cacheInfo').textContent = '';
        } else {
            document.getElementById('cacheStatus').textContent = '活跃';
            document.getElementById('cacheInfo').textContent = `有效缓存: ${validCacheCount}`;
        }
    }

    // 定期清理过期缓存
    cleanExpiredCache() {
        const now = Date.now();
        const keysToDelete = [];

        for (let [key, expiry] of this.cacheExpiry.entries()) {
            if (now >= expiry) {
                keysToDelete.push(key);
            }
        }

        keysToDelete.forEach(key => {
            this.cache.delete(key);
            this.cacheExpiry.delete(key);
        });

        if (keysToDelete.length > 0) {
            console.log(`清理了 ${keysToDelete.length} 个过期缓存`);
            this.updateCacheStatus();
        }
    }

    // 清理资源
    cleanup() {
        // 清理缓存清理定时器
        if (this.cacheCleanupInterval) {
            clearInterval(this.cacheCleanupInterval);
            this.cacheCleanupInterval = null;
        }

        console.log('商品管理资源已清理');
    }

    async loadStats() {
        const statsCacheKey = 'admin_item_stats';

        // 检查统计数据缓存
        const cachedStats = this.getCache(statsCacheKey);
        if (cachedStats) {
            console.log('使用缓存统计数据');
            // 更新桌面端统计
            document.getElementById('totalItems').textContent = AdminUtils.formatNumber(cachedStats.total);
            document.getElementById('activeItems').textContent = AdminUtils.formatNumber(cachedStats.active);
            document.getElementById('inactiveItems').textContent = AdminUtils.formatNumber(cachedStats.inactive);

            // 计算并更新百分比
            this.updatePercentages(cachedStats);

            // 更新手机端统计
            document.getElementById('totalItemsMobile').textContent = AdminUtils.formatNumber(cachedStats.total);
            document.getElementById('activeItemsMobile').textContent = AdminUtils.formatNumber(cachedStats.active);
            document.getElementById('inactiveItemsMobile').textContent = AdminUtils.formatNumber(cachedStats.inactive);
            return;
        }

        try {
            console.log('从服务器加载统计数据');
            const response = await fetch('/admin/item/getItemList?stats=1');
            const result = await response.json();

            if (result.code === 200) {
                const data = result.data || result;

                // 直接使用API返回的统计数据
                const statsData = {
                    total: data.total || 0,
                    active: data.active || 0,
                    inactive: data.inactive || 0
                };

                // 缓存统计数据（较短的缓存时间）
                this.cache.set(statsCacheKey, statsData);
                this.cacheExpiry.set(statsCacheKey, Date.now() + (2 * 60 * 1000)); // 2分钟缓存

                // 更新桌面端统计
                document.getElementById('totalItems').textContent = AdminUtils.formatNumber(statsData.total);
                document.getElementById('activeItems').textContent = AdminUtils.formatNumber(statsData.active);
                document.getElementById('inactiveItems').textContent = AdminUtils.formatNumber(statsData.inactive);

                // 计算并更新百分比
                this.updatePercentages(statsData);

                // 更新手机端统计
                document.getElementById('totalItemsMobile').textContent = AdminUtils.formatNumber(statsData.total);
                document.getElementById('activeItemsMobile').textContent = AdminUtils.formatNumber(statsData.active);
                document.getElementById('inactiveItemsMobile').textContent = AdminUtils.formatNumber(statsData.inactive);
            } else {
                console.error('获取统计数据失败:', result.message || '未知错误');
            }
        } catch (error) {
            console.error('加载统计数据失败:', error);
            // 设置默认值
            document.getElementById('totalItems').textContent = '0';
            document.getElementById('activeItems').textContent = '0';
            document.getElementById('inactiveItems').textContent = '0';
        }
    }

    updatePercentages(statsData) {
        const total = statsData.total || 0;

        if (total > 0) {
            const activePercentage = Math.round((statsData.active / total) * 100);
            const inactivePercentage = Math.round((statsData.inactive / total) * 100);

            // 更新百分比显示
            const activePercentageElement = document.getElementById('activePercentage');
            const inactivePercentageElement = document.getElementById('inactivePercentage');

            if (activePercentageElement) {
                activePercentageElement.textContent = `${activePercentage}%`;
            }

            if (inactivePercentageElement) {
                inactivePercentageElement.textContent = `${inactivePercentage}%`;
            }
        } else {
            // 如果总数为0，显示0%
            const activePercentageElement = document.getElementById('activePercentage');
            const inactivePercentageElement = document.getElementById('inactivePercentage');

            if (activePercentageElement) {
                activePercentageElement.textContent = '0%';
            }

            if (inactivePercentageElement) {
                inactivePercentageElement.textContent = '0%';
            }
        }
    }

    async loadItems() {
        // 防止重复加载
        if (this.isLoading) {
            console.log('正在加载中，跳过重复请求');
            return;
        }

        // 生成缓存键
        const cacheKey = this.generateCacheKey(
            this.currentPage,
            this.searchTerm,
            this.categoryFilter,
            this.statusFilter,
            this.currencyFilter
        );

        // 检查缓存
        const cachedData = this.getCache(cacheKey);
        if (cachedData) {
            console.log('使用缓存数据:', cacheKey);
            this.renderItems(cachedData.list || cachedData.items || cachedData);
            this.updatePagination(cachedData.total || cachedData.count || 0);
            this.updateCacheStatus();
            return;
        }

        const tbody = document.getElementById('itemsTableBody');
        this.isLoading = true;

        tbody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <div class="mt-2">正在加载商品数据...</div>
                </td>
            </tr>
        `;

        try {
            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.pageSize,
                search: this.searchTerm,
                category: this.categoryFilter,
                status: this.statusFilter,
                currency: this.currencyFilter
            });

            console.log('从服务器加载数据:', cacheKey);
            const response = await fetch(`/admin/item/getItemList?${params}`);
            const result = await response.json();

            if (result.code === 200 || result.success) {
                const data = result.data || result;

                // 缓存数据
                this.setCache(cacheKey, data);
                console.log('数据已缓存:', cacheKey);

                this.renderItems(data.list || data.items || data);
                this.updatePagination(data.total || data.count || 0);
                this.updateCacheStatus();
            } else {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="6" class="text-center py-4 text-danger">
                            <i class="bi bi-exclamation-triangle"></i>
                            <div class="mt-2">${result.message || '加载失败'}</div>
                        </td>
                    </tr>
                `;
            }
        } catch (error) {
            console.error('加载商品列表失败:', error);
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center py-4 text-danger">
                        <i class="bi bi-wifi-off"></i>
                        <div class="mt-2">网络错误，请稍后重试</div>
                    </td>
                </tr>
            `;
        } finally {
            this.isLoading = false;
        }
    }

    renderItems(items) {
        // 渲染桌面端表格
        this.renderDesktopItems(items);

        // 渲染手机端列表
        this.renderMobileItems(items);
    }

    renderDesktopItems(items) {
        const tbody = document.getElementById('itemsTableBody');

        if (!items || items.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center py-4 text-muted">
                        <i class="bi bi-inbox"></i>
                        <div class="mt-2">暂无商品数据</div>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = items.map(item => `
            <tr>
                <td>
                    <div class="d-flex align-items-center">
                        <img src="${item.icon || '/static/img/default.png'}"
                             alt="${item.name || item.item_name || '未知商品'}"
                             class="item-image me-2"
                             onerror="this.src='/static/img/default.png'">
                        <div>
                            <div class="fw-medium">${item.name || item.item_name || '未知商品'}</div>
                            <small class="text-muted">ID: ${item.item || item.item_id || item.id}</small>
                        </div>
                    </div>
                </td>
                <td>
                    <span class="badge bg-secondary">${ShopConfig.getCategoryName(item.class || item.category)}</span>
                </td>
                <td>
                    <span class="item-price">${AdminUtils.formatNumber(item.price || 0)}</span>
                </td>
                <td>
                    <span class="badge ${ShopConfig.getCurrencyColorClass(item.price_type || item.currency_type)}">
                        ${ShopConfig.getCurrencyName(item.price_type || item.currency_type)}
                    </span>
                </td>
                <td>
                    <span class="badge ${item.delivery_mode == 1 ? 'bg-primary' : 'bg-info'}">
                        ${item.delivery_mode == 1 ? '批量发送' : '逐条发送'}
                    </span>
                </td>
                <td>
                    <span class="badge ${ShopConfig.getItemStatusColorClass(item.status)}">
                        ${ShopConfig.getItemStatusName(item.status)}
                    </span>
                </td>
                <td>
                    <div class="item-actions">
                        <button class="btn btn-sm btn-outline-primary" onclick="itemManagement.viewItem(${item.id})" title="查看">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-warning" onclick="itemManagement.editItem(${item.id})" title="编辑">
                            <i class="bi bi-pencil"></i>
                        </button>
                        ${item.status == 1 ?
                            `<button class="btn btn-sm btn-outline-secondary" onclick="itemManagement.toggleItemStatus(${item.id}, 2)" title="下架">
                                <i class="bi bi-eye-slash"></i>
                            </button>` :
                            `<button class="btn btn-sm btn-outline-success" onclick="itemManagement.toggleItemStatus(${item.id}, 1)" title="上架">
                                <i class="bi bi-eye"></i>
                            </button>`
                        }
                        <button class="btn btn-sm btn-outline-danger" onclick="itemManagement.deleteItem(${item.id})" title="删除">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    renderMobileItems(items) {
        const container = document.getElementById('mobileItemsList');
        const loadingState = document.getElementById('mobileLoadingState');

        // 隐藏加载状态
        if (loadingState) {
            loadingState.style.display = 'none';
        }

        if (!items || items.length === 0) {
            container.innerHTML = `
                <div class="text-center py-4 text-muted">
                    <i class="bi bi-inbox" style="font-size: 2rem;"></i>
                    <div class="mt-2">暂无商品数据</div>
                </div>
            `;
            return;
        }

        container.innerHTML = items.map(item => `
            <div class="card mb-2 border-0 shadow-sm">
                <div class="card-body p-3">
                    <div class="row align-items-center">
                        <div class="col-3">
                            <img src="${item.icon || '/static/img/default.png'}"
                                 alt="${item.name || item.item_name || '未知商品'}"
                                 class="img-fluid rounded"
                                 style="max-height: 60px; object-fit: cover;"
                                 onerror="this.src='/static/img/default.png'">
                        </div>
                        <div class="col-9">
                            <div class="d-flex justify-content-between align-items-start mb-1">
                                <h6 class="mb-0 text-truncate" style="max-width: 70%;">
                                    ${item.name || item.item_name || '未知商品'}
                                </h6>
                                <span class="badge ${ShopConfig.getItemStatusColorClass(item.status)} ms-1">
                                    ${ShopConfig.getItemStatusName(item.status)}
                                </span>
                            </div>
                            <div class="small text-muted mb-2">
                                ID: ${item.item || item.item_id || item.id} |
                                <span class="badge bg-secondary">${ShopConfig.getCategoryName(item.class || item.category)}</span> |
                                <span class="badge ${item.delivery_mode == 1 ? 'bg-primary' : 'bg-info'}">${item.delivery_mode == 1 ? '批量发送' : '逐条发送'}</span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="fw-bold text-primary">${AdminUtils.formatNumber(item.price || 0)}</span>
                                    <span class="badge ${ShopConfig.getCurrencyColorClass(item.price_type || item.currency_type)} ms-1">
                                        ${ShopConfig.getCurrencyName(item.price_type || item.currency_type)}
                                    </span>
                                </div>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-outline-primary btn-sm" onclick="itemManagement.viewItem(${item.id})" title="查看">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" onclick="itemManagement.editItem(${item.id})" title="编辑">
                                        <i class="bi bi-pencil"></i>
                                    </button>
                                    ${item.status == 1 ?
                                        `<button class="btn btn-outline-secondary btn-sm" onclick="itemManagement.toggleItemStatus(${item.id}, 2)" title="下架">
                                            <i class="bi bi-eye-slash"></i>
                                        </button>` :
                                        `<button class="btn btn-outline-success btn-sm" onclick="itemManagement.toggleItemStatus(${item.id}, 1)" title="上架">
                                            <i class="bi bi-eye"></i>
                                        </button>`
                                    }
                                    <button class="btn btn-outline-danger btn-sm" onclick="itemManagement.deleteItem(${item.id})" title="删除">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }



    updatePagination(total) {
        this.totalPages = Math.ceil(total / this.pageSize);

        const start = (this.currentPage - 1) * this.pageSize + 1;
        const end = Math.min(this.currentPage * this.pageSize, total);

        // 更新桌面端分页信息
        document.getElementById('pageStart').textContent = total > 0 ? start : 0;
        document.getElementById('pageEnd').textContent = end;
        document.getElementById('totalCount').textContent = total;

        // 更新手机端分页信息
        document.getElementById('pageStartMobile').textContent = total > 0 ? start : 0;
        document.getElementById('pageEndMobile').textContent = end;
        document.getElementById('totalCountMobile').textContent = total;

        console.log('updatePagination called - total:', total, 'totalPages:', this.totalPages, 'currentPage:', this.currentPage);

        // 创建或更新桌面端分页组件
        if (!this.pagination) {
            this.pagination = new AdminPagination('#pagination', {
                currentPage: this.currentPage,
                totalPages: this.totalPages,
                onPageChange: (page) => {
                    this.currentPage = page;
                    this.loadItems(); // AdminPagination已经处理了防抖
                }
            });
        } else {
            this.pagination.update(this.currentPage, this.totalPages);
        }

        // 创建或更新手机端分页组件
        if (!this.paginationMobile) {
            console.log('Creating mobile pagination component');
            this.paginationMobile = new AdminPagination('#paginationMobile', {
                currentPage: this.currentPage,
                totalPages: this.totalPages,
                onPageChange: (page) => {
                    this.currentPage = page;
                    this.loadItems(); // AdminPagination已经处理了防抖
                }
            });
        } else {
            console.log('Updating mobile pagination component');
            this.paginationMobile.update(this.currentPage, this.totalPages);
        }
    }

    showAddItemModal() {
        this.showItemDetailModal(null, 'add');
    }

    async viewItem(id) {
        try {
            const response = await fetch(`/admin/item/getItemDetail?item_id=${id}`);
            const result = await response.json();

            if (result.code === 200) {
                const item = result.data;
                this.showItemDetailModal(item, 'view');
            } else {
                AdminUtils.showToast(result.message || '获取商品详情失败', 'danger');
            }
        } catch (error) {
            console.error('查看商品失败:', error);
            AdminUtils.showToast('网络错误，请稍后重试', 'danger');
        }
    }

    async editItem(id) {
        try {
            const response = await fetch(`/admin/item/getItemDetail?item_id=${id}`);
            const result = await response.json();

            if (result.code === 200) {
                const item = result.data;
                this.showItemDetailModal(item, 'edit');
            } else {
                AdminUtils.showToast(result.message || '获取商品详情失败', 'danger');
            }
        } catch (error) {
            console.error('编辑商品失败:', error);
            AdminUtils.showToast('网络错误，请稍后重试', 'danger');
        }
    }

    async showItemDetailModal(item, mode) {
        const modal = new bootstrap.Modal(document.getElementById('itemDetailModal'));
        const title = document.getElementById('itemModalTitle');
        const form = document.getElementById('itemForm');
        const saveButton = document.getElementById('saveItemButton');

        // 设置模态框标题和模式
        this.currentMode = mode;
        this.currentItemId = item ? (item.id || item.item_id) : null;

        switch (mode) {
            case 'view':
                title.textContent = '查看商品详情';
                saveButton.style.display = 'none';
                this.setFormReadonly(true);
                break;
            case 'edit':
                title.textContent = '编辑商品';
                saveButton.style.display = 'block';
                saveButton.textContent = '更新';
                this.setFormReadonly(false);
                break;
            case 'add':
                title.textContent = '添加商品';
                saveButton.style.display = 'block';
                saveButton.textContent = '添加';
                this.setFormReadonly(false);
                break;
        }

        // 加载选项数据
        await Promise.all([
            this.loadCategories(),
            this.loadCurrencyTypes(),
            this.loadRestrictionTypes()
        ]);

        // 填充表单数据
        if (item) {
            this.fillForm(item);
        } else {
            form.reset();
        }

        // 绑定保存按钮事件
        saveButton.onclick = () => this.saveItem();

        modal.show();
    }

    async loadCategories() {
        try {
            const response = await fetch('/admin/item/getCategories');
            const result = await response.json();

            if (result.code === 200) {
                const select = document.getElementById('itemClass');
                select.innerHTML = '';

                result.data.forEach(category => {
                    if (category.value !== '') { // 排除"全部分类"选项
                        const option = document.createElement('option');
                        option.value = category.value;
                        option.textContent = category.label;
                        select.appendChild(option);
                    }
                });
            }
        } catch (error) {
            console.error('加载分类失败:', error);
        }
    }

    async loadCurrencyTypes() {
        try {
            // 使用全局配置中的货币类型
            const currencyTypes = window.ShopConfig.currency_types || {};
            const select = document.getElementById('itemPriceType');
            select.innerHTML = '';

            Object.entries(currencyTypes).forEach(([key, currency]) => {
                const option = document.createElement('option');
                option.value = key;
                option.textContent = `${currency.name} (${currency.short_name})`;
                select.appendChild(option);
            });
        } catch (error) {
            console.error('加载货币类型失败:', error);
            // 如果加载失败，使用默认选项
            const select = document.getElementById('itemPriceType');
            select.innerHTML = `
                <option value="1">泡点</option>
                <option value="2">人民币</option>
                <option value="3">美元</option>
            `;
        }
    }

    async loadRestrictionTypes() {
        try {
            // 使用全局配置中的限制类型
            const restrictionTypes = window.ShopConfig.purchase_restrictions || {};
            const select = document.getElementById('itemRestriction');
            select.innerHTML = '';

            Object.entries(restrictionTypes).forEach(([key, name]) => {
                const option = document.createElement('option');
                option.value = key;
                option.textContent = name;
                select.appendChild(option);
            });
        } catch (error) {
            console.error('加载限制类型失败:', error);
            // 如果加载失败，使用默认选项
            const select = document.getElementById('itemRestriction');
            select.innerHTML = `
                <option value="0">不限购</option>
                <option value="1">账号限购</option>
            `;
        }
    }

    fillForm(item) {
        document.getElementById('itemName').value = item.name || '';
        document.getElementById('itemId').value = item.item || item.item_id || '';
        document.getElementById('itemPrice').value = item.price || '';
        document.getElementById('itemPriceType').value = item.price_type || '1';
        document.getElementById('itemClass').value = item.class || item.category || '';
        document.getElementById('itemStatus').value = item.status || '1';
        document.getElementById('itemRestriction').value = item.restriction || '0';
        document.getElementById('itemLimitQuantity').value = item.limit_quantity || '1';
        document.getElementById('itemDeliveryMode').value = item.delivery_mode || '1';
        document.getElementById('itemInfo').value = item.item_info || item.description || '';
    }

    setFormReadonly(readonly) {
        const form = document.getElementById('itemForm');
        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.readOnly = readonly;
            input.disabled = readonly;
        });
    }

    async saveItem() {
        const form = document.getElementById('itemForm');
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());

        // 字段映射：前端字段名 -> 后端期望字段名
        if (data.class) {
            data.category = data.class;
            delete data.class;
        }
        if (data.item_info) {
            data.description = data.item_info;
            delete data.item_info;
        }

        try {
            let response;
            if (this.currentMode === 'add') {
                response = await fetch('/admin/item/addItem', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
            } else if (this.currentMode === 'edit') {
                data.item_id = this.currentItemId;
                response = await fetch('/admin/item/updateItem', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
            }

            const result = await response.json();

            if (result.code === 200) {
                AdminUtils.showToast(result.message || '操作成功', 'success');
                bootstrap.Modal.getInstance(document.getElementById('itemDetailModal')).hide();
                this.clearAllCache();
                this.loadItems();
                this.loadStats();
            } else {
                AdminUtils.showToast(result.message || '操作失败', 'danger');
            }
        } catch (error) {
            console.error('保存商品失败:', error);
            AdminUtils.showToast('网络错误，请稍后重试', 'danger');
        }
    }

    async toggleItemStatus(id, status) {
        const action = status === 1 ? '上架' : '下架';
        const confirmed = await AdminUtils.confirm(`确定要${action}这个商品吗？`, `${action}商品`);
        if (!confirmed) return;

        try {
            const response = await fetch('/admin/item/updateItemStatus', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ id: id, status: status })
            });

            // 检查HTTP状态
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            // 检查响应内容类型
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                const text = await response.text();
                console.error('服务器返回非JSON响应:', text);
                throw new Error('服务器返回了无效的响应格式');
            }

            const result = await response.json();

            if (result.code === 200 || result.success) {
                AdminUtils.showToast(`商品${action}成功`, 'success');
                this.clearAllCache(); // 清除缓存以确保数据一致性
                this.loadItems(); // 这里不使用防抖，因为是用户操作后的立即刷新
                this.loadStats();
            } else {
                AdminUtils.showToast(result.message || `${action}失败`, 'danger');
            }
        } catch (error) {
            console.error(`${action}商品失败:`, error);
            if (error.message.includes('JSON')) {
                AdminUtils.showToast('服务器响应格式错误，请检查服务器日志', 'danger');
            } else {
                AdminUtils.showToast(error.message || '网络错误，请稍后重试', 'danger');
            }
        }
    }

    async deleteItem(id) {
        // 显示确认对话框
        const confirmed = await AdminUtils.confirm(
            '确定要删除这个商品吗？删除后无法恢复！',
            '删除商品'
        );
        if (!confirmed) return;

        try {
            const response = await fetch('/admin/item/deleteItem', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ item_id: id })
            });

            // 检查HTTP状态
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            // 检查响应内容类型
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                const text = await response.text();
                console.error('服务器返回非JSON响应:', text);
                throw new Error('服务器返回了无效的响应格式');
            }

            const result = await response.json();

            if (result.code === 200) {
                AdminUtils.showToast('商品删除成功', 'success');
                this.clearAllCache(); // 清除缓存以确保数据一致性
                this.loadItems(); // 重新加载商品列表
                this.loadStats(); // 重新加载统计数据
            } else {
                AdminUtils.showToast(result.message || '删除失败', 'danger');
            }
        } catch (error) {
            console.error('删除商品失败:', error);
            if (error.message.includes('JSON')) {
                AdminUtils.showToast('服务器响应格式错误，请检查服务器日志', 'danger');
            } else {
                AdminUtils.showToast(error.message || '网络错误，请稍后重试', 'danger');
            }
        }
    }
}

// 初始化商品管理 - 防止重复初始化
if (typeof window.itemManagement === 'undefined') {
    window.itemManagement = null;
}

// 双UI系统初始化
function initDualUISystem() {
    // 检测设备类型
    const isMobile = window.innerWidth <= 768 || /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

    // 设置UI模式
    if (isMobile) {
        document.body.classList.add('mobile-ui');
        document.body.classList.remove('desktop-ui');
    } else {
        document.body.classList.add('desktop-ui');
        document.body.classList.remove('mobile-ui');
    }

    console.log('双UI系统已初始化:', isMobile ? 'mobile-ui' : 'desktop-ui');
}

// 监听窗口大小变化
window.addEventListener('resize', function() {
    const isMobileNow = window.innerWidth <= 768;
    if (isMobileNow) {
        document.body.classList.add('mobile-ui');
        document.body.classList.remove('desktop-ui');
    } else {
        document.body.classList.add('desktop-ui');
        document.body.classList.remove('mobile-ui');
    }
});

document.addEventListener('DOMContentLoaded', () => {
    // 初始化双UI系统
    initDualUISystem();

    if (!window.itemManagement) {
        window.itemManagement = new ItemManagement();
    }
});
</script>
{/block}
