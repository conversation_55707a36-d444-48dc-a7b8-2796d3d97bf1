-- 周卡月卡系统数据库表结构

-- 卡片配置表
CREATE TABLE IF NOT EXISTS `card_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `card_type` varchar(20) NOT NULL COMMENT '卡片类型：weekly, monthly',
  `name` varchar(50) NOT NULL COMMENT '卡片名称',
  `description` text COMMENT '卡片描述',
  `price` decimal(10,2) NOT NULL COMMENT '价格（人民币）',
  `duration_days` int(11) NOT NULL COMMENT '有效期天数',
  `instant_reward` text COMMENT '即时奖励配置(JSON): [{"type":"item","item_id":32268,"number":1},{"type":"currency","currency_type":"c_coin","amount":500}]',
  `daily_reward` text COMMENT '每日奖励配置(JSON): [{"type":"item","item_id":32269,"number":1},{"type":"currency","currency_type":"point","amount":100}]',
  `icon` varchar(255) DEFAULT NULL COMMENT '卡片图标CSS类',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1启用，0禁用',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_card_type` (`card_type`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='卡片配置表';

-- 用户卡片购买记录表
CREATE TABLE IF NOT EXISTS `user_cards` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(50) NOT NULL COMMENT '用户ID',
  `card_id` int(11) NOT NULL COMMENT '卡片配置ID',
  `card_type` varchar(20) NOT NULL COMMENT '卡片类型',
  `purchase_time` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '购买时间',
  `expire_time` timestamp NOT NULL COMMENT '过期时间',
  `instant_reward_claimed` tinyint(1) DEFAULT 0 COMMENT '即时奖励是否已领取',
  `total_days` int(11) NOT NULL COMMENT '总天数',
  `claimed_days` int(11) DEFAULT 0 COMMENT '已领取天数',
  `last_claim_date` date DEFAULT NULL COMMENT '最后领取日期',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1有效，0过期',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_card_id` (`card_id`),
  KEY `idx_card_type` (`card_type`),
  KEY `idx_expire_time` (`expire_time`),
  KEY `idx_status` (`status`),
  FOREIGN KEY (`card_id`) REFERENCES `card_config`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户卡片购买记录表';

-- 卡片领取记录表
CREATE TABLE IF NOT EXISTS `card_claim_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(50) NOT NULL COMMENT '用户ID',
  `user_card_id` int(11) NOT NULL COMMENT '用户卡片记录ID',
  `claim_type` varchar(20) NOT NULL COMMENT '领取类型：instant, daily',
  `claim_date` date NOT NULL COMMENT '领取日期',
  `reward_data` text COMMENT '奖励数据(JSON)',
  `claim_time` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '领取时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_user_card_id` (`user_card_id`),
  KEY `idx_claim_type` (`claim_type`),
  KEY `idx_claim_date` (`claim_date`),
  FOREIGN KEY (`user_card_id`) REFERENCES `user_cards`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='卡片领取记录表';

-- 插入默认卡片配置
INSERT INTO `card_config` (`card_type`, `name`, `description`, `price`, `duration_days`, `instant_reward`, `daily_reward`, `icon`, `sort_order`) VALUES
('weekly', '周卡', '购买即送500C币+神秘道具，连续7天每天可领取100C币+每日礼包', 6.00, 7,
 '[{"type":"currency","currency_type":"c_coin","amount":500},{"type":"item","item_id":32268,"number":1}]',
 '[{"type":"currency","currency_type":"c_coin","amount":100},{"type":"item","item_id":32269,"number":1}]',
 'bi-calendar-week', 1),
('monthly', '月卡', '购买即送2000C币+高级道具包，连续30天每天可领取150C币+每日豪华礼包', 18.00, 30,
 '[{"type":"currency","currency_type":"c_coin","amount":2000},{"type":"currency","currency_type":"point","amount":1000},{"type":"item","item_id":32270,"number":1}]',
 '[{"type":"currency","currency_type":"c_coin","amount":150},{"type":"currency","currency_type":"point","amount":50},{"type":"item","item_id":32271,"number":1}]',
 'bi-calendar-month', 2);
